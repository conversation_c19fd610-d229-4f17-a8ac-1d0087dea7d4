# Cleanup Static Analysis Report

- Total modules scanned: 77
- Entry points: main, build_package, build_package_backup
- Reachable modules: 55
- Unreachable modules: 22

## Unreachable Modules (from main/build scripts)
- app.__init__
- core.__init__
- license_generator
- services.__init__
- services.bluetooth.__init__
- services.data_protection.__init__
- services.data_protection.hardware_fingerprint_protector
- services.eeg_preprocessing.__init__
- services.eeg_preprocessing.kalman_processor
- services.eeg_preprocessing.preprocessing_pipeline
- services.eeg_preprocessing.rls_filter
- services.eeg_preprocessing.traditional_filter
- services.eeg_processing.__init__
- services.eeg_processing.eeg_data_processor
- services.stimulation.__init__
- tools.analyze_project_usage
- ui.__init__
- ui.components.__init__
- ui.components.interactive_chart
- ui.pages.__init__
- ui.themes.__init__
- utils.__init__

## Files with Unused Imports
- license_generator: 2 unused imports
- app.application: 2 unused imports
- app.background_loader: 10 unused imports
- app.config: 2 unused imports
- core.database: 3 unused imports
- services.api_client: 1 unused imports
- services.auth_service: 3 unused imports
- services.classifier_training_manager: 2 unused imports
- services.license_manager: 6 unused imports
- services.multi_round_training_manager: 1 unused imports
- services.patient_service: 5 unused imports
- services.plan_a_classifier: 2 unused imports
- services.plan_a_manager: 1 unused imports
- services.reference_data_service: 1 unused imports
- services.report_service: 2 unused imports
- services.training_session_manager: 2 unused imports
- services.treatment_service: 1 unused imports
- services.user_service: 1 unused imports
- services.bluetooth.standard_bleak_manager: 3 unused imports
- services.bluetooth.__init__: 2 unused imports
- services.data_protection.__init__: 2 unused imports
- services.eeg_preprocessing.kalman_processor: 2 unused imports
- services.eeg_preprocessing.preprocessing_config: 2 unused imports
- services.eeg_preprocessing.preprocessing_pipeline: 2 unused imports
- services.eeg_preprocessing.realtime_integration_manager: 1 unused imports
- services.eeg_preprocessing.rls_filter: 1 unused imports
- services.eeg_preprocessing.traditional_filter: 2 unused imports
- services.eeg_preprocessing.__init__: 5 unused imports
- services.eeg_processing.eeg_data_processor: 1 unused imports
- services.eeg_processing.__init__: 1 unused imports
- services.stimulation.__init__: 3 unused imports
- tools.analyze_project_usage: 2 unused imports
- ui.login_window: 5 unused imports
- ui.main_window: 3 unused imports
- ui.components.chart_widgets: 1 unused imports
- ui.components.filter_panel: 3 unused imports
- ui.components.interactive_chart: 6 unused imports
- ui.components.no_wheel_widgets: 1 unused imports
- ui.components.parameter_adjuster: 1 unused imports
- ui.components.pyqtgraph_curves_widget: 3 unused imports
- ui.components.sidebar: 9 unused imports
- ui.components.status_indicator: 3 unused imports
- ui.components.themed_message_box: 2 unused imports
- ui.components.topbar: 1 unused imports
- ui.dialogs.activation_dialog: 5 unused imports
- ui.pages.patients_page: 12 unused imports
- ui.pages.reports_page: 5 unused imports
- ui.pages.settings_page: 6 unused imports
- ui.pages.treatment_page: 14 unused imports
- ui.pages.users_page: 16 unused imports
- utils.chart_helpers: 2 unused imports
- utils.db_helpers: 2 unused imports
- utils.path_manager: 2 unused imports
- utils.user_helpers: 1 unused imports
- utils.warning_manager: 1 unused imports
- utils.__init__: 1 unused imports

## Modules with Potentially Unused Top-level Definitions
- build_package: 1 functions, 0 classes
- core.simple_voice: 2 functions, 0 classes
- core.udp_communicator: 1 functions, 0 classes
- services.training_session_manager: 0 functions, 1 classes
- services.data_protection.hardware_fingerprint_protector: 0 functions, 1 classes
- services.eeg_preprocessing.kalman_processor: 0 functions, 1 classes
- services.eeg_preprocessing.preprocessing_config: 0 functions, 1 classes
- services.eeg_preprocessing.preprocessing_pipeline: 0 functions, 1 classes
- services.eeg_preprocessing.rls_filter: 0 functions, 1 classes
- services.eeg_preprocessing.traditional_filter: 0 functions, 1 classes
- services.eeg_processing.eeg_data_processor: 0 functions, 1 classes
- ui.components.chart_widgets: 0 functions, 1 classes
- ui.components.interactive_chart: 0 functions, 1 classes
- ui.components.stats_card: 0 functions, 2 classes
- ui.components.status_indicator: 0 functions, 1 classes
- ui.components.themed_message_box: 2 functions, 0 classes
- utils.db_helpers: 3 functions, 0 classes
- utils.path_manager: 1 functions, 0 classes
- utils.user_helpers: 8 functions, 0 classes
- utils.warning_manager: 1 functions, 0 classes

## Keyword Hits (legacy algorithm/classifier related)
- build_package: 14 hits
- license_generator: 1 hits
- main: 9 hits
- app.application: 12 hits
- app.config: 2 hits
- app.__init__: 1 hits
- core.udp_communicator: 13 hits
- services.auth_service: 3 hits
- services.classifier_training_manager: 111 hits
- services.license_manager: 1 hits
- services.multi_round_training_manager: 11 hits
- services.plan_a_classifier: 9 hits
- services.plan_a_manager: 2 hits
- services.training_session_manager: 20 hits
- services.user_service: 1 hits
- services.bluetooth.standard_bleak_manager: 6 hits
- services.eeg_preprocessing.realtime_integration_manager: 3 hits
- services.eeg_preprocessing.rls_filter: 2 hits
- services.eeg_processing.eeg_data_processor: 12 hits
- services.stimulation.__init__: 1 hits
- tools.analyze_project_usage: 32 hits
- ui.login_window: 3 hits
- ui.main_window: 6 hits
- ui.components.filter_panel: 1 hits
- ui.components.interactive_chart: 1 hits
- ui.components.parameter_adjuster: 5 hits
- ui.components.sidebar: 1 hits
- ui.components.stats_card: 2 hits
- ui.components.status_indicator: 4 hits
- ui.components.themed_message_box: 17 hits
- ui.components.topbar: 11 hits
- ui.components.unified_timer_manager: 11 hits
- ui.dialogs.activation_dialog: 2 hits
- ui.pages.patients_page: 13 hits
- ui.pages.reports_page: 1 hits
- ui.pages.settings_page: 14 hits
- ui.pages.treatment_page: 113 hits
- ui.pages.users_page: 6 hits
- ui.themes.theme_manager: 39 hits
- utils.chart_helpers: 1 hits
- utils.simple_permission_manager: 2 hits