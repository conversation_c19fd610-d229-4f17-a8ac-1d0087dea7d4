# -*- coding: utf-8 -*-
"""
图表生成工具类
Chart Generation Helpers

提供各种图表的生成和主题适配功能
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.figure import Figure
import numpy as np
from typing import List, Dict, Any, Tuple
import io
from PySide6.QtGui import QPixmap
from PySide6.QtCore import QByteArray


class ChartGenerator:
    """图表生成器"""
    
    def __init__(self, theme: str = 'tech'):
        self.theme = theme
        self.setup_theme_colors()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def setup_theme_colors(self):
        """设置主题颜色"""
        if self.theme == 'medical':
            self.colors = {
                'primary': '#3b82f6',
                'secondary': '#10b981', 
                'accent': '#f59e0b',
                'danger': '#ef4444',
                'background': '#ffffff',
                'text': '#1e293b',
                'grid': '#e2e8f0',
                'chart_colors': ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']
            }
        else:  # tech theme
            self.colors = {
                'primary': '#06b6d4',
                'secondary': '#00ff88',
                'accent': '#ffaa00', 
                'danger': '#ff3366',
                'background': '#0f172a',
                'text': '#f1f5f9',
                'grid': '#475569',
                'chart_colors': ['#06b6d4', '#00ff88', '#ffaa00', '#ff3366', '#8b5cf6', '#f97316']
            }
    
    def create_line_chart(self, data: List[Dict[str, Any]],
                         x_field: str, y_field: str,
                         title: str = "", xlabel: str = "", ylabel: str = "",
                         figsize: Tuple[int, int] = (10, 5)) -> QPixmap:
        """
        创建折线图

        Args:
            data: 数据列表
            x_field: X轴字段名
            y_field: Y轴字段名
            title: 图表标题
            xlabel: X轴标签
            ylabel: Y轴标签
            figsize: 图表尺寸

        Returns:
            QPixmap图像
        """
        try:
            fig = Figure(figsize=figsize, facecolor=self.colors['background'], dpi=100)
            ax = fig.add_subplot(111)

            # 提取数据
            x_data = [item[x_field] for item in data]
            y_data = [item[y_field] for item in data]

            # 绘制折线图，使用较小的数据点标记作为视觉引导
            ax.plot(x_data, y_data, color=self.colors['primary'], linewidth=2.5,
                   marker='o', markersize=4, markerfacecolor=self.colors['primary'],
                   markeredgecolor='white', markeredgewidth=1, alpha=0.9)

            # 设置样式
            ax.set_facecolor(self.colors['background'])
            # ax.set_title(title, color=self.colors['text'], fontsize=12, fontweight='bold', pad=15)  # 去掉标题
            ax.set_xlabel(xlabel, color=self.colors['text'], fontsize=10)
            ax.set_ylabel(ylabel, color=self.colors['text'], fontsize=10)

            # 设置网格
            ax.grid(True, color=self.colors['grid'], alpha=0.3, linestyle='-', linewidth=0.5)

            # 设置坐标轴颜色
            ax.tick_params(colors=self.colors['text'], labelsize=8)
            for spine in ax.spines.values():
                spine.set_color(self.colors['grid'])

            # 优化X轴标签显示
            if len(x_data) > 0 and isinstance(x_data[0], str) and '-' in str(x_data[0]):
                # 如果数据点太多，只显示部分标签
                if len(x_data) > 15:
                    # 计算标签间隔
                    step = max(1, len(x_data) // 10)
                    ticks = range(0, len(x_data), step)
                    ax.set_xticks(ticks)
                    ax.set_xticklabels([x_data[i] for i in ticks])
                plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=8)

            # 使用更精确的布局调整
            fig.tight_layout(pad=2.0)

            return self._fig_to_pixmap(fig)
        except Exception as e:
            print(f"创建折线图失败: {e}")
            return self._create_error_pixmap("折线图生成失败")
    
    def create_bar_chart(self, data: List[Dict[str, Any]],
                        x_field: str, y_field: str,
                        title: str = "", xlabel: str = "", ylabel: str = "",
                        figsize: Tuple[int, int] = (10, 5)) -> QPixmap:
        """
        创建柱状图

        Args:
            data: 数据列表
            x_field: X轴字段名
            y_field: Y轴字段名
            title: 图表标题
            xlabel: X轴标签
            ylabel: Y轴标签
            figsize: 图表尺寸

        Returns:
            QPixmap图像
        """
        try:
            fig = Figure(figsize=figsize, facecolor=self.colors['background'], dpi=100)
            ax = fig.add_subplot(111)

            # 提取数据
            x_data = [item[x_field] for item in data]
            y_data = [item[y_field] for item in data]

            # 创建颜色列表
            colors = [self.colors['chart_colors'][i % len(self.colors['chart_colors'])] for i in range(len(x_data))]

            # 绘制柱状图
            bars = ax.bar(x_data, y_data, color=colors, alpha=0.8, edgecolor=self.colors['text'], linewidth=0.5)

            # 计算Y轴范围，为数值标签留出空间
            max_value = max(y_data) if y_data else 0
            y_margin = max_value * 0.15  # 增加15%的上边距
            ax.set_ylim(0, max_value + y_margin)

            # 在柱子上显示数值，调整位置和字体大小
            for bar, value in zip(bars, y_data):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max_value * 0.02,
                       f'{value}', ha='center', va='bottom', color=self.colors['text'],
                       fontsize=9, fontweight='bold')

            # 设置样式
            ax.set_facecolor(self.colors['background'])
            # ax.set_title(title, color=self.colors['text'], fontsize=12, fontweight='bold', pad=15)  # 去掉标题
            ax.set_xlabel(xlabel, color=self.colors['text'], fontsize=10)
            ax.set_ylabel(ylabel, color=self.colors['text'], fontsize=10)

            # 设置网格
            ax.grid(True, color=self.colors['grid'], alpha=0.3, linestyle='-', linewidth=0.5, axis='y')

            # 设置坐标轴颜色
            ax.tick_params(colors=self.colors['text'], labelsize=8)
            for spine in ax.spines.values():
                spine.set_color(self.colors['grid'])

            # 优化X轴标签显示
            if len(x_data) > 10:
                # 如果标签太多，只显示部分
                step = max(1, len(x_data) // 8)
                ticks = range(0, len(x_data), step)
                ax.set_xticks(ticks)
                ax.set_xticklabels([x_data[i] for i in ticks])

            # 根据标签长度决定是否旋转
            max_label_length = max(len(str(label)) for label in x_data) if x_data else 0
            if max_label_length <= 2:
                # 短标签（1-2个字符）正常显示
                plt.setp(ax.get_xticklabels(), rotation=0, ha='center', fontsize=10)
            else:
                # 长标签旋转显示
                plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=8)

            # 使用更精确的布局调整，为底部标签留出更多空间
            fig.tight_layout(pad=2.0)
            fig.subplots_adjust(bottom=0.2)

            return self._fig_to_pixmap(fig)
        except Exception as e:
            print(f"创建柱状图失败: {e}")
            return self._create_error_pixmap("柱状图生成失败")
    
    def create_pie_chart(self, data: List[Dict[str, Any]],
                        label_field: str, value_field: str,
                        title: str = "", figsize: Tuple[int, int] = (4, 4)) -> QPixmap:
        """
        创建饼图

        Args:
            data: 数据列表
            label_field: 标签字段名
            value_field: 数值字段名
            title: 图表标题
            figsize: 图表尺寸

        Returns:
            QPixmap图像
        """
        try:
            fig = Figure(figsize=figsize, facecolor=self.colors['background'], dpi=100)
            ax = fig.add_subplot(111)

            # 提取数据
            labels = [item[label_field] for item in data]
            values = [item[value_field] for item in data]

            # 治疗效果固定颜色映射
            treatment_effect_colors = {
                '优': '#10b981',  # 绿色系
                '良': '#3b82f6',  # 蓝色系
                '中': '#f59e0b',  # 橙色系
                '差': '#ef4444'   # 红色系
            }

            # 检测是否为治疗效果数据并分配颜色
            if all(label in treatment_effect_colors for label in labels):
                # 如果所有标签都是治疗效果，使用固定颜色映射
                colors = [treatment_effect_colors[label] for label in labels]
            else:
                # 否则使用主题颜色
                colors = [self.colors['chart_colors'][i % len(self.colors['chart_colors'])] for i in range(len(labels))]

            # 绘制饼图，调整标签位置和字体大小
            wedges, texts, autotexts = ax.pie(values, labels=labels, colors=colors, autopct='%1.1f%%',
                                            startangle=90,
                                            textprops={'color': self.colors['text'], 'fontsize': 9},
                                            pctdistance=0.85, labeldistance=1.1)

            # 设置百分比文字颜色和大小
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(9)

            # 设置标签文字大小
            for text in texts:
                text.set_fontsize(9)

            # 不显示标题，让饼图使用更多空间
            # ax.set_title(title, color=self.colors['text'], fontsize=12, fontweight='bold', pad=15)

            # 确保饼图是圆形
            ax.set_aspect('equal', adjustable='box')

            # 设置子图位置，为饼图提供更多空间
            fig.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)

            return self._fig_to_pixmap(fig)
        except Exception as e:
            print(f"创建饼图失败: {e}")
            return self._create_error_pixmap("饼图生成失败")
    

    
    def _fig_to_pixmap(self, fig: Figure) -> QPixmap:
        """将matplotlib图形转换为QPixmap"""
        try:
            # 将图形保存到内存中的字节流
            buf = io.BytesIO()
            fig.savefig(buf, format='png', facecolor=self.colors['background'], 
                       edgecolor='none', bbox_inches='tight', dpi=100)
            buf.seek(0)
            
            # 创建QPixmap
            pixmap = QPixmap()
            pixmap.loadFromData(QByteArray(buf.getvalue()))
            
            buf.close()
            plt.close(fig)  # 关闭图形以释放内存
            
            return pixmap
        except Exception as e:
            print(f"图形转换失败: {e}")
            return self._create_error_pixmap("图形转换失败")
    
    def _create_error_pixmap(self, error_msg: str) -> QPixmap:
        """创建错误提示图像"""
        try:
            fig = Figure(figsize=(6, 4), facecolor=self.colors['background'], dpi=100)
            ax = fig.add_subplot(111)
            
            ax.text(0.5, 0.5, error_msg, ha='center', va='center', 
                   color=self.colors['danger'], fontsize=14, fontweight='bold',
                   transform=ax.transAxes)
            
            ax.set_facecolor(self.colors['background'])
            ax.set_xticks([])
            ax.set_yticks([])
            for spine in ax.spines.values():
                spine.set_visible(False)
            
            return self._fig_to_pixmap(fig)
        except:
            # 如果连错误图像都无法创建，返回空的QPixmap
            return QPixmap()
