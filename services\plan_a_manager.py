#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plan A Manager: 提供与UI对接的最小接口，保持与现有管理器相似的方法。
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, Optional
import numpy as np

from .plan_a_classifier import PlanAClassifier, PlanAConfig


@dataclass
class _EWMASmoother:
    enabled: bool = True
    alpha: float = 0.2
    _p: Optional[float] = None

    def reset(self) -> None:
        self._p = None

    def update(self, p: float) -> float:
        if not self.enabled:
            return p
        if self._p is None:
            self._p = float(p)
        else:
            self._p = float(self.alpha * p + (1.0 - self.alpha) * self._p)
        return self._p


class PlanAManager:
    def __init__(self, clf: Optional[PlanAClassifier] = None, config: Optional[PlanAConfig] = None) -> None:
        self.classifier = clf or PlanAClassifier(config=config)
        self._is_fitted = getattr(self.classifier, "_is_fitted", False)

        # 难度-阈值映射（与UI一致）
        self._difficulty_threshold = {
            1: 0.55,
            2: 0.60,
            3: 0.70,
            4: 0.75,
            5: 0.80,
        }
        self._current_difficulty = 3
        self._custom_trigger_threshold: Optional[float] = None

        # 概率平滑（仅概率层，不改变阈值语义）
        self._smoother = _EWMASmoother(enabled=True, alpha=0.2)

        # 调试
        self._debug = False

    # ---------------- 接口：训练/加载 ----------------
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        self.classifier.fit(X, y)
        self._is_fitted = True

    # ---------------- 接口：UI参数 ----------------
    def set_difficulty_level(self, level: int) -> None:
        if level in self._difficulty_threshold:
            self._current_difficulty = level
            # 如设置了自定义阈值，不覆盖

    def set_custom_thresholds(self, trigger_threshold: Optional[float] = None) -> None:
        if trigger_threshold is not None:
            self._custom_trigger_threshold = float(trigger_threshold)

    def get_current_thresholds(self) -> float:
        if self._custom_trigger_threshold is not None:
            return float(self._custom_trigger_threshold)
        return float(self._difficulty_threshold.get(self._current_difficulty, 0.70))

    # ---------------- 接口：平滑与调试 ----------------
    def set_temporal_smoothing(self, enabled: bool, method: str = "ewma", alpha: float = 0.2) -> None:
        # 仅支持ewma；保持接口兼容
        self._smoother.enabled = bool(enabled)
        self._smoother.alpha = float(alpha)

    def reset_temporal_smoother(self) -> None:
        self._smoother.reset()

    def set_realtime_debug(self, enabled: bool = True) -> None:
        self._debug = bool(enabled)

    # ---------------- 接口：预测 ----------------
    def predict_with_details(self, X_window: np.ndarray, simple_output: bool = False) -> Dict[str, Any]:
        if not self._is_fitted:
            raise ValueError("PlanAManager: 模型未训练")

        comps = {}
        try:
            comps = self.classifier.predict_proba_components(X_window)
            p = float(comps.get("p", 0.5))
            p_mdm = float(comps.get("p_mdm", 0.5))
            p_tslr = float(comps.get("p_tslr", 0.5))
        except AttributeError:
            # 兼容旧版本分类器（无组件输出）
            p = self.classifier.predict_proba(X_window)
            p_mdm = float('nan')
            p_tslr = float('nan')

        p_sm = self._smoother.update(p)
        threshold = self.get_current_thresholds()

        trigger = (p_sm >= threshold)
        final_prediction = int(p_sm >= 0.5)
        final_probability = np.array([1.0 - p_sm, p_sm], dtype=np.float32)

        if self._debug:
            pm = 'nan' if np.isnan(p_mdm) else f"{p_mdm:.3f}"
            pt = 'nan' if np.isnan(p_tslr) else f"{p_tslr:.3f}"
            print(f"[PlanA] p={p:.3f}, p_mdm={pm}, p_tslr={pt}, p_ewma={p_sm:.3f}, thr={threshold:.3f}, trigger={trigger}")

        return {
            "final_prediction": final_prediction,
            "final_probability": final_probability,
            "confidence": float(np.max(final_probability)),
            "individual_predictions": {"mdm": p_mdm, "tslr": p_tslr},
            "individual_probabilities": {"mdm": p_mdm, "tslr": p_tslr},
            "voting_weights": {},
            "trigger_decision": bool(trigger),
            "difficulty_level": self._current_difficulty,
            "agreement_level": "na",
            "classification_details": (
                f"p={p:.3f}, p_mdm={p_mdm:.3f}, p_tslr={p_tslr:.3f}, p_ewma={p_sm:.3f}, thr={threshold:.3f}, trigger={trigger}"
            ),
            "simple_result": (
                f"MI {p_sm:.3f} (thr={threshold:.3f})"
            ),
        }

    # ---------------- 接口：状态 ----------------
    def get_system_status(self) -> Dict[str, Any]:
        st = self.classifier.get_status()
        st.update({
            "threshold": self.get_current_thresholds(),
            "difficulty_level": self._current_difficulty,
            "smoothing": {
                "enabled": self._smoother.enabled,
                "alpha": self._smoother.alpha,
            },
        })
        return st


