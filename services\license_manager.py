# -*- coding: utf-8 -*-
"""
许可证管理器
License Manager

实现硬件ID生成、注册码验证和激活状态管理
"""

import re
import hashlib
import subprocess
import uuid
import platform
import time
import json
import base64
import os
import sys
from pathlib import Path
from typing import List, Optional, Tuple
from cryptography.fernet import Fernet


class StableHardwareID:
    """稳定硬件ID生成器"""
    
    @staticmethod
    def _normalize_string(value: str) -> str:
        """标准化字符串：去除空格、特殊字符，统一大小写"""
        if not value:
            return ""
        # 去除空格、制表符、换行符
        value = re.sub(r'\s+', '', value)
        # 只保留字母数字
        value = re.sub(r'[^a-zA-Z0-9]', '', value)
        # 转换为大写
        return value.upper()
    
    @staticmethod
    def _get_cpu_processor_id() -> str:
        """获取CPU ProcessorID"""
        try:
            output = subprocess.check_output(
                'wmic cpu get processorid',
                shell=True, text=True, timeout=5
            )
            lines = output.strip().split('\n')
            # 跳过标题行，查找第一个非空的数据行
            for line in lines[1:]:
                raw_value = line.strip()
                if raw_value:  # 找到非空行
                    normalized = StableHardwareID._normalize_string(raw_value)
                    if normalized and len(normalized) >= 8:  # 确保有效长度
                        return normalized
        except Exception as e:
            print(f"获取CPU ProcessorID失败: {e}")
        return ""

    @staticmethod
    def _get_disk_serial() -> str:
        """获取硬盘序列号"""
        try:
            output = subprocess.check_output(
                'wmic diskdrive where index=0 get serialnumber',
                shell=True, text=True, timeout=5
            )
            lines = output.strip().split('\n')
            # 跳过标题行，查找第一个非空的数据行
            for line in lines[1:]:
                raw_value = line.strip()
                if raw_value:  # 找到非空行
                    normalized = StableHardwareID._normalize_string(raw_value)
                    if normalized and len(normalized) >= 4:  # 确保有效长度
                        return normalized
        except Exception as e:
            print(f"获取硬盘序列号失败: {e}")
        return ""
    
    @staticmethod
    def generate_hardware_id() -> str:
        """生成稳定的硬件ID，仅使用CPU ProcessorID + 硬盘序列号"""
        # print("🔍 开始收集硬件信息...")

        # 1. 获取CPU ProcessorID（固定顺序第一位）
        cpu_processor_id = StableHardwareID._get_cpu_processor_id()
        if cpu_processor_id:
            pass
            # print(f"  ✓ CPU ProcessorID: {cpu_processor_id}")
        else:
            pass
            # print(f"  ✗ CPU ProcessorID: 获取失败")

        # 2. 获取硬盘序列号（固定顺序第二位）
        disk_serial = StableHardwareID._get_disk_serial()
        if disk_serial:
            pass
            # print(f"  ✓ 硬盘序列号: {disk_serial}")
        else:
            pass
            # print(f"  ✗ 硬盘序列号: 获取失败")

        # 按固定顺序组合：CPU ProcessorID + 硬盘序列号
        components = []
        if cpu_processor_id:
            components.append(cpu_processor_id)
        if disk_serial:
            components.append(disk_serial)

        # 确保至少有一个有效组件
        if not components:
            # 极端情况下的备用方案
            fallback = f"FALLBACK{platform.machine()}{platform.processor()}"
            fallback = StableHardwareID._normalize_string(fallback)
            components.append(fallback)
            # print(f"  ⚠️ 使用备用方案: {fallback}")

        # 固定顺序组合（不排序，保持CPU+硬盘的顺序）
        combined_info = ''.join(components)
        # print(f"🔧 组合信息: {combined_info}")

        # 生成稳定的16位硬件ID
        hash_obj = hashlib.sha256(combined_info.encode('utf-8'))
        hardware_id = hash_obj.hexdigest()[:16].upper()

        # print(f"✅ 硬件ID生成完成: {hardware_id}")
        return hardware_id

    @staticmethod
    def test_stability() -> bool:
        """测试硬件ID稳定性"""
        print("🧪 测试硬件ID稳定性...")
        
        ids = []
        for i in range(3):
            hw_id = StableHardwareID.generate_hardware_id()
            ids.append(hw_id)
            print(f"  第{i+1}次: {hw_id}")
        
        is_stable = len(set(ids)) == 1
        if is_stable:
            print("✅ 硬件ID稳定")
        else:
            print("❌ 硬件ID不稳定")
        
        return is_stable


class EnhancedLicenseManager:
    """增强的许可证管理器"""

    # 用于生成注册码的密钥
    SECRET_KEY = "BCI-2024-Medical-Recovery-System"
    
    @staticmethod
    def generate_license_key(hardware_id: str) -> str:
        """生成注册码，包含校验位"""
        if not hardware_id or len(hardware_id) < 8:
            raise ValueError("硬件ID无效")
        
        # 组合信息
        combined = f"{hardware_id}:{EnhancedLicenseManager.SECRET_KEY}"
        
        # 生成主要哈希
        main_hash = hashlib.sha256(combined.encode()).hexdigest()
        
        # 提取20位字符
        license_core = main_hash[:20].upper()
        
        # 计算校验位（简单的校验算法）
        checksum = sum(ord(c) for c in license_core) % 36
        checksum_char = str(checksum) if checksum < 10 else chr(ord('A') + checksum - 10)
        
        # 组合最终注册码：XXXXX-XXXXX-XXXXX-XXXXX-X
        license_with_check = license_core + checksum_char
        formatted = f"{license_with_check[:5]}-{license_with_check[5:10]}-{license_with_check[10:15]}-{license_with_check[15:20]}-{license_with_check[20]}"
        
        return formatted
    
    @staticmethod
    def verify_license_key(hardware_id: str, license_key: str) -> Tuple[bool, str]:
        """验证注册码"""
        try:
            # 清理注册码格式
            clean_license = license_key.replace('-', '').replace(' ', '').upper()
            
            if len(clean_license) != 21:
                return False, "注册码格式错误（应为21位）"
            
            # 分离校验位
            license_core = clean_license[:20]
            provided_checksum = clean_license[20]
            
            # 重新计算校验位
            expected_checksum = sum(ord(c) for c in license_core) % 36
            expected_checksum_char = str(expected_checksum) if expected_checksum < 10 else chr(ord('A') + expected_checksum - 10)
            
            if provided_checksum != expected_checksum_char:
                return False, "注册码校验失败"
            
            # 验证硬件ID匹配
            expected_license = EnhancedLicenseManager.generate_license_key(hardware_id)
            expected_clean = expected_license.replace('-', '').upper()
            
            if clean_license == expected_clean:
                return True, "验证成功"
            else:
                return False, "注册码与当前机器不匹配"
                
        except Exception as e:
            return False, f"验证过程出错: {str(e)}"


class ActivationStorage:
    """激活状态安全存储"""
    
    def __init__(self):
        # 使用统一的路径管理器
        from utils.path_manager import get_config_dir_path, path_manager
        self.config_dir = get_config_dir_path()
        path_manager.ensure_directory_exists(self.config_dir)
        self.activation_file = self.config_dir / ".activation"
    
    # 移除原有的_get_config_dir方法，现在使用统一的路径管理器
    
    def _get_encryption_key(self, hardware_id: str) -> bytes:
        """基于硬件ID生成加密密钥"""
        key_material = f"{hardware_id}:BCI:Encryption".encode()
        key_hash = hashlib.sha256(key_material).digest()
        return base64.urlsafe_b64encode(key_hash)
    
    def save_activation(self, hardware_id: str, license_key: str) -> bool:
        """保存激活状态"""
        try:
            activation_data = {
                'hardware_id': hardware_id,
                'license_key': license_key,
                'activation_time': time.time(),
                'version': '1.0'
            }
            
            # 加密保存
            key = self._get_encryption_key(hardware_id)
            fernet = Fernet(key)
            
            json_data = json.dumps(activation_data)
            encrypted_data = fernet.encrypt(json_data.encode())
            
            with open(self.activation_file, 'wb') as f:
                f.write(encrypted_data)
            
            print("✅ 激活状态已保存")
            return True
            
        except Exception as e:
            print(f"保存激活状态失败: {e}")
            return False
    
    def load_activation(self, hardware_id: str) -> Tuple[bool, dict]:
        """加载激活状态"""
        try:
            if not self.activation_file.exists():
                return False, {}
            
            # 解密读取
            key = self._get_encryption_key(hardware_id)
            fernet = Fernet(key)
            
            with open(self.activation_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = fernet.decrypt(encrypted_data)
            activation_data = json.loads(decrypted_data.decode())
            
            # 验证硬件ID匹配
            if activation_data.get('hardware_id') == hardware_id:
                print("✅ 激活状态验证成功")
                return True, activation_data
            else:
                print("❌ 硬件ID不匹配，激活状态无效")
                return False, {}
                
        except Exception as e:
            print(f"加载激活状态失败: {e}")
            return False, {}

    def clear_activation(self) -> bool:
        """清除激活状态"""
        try:
            if self.activation_file.exists():
                self.activation_file.unlink()
                print("✅ 激活状态已清除")
            return True
        except Exception as e:
            print(f"清除激活状态失败: {e}")
            return False


class LicenseSystem:
    """完整的许可证系统"""
    
    def __init__(self):
        self.hardware_id_generator = StableHardwareID()
        self.license_manager = EnhancedLicenseManager()
        self.activation_storage = ActivationStorage()
        self._hardware_id = None
    
    def get_hardware_id(self) -> str:
        """获取硬件ID（缓存结果）"""
        if self._hardware_id is None:
            self._hardware_id = self.hardware_id_generator.generate_hardware_id()
        return self._hardware_id
    
    def generate_license_for_current_machine(self) -> str:
        """为当前机器生成注册码"""
        hardware_id = self.get_hardware_id()
        return self.license_manager.generate_license_key(hardware_id)
    
    def is_activated(self) -> bool:
        """检查是否已激活"""
        hardware_id = self.get_hardware_id()
        is_valid, activation_data = self.activation_storage.load_activation(hardware_id)
        
        if is_valid and activation_data:
            # 验证存储的注册码是否仍然有效
            stored_license = activation_data.get('license_key', '')
            valid, _ = self.license_manager.verify_license_key(hardware_id, stored_license)
            return valid
        
        return False
    
    def activate_with_license(self, license_key: str) -> Tuple[bool, str]:
        """使用注册码激活"""
        hardware_id = self.get_hardware_id()
        
        # 验证注册码
        valid, message = self.license_manager.verify_license_key(hardware_id, license_key)
        
        if valid:
            # 保存激活状态
            if self.activation_storage.save_activation(hardware_id, license_key):
                return True, "激活成功！"
            else:
                return False, "激活验证成功，但保存激活状态失败"
        else:
            return False, message
    
    def get_machine_info(self) -> dict:
        """获取机器信息用于显示"""
        hardware_id = self.get_hardware_id()
        return {
            'hardware_id': hardware_id,
            'formatted_id': f"{hardware_id[:4]}-{hardware_id[4:8]}-{hardware_id[8:12]}-{hardware_id[12:16]}",
            'os_info': f"{platform.system()} {platform.release()}",
            'machine': platform.machine(),
            'processor': platform.processor()[:50] if platform.processor() else "Unknown"
        }
    
    def test_system(self) -> bool:
        """测试许可证系统"""
        print("🔧 开始测试许可证系统...")
        
        # 测试硬件ID稳定性
        if not self.hardware_id_generator.test_stability():
            print("❌ 硬件ID不稳定")
            return False
        
        # 测试注册码生成和验证
        hardware_id = self.get_hardware_id()
        license_key = self.generate_license_for_current_machine()
        
        print(f"🔑 生成的注册码: {license_key}")
        
        valid, message = self.license_manager.verify_license_key(hardware_id, license_key)
        if valid:
            print("✅ 注册码验证成功")
        else:
            print(f"❌ 注册码验证失败: {message}")
            return False
        
        print("✅ 许可证系统测试通过")
        return True


# 全局许可证系统实例
license_system = LicenseSystem() 