#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
 注册码生成器
License Key Generator 

独立的注册码生成脚本，用于为指定硬件ID生成注册码
"""

import re
import hashlib
import subprocess
import uuid
import platform
import argparse
import sys
from typing import List, Tuple


class StableHardwareID:
    """稳定硬件ID生成器"""
    
    @staticmethod
    def _normalize_string(value: str) -> str:
        """标准化字符串：去除空格、特殊字符，统一大小写"""
        if not value:
            return ""
        # 去除空格、制表符、换行符
        value = re.sub(r'\s+', '', value)
        # 只保留字母数字
        value = re.sub(r'[^a-zA-Z0-9]', '', value)
        # 转换为大写
        return value.upper()
    
    @staticmethod
    def _get_cpu_processor_id() -> str:
        """获取CPU ProcessorID"""
        try:
            output = subprocess.check_output(
                'wmic cpu get processorid',
                shell=True, text=True, timeout=5
            )
            lines = output.strip().split('\n')
            # 跳过标题行，查找第一个非空的数据行
            for line in lines[1:]:
                raw_value = line.strip()
                if raw_value:  # 找到非空行
                    normalized = StableHardwareID._normalize_string(raw_value)
                    if normalized and len(normalized) >= 8:  # 确保有效长度
                        return normalized
        except Exception as e:
            print(f"获取CPU ProcessorID失败: {e}")
        return ""
    
    @staticmethod
    def _get_disk_serial() -> str:
        """获取硬盘序列号"""
        try:
            output = subprocess.check_output(
                'wmic diskdrive where index=0 get serialnumber',
                shell=True, text=True, timeout=5
            )
            lines = output.strip().split('\n')
            # 跳过标题行，查找第一个非空的数据行
            for line in lines[1:]:
                raw_value = line.strip()
                if raw_value:  # 找到非空行
                    normalized = StableHardwareID._normalize_string(raw_value)
                    if normalized and len(normalized) >= 4:  # 确保有效长度
                        return normalized
        except Exception as e:
            print(f"获取硬盘序列号失败: {e}")
        return ""
    
    @staticmethod
    def generate_hardware_id() -> str:
        """生成稳定的硬件ID，仅使用CPU ProcessorID + 硬盘序列号"""
        print("🔍 开始收集硬件信息...")
        
        # 1. 获取CPU ProcessorID（固定顺序第一位）
        cpu_processor_id = StableHardwareID._get_cpu_processor_id()
        if cpu_processor_id:
            print(f"  ✓ CPU ProcessorID: {cpu_processor_id}")
        else:
            print(f"  ✗ CPU ProcessorID: 获取失败")
        
        # 2. 获取硬盘序列号（固定顺序第二位）
        disk_serial = StableHardwareID._get_disk_serial()
        if disk_serial:
            print(f"  ✓ 硬盘序列号: {disk_serial}")
        else:
            print(f"  ✗ 硬盘序列号: 获取失败")
        
        # 按固定顺序组合：CPU ProcessorID + 硬盘序列号
        components = []
        if cpu_processor_id:
            components.append(cpu_processor_id)
        if disk_serial:
            components.append(disk_serial)
        
        # 确保至少有一个有效组件
        if not components:
            # 极端情况下的备用方案
            fallback = f"FALLBACK{platform.machine()}{platform.processor()}"
            fallback = StableHardwareID._normalize_string(fallback)
            components.append(fallback)
            print(f"  ⚠️ 使用备用方案: {fallback}")
        
        # 固定顺序组合（不排序，保持CPU+硬盘的顺序）
        combined_info = ''.join(components)
        print(f"🔧 组合信息: {combined_info}")
        
        # 生成稳定的16位硬件ID
        hash_obj = hashlib.sha256(combined_info.encode('utf-8'))
        hardware_id = hash_obj.hexdigest()[:16].upper()
        
        print(f"✅ 硬件ID生成完成: {hardware_id}")
        return hardware_id


class LicenseGenerator:
    """注册码生成器"""
    
    # 用于生成注册码的密钥
    SECRET_KEY = "BCI-2024-Medical-Recovery-System"
    
    @staticmethod
    def generate_license_key(hardware_id: str) -> str:
        """生成注册码，包含校验位"""
        if not hardware_id or len(hardware_id) < 8:
            raise ValueError("硬件ID无效")
        
        # 组合信息
        combined = f"{hardware_id}:{LicenseGenerator.SECRET_KEY}"
        
        # 生成主要哈希
        main_hash = hashlib.sha256(combined.encode()).hexdigest()
        
        # 提取20位字符
        license_core = main_hash[:20].upper()
        
        # 计算校验位（简单的校验算法）
        checksum = sum(ord(c) for c in license_core) % 36
        checksum_char = str(checksum) if checksum < 10 else chr(ord('A') + checksum - 10)
        
        # 组合最终注册码：XXXXX-XXXXX-XXXXX-XXXXX-X
        license_with_check = license_core + checksum_char
        formatted = f"{license_with_check[:5]}-{license_with_check[5:10]}-{license_with_check[10:15]}-{license_with_check[15:20]}-{license_with_check[20]}"
        
        return formatted
    
    @staticmethod
    def verify_license_key(hardware_id: str, license_key: str) -> Tuple[bool, str]:
        """验证注册码"""
        try:
            # 清理注册码格式
            clean_license = license_key.replace('-', '').replace(' ', '').upper()
            
            if len(clean_license) != 21:
                return False, "注册码格式错误（应为21位）"
            
            # 分离校验位
            license_core = clean_license[:20]
            provided_checksum = clean_license[20]
            
            # 重新计算校验位
            expected_checksum = sum(ord(c) for c in license_core) % 36
            expected_checksum_char = str(expected_checksum) if expected_checksum < 10 else chr(ord('A') + expected_checksum - 10)
            
            if provided_checksum != expected_checksum_char:
                return False, "注册码校验失败"
            
            # 验证硬件ID匹配
            expected_license = LicenseGenerator.generate_license_key(hardware_id)
            expected_clean = expected_license.replace('-', '').upper()
            
            if clean_license == expected_clean:
                return True, "验证成功"
            else:
                return False, "注册码与当前机器不匹配"
                
        except Exception as e:
            return False, f"验证过程出错: {str(e)}"
    
    @staticmethod
    def format_hardware_id(hardware_id: str) -> str:
        """格式化硬件ID为可读格式"""
        if len(hardware_id) == 16:
            return f"{hardware_id[:4]}-{hardware_id[4:8]}-{hardware_id[8:12]}-{hardware_id[12:16]}"
        return hardware_id


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='注册码生成器')
    parser.add_argument('--hardware-id', '-w', help='指定硬件ID生成注册码')
    parser.add_argument('--current-machine', '-c', action='store_true', help='为当前机器生成注册码')
    parser.add_argument('--verify', '-v', nargs=2, metavar=('HARDWARE_ID', 'LICENSE_KEY'), help='验证注册码')
    parser.add_argument('--batch', '-b', help='从文件批量生成注册码（每行一个硬件ID）')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式模式')

    
    args = parser.parse_args()
    
    # 如果没有参数，默认进入交互式模式
    if not any(vars(args).values()):
        args.interactive = True
    
    try:
        if args.current_machine:
            # 为当前机器生成注册码
            hardware_id = StableHardwareID.generate_hardware_id()
            license_key = LicenseGenerator.generate_license_key(hardware_id)
            
            print(f"\n{'='*60}")
            print(f"当前机器信息:")
            print(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}")
            print(f"注册码: {license_key}")
            print(f"{'='*60}")
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}\n")
                    f.write(f"注册码: {license_key}\n")
                print(f"结果已保存到: {args.output}")
        
        elif args.hardware_id:
            # 为指定硬件ID生成注册码
            hardware_id = args.hardware_id.replace('-', '').upper()
            if len(hardware_id) != 16:
                print("❌ 错误：硬件ID必须是16位字符")
                return
            
            license_key = LicenseGenerator.generate_license_key(hardware_id)
            
            print(f"\n{'='*60}")
            print(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}")
            print(f"注册码: {license_key}")
            print(f"{'='*60}")
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}\n")
                    f.write(f"注册码: {license_key}\n")
                print(f"结果已保存到: {args.output}")
        
        elif args.verify:
            # 验证注册码
            hardware_id, license_key = args.verify
            hardware_id = hardware_id.replace('-', '').upper()
            
            valid, message = LicenseGenerator.verify_license_key(hardware_id, license_key)
            
            print(f"\n{'='*60}")
            print(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}")
            print(f"注册码: {license_key}")
            print(f"验证结果: {'✅ ' + message if valid else '❌ ' + message}")
            print(f"{'='*60}")
        
        elif args.batch:
            # 批量生成注册码
            try:
                with open(args.batch, 'r', encoding='utf-8') as f:
                    hardware_ids = [line.strip().replace('-', '').upper() for line in f if line.strip()]
                
                results = []
                for hardware_id in hardware_ids:
                    if len(hardware_id) == 16:
                        license_key = LicenseGenerator.generate_license_key(hardware_id)
                        results.append((hardware_id, license_key))
                        print(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)} -> 注册码: {license_key}")
                    else:
                        print(f"❌ 跳过无效硬件ID: {hardware_id}")
                
                if args.output:
                    with open(args.output, 'w', encoding='utf-8') as f:
                        for hardware_id, license_key in results:
                            f.write(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}\n")
                            f.write(f"注册码: {license_key}\n")
                            f.write("-" * 40 + "\n")
                    print(f"批量结果已保存到: {args.output}")
                
            except FileNotFoundError:
                print(f"❌ 错误：文件不存在 {args.batch}")
        
        elif args.interactive:
            # 交互式模式
            interactive_mode()
    
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


def interactive_mode():
    """交互式模式"""
    print("🔐 NeuroLink Pro 注册码生成器 - 交互式模式")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 为当前机器生成注册码")
        print("2. 为指定硬件ID生成注册码")
        print("3. 验证注册码")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            try:
                hardware_id = StableHardwareID.generate_hardware_id()
                license_key = LicenseGenerator.generate_license_key(hardware_id)
                
                print(f"\n{'='*60}")
                print(f"当前机器信息:")
                print(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}")
                print(f"注册码: {license_key}")
                print(f"{'='*60}")
                
                save = input("\n是否保存到文件? (y/n): ").strip().lower()
                if save == 'y':
                    filename = input("请输入文件名 (默认: license.txt): ").strip() or "license.txt"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}\n")
                        f.write(f"注册码: {license_key}\n")
                    print(f"结果已保存到: {filename}")
                
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        elif choice == '2':
            hardware_id = input("请输入硬件ID (16位): ").strip().replace('-', '').upper()
            if len(hardware_id) != 16:
                print("❌ 错误：硬件ID必须是16位字符")
                continue
            
            try:
                license_key = LicenseGenerator.generate_license_key(hardware_id)
                
                print(f"\n{'='*60}")
                print(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}")
                print(f"注册码: {license_key}")
                print(f"{'='*60}")
                
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        elif choice == '3':
            hardware_id = input("请输入硬件ID: ").strip().replace('-', '').upper()
            license_key = input("请输入注册码: ").strip()
            
            try:
                valid, message = LicenseGenerator.verify_license_key(hardware_id, license_key)
                
                print(f"\n{'='*60}")
                print(f"硬件ID: {LicenseGenerator.format_hardware_id(hardware_id)}")
                print(f"注册码: {license_key}")
                print(f"验证结果: {'✅ ' + message if valid else '❌ ' + message}")
                print(f"{'='*60}")
                
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        elif choice == '4':
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    main() 