#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kalman滤波器处理器

专门用于低信噪比脑电信号的自适应去噪处理
基于Kalman滤波理论，提供实时、高效的信号去噪能力

作者: AI Assistant
创建时间: 2025-01-02
"""

import numpy as np
import time
import logging
from typing import Optional, Tuple
import warnings

from .preprocessing_config import PreprocessingConfig


class KalmanFilterProcessor:
    """
    Kalman滤波器处理器
    
    专门用于低SNR脑电信号的实时自适应去噪
    基于状态空间模型，能够有效跟踪信号变化并抑制噪声
    """
    
    def __init__(self, config: PreprocessingConfig):
        """
        初始化Kalman滤波器处理器
        
        Args:
            config: 预处理配置对象
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Kalman滤波器参数
        self.n_channels = config.n_channels
        self.state_dim = 2  # 状态维度：[信号值, 信号导数]
        
        # 初始化Kalman滤波器矩阵
        self._init_kalman_matrices()
        
        # 性能统计
        self.processing_times = []
        self.processed_count = 0
        
        self.logger.info(f"Kalman滤波器处理器初始化完成 - 通道数: {self.n_channels}")
    
    def _init_kalman_matrices(self):
        """初始化Kalman滤波器的状态空间矩阵"""
        
        # 状态转移矩阵 F (信号值和导数的线性模型)
        dt = 1.0 / self.config.sampling_rate  # 采样间隔
        self.F = np.array([
            [1.0, dt],   # x[k+1] = x[k] + dt * dx[k]
            [0.0, 1.0]   # dx[k+1] = dx[k]
        ])
        
        # 观测矩阵 H (只观测信号值)
        self.H = np.array([[1.0, 0.0]])
        
        # 过程噪声协方差矩阵 Q
        q_var = self.config.kalman_process_noise_var  # 使用配置参数
        self.Q = q_var * np.array([
            [dt**4/4, dt**3/2],
            [dt**3/2, dt**2]
        ])

        # 观测噪声协方差矩阵 R
        self.R = np.array([[self.config.kalman_observation_noise_var]])  # 使用配置参数

        # 为每个通道初始化状态
        self.states = np.zeros((self.n_channels, self.state_dim))  # 状态向量
        self.P = np.tile(np.eye(self.state_dim), (self.n_channels, 1, 1))  # 协方差矩阵

        # 自适应参数
        self.innovation_history = [[] for _ in range(self.n_channels)]
        self.adaptation_window = self.config.kalman_adaptation_window  # 使用配置参数
        self.enable_adaptation = self.config.kalman_enable_adaptation  # 使用配置参数
        
    def process(self, eeg_data: np.ndarray) -> dict:
        """
        执行Kalman滤波处理
        
        Args:
            eeg_data: 输入EEG数据 [8, 4] - 8通道，4个样本点
            
        Returns:
            dict: 包含处理结果和统计信息
        """
        start_time = time.perf_counter()
        
        try:
            # 检查输入数据格式
            if eeg_data.shape != (self.config.n_channels, 4):
                raise ValueError(f"输入数据形状错误: {eeg_data.shape}, 期望: ({self.config.n_channels}, 4)")
            
            result = {
                'input_data': eeg_data.copy(),
                'filtered_data': None,
                'processing_time': 0,
                'steps_applied': [],
                'kalman_info': {
                    'innovation_variance': [],
                    'kalman_gain': [],
                    'state_estimates': []
                }
            }
            
            # 对每个样本点进行Kalman滤波
            filtered_data = np.zeros_like(eeg_data)
            
            for sample_idx in range(4):
                sample_filtered = np.zeros(self.n_channels)
                
                for ch in range(self.n_channels):
                    # 获取当前观测值
                    observation = eeg_data[ch, sample_idx]
                    
                    # Kalman滤波步骤
                    filtered_value, kalman_info = self._kalman_step(ch, observation)
                    sample_filtered[ch] = filtered_value
                    
                    # 记录Kalman信息（仅第一个样本）
                    if sample_idx == 0:
                        result['kalman_info']['innovation_variance'].append(kalman_info['innovation_var'])
                        result['kalman_info']['kalman_gain'].append(kalman_info['kalman_gain'])
                        result['kalman_info']['state_estimates'].append(kalman_info['state'])
                
                filtered_data[:, sample_idx] = sample_filtered
            
            result['filtered_data'] = filtered_data
            result['steps_applied'].append('kalman_filtering')
            
            # 记录处理时间
            processing_time = (time.perf_counter() - start_time) * 1000  # 转换为毫秒
            result['processing_time'] = processing_time
            
            # 性能统计
            self._update_performance_stats(processing_time)
            
            if self.config.enable_debug_output:
                self.logger.debug(f"Kalman滤波完成，耗时: {processing_time:.3f}ms")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Kalman滤波处理失败: {e}")
            return {
                'input_data': eeg_data.copy(),
                'filtered_data': eeg_data.copy(),  # 失败时返回原始数据
                'processing_time': (time.perf_counter() - start_time) * 1000,
                'error': str(e),
                'steps_applied': []
            }
    
    def _kalman_step(self, channel: int, observation: float) -> Tuple[float, dict]:
        """
        执行单个通道的Kalman滤波步骤
        
        Args:
            channel: 通道索引
            observation: 观测值
            
        Returns:
            tuple: (滤波后的值, Kalman信息)
        """
        # 预测步骤
        # 状态预测: x_pred = F * x
        x_pred = self.F @ self.states[channel]
        
        # 协方差预测: P_pred = F * P * F^T + Q
        P_pred = self.F @ self.P[channel] @ self.F.T + self.Q
        
        # 更新步骤
        # 创新(残差): y = z - H * x_pred
        innovation = observation - (self.H @ x_pred)[0]
        
        # 创新协方差: S = H * P_pred * H^T + R
        S = (self.H @ P_pred @ self.H.T + self.R)[0, 0]
        
        # Kalman增益: K = P_pred * H^T * S^(-1)
        K = P_pred @ self.H.T / S
        
        # 状态更新: x = x_pred + K * y
        self.states[channel] = x_pred + K.flatten() * innovation
        
        # 协方差更新: P = (I - K * H) * P_pred
        I_KH = np.eye(self.state_dim) - K @ self.H
        self.P[channel] = I_KH @ P_pred
        
        # 自适应调整（如果启用）
        if self.enable_adaptation:
            self._adaptive_adjustment(channel, innovation, S)
        
        # 返回滤波后的信号值（状态的第一个分量）
        filtered_value = self.states[channel, 0]
        
        kalman_info = {
            'innovation_var': S,
            'kalman_gain': K[0, 0],
            'state': self.states[channel].copy()
        }
        
        return filtered_value, kalman_info
    
    def _adaptive_adjustment(self, channel: int, innovation: float, innovation_var: float):
        """
        自适应调整Kalman滤波器参数
        
        Args:
            channel: 通道索引
            innovation: 创新值
            innovation_var: 创新方差
        """
        # 记录创新历史
        self.innovation_history[channel].append(abs(innovation))
        
        # 保持固定窗口大小
        if len(self.innovation_history[channel]) > self.adaptation_window:
            self.innovation_history[channel].pop(0)
        
        # 如果有足够的历史数据，进行自适应调整
        if len(self.innovation_history[channel]) >= 10:
            recent_innovations = self.innovation_history[channel][-10:]
            avg_innovation = np.mean(recent_innovations)
            
            # 如果创新过大，增加观测噪声方差（降低信任度）
            if avg_innovation > 2.0 * np.sqrt(innovation_var):
                self.R[0, 0] = min(self.R[0, 0] * 1.1, 10.0)
            # 如果创新很小，减少观测噪声方差（增加信任度）
            elif avg_innovation < 0.5 * np.sqrt(innovation_var):
                self.R[0, 0] = max(self.R[0, 0] * 0.9, 0.1)
    
    def _update_performance_stats(self, processing_time: float):
        """更新性能统计"""
        self.processing_times.append(processing_time)
        self.processed_count += 1
        
        # 保持最近1000次的统计
        if len(self.processing_times) > 1000:
            self.processing_times.pop(0)
    
    def get_performance_stats(self) -> dict:
        """获取性能统计信息"""
        if not self.processing_times:
            return {
                'processed_count': 0,
                'avg_processing_time': 0,
                'max_processing_time': 0,
                'min_processing_time': 0
            }
        
        return {
            'processed_count': self.processed_count,
            'avg_processing_time': np.mean(self.processing_times),
            'max_processing_time': np.max(self.processing_times),
            'min_processing_time': np.min(self.processing_times),
            'std_processing_time': np.std(self.processing_times)
        }
    
    def reset_stats(self):
        """重置性能统计"""
        self.processing_times = []
        self.processed_count = 0
        self.logger.info("Kalman滤波器性能统计已重置")
    
    def reset_filter(self):
        """重置Kalman滤波器状态"""
        self._init_kalman_matrices()
        self.logger.info("Kalman滤波器状态已重置")
    
    def get_filter_status(self) -> dict:
        """获取滤波器状态信息"""
        return {
            'n_channels': self.n_channels,
            'state_dim': self.state_dim,
            'current_states': self.states.copy(),
            'observation_noise_var': self.R[0, 0],
            'adaptation_window': self.adaptation_window,
            'innovation_history_lengths': [len(hist) for hist in self.innovation_history]
        }
