# 电刺激设备编程使用文档

## 目录
1. [概述](#概述)
2. [环境准备](#环境准备)
3. [DLL接口详解](#dll接口详解)
4. [编程实现步骤](#编程实现步骤)
5. [代码示例](#代码示例)
6. [参数配置](#参数配置)
7. [错误处理](#错误处理)
8. [最佳实践](#最佳实践)
9. [故障排除](#故障排除)

---

## 概述

本文档详细介绍了如何在脑机接口康复训练系统中编程控制电刺激设备。电刺激设备是康复治疗的核心组件，通过精确的电流刺激帮助患者进行神经功能康复。

### 系统架构

```
用户界面 (UI)
    ↓
电刺激控制器 (StimulationDevice)
    ↓
动态链接库 (RecoveryDLL.dll)
    ↓
硬件设备 (电刺激设备)
```

### 主要功能

- **设备连接管理**: 自动检测和连接电刺激设备
- **双通道控制**: 支持A、B两个独立通道的刺激控制
- **参数配置**: 灵活设置频率、脉宽、电流强度等参数
- **实时监控**: 监控设备状态和通道状态
- **安全保护**: 电流限制、超时保护、异常处理

### 技术特点

- 基于Windows DLL的硬件接口
- Python ctypes库进行DLL调用
- 回调函数机制实现状态监控
- 多线程安全的设备操作
- 完整的错误处理和日志记录

---

## 环境准备

### 系统要求

- **操作系统**: Windows 10/11 (64位)
- **Python版本**: 3.8+
- **硬件要求**: USB端口，电刺激设备

### 必需依赖

```python
# Python标准库
import ctypes
import threading
import time
import logging
from pathlib import Path
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass
from enum import Enum

# 第三方库
from PySide6.QtCore import QTimer, Signal
```

### DLL文件

确保以下DLL文件存在于项目的 `libs` 目录下：

```
libs/
├── RecoveryDLL.dll        # 主要的设备控制DLL
├── RecoveryModuleDLL.h    # C++头文件（参考用）
└── 相关依赖DLL文件
```

### 配置文件

在 `utils/app_config.py` 中配置电刺激设备参数：

```python
STIMULATION_CONFIG = {
    'dll_path': PROJECT_ROOT / 'libs' / 'RecoveryDLL.dll',
    'max_current': 100,          # 最大电流(mA)
    'min_current': 1,            # 最小电流(mA) 
    'current_step': 1,           # 电流步长(mA)
    'default_frequency': 20,     # 默认频率(Hz)
    'default_pulse_width': 200,  # 默认脉宽(μs)
    'default_relax_time': 5,     # 默认休息时间(s)
    'default_climb_time': 2,     # 默认上升时间(s)
    'default_work_time': 10,     # 默认工作时间(s)
    'default_fall_time': 2,      # 默认下降时间(s)
    'default_wave_type': 0,      # 波形类型(0=双相波, 1=单相波)
    'max_channels': 2,           # 最大通道数
    'port_num': 1,               # 默认端口号
    'connection_timeout': 5,     # 连接超时时间(s)
}
```

---

## DLL接口详解

### 核心数据结构

#### 回调函数类型

```python
# 数据回调函数原型
callback_func_type = ctypes.WINFUNCTYPE(
    ctypes.c_int,                    # 返回值类型
    ctypes.c_void_p,                 # HANDLE pHandle
    ctypes.POINTER(ctypes.c_ubyte),  # LPVOID lpBuffer (字节数组)
    ctypes.c_int                     # int nSize
)
```

#### 刺激参数结构

```python
@dataclass
class StimulationParameters:
    """刺激参数"""
    channel_num: int = 1          # 通道号 (1=A通道, 2=B通道)
    frequency: float = 20.0       # 频率 (Hz, 范围: 2-160)
    pulse_width: float = 200.0    # 脉宽 (μs, 范围: 10-500)
    relax_time: float = 5.0       # 休息时间 (s, 范围: 0-16)
    climb_time: float = 2.0       # 上升时间 (s, 范围: 0-5)
    work_time: float = 10.0       # 工作时间 (s, 范围: 0-30)
    fall_time: float = 2.0        # 下降时间 (s, 范围: 0-5)
    wave_type: int = 0            # 波形类型 (0=双相波, 1=单相波)
```

### 主要DLL函数

#### 1. OpenRecPort - 打开设备端口

```python
# 函数原型
dll.OpenRecPort.argtypes = [
    ctypes.c_int,           # portNum - 端口号
    ctypes.c_int,           # iReadSize - 读取数据大小(固定为6)
    callback_func_type,     # funDataProc - 回调函数
    ctypes.c_void_p         # HANDLE pHandle - 句柄(通常为None)
]
dll.OpenRecPort.restype = ctypes.c_int

# 使用示例
result = dll.OpenRecPort(port_num, 6, callback_function, None)
```

**参数说明:**
- `portNum`: 串口端口号 (1-255)
- `iReadSize`: 固定为6，表示每次读取6个short值
- `funDataProc`: 数据回调函数，用于接收设备状态数据
- `pHandle`: 句柄，通常传入None

**返回值:**
- `1`: 连接成功
- `-1`: 连接失败
- `0`: 其他错误

#### 2. CloseRecPort - 关闭设备端口

```python
# 函数原型
dll.CloseRecPort.argtypes = []
dll.CloseRecPort.restype = ctypes.c_int

# 使用示例
result = dll.CloseRecPort()
```

**返回值:**
- `1`: 关闭成功
- `0`: 关闭失败

#### 3. IsRecOpen - 检查设备连接状态

```python
# 函数原型
dll.IsRecOpen.argtypes = []
dll.IsRecOpen.restype = ctypes.c_bool

# 使用示例
is_connected = dll.IsRecOpen()
```

**返回值:**
- `True`: 设备已连接
- `False`: 设备未连接

#### 4. SwitchDeviceState - 切换设备工作状态

```python
# 函数原型
dll.SwitchDeviceState.argtypes = [ctypes.c_int]
dll.SwitchDeviceState.restype = ctypes.c_int

# 使用示例
result = dll.SwitchDeviceState(1)  # 切换到循环刺激状态
```

**参数说明:**
- `nStateNum`: 设备状态编码
  - `0`: 空闲状态
  - `1`: 循环刺激状态

**返回值:**
- `0`: 命令执行正确
- `1`: 命令错误
- `2`: 命令参数错误
- `3`: 校验错误
- `4`: 没有读取到硬件应答
- `5`: 串口写入失败

#### 5. SwitchChannelState - 切换通道工作状态

```python
# 函数原型
dll.SwitchChannelState.argtypes = [ctypes.c_int, ctypes.c_int]
dll.SwitchChannelState.restype = ctypes.c_int

# 使用示例
result = dll.SwitchChannelState(1, 3)  # A通道切换到正常工作状态
```

**参数说明:**
- `nChanNum`: 通道号 (1=A通道, 2=B通道)
- `nStateNum`: 通道状态编码
  - `0`: 停止
  - `1`: 暂停
  - `2`: 电流调节
  - `3`: 正常工作

**返回值:** 同SwitchDeviceState

#### 6. StimPara - 设置刺激参数

```python
# 函数原型
dll.StimPara.argtypes = [
    ctypes.c_int,    # ChanNum - 通道号
    ctypes.c_double, # ActFreq - 频率
    ctypes.c_double, # PulseWidth - 脉宽
    ctypes.c_double, # RelaxTime - 休息时间
    ctypes.c_double, # ClimbTime - 上升时间
    ctypes.c_double, # WorkTime - 工作时间
    ctypes.c_double, # FallTime - 下降时间
    ctypes.c_int     # WaveType - 波形类型
]
dll.StimPara.restype = ctypes.c_int

# 使用示例
result = dll.StimPara(1, 20.0, 200.0, 5.0, 2.0, 10.0, 2.0, 0)
```

**参数说明:**
- `ChanNum`: 通道号 (1=A通道, 2=B通道)
- `ActFreq`: 频率 (Hz, 范围: 2-160)
- `PulseWidth`: 脉宽 (μs, 范围: 10-500)
- `RelaxTime`: 休息时间 (s, 范围: 0-16)
- `ClimbTime`: 上升时间 (s, 范围: 0-5)
- `WorkTime`: 工作时间 (s, 范围: 0-30)
- `FallTime`: 下降时间 (s, 范围: 0-5)
- `WaveType`: 波形类型 (0=双相波, 1=单相波)

**返回值:** 同SwitchDeviceState

#### 7. CurrentSet - 设置电流强度

```python
# 函数原型
dll.CurrentSet.argtypes = [ctypes.c_int, ctypes.c_int]
dll.CurrentSet.restype = ctypes.c_int

# 使用示例
result = dll.CurrentSet(1, 25)  # A通道设置为25mA
```

**参数说明:**
- `nChanNum`: 通道号 (1=A通道, 2=B通道)
- `nValue`: 电流强度值 (mA, 范围: 1-100)

**返回值:** 同SwitchDeviceState

#### 8. RegulateCurrent - 电流调节

```python
# 函数原型
dll.RegulateCurrent.argtypes = [ctypes.c_int, ctypes.c_int, ctypes.c_bool]
dll.RegulateCurrent.restype = ctypes.c_int

# 使用示例
result = dll.RegulateCurrent(1, 3, False)  # A通道上调1mA步长
```

**参数说明:**
- `nChanNum`: 通道号 (1=A通道, 2=B通道)
- `nStep`: 电流调节步长
  - `0`: 0.1mA
  - `1`: 0.2mA
  - `2`: 0.5mA
  - `3`: 1.0mA
  - `4`: 2.0mA
  - `5`: 5.0mA
- `bClimb`: 调节方向
  - `True`: 下调
  - `False`: 上调

**返回值:** 同SwitchDeviceState

---

## 编程实现步骤

### 完整操作流程

```mermaid
graph TD
    A[点击连接电刺激设备] --> B[加载DLL库]
    B --> C[定义函数原型]
    C --> D[创建回调函数]
    D --> E[调用OpenRecPort连接设备]
    E --> F{连接成功?}
    F -->|是| G[切换设备到循环刺激状态]
    F -->|否| H[显示连接失败]
    G --> I[设置刺激参数]
    I --> J[电流调节]
    J --> K[启动预刺激]
    K --> L[开始正式刺激]
    L --> M[监控刺激状态]
    M --> N[停止刺激]
    N --> O[断开设备连接]
```

### 1. 初始化阶段

#### 1.1 加载DLL库

```python
def load_dll(dll_path: str) -> ctypes.CDLL:
    """加载电刺激设备DLL库"""
    try:
        if not Path(dll_path).exists():
            raise FileNotFoundError(f"DLL文件不存在: {dll_path}")
        
        dll = ctypes.CDLL(str(dll_path))
        logging.info(f"成功加载DLL: {dll_path}")
        return dll
    
    except Exception as e:
        logging.error(f"加载DLL失败: {e}")
        return None
```

#### 1.2 定义函数原型

```python
def define_function_prototypes(dll: ctypes.CDLL):
    """定义DLL函数原型"""
    
    # 定义回调函数类型
    callback_func_type = ctypes.WINFUNCTYPE(
        ctypes.c_int,                    # 返回值类型
        ctypes.c_void_p,                 # HANDLE pHandle
        ctypes.POINTER(ctypes.c_ubyte),  # LPVOID lpBuffer
        ctypes.c_int                     # int nSize
    )
    
    # 设备连接函数
    dll.OpenRecPort.argtypes = [
        ctypes.c_int,           # portNum
        ctypes.c_int,           # iReadSize
        callback_func_type,     # funDataProc callback
        ctypes.c_void_p         # HANDLE pHandle
    ]
    dll.OpenRecPort.restype = ctypes.c_int
    
    dll.CloseRecPort.argtypes = []
    dll.CloseRecPort.restype = ctypes.c_int
    
    dll.IsRecOpen.argtypes = []
    dll.IsRecOpen.restype = ctypes.c_bool
    
    # 设备控制函数
    dll.SwitchDeviceState.argtypes = [ctypes.c_int]
    dll.SwitchDeviceState.restype = ctypes.c_int
    
    dll.SwitchChannelState.argtypes = [ctypes.c_int, ctypes.c_int]
    dll.SwitchChannelState.restype = ctypes.c_int
    
    # 参数设置函数
    dll.StimPara.argtypes = [
        ctypes.c_int,    # ChanNum
        ctypes.c_double, # ActFreq
        ctypes.c_double, # PulseWidth
        ctypes.c_double, # RelaxTime
        ctypes.c_double, # ClimbTime
        ctypes.c_double, # WorkTime
        ctypes.c_double, # FallTime
        ctypes.c_int     # WaveType
    ]
    dll.StimPara.restype = ctypes.c_int
    
    # 电流控制函数
    dll.CurrentSet.argtypes = [ctypes.c_int, ctypes.c_int]
    dll.CurrentSet.restype = ctypes.c_int
    
    dll.RegulateCurrent.argtypes = [ctypes.c_int, ctypes.c_int, ctypes.c_bool]
    dll.RegulateCurrent.restype = ctypes.c_int
    
    return callback_func_type
```

#### 1.3 创建回调函数

```python
def create_data_callback(self):
    """创建数据回调函数，用于接收设备状态数据"""
    
    def data_callback(pHandle, lpBuffer, nSize):
        """数据回调函数 - 处理设备返回的状态数据"""
        try:
            if nSize == 0:
                # 心跳包，正常返回
                return nSize
            
            if nSize == 6 and lpBuffer:
                # 6个short值的数据包格式：
                # [设备标识, 设备状态, A通道状态, A进度, B通道状态, B进度]
                short_array = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_short * 6)).contents
                
                # 更新通道状态
                new_a_status = short_array[2]  # A通道状态
                new_b_status = short_array[4]  # B通道状态
                
                # 检测状态变化
                if new_a_status != self.channel_a_status:
                    logging.debug(f"A通道状态变化: {self.channel_a_status} -> {new_a_status}")
                    self.channel_a_status = new_a_status
                
                if new_b_status != self.channel_b_status:
                    logging.debug(f"B通道状态变化: {self.channel_b_status} -> {new_b_status}")
                    self.channel_b_status = new_b_status
            
            return nSize
        
        except Exception as e:
            logging.error(f"回调函数处理数据时发生错误: {e}")
            return nSize
    
    return self.callback_func_type(data_callback)
```

### 2. 设备连接阶段

#### 2.1 连接电刺激设备

```python
def connect_device(self, port_num: int = 1) -> bool:
    """连接电刺激设备"""
    try:
        logging.info(f"正在连接电刺激设备，端口: {port_num}")
        
        # 创建回调函数
        self.callback_function = self.create_data_callback()
        
        # 连接设备
        result = self.dll.OpenRecPort(port_num, 6, self.callback_function, None)
        
        if result == 1:  # 连接成功
            logging.info("电刺激设备连接成功")
            
            # 切换设备到循环刺激状态
            switch_result = self.dll.SwitchDeviceState(1)
            if switch_result == 0:
                logging.info("设备状态切换成功")
                return True
            else:
                logging.warning(f"设备状态切换失败，错误码: {switch_result}")
                return True  # 连接仍然有效
        
        elif result == -1:
            logging.error("电刺激设备连接失败")
            return False
        
        else:
            logging.error(f"连接设备时发生未知错误，错误码: {result}")
            return False
    
    except Exception as e:
        logging.error(f"连接电刺激设备时发生异常: {e}")
        return False
```

### 3. 参数设置阶段

#### 3.1 设置刺激参数

```python
def set_stimulation_parameters(self, params: StimulationParameters) -> bool:
    """设置刺激参数"""
    try:
        if not self.is_connected():
            logging.error("设备未连接，无法设置参数")
            return False
        
        # 调用DLL函数设置参数
        result = self.dll.StimPara(
            params.channel_num,
            params.frequency,
            params.pulse_width,
            params.relax_time,
            params.climb_time,
            params.work_time,
            params.fall_time,
            params.wave_type
        )
        
        if result == 0:
            logging.info(f"刺激参数设置成功: 通道{params.channel_num}, 频率{params.frequency}Hz")
            return True
        else:
            error_msg = self.get_error_message(result)
            logging.error(f"设置刺激参数失败，错误码: {result} ({error_msg})")
            return False
    
    except Exception as e:
        logging.error(f"设置刺激参数时发生错误: {e}")
        return False
```

### 4. 电流控制阶段

#### 4.1 设置电流强度

```python
def set_current(self, channel_num: int, current_ma: float) -> bool:
    """设置电流强度（单位：mA）"""
    try:
        if not self.is_connected():
            logging.error("设备未连接，无法设置电流")
            return False
        
        # 检查电流范围
        max_current = self.config.get('max_current', 50.0)
        min_current = self.config.get('min_current', 0.1)
        
        if not (min_current <= current_ma <= max_current):
            logging.error(f"电流值超出安全范围: {current_ma}mA, 允许范围: {min_current}-{max_current}mA")
            return False
        
        logging.debug(f"开始设置通道{channel_num}电流: {current_ma}mA")
        
        # 步骤1：确保设备处于循环刺激状态
        device_result = self.dll.SwitchDeviceState(1)  # 1: 循环刺激
        if device_result != 0:
            logging.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")
        
        time.sleep(0.1)
        
        # 步骤2：切换通道到电流调节状态
        switch_result = self.dll.SwitchChannelState(channel_num, 2)  # 2: 电流调节状态
        if switch_result != 0:
            error_msg = self.get_error_message(switch_result)
            logging.error(f"切换通道{channel_num}到电流调节状态失败: {error_msg}")
            return False
        
        time.sleep(0.1)
        
        # 步骤3：设置电流值
        current_value = int(current_ma)  # 直接使用mA值
        result = self.dll.CurrentSet(channel_num, current_value)
        
        if result == 0:
            logging.debug(f"通道 {channel_num} 电流设置成功: {current_ma}mA")
            return True
        else:
            error_msg = self.get_error_message(result)
            logging.error(f"设置电流失败，错误码: {result} - {error_msg}")
            return False
    
    except Exception as e:
        logging.error(f"设置电流时发生错误: {e}")
        return False
```

### 5. 刺激控制阶段

#### 5.1 启动预刺激

```python
def start_pre_stimulation(self, channel_num: int, current_ma: float, duration: float = 3.0) -> bool:
    """启动预刺激 - 按照指定电流刺激指定时间后自动停止"""
    try:
        if not self.is_connected():
            logging.error("设备未连接，无法启动预刺激")
            return False
        
        if current_ma <= 0:
            logging.warning(f"电流值无效: {current_ma}mA，跳过预刺激")
            return False
        
        channel_name = "A" if channel_num == 1 else "B"
        logging.info(f"开始{channel_name}通道预刺激: {current_ma}mA, 持续{duration}秒")
        
        # 1. 确保设备处于循环刺激状态
        device_result = self.dll.SwitchDeviceState(1)
        if device_result != 0:
            logging.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")
        
        # 2. 设置电流
        if not self.set_current(channel_num, current_ma):
            logging.error(f"{channel_name}通道电流设置失败")
            return False
        
        # 3. 启动刺激
        result = self.dll.SwitchChannelState(channel_num, 3)  # 3: 正常工作
        if result != 0:
            logging.error(f"{channel_name}通道启动失败，错误码: {result}")
            return False
        
        logging.debug(f"{channel_name}通道预刺激启动成功")
        
        # 4. 启动自动停止定时器
        self.start_auto_stop_timer(channel_num, duration)
        
        return True
    
    except Exception as e:
        logging.error(f"启动预刺激失败: {e}")
        return False
```

#### 5.2 开始正式刺激

```python
def start_stimulation(self, channel_num: int) -> bool:
    """开始正式刺激"""
    try:
        if not self.is_connected():
            logging.error("设备未连接，无法启动刺激")
            return False
        
        logging.info(f"启动通道{channel_num}刺激")
        
        # 确保设备处于循环刺激状态
        device_result = self.dll.SwitchDeviceState(1)
        if device_result != 0:
            error_msg = self.get_error_message(device_result)
            logging.error(f"切换设备到循环刺激状态失败: {error_msg}")
            return False
        
        # 启动通道
        result = self.dll.SwitchChannelState(channel_num, 3)  # 3: 正常工作
        if result == 0:
            logging.info(f"通道{channel_num}刺激启动成功")
            return True
        else:
            error_msg = self.get_error_message(result)
            logging.error(f"通道{channel_num}刺激启动失败: {error_msg}")
            return False
    
    except Exception as e:
        logging.error(f"启动刺激时发生异常: {e}")
        return False
```

#### 5.3 停止刺激

```python
def stop_stimulation(self, channel_num: int) -> bool:
    """停止刺激"""
    try:
        if not self.is_connected():
            logging.warning("设备未连接，无法停止刺激")
            return False
        
        logging.info(f"停止通道{channel_num}刺激")
        
        # 停止通道
        result = self.dll.SwitchChannelState(channel_num, 0)  # 0: 停止
        if result == 0:
            logging.info(f"通道{channel_num}刺激停止成功")
            return True
        else:
            error_msg = self.get_error_message(result)
            logging.error(f"停止通道{channel_num}刺激失败: {error_msg}")
            return False
    
    except Exception as e:
        logging.error(f"停止刺激时发生异常: {e}")
        return False

def stop_all_stimulation(self) -> bool:
    """停止所有通道刺激"""
    try:
        success = True
        
        # 停止A通道
        if not self.stop_stimulation(1):
            success = False
        
        # 停止B通道
        if not self.stop_stimulation(2):
            success = False
        
        if success:
            logging.info("所有通道刺激已停止")
        else:
            logging.warning("部分通道停止失败")
        
        return success
    
    except Exception as e:
        logging.error(f"停止所有刺激时发生异常: {e}")
        return False
```

### 6. 设备断开阶段

#### 6.1 断开设备连接

```python
def disconnect_device(self) -> bool:
    """断开电刺激设备连接"""
    try:
        logging.info("正在断开电刺激设备连接")
        
        # 停止所有刺激
        if self.is_connected():
            self.stop_all_stimulation()
        
        # 关闭设备端口
        if self.dll:
            result = self.dll.CloseRecPort()
            if result == 1:  # 1表示成功
                logging.info("电刺激设备断开成功")
                
                # 清理回调函数引用
                self.callback_function = None
                return True
            else:
                logging.error(f"断开电刺激设备失败，错误码: {result}")
                return False
        
        return True
    
    except Exception as e:
        logging.error(f"断开电刺激设备时发生错误: {e}")
        return False
```

## 代码示例

### 完整的电刺激设备控制类

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备控制器完整示例
"""

import ctypes
import threading
import time
import logging
from pathlib import Path
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass
from enum import Enum

@dataclass
class StimulationParameters:
    """刺激参数"""
    channel_num: int = 1          # 通道号 (1=A通道, 2=B通道)
    frequency: float = 20.0       # 频率 (Hz, 范围: 2-160)
    pulse_width: float = 200.0    # 脉宽 (μs, 范围: 10-500)
    relax_time: float = 5.0       # 休息时间 (s, 范围: 0-16)
    climb_time: float = 2.0       # 上升时间 (s, 范围: 0-5)
    work_time: float = 10.0       # 工作时间 (s, 范围: 0-30)
    fall_time: float = 2.0        # 下降时间 (s, 范围: 0-5)
    wave_type: int = 0            # 波形类型 (0=双相波, 1=单相波)

class StimulationDeviceStatus(Enum):
    """电刺激设备状态枚举"""
    DISCONNECTED = "未连接"
    CONNECTING = "连接中"
    CONNECTED = "已连接"
    STIMULATING = "刺激中"
    ERROR = "错误"

class StimulationDevice:
    """电刺激设备控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化电刺激设备"""
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 设备状态
        self.dll: Optional[ctypes.CDLL] = None
        self.status = StimulationDeviceStatus.DISCONNECTED
        self.callback_function = None
        self.callback_func_type = None
        
        # 通道状态
        self.channel_a_status = 0
        self.channel_b_status = 0
        
        # 当前参数
        self.current_parameters = StimulationParameters()
        
        self.logger.info("电刺激设备控制器初始化完成")
    
    def load_dll(self) -> bool:
        """加载DLL库"""
        try:
            dll_path = self.config['dll_path']
            if not Path(dll_path).exists():
                self.logger.error(f"DLL文件不存在: {dll_path}")
                return False
            
            # 加载DLL
            self.dll = ctypes.CDLL(str(dll_path))
            
            # 定义函数原型
            self._define_function_prototypes()
            
            self.logger.info(f"成功加载DLL: {dll_path}")
            return True
        
        except Exception as e:
            self.logger.error(f"加载DLL失败: {e}")
            return False
    
    def _define_function_prototypes(self):
        """定义DLL函数原型"""
        try:
            # 定义回调函数类型
            self.callback_func_type = ctypes.WINFUNCTYPE(
                ctypes.c_int,                    # 返回值类型
                ctypes.c_void_p,                 # HANDLE pHandle
                ctypes.POINTER(ctypes.c_ubyte),  # LPVOID lpBuffer
                ctypes.c_int                     # int nSize
            )
            
            # 设备连接函数
            self.dll.OpenRecPort.argtypes = [
                ctypes.c_int,           # portNum
                ctypes.c_int,           # iReadSize
                self.callback_func_type, # funDataProc callback
                ctypes.c_void_p         # HANDLE pHandle
            ]
            self.dll.OpenRecPort.restype = ctypes.c_int
            
            self.dll.CloseRecPort.argtypes = []
            self.dll.CloseRecPort.restype = ctypes.c_int
            
            self.dll.IsRecOpen.argtypes = []
            self.dll.IsRecOpen.restype = ctypes.c_bool
            
            # 设备控制函数
            self.dll.SwitchDeviceState.argtypes = [ctypes.c_int]
            self.dll.SwitchDeviceState.restype = ctypes.c_int
            
            self.dll.SwitchChannelState.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.SwitchChannelState.restype = ctypes.c_int
            
            # 参数设置函数
            self.dll.StimPara.argtypes = [
                ctypes.c_int,    # ChanNum
                ctypes.c_double, # ActFreq
                ctypes.c_double, # PulseWidth
                ctypes.c_double, # RelaxTime
                ctypes.c_double, # ClimbTime
                ctypes.c_double, # WorkTime
                ctypes.c_double, # FallTime
                ctypes.c_int     # WaveType
            ]
            self.dll.StimPara.restype = ctypes.c_int
            
            # 电流控制函数
            self.dll.CurrentSet.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.CurrentSet.restype = ctypes.c_int
            
            self.dll.RegulateCurrent.argtypes = [ctypes.c_int, ctypes.c_int, ctypes.c_bool]
            self.dll.RegulateCurrent.restype = ctypes.c_int
            
            self.logger.info("DLL函数原型定义完成")
        
        except Exception as e:
            self.logger.error(f"定义DLL函数原型失败: {e}")
            raise
    
    def _create_data_callback(self):
        """创建数据回调函数"""
        def data_callback(pHandle, lpBuffer, nSize):
            """数据回调函数 - 处理设备返回的状态数据"""
            try:
                if nSize == 0:
                    return nSize
                
                if nSize == 6 and lpBuffer:
                    # 6个short值的数据包格式：
                    # [设备标识, 设备状态, A通道状态, A进度, B通道状态, B进度]
                    short_array = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_short * 6)).contents
                    
                    # 更新通道状态
                    new_a_status = short_array[2]  # A通道状态
                    new_b_status = short_array[4]  # B通道状态
                    
                    # 检测状态变化
                    if new_a_status != self.channel_a_status:
                        self.logger.debug(f"A通道状态变化: {self.channel_a_status} -> {new_a_status}")
                        self.channel_a_status = new_a_status
                    
                    if new_b_status != self.channel_b_status:
                        self.logger.debug(f"B通道状态变化: {self.channel_b_status} -> {new_b_status}")
                        self.channel_b_status = new_b_status
                
                return nSize
            
            except Exception as e:
                self.logger.error(f"回调函数处理数据时发生错误: {e}")
                return nSize
        
        return self.callback_func_type(data_callback)
    
    def connect(self, port_num: int = 1) -> bool:
        """连接电刺激设备"""
        try:
            self.status = StimulationDeviceStatus.CONNECTING
            self.logger.info(f"正在连接电刺激设备，端口: {port_num}")
            
            # 加载DLL
            if not self.dll and not self.load_dll():
                self.status = StimulationDeviceStatus.ERROR
                return False
            
            # 创建回调函数
            self.callback_function = self._create_data_callback()
            
            # 连接设备
            result = self.dll.OpenRecPort(port_num, 6, self.callback_function, None)
            
            if result == 1:  # 连接成功
                self.status = StimulationDeviceStatus.CONNECTED
                self.logger.info("电刺激设备连接成功")
                
                # 切换设备到循环刺激状态
                switch_result = self.dll.SwitchDeviceState(1)
                if switch_result == 0:
                    self.logger.info("设备状态切换成功")
                else:
                    self.logger.warning(f"设备状态切换失败，错误码: {switch_result}")
                
                return True
            
            else:
                self.status = StimulationDeviceStatus.ERROR
                self.logger.error(f"连接电刺激设备失败，错误码: {result}")
                return False
        
        except Exception as e:
            self.logger.error(f"连接电刺激设备时发生异常: {e}")
            self.status = StimulationDeviceStatus.ERROR
            return False
    
    def disconnect(self) -> bool:
        """断开电刺激设备连接"""
        try:
            self.logger.info("正在断开电刺激设备连接")
            
            # 停止所有刺激
            if self.is_connected():
                self.stop_all_stimulation()
            
            # 关闭设备端口
            if self.dll:
                result = self.dll.CloseRecPort()
                if result == 1:  # 1表示成功
                    self.status = StimulationDeviceStatus.DISCONNECTED
                    self.logger.info("电刺激设备断开成功")
                    
                    # 清理回调函数引用
                    self.callback_function = None
                    return True
                else:
                    self.logger.error(f"断开电刺激设备失败，错误码: {result}")
                    return False
            
            return True
        
        except Exception as e:
            self.logger.error(f"断开电刺激设备时发生错误: {e}")
            return False
    
    def is_connected(self) -> bool:
        """检查设备是否已连接"""
        try:
            if self.dll:
                return self.dll.IsRecOpen()
            return False
        except Exception as e:
            self.logger.error(f"检查设备连接状态失败: {e}")
            return False
    
    def set_stimulation_parameters(self, params: StimulationParameters) -> bool:
        """设置刺激参数"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法设置参数")
                return False
            
            # 调用DLL函数设置参数
            result = self.dll.StimPara(
                params.channel_num,
                params.frequency,
                params.pulse_width,
                params.relax_time,
                params.climb_time,
                params.work_time,
                params.fall_time,
                params.wave_type
            )
            
            if result == 0:
                self.current_parameters = params
                self.logger.info(f"刺激参数设置成功: 通道{params.channel_num}, 频率{params.frequency}Hz")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"设置刺激参数失败，错误码: {result} ({error_msg})")
                return False
        
        except Exception as e:
            self.logger.error(f"设置刺激参数时发生错误: {e}")
            return False
    
    def set_current(self, channel_num: int, current_ma: float) -> bool:
        """设置电流强度（单位：mA）"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法设置电流")
                return False
            
            # 检查电流范围
            max_current = self.config.get('max_current', 50.0)
            min_current = self.config.get('min_current', 0.1)
            
            if not (min_current <= current_ma <= max_current):
                self.logger.error(f"电流值超出安全范围: {current_ma}mA, 允许范围: {min_current}-{max_current}mA")
                return False
            
            self.logger.debug(f"开始设置通道{channel_num}电流: {current_ma}mA")
            
            # 步骤1：确保设备处于循环刺激状态
            device_result = self.dll.SwitchDeviceState(1)  # 1: 循环刺激
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")
            
            time.sleep(0.1)
            
            # 步骤2：切换通道到电流调节状态
            switch_result = self.dll.SwitchChannelState(channel_num, 2)  # 2: 电流调节状态
            if switch_result != 0:
                error_msg = self._get_error_message(switch_result)
                self.logger.error(f"切换通道{channel_num}到电流调节状态失败: {error_msg}")
                return False
            
            time.sleep(0.1)
            
            # 步骤3：设置电流值
            current_value = int(current_ma)  # 直接使用mA值
            result = self.dll.CurrentSet(channel_num, current_value)
            
            if result == 0:
                self.logger.debug(f"通道 {channel_num} 电流设置成功: {current_ma}mA")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"设置电流失败，错误码: {result} - {error_msg}")
                return False
        
        except Exception as e:
            self.logger.error(f"设置电流时发生错误: {e}")
            return False
    
    def start_stimulation(self, channel_num: int) -> bool:
        """开始刺激"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法启动刺激")
                return False
            
            logging.info(f"启动通道{channel_num}刺激")
            
            # 确保设备处于循环刺激状态
            device_result = self.dll.SwitchDeviceState(1)
            if device_result != 0:
                error_msg = self._get_error_message(device_result)
                self.logger.error(f"切换设备到循环刺激状态失败: {error_msg}")
                return False
            
            # 启动通道
            result = self.dll.SwitchChannelState(channel_num, 3)  # 3: 正常工作
            if result == 0:
                self.status = StimulationDeviceStatus.STIMULATING
                self.logger.info(f"通道{channel_num}刺激启动成功")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"通道{channel_num}刺激启动失败: {error_msg}")
                return False
        
        except Exception as e:
            self.logger.error(f"启动刺激时发生异常: {e}")
            return False
    
    def stop_stimulation(self, channel_num: int) -> bool:
        """停止刺激"""
        try:
            if not self.is_connected():
                self.logger.warning("设备未连接，无法停止刺激")
                return False
            
            self.logger.info(f"停止通道{channel_num}刺激")
            
            # 停止通道
            result = self.dll.SwitchChannelState(channel_num, 0)  # 0: 停止
            if result == 0:
                self.logger.info(f"通道{channel_num}刺激停止成功")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"停止通道{channel_num}刺激失败: {error_msg}")
                return False
        
        except Exception as e:
            self.logger.error(f"停止刺激时发生异常: {e}")
            return False
    
    def stop_all_stimulation(self) -> bool:
        """停止所有通道刺激"""
        try:
            success = True
            
            # 停止A通道
            if not self.stop_stimulation(1):
                success = False
            
            # 停止B通道
            if not self.stop_stimulation(2):
                success = False
            
            if success:
                self.status = StimulationDeviceStatus.CONNECTED
                self.logger.info("所有通道刺激已停止")
            else:
                self.logger.warning("部分通道停止失败")
            
            return success
        
        except Exception as e:
            self.logger.error(f"停止所有刺激时发生异常: {e}")
            return False
    
    def _get_error_message(self, error_code: int) -> str:
        """根据错误码获取错误信息"""
        error_messages = {
            0: "成功",
            1: "命令错误",
            2: "命令参数错误",
            3: "校验错误",
            4: "没有读取到硬件的命令应答",
            5: "将命令写入串口时失败"
        }
        return error_messages.get(error_code, f"未知错误码: {error_code}")

# 使用示例
def main():
    """使用示例"""
    # 配置参数
    config = {
        'dll_path': './libs/RecoveryDLL.dll',
        'max_current': 50,
        'min_current': 1,
        'port_num': 1,
        'connection_timeout': 5
    }
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建设备实例
    device = StimulationDevice(config)
    
    try:
        # 1. 连接设备
        if device.connect_device(config['port_num']):
            print("设备连接成功")
            
            # 2. 设置刺激参数
            params = StimulationParameters(
                channel_num=1,
                frequency=20.0,
                pulse_width=200.0,
                relax_time=5.0,
                climb_time=2.0,
                work_time=10.0,
                fall_time=2.0,
                wave_type=0
            )
            
            if device.set_stimulation_parameters(params):
                print("刺激参数设置成功")
                
                # 3. 设置电流
                if device.set_current(1, 25.0):
                    print("电流设置成功: 25mA")
                    
                    # 4. 开始刺激
                    if device.start_stimulation(1):
                        print("刺激启动成功")
                        
                        # 5. 刺激10秒后停止
                        time.sleep(10)
                        
                        # 6. 停止刺激
                        if device.stop_stimulation(1):
                            print("刺激停止成功")
                        else:
                            print("刺激停止失败")
                    else:
                        print("刺激启动失败")
                else:
                    print("电流设置失败")
            else:
                print("刺激参数设置失败")
        else:
            print("设备连接失败")
    
    finally:
        # 7. 断开设备连接
        device.disconnect_device()
        print("设备已断开")

if __name__ == "__main__":
    main()
```

## 参数配置

### 系统配置参数

```python
STIMULATION_CONFIG = {
    # DLL文件路径
    'dll_path': './libs/RecoveryDLL.dll',
    
    # 电流限制 (mA)
    'max_current': 100,           # 最大电流
    'min_current': 1,            # 最小电流
    'current_step': 1,           # 电流步长
    
    # 默认刺激参数
    'default_frequency': 20,     # 默认频率 (Hz, 范围: 2-160)
    'default_pulse_width': 200,  # 默认脉宽 (μs, 范围: 10-500)
    'default_relax_time': 5,     # 默认休息时间 (s, 范围: 0-16)
    'default_climb_time': 2,     # 默认上升时间 (s, 范围: 0-5)
    'default_work_time': 10,     # 默认工作时间 (s, 范围: 0-30)
    'default_fall_time': 2,      # 默认下降时间 (s, 范围: 0-5)
    'default_wave_type': 0,      # 默认波形类型 (0=双相波, 1=单相波)
    
    # 设备配置
    'max_channels': 2,           # 最大通道数
    'port_num': 1,               # 默认端口号
    'connection_timeout': 5,     # 连接超时时间 (s)
    'current_steps': [1],        # 电流调节步长选项
}
```

### 刺激参数详解

| 参数 | 类型 | 范围 | 默认值 | 说明 |
|------|------|------|--------|------|
| channel_num | int | 1-2 | 1 | 通道号 (1=A通道, 2=B通道) |
| frequency | float | 2-160 | 20.0 | 刺激频率 (Hz) |
| pulse_width | float | 10-500 | 200.0 | 脉冲宽度 (μs) |
| relax_time | float | 0-16 | 5.0 | 休息时间 (s) |
| climb_time | float | 0-5 | 2.0 | 上升时间 (s) |
| work_time | float | 0-30 | 10.0 | 工作时间 (s) |
| fall_time | float | 0-5 | 2.0 | 下降时间 (s) |
| wave_type | int | 0-1 | 0 | 波形类型 (0=双相波, 1=单相波) |

### 安全限制

- **电流范围**: 1-50mA (可配置)
- **频率范围**: 2-160Hz
- **脉宽范围**: 10-500μs
- **连接超时**: 5秒
- **自动停止**: 预刺激3秒后自动停止

## 错误处理

### 错误码对照表

| 错误码 | 含义 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | 正常执行 |
| 1 | 命令错误 | 检查命令参数是否正确 |
| 2 | 命令参数错误 | 检查参数范围是否合法 |
| 3 | 校验错误 | 检查数据传输是否正常 |
| 4 | 没有读取到硬件应答 | 检查设备连接和硬件状态 |
| 5 | 串口写入失败 | 检查串口连接和权限 |

### 连接错误处理

```python
def handle_connection_error(self, error_code: int) -> str:
    """处理连接错误"""
    if error_code == -1:
        return "设备连接失败，请检查：\n1. 设备是否正确连接\n2. 端口号是否正确\n3. 设备驱动是否安装"
    elif error_code == 0:
        return "连接超时，请检查：\n1. 设备电源是否开启\n2. USB线缆是否正常\n3. 端口是否被占用"
    else:
        return f"未知连接错误，错误码: {error_code}"
```

### 异常捕获和处理

```python
def safe_dll_call(self, func_name: str, *args, **kwargs):
    """安全的DLL函数调用"""
    try:
        if not self.dll:
            self.logger.error(f"DLL未加载，无法调用 {func_name}")
            return None
        
        func = getattr(self.dll, func_name, None)
        if not func:
            self.logger.error(f"DLL中不存在函数 {func_name}")
            return None
        
        result = func(*args, **kwargs)
        return result
    
    except OSError as e:
        self.logger.error(f"调用DLL函数 {func_name} 时发生系统错误: {e}")
        return None
    except Exception as e:
        self.logger.error(f"调用DLL函数 {func_name} 时发生未知错误: {e}")
        return None
```

## 最佳实践

### 1. 设备连接管理

```python
class ConnectionManager:
    """连接管理器"""
    
    def __init__(self, device: StimulationDevice, max_retries: int = 3):
        self.device = device
        self.max_retries = max_retries
        self.retry_delay = 1.0  # 重试间隔(秒)
    
    def connect_with_retry(self, port_num: int) -> bool:
        """带重试的连接"""
        for attempt in range(self.max_retries):
            if self.device.connect(port_num):
                return True
            
            if attempt < self.max_retries - 1:
                logging.warning(f"连接失败，{self.retry_delay}秒后重试... ({attempt + 1}/{self.max_retries})")
                time.sleep(self.retry_delay)
        
        logging.error(f"连接失败，已重试{self.max_retries}次")
        return False
```

### 2. 参数验证

```python
def validate_stimulation_parameters(params: StimulationParameters) -> bool:
    """验证刺激参数"""
    # 频率验证
    if not (2 <= params.frequency <= 160):
        logging.error(f"频率超出范围: {params.frequency}Hz (允许范围: 2-160Hz)")
        return False
    
    # 脉宽验证
    if not (10 <= params.pulse_width <= 500):
        logging.error(f"脉宽超出范围: {params.pulse_width}μs (允许范围: 10-500μs)")
        return False
    
    # 时间参数验证
    if not (0 <= params.relax_time <= 16):
        logging.error(f"休息时间超出范围: {params.relax_time}s (允许范围: 0-16s)")
        return False
    
    # 其他参数验证...
    return True
```

### 3. 状态监控

```python
class DeviceMonitor:
    """设备状态监控器"""
    
    def __init__(self, device: StimulationDevice):
        self.device = device
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logging.info("设备状态监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logging.info("设备状态监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                if not self.device.is_connected():
                    logging.warning("设备连接丢失，尝试重新连接...")
                    # 实现重连逻辑
                
                time.sleep(1.0)  # 每秒检查一次
            except Exception as e:
                logging.error(f"监控过程中发生错误: {e}")
```

### 4. 安全保护

```python
class SafetyManager:
    """安全管理器"""
    
    def __init__(self, device: StimulationDevice):
        self.device = device
        self.max_stimulation_time = 300  # 最大刺激时间(秒)
        self.emergency_stop_enabled = True
    
    def emergency_stop(self):
        """紧急停止"""
        if self.emergency_stop_enabled:
            logging.warning("执行紧急停止")
            self.device.stop_all_stimulation()
            self.device.disconnect_device()
    
    def check_safety_limits(self, current_ma: float) -> bool:
        """检查安全限制"""
        max_current = self.device.config.get('max_current', 50)
        if current_ma > max_current:
            logging.error(f"电流超出安全限制: {current_ma}mA > {max_current}mA")
            return False
        return True
```

## 故障排除

### 常见问题及解决方案

#### 1. 设备连接失败

**症状**: OpenRecPort返回-1或0
**原因**: 
- 设备未正确连接
- 端口号错误
- 设备驱动未安装
- 端口被其他程序占用

**解决方案**:
```python
def troubleshoot_connection(self):
    """连接故障排除"""
    checks = [
        ("检查DLL文件", self._check_dll_file),
        ("检查设备连接", self._check_device_connection),
        ("检查端口占用", self._check_port_usage),
        ("检查设备驱动", self._check_device_driver),
    ]
    
    for check_name, check_func in checks:
        logging.info(f"正在{check_name}...")
        if not check_func():
            logging.error(f"{check_name}失败")
            return False
        else:
            logging.info(f"{check_name}通过")
    
    return True
```

#### 2. 刺激无效果

**症状**: 设备连接正常但无刺激输出
**原因**:
- 电流设置为0
- 通道未启动
- 参数设置错误
- 设备硬件故障

**解决方案**:
```python
def diagnose_stimulation_issue(self, channel_num: int):
    """诊断刺激问题"""
    # 检查设备连接
    if not self.is_connected():
        return "设备未连接"
    
    # 检查通道状态
    channel_status = self.get_channel_status(channel_num)
    if channel_status != 1:  # 1表示刺激中
        return f"通道{channel_num}未处于刺激状态，当前状态: {channel_status}"
    
    # 检查电流设置
    # 实现电流检查逻辑...
    
    return "未发现明显问题，建议检查硬件连接"
```

#### 3. 参数设置失败

**症状**: StimPara返回非0错误码
**原因**:
- 参数超出范围
- 设备状态不正确
- 通信错误

**解决方案**:
```python
def fix_parameter_error(self, params: StimulationParameters, error_code: int):
    """修复参数错误"""
    if error_code == 2:  # 参数错误
        # 检查并修正参数范围
        params.frequency = max(2, min(160, params.frequency))
        params.pulse_width = max(10, min(500, params.pulse_width))
        # 其他参数修正...
        
        logging.info("参数已自动修正，重新尝试设置")
        return self.set_stimulation_parameters(params)
    
    elif error_code == 4:  # 硬件无应答
        logging.warning("硬件无应答，尝试重新连接")
        self.disconnect()
        time.sleep(1)
        return self.connect()
    
    return False
```

### 调试工具

```python
class DebugTools:
    """调试工具"""
    
    @staticmethod
    def log_device_state(device: StimulationDevice):
        """记录设备状态"""
        logging.info("=== 设备状态信息 ===")
        logging.info(f"连接状态: {device.is_connected()}")
        logging.info(f"设备状态: {device.status}")
        logging.info(f"A通道状态: {device.channel_a_status}")
        logging.info(f"B通道状态: {device.channel_b_status}")
        logging.info("==================")
    
    @staticmethod
    def test_dll_functions(device: StimulationDevice):
        """测试DLL函数"""
        if not device.dll:
            logging.error("DLL未加载")
            return
        
        # 测试基本函数
        try:
            is_open = device.dll.IsRecOpen()
            logging.info(f"IsRecOpen: {is_open}")
        except Exception as e:
            logging.error(f"IsRecOpen测试失败: {e}")
```

--- 