<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3116cedc-4b0a-4835-b506-04abde7a0b1a" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yixJw9q8zmFOpalsrkHucoNRjD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.auth_service.executor&quot;: &quot;Run&quot;,
    &quot;Python.http_client.executor&quot;: &quot;Run&quot;,
    &quot;Python.login_window.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.main_window.executor&quot;: &quot;Run&quot;,
    &quot;Python.patients_page.executor&quot;: &quot;Run&quot;,
    &quot;Python.sidebar.executor&quot;: &quot;Run&quot;,
    &quot;Python.simple_voice.executor&quot;: &quot;Run&quot;,
    &quot;Python.tef_extractor.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_real_algorithms.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_signal_comparison.executor&quot;: &quot;Run&quot;,
    &quot;Python.text.executor&quot;: &quot;Run&quot;,
    &quot;Python.theme_manager.executor&quot;: &quot;Run&quot;,
    &quot;Python.treatment_page.executor&quot;: &quot;Run&quot;,
    &quot;Python.udp_communicator.executor&quot;: &quot;Run&quot;,
    &quot;Python.voice_prompt.executor&quot;: &quot;Run&quot;,
    &quot;Python.weighted_voting_classifier.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/NK_Python/脑机接口康复训练&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.25659.43" />
        <option value="bundled-python-sdk-181015f7ab06-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.25659.43" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3116cedc-4b0a-4835-b506-04abde7a0b1a" name="更改" comment="" />
      <created>1750331126248</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750331126248</updated>
      <workItem from="1750331127371" duration="13601000" />
      <workItem from="1750377053922" duration="2367000" />
      <workItem from="1750379438929" duration="9392000" />
      <workItem from="1750388890232" duration="5997000" />
      <workItem from="1750403402133" duration="755000" />
      <workItem from="1750407338559" duration="15702000" />
      <workItem from="1750423175804" duration="3413000" />
      <workItem from="1750426623276" duration="2470000" />
      <workItem from="1750464107654" duration="7990000" />
      <workItem from="1750473764374" duration="1514000" />
      <workItem from="1750475298836" duration="70000" />
      <workItem from="1750475376934" duration="7896000" />
      <workItem from="1750498453102" duration="15000" />
      <workItem from="1750498512646" duration="78000" />
      <workItem from="1750498600708" duration="33000" />
      <workItem from="1750501094713" duration="106000" />
      <workItem from="1750501214553" duration="143000" />
      <workItem from="1750501368937" duration="108000" />
      <workItem from="1750501482357" duration="35000" />
      <workItem from="1750501594205" duration="24000" />
      <workItem from="1750504465046" duration="5478000" />
      <workItem from="1750510740555" duration="5834000" />
      <workItem from="1750553951739" duration="42000" />
      <workItem from="1750554046684" duration="6066000" />
      <workItem from="1750561447059" duration="3759000" />
      <workItem from="1750588505026" duration="2068000" />
      <workItem from="1750637071800" duration="9563000" />
      <workItem from="1750667176596" duration="485000" />
      <workItem from="1750667675019" duration="710000" />
      <workItem from="1750669002221" duration="18746000" />
      <workItem from="1750723132783" duration="1628000" />
      <workItem from="1750724877570" duration="9402000" />
      <workItem from="1750734357228" duration="19911000" />
      <workItem from="1750761429961" duration="9856000" />
      <workItem from="1750809172591" duration="4068000" />
      <workItem from="1750832327617" duration="21441000" />
      <workItem from="1750856599988" duration="4061000" />
      <workItem from="1750895727033" duration="2776000" />
      <workItem from="1750898618621" duration="11390000" />
      <workItem from="1750939495238" duration="725000" />
      <workItem from="1750994368015" duration="4728000" />
      <workItem from="1751007282868" duration="1643000" />
      <workItem from="1751008945668" duration="1993000" />
      <workItem from="1751013947167" duration="7117000" />
      <workItem from="1751021400707" duration="10277000" />
      <workItem from="1751068815735" duration="599000" />
      <workItem from="1751070739506" duration="27000" />
      <workItem from="1751070783139" duration="62000" />
      <workItem from="1751070921171" duration="16449000" />
      <workItem from="1751098341477" duration="3210000" />
      <workItem from="1751160026615" duration="2681000" />
      <workItem from="1751162841868" duration="4675000" />
      <workItem from="1751168466346" duration="2807000" />
      <workItem from="1751188640719" duration="39000" />
      <workItem from="1751188713928" duration="4863000" />
      <workItem from="1751193622304" duration="4036000" />
      <workItem from="1751241346973" duration="25175000" />
      <workItem from="1751327658734" duration="5457000" />
      <workItem from="1751362845493" duration="5243000" />
      <workItem from="1751414302387" duration="14825000" />
      <workItem from="1751438031714" duration="27000000" />
      <workItem from="1751500351698" duration="626000" />
      <workItem from="1751501587314" duration="80000" />
      <workItem from="1751503713661" duration="18136000" />
      <workItem from="1751591079120" duration="301000" />
      <workItem from="1751591387181" duration="75000" />
      <workItem from="1751591476522" duration="24000" />
      <workItem from="1751591509103" duration="327000" />
      <workItem from="1751591857303" duration="1668000" />
      <workItem from="1751594074265" duration="22675000" />
      <workItem from="1751674632551" duration="29307000" />
      <workItem from="1751760642772" duration="934000" />
      <workItem from="1751761884066" duration="6038000" />
      <workItem from="1751768001538" duration="1855000" />
      <workItem from="1751769870216" duration="3265000" />
      <workItem from="1751773219426" duration="12707000" />
      <workItem from="1751801586565" duration="5870000" />
      <workItem from="1751807495943" duration="52000" />
      <workItem from="1751807555162" duration="15000" />
      <workItem from="1751807592097" duration="314000" />
      <workItem from="1751807919784" duration="550000" />
      <workItem from="1751846129670" duration="2453000" />
      <workItem from="1751849153128" duration="2959000" />
      <workItem from="1751852276562" duration="2767000" />
      <workItem from="1751855772367" duration="31789000" />
      <workItem from="1751894797799" duration="4225000" />
      <workItem from="1751932502083" duration="15676000" />
      <workItem from="1751957602394" duration="14407000" />
      <workItem from="1751972204701" duration="13026000" />
      <workItem from="1752018897633" duration="18660000" />
      <workItem from="1752046583744" duration="20227000" />
      <workItem from="1752105556343" duration="30796000" />
      <workItem from="1752152157746" duration="35000" />
      <workItem from="1752152209117" duration="6662000" />
      <workItem from="1752191785861" duration="18548000" />
      <workItem from="1752246739654" duration="6480000" />
      <workItem from="1752307007502" duration="7648000" />
      <workItem from="1752322876577" duration="5000" />
      <workItem from="1752324108856" duration="8082000" />
      <workItem from="1752365165311" duration="1302000" />
      <workItem from="1752366481770" duration="25264000" />
      <workItem from="1752452403823" duration="8140000" />
      <workItem from="1752461542388" duration="6020000" />
      <workItem from="1752476377820" duration="16172000" />
      <workItem from="1752493451024" duration="11170000" />
      <workItem from="1752537433496" duration="3423000" />
      <workItem from="1752550563041" duration="3270000" />
      <workItem from="1752564566417" duration="19012000" />
      <workItem from="1752585756038" duration="5942000" />
      <workItem from="1752623596090" duration="146000" />
      <workItem from="1752623753816" duration="6068000" />
      <workItem from="1752677020718" duration="630000" />
      <workItem from="1752847074301" duration="1690000" />
      <workItem from="1752909734835" duration="24000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/$tef_extractor.coverage" NAME="tef_extractor 覆盖结果" MODIFIED="1751514581539" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/services/feature_extraction" />
    <SUITE FILE_PATH="coverage/$test_real_algorithms.coverage" NAME="test_real_algorithms 覆盖结果" MODIFIED="1751870443258" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$patients_page.coverage" NAME="patients_page 覆盖结果" MODIFIED="1750685017558" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui/pages" />
    <SUITE FILE_PATH="coverage/$voice_prompt.coverage" NAME="voice_prompt 覆盖结果" MODIFIED="1751848333328" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/$theme_manager.coverage" NAME="theme_manager 覆盖结果" MODIFIED="1750837584180" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui/themes" />
    <SUITE FILE_PATH="coverage/$test_signal_comparison.coverage" NAME="test_signal_comparison 覆盖结果" MODIFIED="1751505760374" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$treatment_page.coverage" NAME="treatment_page 覆盖结果" MODIFIED="1752328876279" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui/pages" />
    <SUITE FILE_PATH="coverage/$simple_voice.coverage" NAME="simple_voice 覆盖结果" MODIFIED="1752379337972" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/$login_window.coverage" NAME="login_window 覆盖结果" MODIFIED="1752586710683" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui" />
    <SUITE FILE_PATH="coverage/$sidebar.coverage" NAME="sidebar 覆盖结果" MODIFIED="1752587417831" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui/components" />
    <SUITE FILE_PATH="coverage/$auth_service.coverage" NAME="auth_service 覆盖结果" MODIFIED="1752583709290" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/services" />
    <SUITE FILE_PATH="coverage/$main.coverage" NAME="main 覆盖结果" MODIFIED="1752847454289" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$http_client.coverage" NAME="http_client 覆盖结果" MODIFIED="1750676875150" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/$weighted_voting_classifier.coverage" NAME="weighted_voting_classifier 覆盖结果" MODIFIED="1752058056096" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/services" />
    <SUITE FILE_PATH="coverage/$udp_communicator.coverage" NAME="udp_communicator 覆盖结果" MODIFIED="1752378394940" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/$text.coverage" NAME="text 覆盖结果" MODIFIED="1751099367631" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$main_window.coverage" NAME="main_window 覆盖结果" MODIFIED="1752587052125" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui" />
  </component>
</project>