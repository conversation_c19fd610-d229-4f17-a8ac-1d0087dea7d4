[{"round_number": 1, "timestamp": "2025-08-11T22:46:06.872602", "data_samples": 20, "feature_files": {}, "classifier_files": {"plan_a_model": "models\\plan_a_model_56127121321_round_1.pkl", "plan_a_report": "models\\plan_a_report_56127121321_round_1.txt"}, "performance_metrics": {"total_samples": 20.0, "motor_imagery_samples": 10.0, "rest_samples": 10.0, "data_quality_avg": 0.8000000000000002}, "training_config": {"trials_per_class": 10, "trial_duration": 2.0, "total_samples": 20}, "notes": "第1轮训练完成"}, {"round_number": 2, "timestamp": "2025-08-11T22:51:04.320639", "data_samples": 40, "feature_files": {}, "classifier_files": {"plan_a_model": "models\\plan_a_model_56127121321_round_2.pkl", "plan_a_report": "models\\plan_a_report_56127121321_round_2.txt"}, "performance_metrics": {"total_samples": 40.0, "motor_imagery_samples": 20.0, "rest_samples": 20.0, "data_quality_avg": 0.8000000000000002}, "training_config": {"trials_per_class": 10, "trial_duration": 2.0, "total_samples": 40}, "notes": "第2轮训练完成"}, {"round_number": 3, "timestamp": "2025-08-11T22:55:38.586539", "data_samples": 60, "feature_files": {}, "classifier_files": {"plan_a_model": "models\\plan_a_model_56127121321_round_3.pkl", "plan_a_report": "models\\plan_a_report_56127121321_round_3.txt"}, "performance_metrics": {"total_samples": 60.0, "motor_imagery_samples": 30.0, "rest_samples": 30.0, "data_quality_avg": 0.8000000000000002}, "training_config": {"trials_per_class": 10, "trial_duration": 2.0, "total_samples": 60}, "notes": "第3轮训练完成"}]