#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP通信器模块
UDP Communicator Module

用于与VR系统进行UDP通信的功能
"""

import socket
import logging
import time
from enum import Enum
from typing import Optional
from PySide6.QtCore import QObject, Signal


class UDPCommand(Enum):
    """UDP指令枚举"""
    TREAT = "treat"      # 进入治疗模式
    START = "start"      # 开始VR动画/反馈
    STOP = "stop"        # 停止当前动作
    STOPALL = "stopall"  # 结束治疗会话
    EXIT = "exit"        # 退出VR软件


class UDPCommunicator(QObject):
    """UDP通信器"""
    
    # Qt信号定义
    command_sent = Signal(str)  # 指令发送成功信号
    command_failed = Signal(str, str)  # 指令发送失败信号 (command, error)
    
    def __init__(self, vr_host: str = "127.0.0.1", vr_port: int = 3004, local_port: int = 3005):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        
        # VR系统配置
        self.vr_host = vr_host
        self.vr_port = vr_port
        self.local_port = local_port
        
        # UDP套接字
        self.socket: Optional[socket.socket] = None
        self.is_connected = False
        
        # 重试配置
        self.retry_times = 3
        self.retry_interval = 0.01  # 10ms
        
        self.logger.info(f"UDP通信器初始化 - VR系统: {vr_host}:{vr_port}, 本地端口: {local_port}")

    def start_listening(self) -> bool:
        """启动UDP监听"""
        try:
            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            
            # 绑定本地端口
            self.socket.bind(('', self.local_port))
            
            # 设置超时
            self.socket.settimeout(1.0)
            
            self.is_connected = True
            self.logger.info(f"UDP监听启动成功 - 本地端口: {self.local_port}")
            return True
            
        except Exception as e:
            self.logger.error(f"UDP监听启动失败: {e}")
            self.is_connected = False
            return False

    def stop_listening(self):
        """停止UDP监听"""
        try:
            if self.socket:
                self.socket.close()
                self.socket = None
            
            self.is_connected = False
            self.logger.info("UDP监听已停止")
            
        except Exception as e:
            self.logger.error(f"停止UDP监听失败: {e}")

    def send_command(self, command: UDPCommand) -> bool:
        """
        发送UDP指令到VR系统
        
        Args:
            command: UDP指令枚举
            
        Returns:
            bool: 发送是否成功
        """
        if not self.socket:
            self.logger.warning("UDP套接字未初始化")
            return False
        
        command_str = command.value
        
        # 重试发送
        for attempt in range(self.retry_times):
            try:
                # 发送指令
                message = command_str.encode('utf-8')
                self.socket.sendto(message, (self.vr_host, self.vr_port))
                
                self.logger.info(f"UDP指令发送成功: {command_str} -> {self.vr_host}:{self.vr_port}")
                self.command_sent.emit(command_str)
                return True
                
            except Exception as e:
                self.logger.warning(f"UDP指令发送失败 (尝试 {attempt + 1}/{self.retry_times}): {e}")
                
                if attempt < self.retry_times - 1:
                    time.sleep(self.retry_interval)
                else:
                    # 最后一次尝试失败
                    error_msg = f"UDP指令发送失败: {e}"
                    self.logger.error(error_msg)
                    self.command_failed.emit(command_str, str(e))
                    return False
        
        return False

    def send_treat_command(self) -> bool:
        """发送TREAT指令"""
        return self.send_command(UDPCommand.TREAT)

    def send_start_command(self) -> bool:
        """发送START指令"""
        return self.send_command(UDPCommand.START)

    def send_stop_command(self) -> bool:
        """发送STOP指令"""
        return self.send_command(UDPCommand.STOP)

    def send_stopall_command(self) -> bool:
        """发送STOPALL指令"""
        return self.send_command(UDPCommand.STOPALL)

    def send_exit_command(self) -> bool:
        """发送EXIT指令"""
        return self.send_command(UDPCommand.EXIT)

    def test_connection(self) -> bool:
        """测试UDP连接"""
        try:
            # 创建临时套接字测试连接
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            test_socket.settimeout(1.0)
            
            # 发送测试消息
            test_message = b"test"
            test_socket.sendto(test_message, (self.vr_host, self.vr_port))
            
            test_socket.close()
            self.logger.info("UDP连接测试成功")
            return True
            
        except Exception as e:
            self.logger.warning(f"UDP连接测试失败: {e}")
            return False


# 全局UDP通信器实例
_udp_communicator: Optional[UDPCommunicator] = None


def get_udp_communicator() -> UDPCommunicator:
    """获取全局UDP通信器实例"""
    global _udp_communicator
    
    if _udp_communicator is None:
        _udp_communicator = UDPCommunicator()
    
    return _udp_communicator


def initialize_udp_communicator(vr_host: str = "127.0.0.1", vr_port: int = 3004, local_port: int = 3005) -> UDPCommunicator:
    """
    初始化UDP通信器
    
    Args:
        vr_host: VR系统主机地址
        vr_port: VR系统端口
        local_port: 本地绑定端口
        
    Returns:
        UDPCommunicator: UDP通信器实例
    """
    global _udp_communicator
    
    _udp_communicator = UDPCommunicator(vr_host, vr_port, local_port)
    return _udp_communicator
