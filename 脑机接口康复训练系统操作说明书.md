# 脑机接口康复训练系统操作说明书

## 系统概述

脑机接口康复训练系统（BCI-based Rehabilitation Training System）是一套专业的医疗康复设备，集成了脑电信号采集、实时信号处理、电刺激治疗和VR反馈等功能，为患者提供个性化的康复训练方案。

### 系统版本
- **软件版本**: V2.0.0
- **系统名称**: NeuroLink Pro
- **开发单位**: 海天智能

## 目录
1. [系统启动](#系统启动)
2. [用户登录](#用户登录)
3. [主界面介绍](#主界面介绍)
4. [患者管理](#患者管理)
5. [治疗系统](#治疗系统)
6. [报告分析](#报告分析)
7. [用户管理](#用户管理)
8. [系统设置](#系统设置)
9. [设备连接](#设备连接)
10. [故障排除](#故障排除)
11. [注意事项](#注意事项)

---

## 系统启动

### 启动方式
1. **双击桌面图标**：双击"脑机接口康复训练系统"图标
2. **从开始菜单**：开始菜单 → 程序 → 脑机接口康复训练系统
3. **直接运行**：运行 `main.py` 文件

### 启动过程
1. 系统会自动检查许可证状态
2. 如未激活，会弹出激活对话框
3. 激活成功后显示登录界面
4. 系统在后台预加载必要组件

### 系统要求
- **操作系统**: Windows 10/11 (64位)
- **内存**: 最低4GB，推荐8GB
- **磁盘空间**: 2GB可用空间
- **分辨率**: 最低1200×800，推荐1400×900

---

## 用户登录

### 登录界面
系统启动后会显示登录窗口，包含以下元素：
- **系统Logo**: 脑机接口康复训练系统标识
- **用户选择**: 下拉菜单选择用户
- **密码输入**: 输入对应用户密码
- **登录按钮**: 确认登录
- **退出按钮**: 退出系统

### 默认用户账户
系统预设以下用户账户：

| 用户名 | 密码 | 角色 | 权限说明 |
|--------|------|------|----------|
| admin | admin123 | 管理员 | 全部功能权限 |
| doctor | doctor123 | 医生 | 患者管理、治疗、报告查看 |
| operator | operator123 | 操作员 | 治疗操作、基础查看 |

### 登录步骤
1. 从下拉菜单选择用户
2. 输入对应密码
3. 点击"登录"按钮或按回车键
4. 系统验证成功后进入主界面

### 登录注意事项
- 密码区分大小写
- 连续登录失败会有安全提示
- 系统会记录登录时间和次数

---

## 主界面介绍

### 界面布局
主界面采用现代化设计，分为三个主要区域：

#### 1. 侧边栏（左侧）
- **系统Logo**: 显示"BCI"标识和系统名称
- **导航菜单**: 包含所有功能模块
- **用户信息**: 显示当前登录用户信息
- **折叠按钮**: 可收起/展开侧边栏

#### 2. 顶部栏
- **菜单切换**: 控制侧边栏显示/隐藏
- **页面标题**: 显示当前页面名称（中英文）
- **系统状态**: 显示设备连接状态
- **主题切换**: 医疗主题/科技主题切换

#### 3. 主内容区
- **页面内容**: 显示当前选中功能的详细界面
- **操作区域**: 各种功能操作按钮和表单

### 导航菜单
侧边栏包含以下功能模块：

| 图标 | 功能名称 | 英文名称 | 说明 |
|------|----------|----------|------|
| ● | 患者管理 | Patient Management | 患者信息管理 |
| ■ | 治疗系统 | Treatment System | 脑机接口治疗 |
| ♦ | 报告分析 | Report Analysis | 治疗数据分析 |
| ★ | 用户管理 | User Management | 系统用户管理 |
| ◆ | 系统设置 | System Settings | 系统参数配置 |
| ✕ | 退出系统 | Exit System | 安全退出系统 |

### 主题切换
系统提供两种主题：
- **医疗主题**: 适合医疗环境，色调温和
- **科技主题**: 现代科技风格，对比度高

---

## 患者管理

### 功能概述
患者管理模块用于管理所有患者的基本信息、治疗记录和康复进度。

### 患者列表
#### 列表显示
- **患者卡片**: 每个患者以卡片形式显示
- **基本信息**: 姓名、ID、性别、年龄
- **状态标识**: 在院/出院状态
- **操作按钮**: 查看详情、开始治疗

#### 搜索和筛选
- **搜索框**: 支持按姓名、ID搜索
- **状态筛选**: 可选择显示在院或所有患者
- **分页显示**: 支持大量患者数据分页浏览

### 患者详情
点击患者卡片可查看详细信息：

#### 基本信息
- 姓名、性别、年龄
- 患者编号、联系方式
- 入院时间、诊断信息
- 主治医生、科室信息

#### 治疗记录
- 治疗日期和时间
- 治疗时长和类型
- 治疗参数设置
- 治疗效果评估

#### 康复进度
- 治疗次数统计
- 康复进度图表
- 效果趋势分析

### 新增患者
#### 操作步骤
1. 点击"新增患者"按钮
2. 填写患者基本信息：
   - 姓名（必填）
   - 性别（必选）
   - 年龄（必填）
   - 联系方式
   - 诊断信息
   - 备注说明
3. 点击"保存"确认添加

#### 注意事项
- 患者编号系统自动生成
- 必填项目不能为空
- 联系方式格式需正确

### 编辑患者信息
1. 在患者详情页点击"编辑"按钮
2. 修改需要更新的信息
3. 点击"保存"确认修改
4. 系统会记录修改历史

### 开始治疗
1. 选择需要治疗的患者
2. 点击"开始治疗"按钮
3. 系统自动跳转到治疗系统页面
4. 患者信息自动带入治疗界面

---

## 治疗系统

### 功能概述
治疗系统是核心功能模块，集成了脑电信号采集、实时处理、电刺激治疗和VR反馈等功能。

### 治疗界面布局

#### 1. 患者信息区
- 显示当前治疗患者基本信息
- 治疗开始时间
- 预计治疗时长

#### 2. 设备状态区
- **脑电设备状态**: 显示蓝牙连接状态
- **电刺激设备状态**: 显示串口连接状态
- **VR系统状态**: 显示UDP通信状态

#### 3. 治疗参数区
包含多个参数调节器：

**脑机接口参数**
- 难度等级：1-5级可调
- 触发阈值：影响治疗敏感度
- 分类器权重：性能/平均/自定义

**电刺激参数**
- 电流强度：1-100mA
- 刺激频率：2-160Hz
- 脉冲宽度：10-500μs
- 工作时间：0-30秒
- 休息时间：0-16秒

#### 4. 实时监测区
- **准确率显示**: 当前分类准确率
- **触发统计**: 成功触发次数/总次数
- **治疗倒计时**: 剩余治疗时间
- **实时波形**: 脑电信号波形显示

#### 5. 治疗控制区
- **开始治疗**: 启动完整治疗流程
- **暂停治疗**: 暂停当前治疗
- **停止治疗**: 结束治疗并保存数据
- **紧急停止**: 立即停止所有设备

### 治疗流程

#### 准备阶段
1. **设备连接检查**
   - 确认脑电设备已连接
   - 确认电刺激设备已连接
   - 确认VR系统通信正常

2. **参数设置**
   - 根据患者情况调整治疗参数
   - 设置治疗时长和难度等级
   - 确认电刺激安全参数

3. **患者准备**
   - 佩戴脑电采集设备
   - 放置电刺激电极
   - 确认患者舒适度

#### 治疗阶段
1. **信号采集**
   - 系统开始采集脑电信号
   - 实时显示信号质量
   - 自动进行信号预处理

2. **意念识别**
   - 系统提示患者进行运动想象
   - 实时分析脑电信号特征
   - 计算运动意念分类结果

3. **反馈控制**
   - 根据识别结果触发VR反馈
   - 同时启动电刺激治疗
   - 记录治疗效果数据

4. **循环训练**
   - 按设定间隔重复训练
   - 动态调整难度等级
   - 监控患者疲劳状态

#### 结束阶段
1. **数据保存**
   - 自动保存治疗数据
   - 生成治疗报告
   - 更新患者治疗记录

2. **设备清理**
   - 安全断开所有设备连接
   - 清理电极和传感器
   - 设备状态复位

### 安全机制

#### 电刺激安全
- **电流限制**: 最大电流不超过100mA
- **时间限制**: 单次刺激时间受限
- **紧急停止**: 随时可中断刺激
- **参数验证**: 所有参数在安全范围内

#### 信号监测
- **信号质量检查**: 实时监测信号质量
- **异常检测**: 自动识别异常信号
- **连接监控**: 持续监控设备连接状态

#### 治疗监控
- **时间控制**: 严格控制治疗时长
- **疲劳检测**: 监测患者疲劳状态
- **效果评估**: 实时评估治疗效果

---

## 报告分析

### 功能概述
报告分析模块提供治疗数据的统计分析和可视化展示，帮助医生评估治疗效果。

### 报告类型

#### 1. 患者个人报告
- **基本信息**: 患者基本资料
- **治疗概况**: 治疗次数、总时长
- **效果趋势**: 准确率变化趋势
- **参数记录**: 历次治疗参数设置

#### 2. 治疗统计报告
- **日统计**: 每日治疗情况
- **周统计**: 周治疗汇总
- **月统计**: 月度治疗分析
- **年统计**: 年度治疗总结

#### 3. 设备使用报告
- **设备状态**: 设备运行状态统计
- **故障记录**: 设备故障和维护记录
- **使用时长**: 设备累计使用时间

### 数据分析功能

#### 统计图表
- **折线图**: 显示治疗效果趋势
- **柱状图**: 对比不同时期数据
- **饼图**: 显示治疗类型分布
- **散点图**: 分析参数相关性

#### 数据导出
- **PDF报告**: 生成专业治疗报告
- **Excel表格**: 导出详细数据
- **图片导出**: 保存图表图片
- **打印功能**: 直接打印报告

### 报告查看步骤
1. 进入"报告分析"页面
2. 选择报告类型和时间范围
3. 选择需要分析的患者（可多选）
4. 点击"生成报告"按钮
5. 查看分析结果和图表
6. 可选择导出或打印报告

---

## 用户管理

### 功能概述
用户管理模块用于管理系统用户账户，包括用户创建、权限分配和账户维护。

### 用户角色

#### 管理员 (Admin)
- **权限范围**: 所有功能权限
- **主要职责**: 系统管理、用户管理、设置配置
- **操作权限**: 增删改查所有数据

#### 医生 (Doctor)
- **权限范围**: 患者管理、治疗操作、报告查看
- **主要职责**: 患者诊疗、治疗方案制定
- **操作权限**: 患者数据、治疗记录、分析报告

#### 操作员 (Operator)
- **权限范围**: 治疗操作、基础查看
- **主要职责**: 设备操作、治疗执行
- **操作权限**: 治疗系统、基础查询

### 用户管理操作

#### 新增用户
1. 点击"新增用户"按钮
2. 填写用户信息：
   - 用户名（唯一标识）
   - 姓名（真实姓名）
   - 密码（安全密码）
   - 角色（选择权限级别）
   - 邮箱（联系方式）
   - 部门（所属科室）
3. 点击"保存"创建用户

#### 编辑用户
1. 在用户列表中选择用户
2. 点击"编辑"按钮
3. 修改用户信息
4. 点击"保存"确认修改

#### 删除用户
1. 选择需要删除的用户
2. 点击"删除"按钮
3. 确认删除操作
4. 用户将被标记为非活跃状态

#### 重置密码
1. 选择需要重置密码的用户
2. 点击"重置密码"按钮
3. 设置新密码
4. 通知用户新密码

### 权限管理
- **页面访问控制**: 根据角色限制页面访问
- **功能权限控制**: 细化到具体操作权限
- **数据访问控制**: 限制数据查看和修改范围

---

## 系统设置

### 功能概述
系统设置模块用于配置系统运行参数，包括基本设置、设备配置和治疗参数。

### 设置分类

#### 1. 基本设置
**系统信息**
- 系统名称：脑机接口康复训练系统
- 医院编号：医疗机构标识
- 医院名称：医疗机构名称
- 科室名称：使用科室
- 设备编号：设备唯一标识

**治疗配置**
- 刺激时长：单次电刺激持续时间（10-60秒）
- 治疗时长：完整治疗会话时间（10-60分钟）
- 鼓励间隔：语音鼓励间隔时间（20-60秒）
- 最小保存时长：数据保存最小时长（1-15分钟）

#### 2. 脑电设备设置
**连接配置**
- 目标设备名称：蓝牙设备名称（默认BLE5201）
- 自动连接：启用/禁用自动连接
- 连接超时：连接超时时间（5-30秒）
- 扫描超时：设备扫描超时（5-15秒）

**重连配置**
- 最大重连次数：连接失败重试次数（1-10次）
- 重连间隔：重试间隔时间（1-10秒）
- 信号强度阈值：最低信号强度要求（-90到-30dBm）

**高级配置**
- 连接优先级：优先已知设备/总是扫描新设备
- GATT服务模式：仅连接(推荐)/完整服务发现
- 采样率：信号采样频率（1-1000Hz）
- 通道数：采集通道数量（1-64）

**设备管理**
- 扫描设备：搜索可用蓝牙设备
- 清除配对历史：清除已配对设备记录

#### 3. 电刺激设备设置
**端口配置**
- 端口号：串口号选择（COM1-COM19）
- 默认波形类型：双相波/单相波

**电流参数**
- 最大电流：安全电流上限（1-100mA）
- 最小电流：电流下限（1-100mA）
- 电流步长：调节步长（0.1-5.0mA）

**刺激参数**
- 默认频率：刺激频率（2-160Hz）
- 默认脉宽：脉冲宽度（10-500μs）
- 默认休息时间：刺激间隔（0-16秒）
- 默认上升时间：电流上升时间（0-5秒）
- 默认工作时间：刺激持续时间（0-30秒）
- 默认下降时间：电流下降时间（0-5秒）

### 设置操作

#### 修改设置
1. 进入"系统设置"页面
2. 选择相应的设置标签页
3. 修改需要调整的参数
4. 点击"保存设置"按钮
5. 系统提示保存成功

#### 重置设置
1. 点击"重置设置"按钮
2. 确认重置操作
3. 所有设置恢复为默认值
4. 需要重新配置个性化参数

#### 设置生效
- 大部分设置立即生效
- 设备连接设置需要重新连接设备
- 治疗参数在下次治疗时生效

---

## 设备连接

### 脑电设备连接

#### 设备要求
- **设备型号**: BLE5201或兼容设备
- **连接方式**: 蓝牙BLE连接
- **信号强度**: 建议-70dBm以上

#### 连接步骤
1. **设备准备**
   - 确保脑电设备已开机
   - 检查设备电量充足
   - 确认设备处于可发现状态

2. **系统连接**
   - 进入治疗系统页面
   - 点击"连接脑电设备"按钮
   - 系统自动搜索并连接设备
   - 连接成功后状态指示器变为绿色

3. **连接验证**
   - 检查信号质量指示
   - 确认数据正常接收
   - 测试信号稳定性

#### 连接故障处理
- **设备未发现**: 检查设备开机状态和距离
- **连接超时**: 重启设备和蓝牙适配器
- **信号不稳定**: 调整设备位置，减少干扰
- **频繁断连**: 检查电量和信号强度

### 电刺激设备连接

#### 设备要求
- **设备型号**: C2电刺激器或兼容设备
- **连接方式**: 串口连接（USB转串口）
- **驱动程序**: 需要安装相应驱动

#### 连接步骤
1. **硬件连接**
   - 使用USB线连接设备到电脑
   - 确认驱动程序已正确安装
   - 检查设备管理器中的端口号

2. **软件配置**
   - 进入系统设置→电刺激设备设置
   - 选择正确的COM端口号
   - 设置通信参数

3. **连接测试**
   - 在治疗系统中点击"连接电刺激设备"
   - 系统自动建立串口连接
   - 连接成功后进行设备自检

#### 连接故障处理
- **端口无法打开**: 检查端口号设置和设备连接
- **通信失败**: 确认波特率和通信参数
- **设备无响应**: 检查设备电源和连接线
- **驱动问题**: 重新安装设备驱动程序

### VR系统连接

#### 系统要求
- **VR软件**: 配套VR反馈软件
- **通信协议**: UDP通信
- **网络配置**: 本地网络连接

#### 连接配置
- **VR系统地址**: 127.0.0.1（本机）
- **VR系统端口**: 3004
- **本地监听端口**: 3005

#### 连接验证
- 系统自动检测VR软件运行状态
- 发送测试指令验证通信
- 确认反馈指令正常响应

---

## 故障排除

### 常见问题及解决方案

#### 1. 系统启动问题

**问题**: 系统无法启动
**可能原因**:
- 缺少必要的运行库
- 数据库文件损坏
- 许可证过期

**解决方案**:
- 安装Visual C++ Redistributable
- 检查数据库文件完整性
- 重新激活软件许可证

**问题**: 启动速度慢
**解决方案**:
- 关闭不必要的后台程序
- 检查磁盘空间是否充足
- 优化系统启动项

#### 2. 登录问题

**问题**: 无法登录系统
**可能原因**:
- 用户名或密码错误
- 用户账户被禁用
- 数据库连接问题

**解决方案**:
- 确认用户名和密码正确
- 联系管理员检查账户状态
- 检查数据库文件权限

#### 3. 设备连接问题

**问题**: 脑电设备连接失败
**解决方案**:
- 检查蓝牙适配器是否正常工作
- 确认设备在可发现状态
- 重启蓝牙服务
- 清除设备配对历史后重新连接

**问题**: 电刺激设备无法连接
**解决方案**:
- 检查USB连接线
- 确认COM端口号正确
- 重新安装设备驱动
- 检查设备电源状态

**问题**: VR系统通信异常
**解决方案**:
- 确认VR软件正在运行
- 检查网络连接状态
- 验证端口配置正确
- 重启VR软件

#### 4. 治疗过程问题

**问题**: 信号质量差
**解决方案**:
- 检查电极接触是否良好
- 清洁电极和皮肤接触面
- 调整电极位置
- 减少环境电磁干扰

**问题**: 分类准确率低
**解决方案**:
- 重新训练分类器
- 调整信号处理参数
- 检查患者配合度
- 优化电极放置位置

**问题**: 电刺激无效果
**解决方案**:
- 检查电极连接
- 确认刺激参数设置
- 验证患者感受
- 调整电流强度

#### 5. 数据问题

**问题**: 数据保存失败
**解决方案**:
- 检查磁盘空间
- 确认数据库权限
- 验证文件路径正确
- 重启数据库服务

**问题**: 报告生成失败
**解决方案**:
- 检查数据完整性
- 确认报告模板存在
- 验证导出路径权限
- 重新生成报告

### 系统维护

#### 定期维护
- **每日**: 检查设备连接状态
- **每周**: 清理临时文件和日志
- **每月**: 备份重要数据
- **每季度**: 系统性能优化

#### 数据备份
- **自动备份**: 系统自动备份治疗数据
- **手动备份**: 定期手动备份完整数据库
- **异地备份**: 重要数据异地存储

#### 系统更新
- **软件更新**: 定期检查软件更新
- **驱动更新**: 保持设备驱动最新
- **安全更新**: 及时安装安全补丁

---

## 注意事项

### 安全注意事项

#### 电气安全
- **设备接地**: 确保所有设备良好接地
- **电流限制**: 严格控制电刺激电流在安全范围
- **紧急停止**: 熟悉紧急停止操作程序
- **定期检查**: 定期检查设备电气安全

#### 患者安全
- **过敏检查**: 治疗前检查患者是否对电极材料过敏
- **皮肤检查**: 确认电极放置部位皮肤完整
- **舒适度确认**: 治疗过程中持续关注患者舒适度
- **异常处理**: 出现异常立即停止治疗

#### 数据安全
- **隐私保护**: 严格保护患者隐私信息
- **访问控制**: 合理设置用户权限
- **数据备份**: 定期备份重要数据
- **安全传输**: 确保数据传输安全

### 操作注意事项

#### 设备操作
- **操作培训**: 操作人员必须经过专业培训
- **标准流程**: 严格按照标准操作流程执行
- **记录完整**: 完整记录治疗过程和参数
- **异常报告**: 及时报告设备异常情况

#### 环境要求
- **温度控制**: 保持适宜的室内温度（18-25°C）
- **湿度控制**: 相对湿度控制在40-70%
- **电磁环境**: 避免强电磁干扰
- **清洁卫生**: 保持治疗环境清洁卫生

#### 维护保养
- **日常清洁**: 每日清洁设备表面
- **电极维护**: 定期更换和清洁电极
- **软件维护**: 定期更新软件版本
- **硬件检查**: 定期检查硬件连接状态

### 法规遵循

#### 医疗器械法规
- 严格按照医疗器械管理法规使用
- 确保操作人员具备相应资质
- 定期进行设备校准和检测
- 建立完整的使用记录档案

#### 数据保护法规
- 遵循患者数据保护相关法规
- 建立数据访问审计机制
- 确保数据存储和传输安全
- 定期进行安全风险评估

---

## 技术支持

### 联系方式
- **技术热线**: [技术支持电话]
- **邮箱支持**: [技术支持邮箱]
- **在线支持**: [在线支持平台]
- **现场服务**: 提供现场技术服务

### 服务内容
- **安装调试**: 系统安装和调试服务
- **培训服务**: 操作人员培训
- **维护保养**: 定期维护保养服务
- **故障维修**: 设备故障维修服务

### 服务时间
- **工作日**: 8:00-18:00
- **紧急支持**: 24小时紧急技术支持
- **响应时间**: 2小时内响应，24小时内解决

---

## 附录

### 系统配置文件
- **主配置**: config/settings.json
- **用户配置**: config/users.json
- **数据库**: ShuJu.db

### 日志文件
- **系统日志**: logs/system.log
- **治疗日志**: logs/treatment.log
- **错误日志**: logs/error.log

### 数据目录
- **患者数据**: data/patients/
- **治疗记录**: data/treatments/
- **报告文件**: data/reports/

---

**版本信息**: V2.0.0  
**编制日期**: 2025年1月  
**编制单位**: 海天智能技术团队  
**审核状态**: 已审核  

*本操作说明书基于系统实际代码编写，如有疑问请联系技术支持团队。*