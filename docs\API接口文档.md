# API接口文档


## 内部API接口

### 1. 电刺激设备接口 (StimulationDevice)

#### 1.1 设备连接
```python
def connect(self, port_num: int = 1) -> bool:
    """
    连接电刺激设备
    
    参数:
        port_num: 端口号
    
    返回:
        bool: 连接成功返回True，失败返回False
    """

def disconnect(self) -> bool:
    """
    断开电刺激设备连接
    
    返回:
        bool: 断开成功返回True，失败返回False
    """

def is_connected(self) -> bool:
    """
    检查设备连接状态
    
    返回:
        bool: 已连接返回True，未连接返回False
    """
```

#### 1.2 参数设置
```python
def set_parameters(self, channel: str, params: Dict[str, Any]) -> bool:
    """
    设置刺激参数
    
    参数:
        channel: 通道 ('A' 或 'B')
        params: 参数字典
        {
            'frequency': int,     # 频率 (2-160 Hz)
            'pulse_width': int,   # 脉宽 (10-500 μs)
            'current': int,       # 电流 (1-100 mA)
            'relax_time': int,    # 休息时间 (0-16 s)
            'climb_time': int,    # 上升时间 (0-5 s)
            'work_time': int,     # 工作时间 (0-30 s)
            'fall_time': int,     # 下降时间 (0-5 s)
            'wave_type': int      # 波形类型 (0:双相, 1:单相)
        }
    
    返回:
        bool: 设置成功返回True，失败返回False
    """
```

#### 1.3 刺激控制
```python
def start_stimulation(self, channels: List[str]) -> bool:
    """
    开始电刺激
    
    参数:
        channels: 要启动的通道列表 ['A'] 或 ['B'] 或 ['A', 'B']
    
    返回:
        bool: 启动成功返回True，失败返回False
    """

def stop_stimulation(self) -> bool:
    """
    停止电刺激
    
    返回:
        bool: 停止成功返回True，失败返回False
    """

def get_stimulation_status(self) -> int:
    """
    获取刺激状态
    
    返回:
        int: 状态码 (0:停止, 1:刺激中)
    """
```


## 外部API接口

### 1. HTTP API接口

#### 1.1 患者数据上传
```http
POST /shdekf/Api/uploadPatient
Content-Type: application/json

{
    "patientId": "string",           # 患者编号
    "name": "string",                # 姓名
    "age": 25,                       # 年龄
    "gender": "男",                  # 性别
    "idCard": "string",              # 身份证号
    "diagnosis": "string",           # 诊断
    "doctor": "string",              # 主治医师
    "hospitalID": 1,                 # 医院ID
    "department": "string",          # 科室
    "equipmentNum": "string",        # 设备编号
    "operator": "string",            # 操作员
    "createTime": "2024-12-19 10:30:00"  # 创建时间
}
```

**响应:**
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

#### 1.2 治疗数据上传
```http
POST /shdekf/Api/uploadTreatment
Content-Type: application/json

{
    "patientId": "string",           # 患者编号
    "treatNum": 1,                   # 治疗次数
    "treatDate": "2024-12-19",       # 治疗日期
    "treatTime": 10,                 # 治疗时长(分钟)
    "score": "49/61",                # 得分
    "triggerCount": 49,              # 触发次数
    "commentsOfTreatment": "良",     # 治疗评价
    "hospitalID": 1,                 # 医院ID
    "department": "string",          # 科室
    "equipmentNum": "string",        # 设备编号
    "operator": "string",            # 操作员
    "createTime": "2024-12-19 10:30:00"  # 创建时间
}
```

**响应:**
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

#### 1.3 设备状态上传
```http
POST /shdekf/Api/updateEquipment
Content-Type: application/json

{
    "hospitalID": 1,                 # 医院ID
    "equipmentNum": "string",        # 设备编号
    "status": 1                      # 状态 (1:在线, 0:离线)
}
```

**响应:**
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

### 2. UDP通信接口

#### 2.1 VR系统通信
```python
# 发送地址: 127.0.0.1:3004
# 本地绑定: 127.0.0.1:3005

# 治疗指令
commands = {
    'treat': '开始治疗准备',      # 初始化治疗
    'start': '开始电刺激',        # 启动电刺激
    'stop': '停止电刺激',         # 停止电刺激
    'stopall': '结束治疗'         # 结束治疗
}

# 发送格式
message = command_key  # 直接发送命令键
```

## 错误码定义

### HTTP API错误码
- **200**: 成功
- **400**: 请求参数错误
- **401**: 未授权
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误
- **-1**: 网络连接失败
- **-2**: 数据格式错误

### 设备接口错误码
- **0**: 成功
- **-1**: 设备未连接
- **-2**: 参数错误
- **-3**: 设备忙碌
- **-4**: 硬件故障
- **-5**: 超时错误

### 数据库错误码
- **0**: 成功
- **-1**: 连接失败
- **-2**: SQL语法错误
- **-3**: 数据约束违反
- **-4**: 事务回滚
- **-5**: 权限不足
