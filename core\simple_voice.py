"""
简洁语音引擎

极简设计的语音提示系统，避免复杂的线程管理和锁机制
"""

import logging
import threading
from typing import Optional

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    pyttsx3 = None

logger = logging.getLogger(__name__)

class SimpleVoiceEngine:
    """
    极简语音引擎

    设计原则：
    1. 只使用同步播放，完全避免线程问题
    2. 每次播放都重新初始化引擎，确保可靠性
    3. 容错性强，失败不影响主程序
    """

    def __init__(self, rate: int = 150, volume: float = 0.9):
        """
        初始化语音引擎

        Args:
            rate: 语音速度
            volume: 音量
        """
        self.rate = rate
        self.volume = volume
        self._enabled = True

        # 不预先初始化引擎，每次使用时初始化
    
    def _create_engine(self):
        """创建新的TTS引擎实例"""
        try:
            if not TTS_AVAILABLE:
                return None

            engine = pyttsx3.init()

            # 设置参数
            engine.setProperty('rate', self.rate)
            engine.setProperty('volume', self.volume)

            # 尝试设置中文语音
            try:
                voices = engine.getProperty('voices')
                for voice in voices:
                    if 'chinese' in voice.id.lower() or 'zh' in voice.id.lower():
                        engine.setProperty('voice', voice.id)
                        break
            except:
                pass  # 忽略语音设置错误

            return engine

        except Exception as e:
            logger.error(f"创建语音引擎失败: {e}")
            return None
    
    def speak(self, text: str):
        """
        播放语音（极简同步版本）

        Args:
            text: 要播放的文本
        """
        if not self._enabled:
            logger.debug(f"语音已禁用，跳过播放: {text}")
            return

        try:
            logger.debug(f"开始播放语音: {text}")

            # 每次都创建新的引擎实例，避免状态问题
            engine = self._create_engine()
            if engine is None:
                logger.warning(f"无法创建语音引擎，跳过播放: {text}")
                return

            # 直接播放，不使用锁
            engine.say(text)
            engine.runAndWait()

            # 立即清理引擎
            try:
                engine.stop()
            except:
                pass

            logger.debug(f"语音播放完成: {text}")

        except Exception as e:
            logger.warning(f"语音播放失败: {e}")
            # 失败不影响程序运行
    
    def speak_async(self, text: str):
        """
        异步播放语音（简化版本）

        Args:
            text: 要播放的文本
        """
        if not self._enabled:
            logger.debug(f"语音已禁用，跳过异步播放: {text}")
            return

        try:
            # 创建简单的守护线程
            thread = threading.Thread(
                target=self.speak,
                args=(text,),
                daemon=True
            )
            thread.start()
            logger.debug(f"异步语音线程已启动: {text}")
        except Exception as e:
            logger.error(f"异步语音播放失败: {e}")
    
    def stop(self):
        """
        停止语音播放（极简版本）
        """
        try:
            # 由于每次播放都创建新引擎，停止功能主要是设置禁用标志
            logger.debug("语音播放停止请求")
            # 可以在这里添加更复杂的停止逻辑，但目前保持简单
        except Exception as e:
            logger.error(f"停止语音播放异常: {e}")
    
    def is_available(self) -> bool:
        """检查语音引擎是否可用"""
        return self._enabled and TTS_AVAILABLE

    def enable(self):
        """启用语音"""
        self._enabled = True
        logger.info("语音已启用")

    def disable(self):
        """禁用语音"""
        self._enabled = False
        logger.info("语音已禁用")

# 全局语音引擎实例
_simple_voice_engine: Optional[SimpleVoiceEngine] = None

def get_simple_voice_engine() -> SimpleVoiceEngine:
    """获取全局简洁语音引擎实例"""
    global _simple_voice_engine
    
    if _simple_voice_engine is None:
        _simple_voice_engine = SimpleVoiceEngine()
    
    return _simple_voice_engine

# 训练提示语音模板
SIMPLE_TRAINING_PROMPTS = {
    'training_start': "开始训练",
    'motor_imagery': "想象运动",
    'rest': "保持平静",
    'trial_end': "休息",
    'training_complete': "训练完成",
    'training_stopped': "训练停止",
    'save_success': "保存成功",
    'save_failed': "保存失败"
}

# 治疗提示语音模板
TREATMENT_PROMPTS = {
    'treatment_start': "开始治疗，请想象运动",
    'encouragement': "请继续努力，保持专注",
    'success_feedback': "你做的很棒，请继续想象运动",
    'treatment_resume': "治疗恢复，请继续想象运动",
    'treatment_paused': "暂停治疗",
    'treatment_complete': "治疗完成",
    'treatment_stopped': "治疗停止"
}

def speak_simple(text: str, async_mode: bool = True):
    """
    简单语音播放函数
    
    Args:
        text: 要播放的文本
        async_mode: 是否异步播放
    """
    try:
        engine = get_simple_voice_engine()
        if async_mode:
            engine.speak_async(text)
        else:
            engine.speak(text)
    except Exception as e:
        logger.error(f"简单语音播放失败: {e}")

def speak_prompt(prompt_key: str, async_mode: bool = True):
    """
    播放训练提示语音

    Args:
        prompt_key: 提示键名
        async_mode: 是否异步播放
    """
    try:
        if prompt_key in SIMPLE_TRAINING_PROMPTS:
            text = SIMPLE_TRAINING_PROMPTS[prompt_key]
            speak_simple(text, async_mode)
        else:
            logger.warning(f"未知的训练提示键: {prompt_key}")
    except Exception as e:
        logger.error(f"播放训练提示失败: {e}")

def speak_treatment_prompt(prompt_key: str, async_mode: bool = True):
    """
    播放治疗提示语音

    Args:
        prompt_key: 治疗提示键名
        async_mode: 是否异步播放
    """
    try:
        if prompt_key in TREATMENT_PROMPTS:
            text = TREATMENT_PROMPTS[prompt_key]
            speak_simple(text, async_mode)
            logger.info(f"播放治疗语音提示: {text}")
        else:
            logger.warning(f"未知的治疗提示键: {prompt_key}")
    except Exception as e:
        logger.error(f"播放治疗提示失败: {e}")

def stop_voice():
    """停止语音播放"""
    try:
        engine = get_simple_voice_engine()
        engine.stop()
    except Exception as e:
        logger.error(f"停止语音失败: {e}")

def disable_voice():
    """禁用语音"""
    try:
        engine = get_simple_voice_engine()
        engine.disable()
    except Exception as e:
        logger.error(f"禁用语音失败: {e}")

def enable_voice():
    """启用语音"""
    try:
        engine = get_simple_voice_engine()
        engine.enable()
    except Exception as e:
        logger.error(f"启用语音失败: {e}")
