# -*- coding: utf-8 -*-
"""
API客户端
API Client

处理与远程服务器的HTTP通信
"""

import json
import requests
from typing import Dict, Any, Optional
import time
from core.network_config import API_ENDPOINTS, NETWORK_CONFIG

class ApiClient:
    """API客户端类"""
    
    def __init__(self):
        self.timeout = NETWORK_CONFIG['timeout']
        self.retry_times = NETWORK_CONFIG['retry_times']
        self.retry_delay = NETWORK_CONFIG['retry_delay']
    
    def _make_request(self, url: str, data: Dict[str, Any]) -> bool:
        """发送HTTP请求"""
        try:
            # 转换为JSON字符串
            json_data = json.dumps(data, ensure_ascii=False, separators=(',', ':'))

            # 构建完整URL（模拟QT的方式）
            full_url = url + json_data

            # 发送GET请求（根据原代码，使用GET方式传递数据）
            response = requests.get(full_url, timeout=self.timeout)

            # 检查响应状态
            if response.status_code == 200:
                # 尝试解析响应数据
                try:
                    response_data = response.json()
                    # 检查业务层面的成功状态（0表示成功）
                    if isinstance(response_data, dict):
                        server_status = response_data.get('status', '')
                        if str(server_status) == "0":
                            return True
                        else:
                            # 详细的错误状态码说明
                            status_messages = {
                                '-1': '网络连接失败',
                                '-2': '数据格式错误或参数验证失败',
                                '-3': '数据约束违反',
                                '-4': '事务回滚',
                                '-5': '权限不足',
                                '1': '一般业务错误',
                                '2': '数据已存在',
                                '3': '数据不存在'
                            }
                            error_msg = status_messages.get(str(server_status), f'未知错误码: {server_status}')
                            print(f"API业务错误，状态码: {server_status} ({error_msg})")

                            # 打印完整的服务器响应用于调试
                            print(f"📋 服务器完整响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                            return False
                    else:
                        # 如果无法解析JSON，认为成功（兼容性考虑）
                        return True
                except:
                    # 如果无法解析JSON，认为成功（兼容性考虑）
                    return True
            else:
                print(f"API请求失败，HTTP状态码: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            print("API请求超时")
            return False
        except requests.exceptions.ConnectionError:
            print("API连接错误")
            return False
        except Exception as e:
            print(f"API请求异常: {e}")
            return False
    
    def _retry_request(self, url: str, data: Dict[str, Any]) -> bool:
        """带重试的请求"""
        for attempt in range(self.retry_times):
            if self._make_request(url, data):
                return True
            
            if attempt < self.retry_times - 1:
                print(f"请求失败，{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        
        print(f"请求失败，已重试{self.retry_times}次")
        return False
    
    def _convert_patient_data_for_add(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换患者数据格式（新增患者）"""
        # 使用ReferenceDataService获取医院信息
        from services.reference_data_service import reference_data_service

        hospital_id = reference_data_service.get_hospital_id()
        department = reference_data_service.get_department()
        device_id = reference_data_service.get_device_id()

        # 按照QT代码的sendPatients格式，包含完整字段
        api_data = {
            "num": str(patient_data.get('bianhao', '')),
            "name": patient_data.get('name', ''),
            "sex": patient_data.get('xingbie', ''),
            "age": int(patient_data.get('age', 0)),
            "hospitalID": hospital_id,
            "idCard": patient_data.get('cardid', '') or '',
            "department": patient_data.get('keshi', '') or department,  # 科室
            "equipmentNum": device_id,
            "attdoctor": patient_data.get('zhuzhi', '') or '',  # 主治医师
            "operator": patient_data.get('czy', '') or '',
            "jiwangshi": patient_data.get('bingshi', '') or '',
            "zhenduan": patient_data.get('zhenduan', '') or ''
        }

        return api_data

    def _convert_patient_data_for_update(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换患者数据格式（更新患者）"""
        # 使用ReferenceDataService获取医院信息
        from services.reference_data_service import reference_data_service

        hospital_id = reference_data_service.get_hospital_id()

        # 按照QT代码的updatePatients格式，只包含必要字段
        api_data = {
            "num": str(patient_data.get('bianhao', '')),
            "name": patient_data.get('name', ''),
            "sex": patient_data.get('xingbie', ''),
            "age": int(patient_data.get('age', 0)),
            "hospitalID": hospital_id,
            "idCard": patient_data.get('cardid', '') or '',
            "jiwangshi": patient_data.get('bingshi', '') or '',
            "zhenduan": patient_data.get('zhenduan', '') or ''
        }

        return api_data
    
    def upload_patient(self, patient_data: Dict[str, Any], is_update: bool = False) -> bool:
        """上传患者数据（不重试，避免阻塞界面）"""
        try:
            # 根据操作类型选择不同的数据转换方法
            if is_update:
                api_data = self._convert_patient_data_for_update(patient_data)
            else:
                api_data = self._convert_patient_data_for_add(patient_data)

            # 打印JSON数据内容用于调试
            # print(f"📋 患者数据{'更新' if is_update else '上传'}JSON内容:")
            # print(json.dumps(api_data, ensure_ascii=False, indent=2))

            # 选择API端点
            endpoint = API_ENDPOINTS['update_patient'] if is_update else API_ENDPOINTS['add_patient']

            # 发送请求（不重试，直接返回结果）
            success = self._make_request(endpoint, api_data)

            if success:
                print(f"患者数据{'更新' if is_update else '上传'}成功: {patient_data.get('name', 'Unknown')}")
            # 失败时不打印错误信息，由调用方处理

            return success

        except Exception as e:
            print(f"上传患者数据异常: {e}")
            return False
    
    def upload_treatment(self, treatment_data: Dict[str, Any]) -> bool:
        """上传治疗数据（不重试，避免阻塞界面）"""
        try:
            # 转换治疗数据格式
            api_data = self._convert_treatment_data(treatment_data)

            # 发送请求
            success = self._make_request(API_ENDPOINTS['add_treatment'], api_data)

            if success:
                print(f"治疗数据上传成功")
            # 失败时不打印错误信息，由调用方处理

            return success

        except Exception as e:
            print(f"上传治疗数据异常: {e}")
            return False

    def _convert_treatment_data(self, treatment_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换治疗数据格式（按照原QT项目的字段顺序和格式）"""
        # 按照原QT项目的字段顺序和格式组织数据
        api_data = {
            "actualTimes": treatment_data.get("actualTimes", 0),
            "commentsOfTreatment": treatment_data.get("commentsOfTreatment", ""),
            "timesOfImagination": treatment_data.get("timesOfImagination", 0),
            "treatScore": treatment_data.get("treatScore", 0),
            "treatTime": treatment_data.get("treatTime", 0),  # 修正：应该是数字0，不是空字符串
            "usageTime": treatment_data.get("usageTime", ""),
            "patientNum": treatment_data.get("patientNum", ""),
            "treatNum": treatment_data.get("treatNum", ""),
            "hospitalID": treatment_data.get("hospitalID", 3),  # 
            "department": treatment_data.get("department", ""),
            "equipmentNum": treatment_data.get("equipmentNum", ""),
            "attdoctor": treatment_data.get("attdoctor", ""),
            "operator": treatment_data.get("operator", ""),
            "idCard": treatment_data.get("idCard", "")
        }

        return api_data

    def upload_equipment_status(self, status: str = "1") -> bool:
        """上传设备状态（不重试，避免阻塞界面）"""
        try:
            # 使用ReferenceDataService获取医院信息
            from services.reference_data_service import reference_data_service

            hospital_id = reference_data_service.get_hospital_id()
            device_id = reference_data_service.get_device_id()

            data = {
                "hospitalID": hospital_id,
                "equipmentNum": device_id,
                "status": status
            }

            success = self._make_request(API_ENDPOINTS['update_equipment'], data)

            if success:
                print(f"设备状态上传成功: {status}")
            else:
                print(f"设备状态上传失败: {status}")

            return success

        except Exception as e:
            print(f"上传设备状态异常: {e}")
            return False

# 全局API客户端实例
api_client = ApiClient()
