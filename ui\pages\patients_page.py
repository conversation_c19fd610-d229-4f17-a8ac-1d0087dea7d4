# -*- coding: utf-8 -*-
"""
患者管理页面
Patients Management Page

完全按照HTML设计实现的患者管理页面
包含患者列表、详情展示、新增患者等功能
"""

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel,
    QPushButton, QFrame, QScrollArea, QLineEdit,
    QGridLayout, QStackedWidget, QTabWidget, QListWidget,
    QListWidgetItem, QSplitter, QComboBox, QMessageBox,
    QDialog, QFormLayout, QTextEdit, QDialogButtonBox,
    QDateEdit
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, QDate
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush


from ui.pages.base_page import BasePage
from services.patient_service import patient_service
from services.reference_data_service import reference_data_service
from ui.components.themed_message_box import show_information, show_warning, show_critical, show_question
from datetime import datetime
import os


class PatientCard(QFrame):
    """患者卡片组件 - 完全按照HTML设计"""

    patient_selected = Signal(dict)
    clicked = Signal()
    patient_double_clicked = Signal(dict)  # 新增双击信号

    def __init__(self, patient_data: dict):
        super().__init__()
        self.patient_data = patient_data
        self.is_selected = False

        self.setObjectName("patient_card")
        self.setFixedHeight(100)  # 增加高度避免文字遮挡
        self.setCursor(Qt.CursorShape.PointingHandCursor)

        self._init_ui()
        self._setup_style()

    def _init_ui(self):
        """初始化UI - 完全按照HTML设计"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)  # 匹配HTML的padding: 20px
        layout.setSpacing(12)  # 匹配HTML的gap: 16px

        # 左侧头像区域 - 56x56像素，圆角14px
        self.avatar = QLabel()
        self.avatar.setObjectName("patient_avatar")
        self.avatar.setFixedSize(48, 48)  # 匹配HTML设计
        self.avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 调整头像字体大小以匹配HTML视觉效果
        avatar_font = QFont("Microsoft YaHei", 15, QFont.Weight.Bold)
        avatar_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.avatar.setFont(avatar_font)
        self.avatar.setText(self.patient_data.get('name', 'P')[0])
        layout.addWidget(self.avatar)

        # 中间信息区域 - flex: 1
        info_area = QWidget()
        info_layout = QVBoxLayout(info_area)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(6)  # 增加间距避免文字重叠

        # 患者姓名 - 调整字体大小以匹配HTML视觉效果
        self.name_label = QLabel(self.patient_data.get('name', '未知'))
        name_font = QFont("Microsoft YaHei", 13, QFont.Weight.DemiBold)
        name_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.name_label.setFont(name_font)
        self.name_label.setObjectName("patient_name")
        # 样式将在_apply_theme_styles中设置
        info_layout.addWidget(self.name_label)

        # 患者信息行 - 调整字体大小以匹配HTML视觉效果
        patient_id = self.patient_data.get('bianhao', 'N/A')
        gender = self.patient_data.get('xingbie', '未知')
        age = self.patient_data.get('age', '未知')
        info_text = f"ID: {patient_id} | {gender} | {age}岁"
        self.info_label = QLabel(info_text)
        info_font = QFont("Microsoft YaHei", 11)
        info_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.info_label.setFont(info_font)
        self.info_label.setObjectName("patient_info")
        # 样式将在_apply_theme_styles中设置
        info_layout.addWidget(self.info_label)

        # 移除状态标签区域，简化布局
        layout.addWidget(info_area, 1)  # flex: 1

        # 右侧区域 - 包含悬停按钮和时间信息
        right_area = QWidget()
        right_layout = QHBoxLayout(right_area)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(12)
        right_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignRight)

        # 悬停按钮区域 - 放在时间信息左侧
        self.hover_actions = QFrame()
        self.hover_actions.setObjectName("patient_hover_actions")
        hover_layout = QHBoxLayout(self.hover_actions)
        hover_layout.setContentsMargins(0, 0, 0, 0)
        hover_layout.setSpacing(8)

        # 编辑按钮 - 使用全局样式
        self.edit_btn = QPushButton("编辑")
        self.edit_btn.setObjectName("btn_secondary")  # 使用全局样式
        self.edit_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        # 不设置字体，让全局样式控制
        hover_layout.addWidget(self.edit_btn)

        # 出院按钮 - 使用全局样式（替代删除按钮）
        self.discharge_btn = QPushButton("出院")
        self.discharge_btn.setObjectName("btn_warning")  # 使用全局危险按钮样式
        self.discharge_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        # 不设置字体，让全局样式控制
        hover_layout.addWidget(self.discharge_btn)

        # 初始隐藏悬停按钮
        self.hover_actions.hide()
        right_layout.addWidget(self.hover_actions)

        # 时间信息区域
        time_area = QWidget()
        time_layout = QVBoxLayout(time_area)
        time_layout.setContentsMargins(0, 0, 0, 0)
        time_layout.setSpacing(0)
        time_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignRight)

        # 时间标题 - 11px字体，颜色与时间值一致
        self.time_title = QLabel("最后访问")
        self.time_title.setFont(QFont("Microsoft YaHei", 11))
        self.time_title.setObjectName("patient_time_value")  # 使用与时间值相同的样式
        self.time_title.setAlignment(Qt.AlignmentFlag.AlignRight)
        time_layout.addWidget(self.time_title)

        # 时间值 - 11px字体
        last_treatment = self.patient_data.get('lrshijian', '2025-06-02')
        if isinstance(last_treatment, str) and len(last_treatment) > 10:
            last_treatment = last_treatment[:10]  # 只显示日期部分

        self.last_treatment = QLabel(last_treatment)
        self.last_treatment.setFont(QFont("Microsoft YaHei", 11))
        self.last_treatment.setObjectName("patient_time_value")
        self.last_treatment.setAlignment(Qt.AlignmentFlag.AlignRight)
        time_layout.addWidget(self.last_treatment)

        right_layout.addWidget(time_area)
        layout.addWidget(right_area)




    def _setup_style(self):
        """设置样式"""
        self.setProperty("selected", False)

        # 应用主题感知的样式
        self._apply_theme_styles()

        # 设置状态标签的颜色
        self._update_status_colors()

    def _apply_theme_styles(self):
        """应用主题感知的样式 - 现在完全依赖全局样式表"""
        # 不再设置任何本地样式，完全依赖全局样式表
        pass

    def _update_status_colors(self):
        """更新状态标签颜色 - 已移除状态标签"""
        pass



    def update_theme(self):
        """更新主题样式 - 让全局样式表完全控制"""


        # 清除所有本地样式设置，让全局样式表生效
        self.setStyleSheet("")  # 清空本地样式

        # 清除头像样式，让全局样式表控制
        if hasattr(self, 'avatar'):
            self.avatar.setStyleSheet("")  # 清除头像本地样式
            # 强制刷新头像样式
            self.avatar.style().unpolish(self.avatar)
            self.avatar.style().polish(self.avatar)
            self.avatar.update()
        if hasattr(self, 'name_label'):
            self.name_label.setStyleSheet("")
        if hasattr(self, 'info_label'):
            self.info_label.setStyleSheet("")
        if hasattr(self, 'time_title'):
            self.time_title.setStyleSheet("")
        if hasattr(self, 'last_treatment'):
            self.last_treatment.setStyleSheet("")

        # 重置palette为默认
        self.setPalette(self.style().standardPalette())
        self.setAutoFillBackground(False)

        # 强制刷新样式
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()



        self._update_status_colors()

    def enterEvent(self, event):
        """鼠标进入事件 - 显示悬停按钮"""
        super().enterEvent(event)
        if hasattr(self, 'hover_actions'):
            self.hover_actions.show()

    def leaveEvent(self, event):
        """鼠标离开事件 - 隐藏悬停按钮"""
        super().leaveEvent(event)
        if hasattr(self, 'hover_actions'):
            self.hover_actions.hide()

    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.is_selected = selected
        self.setProperty("selected", selected)
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        super().mousePressEvent(event)
        # 发送选中信号
        self.clicked.emit()

    def paintEvent(self, event):
        """重写绘制事件，强制绘制背景色"""
        from PySide6.QtGui import QPainter, QColor, QBrush
        from PySide6.QtCore import QRect

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景
        if hasattr(self, 'theme_bg_color'):
            bg_color = QColor(self.theme_bg_color)
            painter.setBrush(QBrush(bg_color))
            painter.setPen(QColor("transparent"))

            # 绘制圆角矩形背景
            rect = QRect(0, 0, self.width(), self.height())
            painter.drawRoundedRect(rect, 16, 16)

        painter.end()

        # 调用父类的paintEvent来绘制子组件
        super().paintEvent(event)

    def enterEvent(self, event):
        """鼠标进入事件 - 显示悬停按钮"""
        self.hover_actions.show()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件 - 隐藏悬停按钮"""
        self.hover_actions.hide()
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.set_selected(True)
            self.patient_selected.emit(self.patient_data)
        super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件 - 跳转到治疗系统"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.patient_double_clicked.emit(self.patient_data)
        super().mouseDoubleClickEvent(event)




class PatientDetailPanel(QFrame):
    """患者详情面板 - 按照HTML设计"""

    def __init__(self):
        super().__init__()
        self.setObjectName("patient_detail_panel")
        self.current_patient = None

        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)  # 主布局不设边距，让头部能覆盖整个区域
        layout.setSpacing(0)

        # 患者头部信息
        self.header = self._create_header()
        layout.addWidget(self.header)

        # 标签页区域 - 按照HTML设计重新实现
        self.tabs_area = self._create_tabs_area()
        layout.addWidget(self.tabs_area, 1)

    def _create_header(self) -> QWidget:
        """创建头部区域"""
        header = QFrame()
        header.setObjectName("patient_header")
        header.setFixedHeight(0)

        layout = QVBoxLayout(header)
        layout.setContentsMargins(0, 0, 0, 0)  # 配合主布局的20px边距
        layout.setSpacing(0)

        # 患者头像和基本信息
        top_row = QHBoxLayout()
        top_row.setSpacing(0)

        # 头像
        self.detail_avatar = QLabel("P")
        self.detail_avatar.setObjectName("detail_avatar")
        self.detail_avatar.setFixedSize(80, 80)
        self.detail_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.detail_avatar.setFont(QFont("Microsoft YaHei", 28, QFont.Weight.Bold))  # 从32减少到28（减少2个字号）
        top_row.addWidget(self.detail_avatar)

        # 基本信息
        info_area = QWidget()
        info_layout = QVBoxLayout(info_area)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(8)

        self.detail_name = QLabel("请选择患者")
        self.detail_name.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))  # 从24减少到20（减少2个字号）
        info_layout.addWidget(self.detail_name)

        self.detail_info = QLabel("患者详细信息将在这里显示")
        self.detail_info.setFont(QFont("Microsoft YaHei", 14))
        info_layout.addWidget(self.detail_info)

        self.detail_treatments = QLabel("请选择患者")
        self.detail_treatments.setFont(QFont("Microsoft YaHei", 13))
        info_layout.addWidget(self.detail_treatments)

        top_row.addWidget(info_area, 1)
        layout.addLayout(top_row)

        return header

    def _create_tabs_area(self) -> QWidget:
        """创建标签页区域 - 按照HTML设计"""
        tabs_container = QWidget()
        layout = QVBoxLayout(tabs_container)
        layout.setContentsMargins(16, 6, 16, 6)  # 设置左右边距20px，避免内容贴边
        layout.setSpacing(8)

        # 标签页按钮区域
        self.tab_buttons_area = self._create_tab_buttons()
        layout.addWidget(self.tab_buttons_area)

        # 标签页内容区域
        self.tab_content_area = QStackedWidget()
        self.tab_content_area.setObjectName("tab_content_stack")

        # 创建各个标签页内容
        self.basic_info_tab = self._create_basic_info_tab()
        self.treatment_tab = self._create_treatment_tab()
        self.history_tab = self._create_history_tab()

        # 添加到堆叠窗口
        self.tab_content_area.addWidget(self.basic_info_tab)
        self.tab_content_area.addWidget(self.treatment_tab)
        self.tab_content_area.addWidget(self.history_tab)

        layout.addWidget(self.tab_content_area, 1)

        # 默认显示基本信息
        self.current_tab = 0
        self.tab_content_area.setCurrentIndex(0)

        return tabs_container

    def _create_tab_buttons(self) -> QWidget:
        """创建标签页按钮 - 按照HTML设计"""
        buttons_container = QWidget()
        layout = QVBoxLayout(buttons_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 按钮行
        buttons_row = QWidget()
        buttons_layout = QHBoxLayout(buttons_row)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(0)

        # 创建标签页按钮
        self.tab_buttons = []
        tab_names = ["基本信息", "治疗记录", "历史数据"]

        for i, name in enumerate(tab_names):
            btn = QPushButton(name)
            btn.setObjectName("patient_tab_btn")
            btn.setProperty("tab_index", i)
            # 设置激活状态属性
            if i == 0:
                btn.setProperty("active", "true")
            else:
                btn.setProperty("active", "false")
            # 调整标签页按钮字体大小
            tab_font = QFont("Microsoft YaHei", 11)
            tab_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
            btn.setFont(tab_font)
            btn.clicked.connect(lambda checked=False, index=i: self._switch_tab(index))
            self.tab_buttons.append(btn)
            buttons_layout.addWidget(btn)

        # 添加弹性空间
        buttons_layout.addStretch()

        layout.addWidget(buttons_row)

        # 底部边框线
        border_line = QFrame()
        border_line.setObjectName("tab_border_line")
        border_line.setFixedHeight(1)
        layout.addWidget(border_line)

        return buttons_container

    def _switch_tab(self, index: int):
        """切换标签页"""
        if index == self.current_tab:
            return

        # 更新按钮状态
        for i, btn in enumerate(self.tab_buttons):
            if i == index:
                btn.setProperty("active", "true")
            else:
                btn.setProperty("active", "false")
            btn.style().unpolish(btn)
            btn.style().polish(btn)
            btn.update()

        # 切换内容
        self.tab_content_area.setCurrentIndex(index)
        self.current_tab = index

    def _create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页 - 按照HTML设计，添加滚动支持"""
        tab = QWidget()
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)

        # 创建滚动内容容器
        scroll_content = QWidget()
        layout = QVBoxLayout(scroll_content)
        layout.setContentsMargins(10, 0, 10, 0)
        layout.setSpacing(10)  # 恢复原来的间距

        # 创建网格布局显示基本信息
        grid_layout = QGridLayout()
        grid_layout.setSpacing(16)  # 恢复原来的间距
        # 设置列宽比例，确保两列平均分配空间
        grid_layout.setColumnStretch(0, 1)  # 左列
        grid_layout.setColumnStretch(1, 1)  # 右列

        # 基本信息字段（网格布局）
        basic_fields = [
            ('姓名', 'detailName'),
            ('年龄', 'detailAge'),
            ('性别', 'detailGender'),
            ('身份证号', 'detailIdCard'),
            ('主治医生', 'detailDoctor'),
            ('联系电话', 'detailPhone'),
            ('地址', 'detailAddress'),
            ('设备编号', 'detailDeviceId')
        ]

        self.detail_fields = {}

        for i, (label_text, field_id) in enumerate(basic_fields):
            row = i // 2
            col = i % 2

            # 创建字段容器
            field_widget = QWidget()
            field_layout = QVBoxLayout(field_widget)
            field_layout.setContentsMargins(0, 0, 0, 0)
            field_layout.setSpacing(4)  # 恢复原来的间距

            # 标签
            label = QLabel(label_text)
            label.setFont(QFont("Microsoft YaHei", 11))
            label.setObjectName("info_label")  # 使用更淡的颜色
            field_layout.addWidget(label)

            # 值
            value_label = QLabel("--")
            value_label.setFont(QFont("Microsoft YaHei", 13, QFont.Weight.DemiBold))
            value_label.setObjectName("info_value")  # 使用主要文字颜色
            field_layout.addWidget(value_label)

            self.detail_fields[field_id] = value_label
            grid_layout.addWidget(field_widget, row, col)

        layout.addLayout(grid_layout)

        # 诊断信息（单独一行，较大空间）
        diagnosis_widget = QWidget()
        diagnosis_layout = QVBoxLayout(diagnosis_widget)
        diagnosis_layout.setContentsMargins(0, 0, 0, 0)
        diagnosis_layout.setSpacing(4)  # 恢复原来的间距

        diagnosis_label = QLabel("诊断信息")
        diagnosis_label.setFont(QFont("Microsoft YaHei", 11))
        diagnosis_label.setObjectName("info_label")  # 使用更淡的颜色
        diagnosis_layout.addWidget(diagnosis_label)

        # 使用QTextEdit替代QLabel，但设置为只读并调整样式
        self.detail_diagnosis = QTextEdit("--")
        self.detail_diagnosis.setFont(QFont("Microsoft YaHei", 13))
        self.detail_diagnosis.setMinimumHeight(60)  # 设置最小高度
        self.detail_diagnosis.setMaximumHeight(120)  # 设置最大高度，避免过度拉伸
        self.detail_diagnosis.setReadOnly(True)  # 设置为只读
        self.detail_diagnosis.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.detail_diagnosis.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.detail_diagnosis.setFrameStyle(QFrame.Shape.NoFrame)  # 移除边框
        self.detail_diagnosis.setObjectName("info_value_text")  # 使用特殊的对象名
        diagnosis_layout.addWidget(self.detail_diagnosis)

        layout.addWidget(diagnosis_widget)

        # 病史（单独一行，较大空间）
        history_widget = QWidget()
        history_layout = QVBoxLayout(history_widget)
        history_layout.setContentsMargins(0, 0, 0, 0)
        history_layout.setSpacing(4)  # 恢复原来的间距

        history_label = QLabel("病史")
        history_label.setFont(QFont("Microsoft YaHei", 11))
        history_label.setObjectName("info_label")  # 使用更淡的颜色
        history_layout.addWidget(history_label)

        # 使用QTextEdit替代QLabel，但设置为只读并调整样式
        self.detail_history = QTextEdit("--")
        self.detail_history.setFont(QFont("Microsoft YaHei", 13))
        self.detail_history.setMinimumHeight(60)  # 设置最小高度
        self.detail_history.setMaximumHeight(120)  # 设置最大高度，避免过度拉伸
        self.detail_history.setReadOnly(True)  # 设置为只读
        self.detail_history.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.detail_history.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.detail_history.setFrameStyle(QFrame.Shape.NoFrame)  # 移除边框
        self.detail_history.setObjectName("info_value_text")  # 使用特殊的对象名
        history_layout.addWidget(self.detail_history)

        layout.addWidget(history_widget)
        layout.addStretch()

        # 将滚动内容设置到滚动区域
        scroll_area.setWidget(scroll_content)
        tab_layout.addWidget(scroll_area)

        return tab

    def _create_treatment_tab(self) -> QWidget:
        """创建治疗记录标签页 - 按照HTML设计"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(4, 0, 4, 0)
        layout.setSpacing(4)

        # 筛选和操作区域
        filter_area = QWidget()
        filter_layout = QHBoxLayout(filter_area)
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(8)

        # # 时间筛选
        # self.time_filter = QComboBox()
        # self.time_filter.addItems(["全部记录", "最近7天", "最近30天", "最近3个月"])
        # self.time_filter.setFont(QFont("Microsoft YaHei", 12))
        # self.time_filter.currentTextChanged.connect(self._on_time_filter_changed)
        # filter_layout.addWidget(self.time_filter)

        # filter_layout.addStretch()

        # layout.addWidget(filter_area)

        # 治疗记录滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)
        scroll_area.setMaximumHeight(700)

        # 治疗记录容器
        records_container = QWidget()
        self.records_layout = QVBoxLayout(records_container)
        self.records_layout.setContentsMargins(0, 0, 0, 0)
        self.records_layout.setSpacing(8)

        # 治疗记录将在患者选择时动态加载
        self.treatment_records_container = records_container

        scroll_area.setWidget(records_container)
        layout.addWidget(scroll_area, 1)



        return tab

    def _load_treatment_records(self, patient_id: str):
        """加载患者的真实治疗记录"""
        # 清空现有记录
        for i in reversed(range(self.records_layout.count())):
            child = self.records_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        try:
            from services.treatment_service import treatment_service
            # 获取当前时间筛选条件
            time_filter = self.time_filter.currentText() if hasattr(self, 'time_filter') else "全部记录"
            treatments = treatment_service.get_treatments_by_patient(patient_id, time_filter)

            if not treatments:
                # 显示无记录提示
                no_records_label = QLabel("暂无治疗记录")
                no_records_label.setFont(QFont("Microsoft YaHei", 14))
                no_records_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                no_records_label.setStyleSheet("color: #666666; padding: 40px;")
                self.records_layout.addWidget(no_records_label)
                return

            # 显示治疗记录（序号倒序，最新的记录序号最大）
            total_count = len(treatments)
            for i, treatment in enumerate(treatments):
                record_index = total_count - i  # 倒序序号
                record_widget = self._create_treatment_record_widget(treatment, record_index)
                self.records_layout.addWidget(record_widget)

        except Exception as e:
            print(f"加载治疗记录失败: {e}")
            error_label = QLabel("加载治疗记录失败")
            error_label.setFont(QFont("Microsoft YaHei", 14))
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: #ff6b6b; padding: 40px;")
            self.records_layout.addWidget(error_label)

    def _create_treatment_record_widget(self, treatment: dict, index: int) -> QFrame:
        """创建治疗记录组件"""
        record_widget = QFrame()
        record_widget.setObjectName("treatment_record")
        record_widget.setMaximumHeight(150)  # 设置最大高度限制

        layout = QVBoxLayout(record_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # 记录头部
        header_layout = QHBoxLayout()
        header_layout.setSpacing(12)

        # 状态点
        status_dot = QLabel()
        status_dot.setFixedSize(12, 12)
        status_dot.setObjectName("record_status_dot")
        header_layout.addWidget(status_dot)

        # 记录信息
        record_info = QWidget()
        info_layout = QVBoxLayout(record_info)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(4)

        # 标题行 - 包含序号和频率脉宽信息
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(8)

        title_label = QLabel(f"脑机接口训练 #{index}")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_layout.addWidget(title_label)

        # 添加频率脉宽信息（如果存在）
        stimulation_params = treatment.get('stimulation_params', '')
        if stimulation_params:
            _, freq_pulse_info = self._format_stimulation_params(stimulation_params)
            if freq_pulse_info:
                freq_pulse_label = QLabel(freq_pulse_info)
                freq_pulse_label.setFont(QFont("Microsoft YaHei", 11))
                freq_pulse_label.setObjectName("record_time")  # 使用与时间相同的样式
                title_layout.addWidget(freq_pulse_label)

        title_layout.addStretch()  # 推到左边
        info_layout.addLayout(title_layout)

        # 格式化治疗时间
        treatment_date = treatment.get('date', '')
        duration = treatment.get('duration', 0)
        if treatment_date and duration:
            time_label = QLabel(f"{treatment_date} | 时长: {duration}分钟")
        else:
            time_label = QLabel(treatment_date or "未知时间")
        time_label.setFont(QFont("Microsoft YaHei", 11))
        time_label.setObjectName("record_time")
        info_layout.addWidget(time_label)

        header_layout.addWidget(record_info, 1)

        layout.addLayout(header_layout)

        # 数据统计
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(12)

        # 计算识别准确率
        required_count = treatment.get('required_count', 0)
        actual_count = treatment.get('actual_count', 0)
        accuracy = 0
        if required_count > 0:
            accuracy = int((actual_count / required_count) * 100)

        # 基础统计数据
        stats_data = [
            (f"{duration}分钟", "治疗时长"),
            (f"{accuracy}%", "识别准确率"),
            (treatment.get('effect', '--'), "效果")
        ]

        # 添加通道信息（如果存在）
        stimulation_params = treatment.get('stimulation_params', '')
        if stimulation_params:
            channel_info, _ = self._format_stimulation_params(stimulation_params)
            if channel_info:
                stats_data.append((channel_info, "刺激通道"))

        for value, label in stats_data:
            stat_widget = QWidget()
            stat_layout = QVBoxLayout(stat_widget)
            stat_layout.setContentsMargins(0, 0, 0, 0)
            stat_layout.setSpacing(2)
            stat_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

            value_label = QLabel(str(value))
            value_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
            value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            value_label.setObjectName("stat_value")
            stat_layout.addWidget(value_label)

            desc_label = QLabel(label)
            desc_label.setFont(QFont("Microsoft YaHei", 10))
            desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            desc_label.setObjectName("stat_desc")
            stat_layout.addWidget(desc_label)

            stats_layout.addWidget(stat_widget)

        layout.addLayout(stats_layout)

        return record_widget

    def _format_stimulation_params(self, params_json: str) -> tuple:
        """格式化电刺激参数，返回(通道信息, 频率脉宽信息)元组"""
        try:
            import json
            params = json.loads(params_json)

            # 提取通道信息
            channels = []
            if 'channel_a' in params:
                current_a = params['channel_a'].get('current', 0)
                channels.append(f"A:{current_a}mA")
            if 'channel_b' in params:
                current_b = params['channel_b'].get('current', 0)
                channels.append(f"B:{current_b}mA")

            # 提取频率和脉宽
            freq_pulse_parts = []
            if 'frequency' in params:
                freq_value = params['frequency'].get('value', 0)
                freq_pulse_parts.append(f"{freq_value}Hz")
            if 'pulse_width' in params:
                pw_value = params['pulse_width'].get('value', 0)
                freq_pulse_parts.append(f"{pw_value}μs")

            channel_info = " ".join(channels) if channels else ""
            freq_pulse_info = " ".join(freq_pulse_parts) if freq_pulse_parts else ""

            return (channel_info, freq_pulse_info)

        except Exception as e:
            print(f"格式化电刺激参数失败: {e}")

        return ("", "")

    def refresh_current_patient_details(self):
        """刷新当前患者的详情信息"""
        try:
            if hasattr(self, 'current_patient') and self.current_patient:
                patient_id = self.current_patient.get('bianhao', '')
                if patient_id:
                    # 重新加载患者详情
                    self._show_patient_details(self.current_patient)
                    print(f"✅ 患者详情已刷新 - ID: {patient_id}")
        except Exception as e:
            print(f"❌ 刷新患者详情失败: {e}")

    def _on_time_filter_changed(self, filter_text: str):
        """时间筛选改变时的处理"""
        if hasattr(self, 'current_patient') and self.current_patient:
            patient_id = self.current_patient.get('bianhao', '')
            if patient_id:
                self._load_treatment_records(patient_id)

    def _create_history_tab(self) -> QWidget:
        """创建历史数据标签页 - 按照HTML设计"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 0, 10, 0)
        layout.setSpacing(10)

        # 统计信息卡片
        stats_card = QFrame()
        stats_card.setObjectName("history_stats_card")

        stats_layout = QVBoxLayout(stats_card)
        stats_layout.setContentsMargins(16, 16, 16, 16)
        stats_layout.setSpacing(12)

        # 统计项目
        stats_items = [
            ("总治疗次数", "totalTreatments", "次"),
            ("总治疗时长", "totalTime", "分钟"),
            ("平均识别率", "avgAccuracy", "%"),
            ("最后治疗", "lastTreatment", "")
        ]

        self.history_fields = {}

        for label_text, field_id, unit in stats_items:
            item_layout = QHBoxLayout()
            item_layout.setSpacing(8)

            # 标签
            label = QLabel(label_text)
            label.setFont(QFont("Microsoft YaHei", 12))
            label.setObjectName("history_label")
            item_layout.addWidget(label)

            item_layout.addStretch()

            # 值
            value_label = QLabel("--")
            value_label.setFont(QFont("Microsoft YaHei", 13, QFont.Weight.Bold))
            value_label.setObjectName("history_value")
            self.history_fields[field_id] = (value_label, unit)
            item_layout.addWidget(value_label)

            stats_layout.addLayout(item_layout)

        layout.addWidget(stats_card)
        layout.addStretch()

        return tab

    def update_patient_info(self, patient_data: dict):
        """更新患者信息"""
        self.current_patient = patient_data

        # 更新头部信息
        name = patient_data.get('name', '未知患者')
        self.detail_avatar.setText(patient_data.get('avatar', name[0] if name else 'P'))
        self.detail_name.setText(name)

        info_text = f"ID: {patient_data.get('bianhao', 'N/A')} | 年龄: {patient_data.get('age', 'N/A')}岁 | 性别: {patient_data.get('xingbie', 'N/A')}"
        self.detail_info.setText(info_text)

        # 更新治疗次数标签，包含入院次数信息
        treatments = patient_data.get('treatment_count', 0)

        # 获取入院次数信息
        try:
            from services.patient_service import patient_service
            patient_id = patient_data.get('bianhao', '')
            if patient_id:
                current_hospitalization_number = patient_service.get_current_hospitalization_number(patient_id)
                if current_hospitalization_number > 0:
                    self.detail_treatments.setText(f"第{current_hospitalization_number}次入院 | {treatments}次治疗")
                else:
                    self.detail_treatments.setText(f"{treatments}次治疗")
            else:
                self.detail_treatments.setText(f"{treatments}次治疗")
        except Exception as e:
            print(f"获取入院次数失败: {e}")
            self.detail_treatments.setText(f"{treatments}次治疗")

        # 加载治疗记录
        patient_id = patient_data.get('bianhao', '')
        if patient_id and hasattr(self, 'treatment_records_container'):
            self._load_treatment_records(patient_id)

        # 更新基本信息标签页
        if hasattr(self, 'detail_fields'):
            self.detail_fields['detailName'].setText(name)
            self.detail_fields['detailAge'].setText(f"{patient_data.get('age', '--')}岁")
            self.detail_fields['detailGender'].setText(patient_data.get('xingbie', '--'))
            self.detail_fields['detailIdCard'].setText(patient_data.get('cardid', '未填写'))
            self.detail_fields['detailDoctor'].setText(patient_data.get('zhuzhi', '--'))
            self.detail_fields['detailPhone'].setText(patient_data.get('phone', '--'))
            self.detail_fields['detailAddress'].setText(patient_data.get('address', '--'))
            self.detail_fields['detailDeviceId'].setText(patient_data.get('hardid', '--'))

        # 更新诊断信息和病史
        if hasattr(self, 'detail_diagnosis'):
            self.detail_diagnosis.setPlainText(patient_data.get('zhenduan', '--'))
        if hasattr(self, 'detail_history'):
            self.detail_history.setPlainText(patient_data.get('bingshi', '--'))

        # 更新历史数据标签页 - 使用真实数据库数据
        if hasattr(self, 'history_fields'):
            patient_id = patient_data.get('bianhao', '')
            if patient_id:
                try:
                    from services.treatment_service import treatment_service
                    stats = treatment_service.get_patient_statistics(patient_id)

                    # 计算平均识别率（使用与治疗记录相同的逻辑）
                    treatments = treatment_service.get_treatments_by_patient(patient_id)
                    total_accuracy = 0
                    valid_count = 0
                    for treatment in treatments:
                        required = treatment.get('required_count', 0)
                        actual = treatment.get('actual_count', 0)
                        if required > 0:
                            accuracy = int((actual / required) * 100)
                            total_accuracy += accuracy
                            valid_count += 1

                    avg_accuracy = int(total_accuracy / valid_count) if valid_count > 0 else 0

                    self.history_fields['totalTreatments'][0].setText(f"{stats['total_treatments']}")
                    self.history_fields['totalTime'][0].setText(f"{stats['total_duration']}")
                    self.history_fields['avgAccuracy'][0].setText(f"{avg_accuracy}%")  # 显示为百分数
                    self.history_fields['lastTreatment'][0].setText(stats['last_treatment_date'] or '--')
                except Exception as e:
                    print(f"加载历史统计数据失败: {e}")
                    # 使用默认值
                    self.history_fields['totalTreatments'][0].setText("--")
                    self.history_fields['totalTime'][0].setText("--")
                    self.history_fields['avgAccuracy'][0].setText("--")
                    self.history_fields['lastTreatment'][0].setText("--")
            else:
                # 没有患者ID时显示默认值
                self.history_fields['totalTreatments'][0].setText("--")
                self.history_fields['totalTime'][0].setText("--")
                self.history_fields['avgAccuracy'][0].setText("--")
                self.history_fields['lastTreatment'][0].setText("--")

    def update_theme(self):
        """更新主题样式 - 完全依赖全局样式表"""
        # print(f"PatientDetailPanel.update_theme called")

        # 清除所有本地样式设置，让全局样式表生效
        self.setStyleSheet("")  # 清空本地样式

        # 清除头部样式
        if hasattr(self, 'header'):
            self.header.setStyleSheet("")

        # 清除头像样式
        if hasattr(self, 'detail_avatar'):
            self.detail_avatar.setStyleSheet("")

        # 清除所有文字标签的本地样式
        if hasattr(self, 'detail_name'):
            self.detail_name.setStyleSheet("")
        if hasattr(self, 'detail_info'):
            self.detail_info.setStyleSheet("")
        if hasattr(self, 'detail_status'):
            self.detail_status.setStyleSheet("")

        # 清除详情字段的样式
        if hasattr(self, 'detail_fields'):
            for field_id, label in self.detail_fields.items():
                label.setStyleSheet("")

        # 强制刷新样式
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()

        # print(f"PatientDetailPanel theme update completed - using global stylesheet")


class PatientsPage(BasePage):
    """患者管理页面 - 按照HTML设计实现"""

    def __init__(self):
        # 初始化属性
        self.patient_cards = []
        self.selected_patient = None
        self.current_page = 1
        self.patients_per_page = 7 #每页显示的患者数量
        self.search_term = ""

        # UI组件
        self.patients_scroll = None
        self.patients_container = None
        self.patients_layout = None
        self.detail_panel = None
        self.search_input = None
        self.prev_btn = None
        self.next_btn = None
        self.page_info = None
        self.count_badge = None

        # 模糊遮罩层
        self.blur_overlay = None

        # 数据缓存
        self.patients_data = []
        self.pagination_info = {}

        super().__init__("patients", "患者管理")

    def update_theme(self):
        """更新主题 - 响应主题切换"""
        super().update_theme()

        # 更新所有患者卡片的主题
        for card in self.patient_cards:
            if hasattr(card, 'update_theme'):
                card.update_theme()

        # 更新患者详情面板的主题
        if hasattr(self, 'detail_panel') and hasattr(self.detail_panel, 'update_theme'):
            self.detail_panel.update_theme()

        # 重新应用页面样式
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()

    def _init_content(self):
        """初始化页面内容"""
        # 创建双栏布局 - 按照HTML设计
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)

        # 左侧患者列表区域 (2/3宽度)
        left_area = self._create_left_area()
        content_layout.addWidget(left_area, 2)

        # 右侧患者详情区域 (1/3宽度)
        right_area = self._create_right_area()
        content_layout.addWidget(right_area, 1)

        self.main_layout.addLayout(content_layout)

        # 加载患者列表
        self._load_patients_from_database()
    
    def _create_left_area(self) -> QWidget:
        """创建左侧患者列表区域 - 添加卡片效果"""
        left_area = QFrame()
        left_area.setObjectName("patients_list_card")  # 改为卡片样式

        layout = QVBoxLayout(left_area)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 头部区域
        header = self._create_list_header()
        layout.addWidget(header)

        # 搜索区域
        search_area = self._create_search_area()
        layout.addWidget(search_area)

        # 患者列表滚动区域
        self.patients_scroll = QScrollArea()
        self.patients_scroll.setWidgetResizable(True)
        self.patients_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.patients_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.patients_scroll.setFrameStyle(QFrame.Shape.NoFrame)
        self.patients_scroll.setObjectName("patients_scroll")

        # 患者列表容器
        self.patients_container = QWidget()
        self.patients_layout = QVBoxLayout(self.patients_container)
        self.patients_layout.setContentsMargins(24, 16, 24, 10)  # 增加左右边距
        self.patients_layout.setSpacing(0)  # 增加卡片间距

        self.patients_scroll.setWidget(self.patients_container)
        layout.addWidget(self.patients_scroll, 1)

        # 分页控制
        pagination_area = self._create_pagination_area()
        layout.addWidget(pagination_area)

        return left_area
    
    def _create_right_area(self) -> QWidget:
        """创建右侧患者详情区域 - 添加卡片效果"""
        right_area = QFrame()
        right_area.setObjectName("patient_detail_card")  # 改为卡片样式

        layout = QVBoxLayout(right_area)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 头部区域
        header = self._create_detail_header()
        layout.addWidget(header)

        # 患者详情面板
        self.detail_panel = PatientDetailPanel()
        layout.addWidget(self.detail_panel, 1)

        return right_area
    
    def _create_list_header(self) -> QWidget:
        """创建列表头部"""
        header = QFrame()
        header.setObjectName("list_header")
        header.setFixedHeight(70)  # 增加高度以容纳按钮
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(32, 6, 32, 6)
        layout.setSpacing(12)
        
        # 标题区域
        title_area = QWidget()
        title_layout = QHBoxLayout(title_area)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(12)
        
        # 图标
        icon_label = QLabel("👤")
        # 调整图标字体大小以匹配HTML视觉效果
        icon_font = QFont("Microsoft YaHei", 18)
        icon_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        icon_label.setFont(icon_font)
        title_layout.addWidget(icon_label)
        
        # 标题
        title_label = QLabel("患者列表")
        # 调整列表标题字体大小以匹配HTML视觉效果
        list_title_font = QFont("Microsoft YaHei", 15, QFont.Weight.Bold)
        list_title_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        title_label.setFont(list_title_font)
        title_layout.addWidget(title_label)

        # 徽章
        self.count_badge = QLabel("0")
        self.count_badge.setObjectName("count_badge")
        self.count_badge.setFixedSize(32, 20)
        self.count_badge.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 调整徽章字体大小以匹配HTML视觉效果
        badge_font = QFont("Microsoft YaHei", 10, QFont.Weight.Bold)
        badge_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.count_badge.setFont(badge_font)
        # 样式将通过主题系统设置
        title_layout.addWidget(self.count_badge)
        
        title_layout.addStretch()
        layout.addWidget(title_area, 1)
        
        # 操作按钮区域
        actions_area = QWidget()
        actions_layout = QHBoxLayout(actions_area)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(12)
        
        # 搜索按钮 - 与用户管理页面保持一致
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setObjectName("btn_secondary")
        refresh_btn.clicked.connect(self._refresh_patients)
        # 不设置字体，让全局样式控制
        actions_layout.addWidget(refresh_btn)

        # 新增患者按钮 - 与用户管理页面保持一致
        add_btn = QPushButton("➕ 新增患者")
        add_btn.setObjectName("btn_secondary")
        add_btn.clicked.connect(self._show_add_patient_dialog)
        # 不设置字体，让全局样式控制
        actions_layout.addWidget(add_btn)
        
        layout.addWidget(actions_area)
        
        return header
    
    def _create_detail_header(self) -> QWidget:
        """创建详情头部"""
        header = QFrame()
        header.setObjectName("detail_header")
        header.setFixedHeight(70)  # 增加高度以容纳按钮
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(32, 6, 32, 6)
        layout.setSpacing(12)
        
        # 标题
        title_label = QLabel("📋 患者详情")
        # 调整详情标题字体大小以匹配HTML视觉效果
        detail_title_font = QFont("Microsoft YaHei", 15, QFont.Weight.Bold)
        detail_title_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        title_label.setFont(detail_title_font)
        layout.addWidget(title_label, 1)
        
        # 操作按钮 - 与用户管理页面保持一致
        edit_btn = QPushButton("✏️ 编辑")
        edit_btn.setObjectName("btn_secondary")  # 使用全局样式
        edit_btn.clicked.connect(self._show_edit_patient_dialog)
        # 不设置字体，让全局样式控制
        layout.addWidget(edit_btn)

        treatment_btn = QPushButton("▶️ 治疗")
        treatment_btn.setObjectName("btn_secondary")  # 使用全局样式
        treatment_btn.clicked.connect(self._start_treatment)
        # 不设置字体，让全局样式控制
        layout.addWidget(treatment_btn)
        
        return header
    
    def _create_search_area(self) -> QWidget:
        """创建搜索区域 - 按照HTML设计"""
        search_area = QFrame()
        search_area.setFixedHeight(50)

        layout = QHBoxLayout(search_area)
        layout.setContentsMargins(24, 0, 32, 0)

        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索患者姓名或ID...")
        self.search_input.setFixedHeight(48)
        self.search_input.setFont(QFont("Microsoft YaHei", 14))
        self.search_input.setObjectName("patient_search")

        # 连接搜索事件
        self.search_input.textChanged.connect(self._on_search_changed)

        layout.addWidget(self.search_input)

        return search_area

    def _create_pagination_area(self) -> QWidget:
        """创建分页控制区域 - 按照HTML设计"""
        pagination_area = QFrame()
        pagination_area.setFixedHeight(50)

        layout = QHBoxLayout(pagination_area)
        layout.setContentsMargins(32, 6, 32, 6)
        layout.setSpacing(6)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 上一页按钮
        self.prev_btn = QPushButton("◀ 上一页")
        self.prev_btn.setFont(QFont("Microsoft YaHei", 10))
        self.prev_btn.setObjectName("pagination_btn")
        self.prev_btn.clicked.connect(lambda: self._change_page(-1))
        layout.addWidget(self.prev_btn)

        # 页面信息
        self.page_info = QLabel("第 1 页，共 1 页")
        self.page_info.setFont(QFont("Microsoft YaHei", 12))
        self.page_info.setObjectName("page_info")
        layout.addWidget(self.page_info)

        # 下一页按钮
        self.next_btn = QPushButton("下一页 ▶")
        self.next_btn.setFont(QFont("Microsoft YaHei", 10))
        self.next_btn.setObjectName("pagination_btn")
        self.next_btn.clicked.connect(lambda: self._change_page(1))
        layout.addWidget(self.next_btn)

        return pagination_area

    def _load_patients_from_database(self):
        """从数据库加载患者列表"""
        try:
            # 从数据库获取患者数据
            result = patient_service.get_patients(
                page=self.current_page,
                limit=self.patients_per_page,
                search_term=self.search_term
            )

            self.patients_data = result['patients']
            self.pagination_info = result['pagination']

            # 更新UI
            self._update_patients_display()
            self._update_count_badge()

        except Exception as e:
            print(f"加载患者数据失败: {e}")
            show_warning(self, "错误", f"加载患者数据失败: {str(e)}")

    def _update_patients_display(self):
        """更新患者显示"""
        # 检查布局是否已初始化
        if not hasattr(self, 'patients_layout') or self.patients_layout is None:
            return

        # 清除现有的患者卡片
        for card in self.patient_cards:
            card.setParent(None)
        self.patient_cards.clear()

        # 清除布局中的所有项目
        while self.patients_layout.count():
            child = self.patients_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 创建患者卡片
        for patient_data in self.patients_data:
            card = PatientCard(patient_data)
            card.patient_selected.connect(self._on_patient_selected)
            card.clicked.connect(lambda c=card: self._on_card_clicked(c))
            card.patient_double_clicked.connect(self._on_patient_double_clicked)  # 连接双击信号

            # 连接编辑和出院按钮
            card.edit_btn.clicked.connect(lambda checked=False, p=patient_data: self._show_edit_patient_dialog(p))
            card.discharge_btn.clicked.connect(lambda checked=False, p=patient_data: self._discharge_patient(p))

            self.patients_layout.addWidget(card)
            self.patient_cards.append(card)

        # 添加弹性空间
        self.patients_layout.addStretch()

        # 更新分页信息
        self._update_pagination()

        # 如果是第一次加载且有患者，选择第一个
        if not self.selected_patient and self.patients_data:
            self._on_patient_selected(self.patients_data[0])

    def _update_count_badge(self):
        """更新患者数量徽章"""
        if hasattr(self, 'count_badge') and self.count_badge:
            total_count = self.pagination_info.get('total_count', 0)
            self.count_badge.setText(str(total_count))

    def _on_patient_selected(self, patient_data: dict):
        """患者选中处理"""
        # 取消其他患者的选中状态
        for card in self.patient_cards:
            if card.patient_data != patient_data:
                card.set_selected(False)

        # 更新选中的患者
        self.selected_patient = patient_data

        # 更新详情面板
        self.detail_panel.update_patient_info(patient_data)

    def _on_search_changed(self, text: str):
        """搜索文本变化处理"""
        self.search_term = text.strip()
        self.current_page = 1  # 重置到第一页
        self._load_patients_from_database()

    def _change_page(self, direction: int):
        """切换页面"""
        new_page = self.current_page + direction
        total_pages = self.pagination_info.get('total_pages', 1)

        if 1 <= new_page <= total_pages:
            self.current_page = new_page
            self._load_patients_from_database()

    def _update_pagination(self):
        """更新分页信息"""
        current_page = self.pagination_info.get('current_page', 1)
        total_pages = self.pagination_info.get('total_pages', 1)

        # 更新页面信息
        self.page_info.setText(f"第 {current_page} 页，共 {total_pages} 页")

        # 更新按钮状态
        self.prev_btn.setEnabled(self.pagination_info.get('has_prev', False))
        self.next_btn.setEnabled(self.pagination_info.get('has_next', False))

    def _refresh_patients(self):
        """刷新患者列表"""
        self._load_patients_from_database()

    def _show_add_patient_dialog(self):
        """显示新增患者对话框"""
        # 应用模糊效果
        self._apply_blur_effect()

        dialog = PatientEditDialog(self)
        try:
            if dialog.exec() == QDialog.DialogCode.Accepted:
                patient_data = dialog.get_patient_data()
                self._create_patient(patient_data)
        finally:
            # 无论对话框如何关闭，都要移除模糊效果
            self._remove_blur_effect()

    def _show_edit_patient_dialog(self, patient_data=None):
        """显示编辑患者对话框"""
        if not patient_data and self.selected_patient:
            patient_data = self.selected_patient

        if not patient_data:
            show_warning(self, "提示", "请先选择要编辑的患者")
            return

        # 应用模糊效果
        self._apply_blur_effect()

        dialog = PatientEditDialog(self, patient_data)
        try:
            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_data = dialog.get_patient_data()
                self._update_patient(patient_data['bianhao'], updated_data)
        finally:
            # 无论对话框如何关闭，都要移除模糊效果
            self._remove_blur_effect()

    def _create_patient(self, patient_data):
        """创建新患者"""
        try:
            result = patient_service.create_patient(patient_data)
            if result['success']:
                show_information(self, "成功", "患者创建成功！")
                self._load_patients_from_database()
                # 更新主窗口的患者数量徽章
                main_window = self._get_main_window()
                if main_window:
                    main_window.update_patients_count()
            else:
                # 服务器端验证失败，这里应该很少发生，因为对话框已经做了客户端验证
                error_msg = "\n".join([f"{msg}" for field, msg in result['errors'].items()])
                show_warning(self, "创建失败", f"患者创建失败：\n{error_msg}")
        except Exception as e:
            show_critical(self, "错误", f"创建患者时发生错误：{str(e)}")

    def _update_patient(self, patient_id, patient_data):
        """更新患者信息"""
        try:
            result = patient_service.update_patient(patient_id, patient_data)
            if result['success']:
                show_information(self, "成功", "患者信息更新成功！")
                self._load_patients_from_database()
            else:
                # 服务器端验证失败，这里应该很少发生，因为对话框已经做了客户端验证
                error_msg = "\n".join([f"{msg}" for field, msg in result['errors'].items()])
                show_warning(self, "更新失败", f"患者信息更新失败：\n{error_msg}")
        except Exception as e:
            show_critical(self, "错误", f"更新患者信息时发生错误：{str(e)}")

    def _discharge_patient(self, patient_data):
        """患者出院"""
        # 显示出院确认对话框
        dialog = PatientDischargeDialog(patient_data, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            discharge_data = dialog.get_discharge_data()

            try:
                result = patient_service.discharge_patient(
                    patient_data['bianhao'],
                    discharge_data['discharge_date'],
                    discharge_data['discharge_reason']
                )

                if result['success']:
                    show_information(self, "成功", f"患者 {patient_data['name']} 已成功出院！")
                    self._load_patients_from_database()
                    # 更新主窗口的患者数量徽章
                    main_window = self._get_main_window()
                    if main_window:
                        main_window.update_patients_count()
                else:
                    error_msg = result['errors'].get('general', '出院操作失败')
                    show_warning(self, "出院失败", error_msg)
            except Exception as e:
                show_critical(self, "错误", f"出院操作时发生错误：{str(e)}")

    def _start_treatment(self):
        """开始治疗 - 直接跳转到治疗系统"""
        if not self.selected_patient:
            show_warning(self, "提示", "请先选择要治疗的患者")
            return

        # 获取主窗口引用并跳转到治疗系统页面
        main_window = self._get_main_window()
        if main_window:
            # 切换到治疗系统页面并传递患者信息
            main_window.switch_to_treatment_with_patient(self.selected_patient)

    def _on_card_clicked(self, card):
        """患者卡片点击处理"""
        # 设置选中状态
        for c in self.patient_cards:
            c.set_selected(c == card)

        # 触发选中事件
        card.patient_selected.emit(card.patient_data)

    def _on_patient_double_clicked(self, patient_data: dict):
        """患者卡片双击处理 - 跳转到治疗系统"""
        # 获取主窗口引用
        main_window = self._get_main_window()
        if main_window:
            # 切换到治疗系统页面并传递患者信息
            main_window.switch_to_treatment_with_patient(patient_data)

    def _get_main_window(self):
        """获取主窗口引用"""
        # 向上遍历父组件，找到主窗口
        parent = self.parent()
        while parent:
            if hasattr(parent, 'main_content'):  # 主窗口有main_content属性
                return parent
            parent = parent.parent()
        return None

    def _apply_blur_effect(self):
        """应用模糊效果到背景内容 - 使用半透明遮罩层"""
        # 获取主窗口的main_content区域
        main_window = self._get_main_window()
        if main_window and hasattr(main_window, 'main_content'):
            # 创建半透明遮罩层
            self.blur_overlay = QWidget(main_window.main_content)
            self.blur_overlay.setObjectName("blur_overlay")

            # 设置遮罩层样式
            # rgba(红, 绿, 蓝, 透明度)
            # 透明度: 0.0=完全透明, 1.0=完全不透明
            # 0.5 = 50%透明度，效果更重
            self.blur_overlay.setStyleSheet("""
                QWidget#blur_overlay {
                    background-color: rgba(0, 0, 0, 0.5);
                    border-radius: 0px;
                }
            """)

            # 设置遮罩层大小和位置
            self.blur_overlay.setGeometry(main_window.main_content.rect())
            self.blur_overlay.show()
            self.blur_overlay.raise_()

    def _remove_blur_effect(self):
        """移除模糊效果"""
        # 移除半透明遮罩层
        if hasattr(self, 'blur_overlay') and self.blur_overlay:
            self.blur_overlay.hide()
            self.blur_overlay.deleteLater()
            self.blur_overlay = None


class PatientDischargeDialog(QDialog):
    """患者出院确认对话框 - 无边框圆角样式"""

    def __init__(self, patient_data: dict, parent=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.setWindowTitle("患者出院")
        self.setModal(True)
        self.setFixedSize(450, 350)

        # 设置无边框窗口
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 应用全局样式
        if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
            self.setStyleSheet(parent.styleSheet())

        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("discharge_dialog_container")
        # 不设置局部样式，让全局样式处理

        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 标题
        title_label = QLabel("患者出院确认")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setObjectName("dialog_title")  # 使用对象名，让全局样式处理
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 患者信息
        patient_info = QLabel(f"患者：{self.patient_data['name']} (编号: {self.patient_data['bianhao']})")
        patient_info.setFont(QFont("Microsoft YaHei", 12))
        patient_info.setObjectName("dialog_info")  # 使用对象名，让全局样式处理
        patient_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(patient_info)

        # 出院日期
        date_layout = QHBoxLayout()
        date_label = QLabel("出院日期：")
        date_label.setFont(QFont("Microsoft YaHei", 12))
        date_label.setObjectName("dialog_label")  # 使用对象名，让全局样式处理
        date_layout.addWidget(date_label)

        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setFont(QFont("Microsoft YaHei", 12))
        # 强制刷新样式，确保全局样式生效
        self.date_edit.style().unpolish(self.date_edit)
        self.date_edit.style().polish(self.date_edit)
        date_layout.addWidget(self.date_edit)

        layout.addLayout(date_layout)

        # 出院原因
        reason_label = QLabel("出院原因：")
        reason_label.setFont(QFont("Microsoft YaHei", 12))
        reason_label.setObjectName("dialog_label")  # 使用对象名，让全局样式处理
        layout.addWidget(reason_label)

        self.reason_edit = QTextEdit()
        self.reason_edit.setFont(QFont("Microsoft YaHei", 12))
        self.reason_edit.setPlaceholderText("请输入出院原因（可选）")
        self.reason_edit.setMaximumHeight(80)
        # 强制刷新样式，确保全局样式生效
        self.reason_edit.style().unpolish(self.reason_edit)
        self.reason_edit.style().polish(self.reason_edit)
        layout.addWidget(self.reason_edit)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("btn_secondary")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        confirm_btn = QPushButton("确认出院")
        confirm_btn.setObjectName("btn_warning")
        confirm_btn.clicked.connect(self.accept)
        button_layout.addWidget(confirm_btn)

        layout.addLayout(button_layout)

        # 设置主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)

    def paintEvent(self, event):
        """绘制圆角背景 - 让全局样式处理背景"""
        # 移除自定义绘制，让全局样式表处理背景
        super().paintEvent(event)

    def get_discharge_data(self) -> dict:
        """获取出院数据"""
        return {
            'discharge_date': self.date_edit.date().toString('yyyy-MM-dd'),
            'discharge_reason': self.reason_edit.toPlainText().strip()
        }


class PatientReadmissionConfirmDialog(QDialog):
    """患者再次入院确认对话框 - 中文按钮"""

    def __init__(self, patient_id: str, patient_name: str, parent=None):
        super().__init__(parent)
        self.patient_id = patient_id
        self.patient_name = patient_name
        self.setWindowTitle("患者再次入院")
        self.setModal(True)
        self.setFixedSize(400, 200)

        # 设置无边框窗口
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 应用全局样式
        if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
            self.setStyleSheet(parent.styleSheet())

        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("discharge_dialog_container")

        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 标题
        title_label = QLabel("患者再次入院")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 消息内容
        message_label = QLabel(f"患者编号 {self.patient_id} 已存在且已出院。\n"
                              f"患者姓名：{self.patient_name}\n\n"
                              f"是否为该患者办理再次入院？")
        message_label.setFont(QFont("Microsoft YaHei", 12))
        message_label.setObjectName("dialog_info")
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        no_btn = QPushButton("否")
        no_btn.setObjectName("btn_secondary")
        no_btn.setFont(QFont("Microsoft YaHei", 12))
        no_btn.clicked.connect(self.reject)
        button_layout.addWidget(no_btn)

        yes_btn = QPushButton("是")
        yes_btn.setObjectName("btn_primary")
        yes_btn.setFont(QFont("Microsoft YaHei", 12))
        yes_btn.clicked.connect(self.accept)
        button_layout.addWidget(yes_btn)

        layout.addLayout(button_layout)

        # 设置主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)

    def paintEvent(self, event):
        """绘制圆角背景 - 让全局样式处理背景"""
        super().paintEvent(event)


class PatientReadmissionDialog(QDialog):
    """患者再次入院确认对话框"""

    def __init__(self, patient_data: dict, parent=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.setWindowTitle("患者再次入院")
        self.setModal(True)
        self.setFixedSize(500, 600)

        # 设置无边框窗口
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 应用全局样式
        if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
            self.setStyleSheet(parent.styleSheet())

        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("discharge_dialog_container")
        # 不设置局部样式，让全局样式处理

        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 标题
        title_label = QLabel("患者再次入院确认")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 患者基本信息（只读）
        info_frame = QFrame()
        info_frame.setObjectName("info_frame")
        info_layout = QFormLayout(info_frame)

        # 只读信息
        readonly_info = QLabel(f"编号：{self.patient_data['bianhao']}  |  "
                              f"姓名：{self.patient_data['name']}  |  "
                              f"性别：{self.patient_data['xingbie']}  |  "
                              f"身份证：{self.patient_data['cardid']}")
        readonly_info.setObjectName("dialog_info")
        readonly_info.setWordWrap(True)
        layout.addWidget(readonly_info)

        # 使用表单布局，统一左对齐
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignLeft)
        form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # 年龄
        age_label = QLabel("年龄：")
        age_label.setObjectName("dialog_label")
        age_label.setFont(QFont("Microsoft YaHei", 12))

        self.age_edit = QLineEdit()
        self.age_edit.setText(str(self.patient_data.get('age', '')))
        self.age_edit.setPlaceholderText("请输入年龄")
        self.age_edit.setFont(QFont("Microsoft YaHei", 12))
        form_layout.addRow(age_label, self.age_edit)

        # 联系电话
        phone_label = QLabel("联系电话：")
        phone_label.setObjectName("dialog_label")
        phone_label.setFont(QFont("Microsoft YaHei", 12))

        self.phone_edit = QLineEdit()
        self.phone_edit.setText(self.patient_data.get('phone', ''))
        self.phone_edit.setPlaceholderText("请输入联系电话")
        self.phone_edit.setFont(QFont("Microsoft YaHei", 12))
        form_layout.addRow(phone_label, self.phone_edit)

        # 地址（改为普通文本框）
        address_label = QLabel("地址：")
        address_label.setObjectName("dialog_label")
        address_label.setFont(QFont("Microsoft YaHei", 12))

        self.address_edit = QLineEdit()
        self.address_edit.setText(self.patient_data.get('address', ''))
        self.address_edit.setPlaceholderText("请输入地址")
        self.address_edit.setFont(QFont("Microsoft YaHei", 12))
        form_layout.addRow(address_label, self.address_edit)

        # 主治医生
        doctor_label = QLabel("主治医生：")
        doctor_label.setObjectName("dialog_label")
        doctor_label.setFont(QFont("Microsoft YaHei", 12))

        self.doctor_edit = QLineEdit()
        self.doctor_edit.setText(self.patient_data.get('zhuzhi', ''))
        self.doctor_edit.setPlaceholderText("请输入主治医生")
        self.doctor_edit.setFont(QFont("Microsoft YaHei", 12))
        form_layout.addRow(doctor_label, self.doctor_edit)

        # 诊断信息（改为普通文本框）
        diagnosis_label = QLabel("诊断信息：")
        diagnosis_label.setObjectName("dialog_label")
        diagnosis_label.setFont(QFont("Microsoft YaHei", 12))

        self.diagnosis_edit = QLineEdit()
        self.diagnosis_edit.setText(self.patient_data.get('zhenduan', ''))
        self.diagnosis_edit.setPlaceholderText("请输入诊断信息")
        self.diagnosis_edit.setFont(QFont("Microsoft YaHei", 12))
        form_layout.addRow(diagnosis_label, self.diagnosis_edit)

        layout.addLayout(form_layout)

        # 入院信息表单布局
        admission_form_layout = QFormLayout()
        admission_form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignLeft)
        admission_form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # 入院日期
        date_label = QLabel("入院日期：")
        date_label.setObjectName("dialog_label")
        date_label.setFont(QFont("Microsoft YaHei", 12))

        self.admission_date_edit = QDateEdit()
        self.admission_date_edit.setDate(QDate.currentDate())
        self.admission_date_edit.setCalendarPopup(True)
        self.admission_date_edit.setFont(QFont("Microsoft YaHei", 12))
        admission_form_layout.addRow(date_label, self.admission_date_edit)

        # 入院原因（选填，改为普通文本框）
        reason_label = QLabel("入院原因（选填）：")
        reason_label.setObjectName("dialog_label")
        reason_label.setFont(QFont("Microsoft YaHei", 12))

        self.admission_reason_edit = QLineEdit()
        self.admission_reason_edit.setPlaceholderText("请输入入院原因（可选）")
        self.admission_reason_edit.setFont(QFont("Microsoft YaHei", 12))
        admission_form_layout.addRow(reason_label, self.admission_reason_edit)

        layout.addLayout(admission_form_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("btn_secondary")
        cancel_btn.setFont(QFont("Microsoft YaHei", 12))
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        confirm_btn = QPushButton("确认入院")
        confirm_btn.setObjectName("btn_primary")
        confirm_btn.setFont(QFont("Microsoft YaHei", 12))
        confirm_btn.clicked.connect(self.accept)
        button_layout.addWidget(confirm_btn)

        layout.addLayout(button_layout)

        # 设置主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)

    def paintEvent(self, event):
        """绘制圆角背景 - 让全局样式处理背景"""
        # 移除自定义绘制，让全局样式表处理背景
        super().paintEvent(event)

    def get_readmission_data(self) -> dict:
        """获取再次入院数据"""
        return {
            'updated_info': {
                'age': self.age_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'address': self.address_edit.text().strip(),  # 改为text()
                'zhuzhi': self.doctor_edit.text().strip(),
                'zhenduan': self.diagnosis_edit.text().strip()  # 改为text()
            },
            'admission_info': {
                'admission_date': self.admission_date_edit.date().toString('yyyy-MM-dd'),
                'admission_reason': self.admission_reason_edit.text().strip(),  # 改为text()
                'department': self.patient_data.get('keshi', ''),
                'doctor': self.doctor_edit.text().strip()
            }
        }




class PatientEditDialog(QDialog):
    """患者编辑对话框 - 按照HTML设计，支持主题切换"""

    def __init__(self, parent=None, patient_data=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.is_edit_mode = patient_data is not None

        # 验证状态
        self.field_errors = {}
        self.error_labels = {}

        self.setWindowTitle("编辑患者" if self.is_edit_mode else "新增患者")
        self.setModal(True)
        self.setFixedSize(700, 800)
        self.setObjectName("patient_edit_dialog")

        # 设置无边框窗口 - 与其他对话框保持一致
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 应用全局样式
        if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
            self.setStyleSheet(parent.styleSheet())

        self._init_ui()
        self._setup_validation()
        self._apply_theme()
        self._load_reference_data()

        if self.is_edit_mode:
            self._load_patient_data()

    def _apply_theme(self):
        """强制应用主题样式"""
        # 获取主窗口的主题管理器
        main_window = self._get_main_window()
        if main_window and hasattr(main_window, 'theme_manager'):
            # 强制应用当前主题的样式表
            current_stylesheet = main_window.theme_manager.get_current_stylesheet()
            self.setStyleSheet(current_stylesheet)

    def _get_main_window(self):
        """获取主窗口引用"""
        # 向上遍历父组件，找到主窗口
        parent = self.parent()
        while parent:
            if hasattr(parent, 'theme_manager'):  # 主窗口有theme_manager属性
                return parent
            parent = parent.parent()
        return None

    def _load_reference_data(self):
        """加载参考数据并填充到表单"""
        try:
            # 获取医生列表并填充到下拉框
            doctors = reference_data_service.get_doctors()
            self.doctor_combo.clear()
            self.doctor_combo.addItem("")  # 添加空选项
            self.doctor_combo.addItems(doctors)

            # 自动填充设备编号
            device_id = reference_data_service.get_device_id()
            self.hardid_edit.setText(device_id)

        except Exception as e:
            print(f"加载参考数据失败: {e}")
            # 设置默认值
            self.doctor_combo.clear()
            self.doctor_combo.addItems(["", "张三", "李四", "王五"])
            self.hardid_edit.setText("160701")

    def _init_ui(self):
        """初始化UI - 按照HTML设计"""
        # 创建主容器 - 与其他对话框保持一致
        main_container = QFrame()
        main_container.setObjectName("discharge_dialog_container")  # 使用统一的容器样式

        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(32, 32, 32, 32)
        main_layout.setSpacing(24)

        # 设置对话框的主布局
        dialog_layout = QVBoxLayout(self)
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(main_container)

        # 标题区域
        header_layout = QHBoxLayout()

        title = QLabel("编辑患者" if self.is_edit_mode else "新增患者")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setObjectName("dialog_title")
        header_layout.addWidget(title)

        header_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("✕")  # 使用更明显的关闭图标
        close_btn.setObjectName("dialog_close_btn")
        close_btn.setFixedSize(36, 36)  # 稍微增大尺寸
        close_btn.clicked.connect(self.reject)
        header_layout.addWidget(close_btn)

        main_layout.addLayout(header_layout)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)
        scroll_area.setObjectName("dialog_scroll")

        # 表单容器
        form_container = QWidget()
        form_layout = QVBoxLayout(form_container)
        form_layout.setSpacing(24)
        form_layout.setContentsMargins(0, 0, 0, 0)

        # 基本信息部分
        self._create_basic_info_section(form_layout)

        # 医疗信息部分
        self._create_medical_info_section(form_layout)

        scroll_area.setWidget(form_container)
        main_layout.addWidget(scroll_area, 1)

        # 按钮区域
        self._create_button_area(main_layout)

    def _create_basic_info_section(self, parent_layout):
        """创建基本信息部分"""

        # 第一行：患者编号、姓名、性别
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(16)

        # 患者编号
        bianhao_widget = self._create_field_widget("患者编号 *", "bianhao_edit", QLineEdit, placeholder="请输入患者编号")
        self.bianhao_edit = bianhao_widget.findChild(QLineEdit)
        row1_layout.addWidget(bianhao_widget)

        # 姓名
        name_widget = self._create_field_widget("姓名 *", "name_edit", QLineEdit, placeholder="请输入患者姓名")
        self.name_edit = name_widget.findChild(QLineEdit)
        row1_layout.addWidget(name_widget)

        # 性别
        gender_widget = self._create_field_widget("性别 *", "gender_combo", QComboBox)
        self.gender_combo = gender_widget.findChild(QComboBox)
        self.gender_combo.addItems(["", "男", "女"])
        row1_layout.addWidget(gender_widget)

        parent_layout.addLayout(row1_layout)

        # 第二行：年龄
        row2_layout = QHBoxLayout()
        row2_layout.setSpacing(16)

        # 年龄
        age_widget = self._create_field_widget("年龄 *", "age_edit", QLineEdit, placeholder="请输入年龄")
        self.age_edit = age_widget.findChild(QLineEdit)
        row2_layout.addWidget(age_widget)

        # 添加空白占位
        row2_layout.addWidget(QWidget())
        row2_layout.addWidget(QWidget())

        parent_layout.addLayout(row2_layout)

        # 第三行：身份证号、联系电话
        row3_layout = QHBoxLayout()
        row3_layout.setSpacing(16)

        # 身份证号
        cardid_widget = self._create_field_widget("证件号码", "cardid_edit", QLineEdit, placeholder="请输入身份证号或其他证件号")
        self.cardid_edit = cardid_widget.findChild(QLineEdit)
        row3_layout.addWidget(cardid_widget)

        # 联系电话
        phone_widget = self._create_field_widget("联系电话", "phone_edit", QLineEdit, placeholder="请输入手机号或座机号")
        self.phone_edit = phone_widget.findChild(QLineEdit)
        row3_layout.addWidget(phone_widget)

        parent_layout.addLayout(row3_layout)

        # 第四行：地址
        address_widget = self._create_field_widget("地址", "address_edit", QLineEdit, placeholder="请输入地址")
        self.address_edit = address_widget.findChild(QLineEdit)
        parent_layout.addWidget(address_widget)

    def _create_medical_info_section(self, parent_layout):
        """创建医疗信息部分"""

        # 第一行：主治医生、设备编号
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(16)

        # 主治医生（下拉选择）
        doctor_widget = self._create_field_widget("主治医生", "doctor_combo", QComboBox)
        self.doctor_combo = doctor_widget.findChild(QComboBox)
        row1_layout.addWidget(doctor_widget)

        # 设备编号（只读）
        hardid_widget = self._create_field_widget("设备编号", "hardid_edit", QLineEdit, placeholder="自动填充")
        self.hardid_edit = hardid_widget.findChild(QLineEdit)
        self.hardid_edit.setReadOnly(True)  # 设置为只读
        # 不设置自定义样式，让全局样式表处理只读状态
        row1_layout.addWidget(hardid_widget)

        parent_layout.addLayout(row1_layout)

        # 诊断信息
        diagnosis_widget = self._create_field_widget("诊断信息", "diagnosis_edit", QTextEdit, placeholder="请输入诊断信息")
        self.diagnosis_edit = diagnosis_widget.findChild(QTextEdit)
        self.diagnosis_edit.setMaximumHeight(80)
        parent_layout.addWidget(diagnosis_widget)

        # 病史
        history_widget = self._create_field_widget("病史", "history_edit", QTextEdit, placeholder="请输入病史")
        self.history_edit = history_widget.findChild(QTextEdit)
        self.history_edit.setMaximumHeight(80)
        parent_layout.addWidget(history_widget)

    def _create_field_widget(self, label_text, object_name, widget_class, placeholder=""):
        """创建字段组件"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 标签
        label = QLabel(label_text)
        label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Medium))
        label.setObjectName("field_label")
        layout.addWidget(label)

        # 输入控件
        widget = widget_class()
        widget.setObjectName(object_name)

        if hasattr(widget, 'setPlaceholderText') and placeholder:
            widget.setPlaceholderText(placeholder)

        # 设置样式
        if isinstance(widget, (QLineEdit, QTextEdit)):
            widget.setFont(QFont("Microsoft YaHei", 10))
        elif isinstance(widget, QComboBox):
            widget.setFont(QFont("Microsoft YaHei", 10))

        layout.addWidget(widget)

        # 错误提示标签
        error_label = QLabel()
        error_label.setObjectName("error_label")
        error_label.setWordWrap(True)
        error_label.hide()  # 默认隐藏
        layout.addWidget(error_label)

        # 保存错误标签引用
        field_name = object_name.replace('_edit', '').replace('_combo', '')
        self.error_labels[field_name] = error_label

        return container

    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(12)

        button_layout.addStretch()

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("btn_secondary")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        # 保存按钮
        self.save_btn = QPushButton("保存" if self.is_edit_mode else "添加患者")
        self.save_btn.setObjectName("btn_primary")
        self.save_btn.clicked.connect(self._validate_and_accept)
        button_layout.addWidget(self.save_btn)

        parent_layout.addLayout(button_layout)

    def paintEvent(self, event):
        """绘制圆角背景 - 让全局样式处理背景"""
        # 移除自定义绘制，让全局样式表处理背景
        super().paintEvent(event)

    def _load_patient_data(self):
        """加载患者数据到表单"""
        if not self.patient_data:
            return

        self.bianhao_edit.setText(str(self.patient_data.get('bianhao', '')))
        # 在编辑模式下设置患者编号为只读，不可修改（与设备编号相同的状态）
        if self.is_edit_mode:
            self.bianhao_edit.setReadOnly(True)
            self.bianhao_edit.setFocusPolicy(Qt.FocusPolicy.NoFocus)  # 不可获得焦点
            # 明确设置readOnly属性，确保样式表能正确识别
            self.bianhao_edit.setProperty("readOnly", "true")
            # 强制刷新样式，确保只读状态生效
            self.bianhao_edit.style().unpolish(self.bianhao_edit)
            self.bianhao_edit.style().polish(self.bianhao_edit)
        self.name_edit.setText(self.patient_data.get('name', ''))
        self.age_edit.setText(str(self.patient_data.get('age', '')))

        gender = self.patient_data.get('xingbie', '')
        index = self.gender_combo.findText(gender)
        if index >= 0:
            self.gender_combo.setCurrentIndex(index)

        self.cardid_edit.setText(self.patient_data.get('cardid', ''))
        self.phone_edit.setText(self.patient_data.get('phone', ''))
        self.address_edit.setText(self.patient_data.get('address', ''))

        # 设置主治医生下拉框
        doctor_name = self.patient_data.get('zhuzhi', '')
        doctor_index = self.doctor_combo.findText(doctor_name)
        if doctor_index >= 0:
            self.doctor_combo.setCurrentIndex(doctor_index)
        else:
            self.doctor_combo.setCurrentIndex(0)  # 设置为空选项

        self.hardid_edit.setText(self.patient_data.get('hardid', ''))
        self.diagnosis_edit.setPlainText(self.patient_data.get('zhenduan', ''))
        self.history_edit.setPlainText(self.patient_data.get('bingshi', ''))

    def _setup_validation(self):
        """设置实时验证"""
        # 连接输入变化事件
        self.bianhao_edit.textChanged.connect(lambda: self._validate_field('bianhao'))
        self.name_edit.textChanged.connect(lambda: self._validate_field('name'))
        self.age_edit.textChanged.connect(lambda: self._validate_field('age'))

        # 连接患者编号输入框的焦点离开事件（仅在新增模式下）
        if not self.is_edit_mode:
            self.bianhao_edit.editingFinished.connect(self._check_existing_patient)

        # 连接其他字段的验证事件
        self.gender_combo.currentTextChanged.connect(lambda: self._validate_field('xingbie'))
        self.cardid_edit.textChanged.connect(lambda: self._validate_field('cardid'))
        self.phone_edit.textChanged.connect(lambda: self._validate_field('phone'))

    def _check_existing_patient(self):
        """检查患者编号是否已存在"""
        if self.is_edit_mode:
            return  # 编辑模式下不检查

        patient_id = self.bianhao_edit.text().strip()
        if not patient_id:
            return  # 编号为空时不检查

        try:
            from services.patient_service import patient_service

            # 检查患者状态
            status_check = patient_service.check_patient_existence_and_status(patient_id)

            if not status_check['exists']:
                # 患者不存在，正常新增流程
                return

            if status_check['status'] == 'in_hospital':
                # 患者已在院
                show_warning(self, "患者已在院",
                           f"患者编号 {patient_id} 已在院，无法重复入院。\n"
                           f"患者姓名：{status_check['patient_data']['name']}")
                self.bianhao_edit.clear()
                self.bianhao_edit.setFocus()
                return

            elif status_check['status'] == 'discharged':
                # 患者已出院，询问是否再次入院
                dialog = PatientReadmissionConfirmDialog(
                    patient_id,
                    status_check['patient_data']['name'],
                    self
                )

                if dialog.exec() == QDialog.DialogCode.Accepted:
                    # 显示再次入院对话框
                    self._show_readmission_dialog(status_check['patient_data'])
                else:
                    # 用户取消，清空编号
                    self.bianhao_edit.clear()
                    self.bianhao_edit.setFocus()

        except Exception as e:
            show_critical(self, "错误", f"检查患者状态时发生错误：{str(e)}")

    def _show_readmission_dialog(self, patient_data):
        """显示再次入院对话框"""
        dialog = PatientReadmissionDialog(patient_data, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            readmission_data = dialog.get_readmission_data()

            try:
                from services.patient_service import patient_service

                result = patient_service.readmit_patient(
                    patient_data['bianhao'],
                    readmission_data['updated_info'],
                    readmission_data['admission_info']
                )

                if result['success']:
                    show_information(self, "成功", f"患者 {patient_data['name']} 再次入院成功！")
                    # 通知父页面刷新患者列表
                    if hasattr(self.parent(), '_load_patients_from_database'):
                        self.parent()._load_patients_from_database()
                        # 更新主窗口的患者数量徽章
                        main_window = self.parent()._get_main_window()
                        if main_window:
                            main_window.update_patients_count()
                    # 直接关闭对话框，不触发accept逻辑
                    self.reject()  # 使用reject而不是accept，避免触发创建患者逻辑
                else:
                    error_msg = result['errors'].get('general', '再次入院失败')
                    show_warning(self, "再次入院失败", error_msg)
            except Exception as e:
                show_critical(self, "错误", f"再次入院时发生错误：{str(e)}")
        else:
            # 用户取消再次入院，清空编号
            self.bianhao_edit.clear()
            self.bianhao_edit.setFocus()

    def _validate_field(self, field_name):
        """验证单个字段"""
        # 获取当前数据
        current_data = self.get_patient_data()

        # 导入验证函数
        from utils.db_helpers import validate_patient_data

        # 验证数据
        errors = validate_patient_data(current_data)

        # 获取对应的输入控件和错误标签
        widget = None
        if field_name == 'bianhao':
            widget = self.bianhao_edit
        elif field_name == 'name':
            widget = self.name_edit
        elif field_name == 'age':
            widget = self.age_edit
        elif field_name == 'xingbie':
            widget = self.gender_combo
        elif field_name == 'cardid':
            widget = self.cardid_edit
        elif field_name == 'phone':
            widget = self.phone_edit

        error_label = self.error_labels.get(field_name)

        if field_name in errors:
            # 有错误
            self.field_errors[field_name] = errors[field_name]
            if widget:
                widget.setProperty("error", "true")
                widget.style().unpolish(widget)
                widget.style().polish(widget)
            if error_label:
                error_label.setText(errors[field_name])
                error_label.show()
        else:
            # 无错误
            if field_name in self.field_errors:
                del self.field_errors[field_name]
            if widget:
                widget.setProperty("error", "false")
                widget.style().unpolish(widget)
                widget.style().polish(widget)
            if error_label:
                error_label.hide()

        # 更新保存按钮状态
        self._update_save_button()

    def _update_save_button(self):
        """更新保存按钮状态"""
        has_errors = bool(self.field_errors)
        self.save_btn.setEnabled(not has_errors)

        if has_errors:
            self.save_btn.setToolTip("请修正错误后再保存")
        else:
            self.save_btn.setToolTip("")

    def _validate_and_accept(self):
        """验证所有字段并接受对话框"""
        # 验证所有字段
        self._validate_field('bianhao')
        self._validate_field('name')
        self._validate_field('age')
        self._validate_field('xingbie')
        self._validate_field('cardid')
        self._validate_field('phone')

        # 如果有错误，跳转到第一个错误字段
        if self.field_errors:
            first_error_field = list(self.field_errors.keys())[0]
            self._focus_error_field(first_error_field)
            return

        # 无错误，接受对话框
        self.accept()

    def _focus_error_field(self, field_name):
        """跳转到错误字段"""
        widget = None
        if field_name == 'bianhao':
            widget = self.bianhao_edit
        elif field_name == 'name':
            widget = self.name_edit
        elif field_name == 'age':
            widget = self.age_edit
        elif field_name == 'xingbie':
            widget = self.gender_combo
        elif field_name == 'cardid':
            widget = self.cardid_edit
        elif field_name == 'phone':
            widget = self.phone_edit

        if widget:
            widget.setFocus()
            if hasattr(widget, 'selectAll'):
                widget.selectAll()

    def get_patient_data(self):
        """获取表单数据"""
        # 获取基本表单数据
        data = {
            'bianhao': self.bianhao_edit.text().strip(),
            'name': self.name_edit.text().strip(),
            'age': self.age_edit.text().strip(),
            'xingbie': self.gender_combo.currentText(),
            'cardid': self.cardid_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.text().strip(),
            'zhuzhi': self.doctor_combo.currentText(),  # 从下拉框获取
            'hardid': self.hardid_edit.text().strip(),
            'zhenduan': self.diagnosis_edit.toPlainText().strip(),
            'bingshi': self.history_edit.toPlainText().strip(),
        }

        # 自动填充缺失的字段
        try:
            data['yiyuanid'] = reference_data_service.get_hospital_id()
            data['keshi'] = reference_data_service.get_department()
            data['czy'] = reference_data_service.get_default_operator()
        except Exception as e:
            print(f"获取参考数据失败: {e}")
            # 使用默认值
            data['yiyuanid'] = 3
            data['keshi'] = '康复科'
            # 从认证服务获取当前登录用户作为操作员
            try:
                from services.auth_service import auth_service
                current_user = auth_service.get_current_user()
                if current_user:
                    data['czy'] = current_user.get('name') or current_user.get('username', '操作员')
                else:
                    data['czy'] = '操作员'
            except:
                data['czy'] = '操作员'

        return data
