# 权限验证系统使用说明

## 概述

本系统实现了一个轻量级的权限验证系统，专为医疗器械认证优化，确保启动速度和运行效率。系统采用静态权限控制，在用户登录时确定权限，避免运行时频繁检查。

## 权限架构

### 角色定义

系统定义了三个角色，每个角色具有不同的权限：

#### 1. 管理员 (admin)
- **页面权限**: 患者管理、治疗系统、报告分析、用户管理、系统设置
- **操作权限**: 创建用户、编辑用户、删除用户、切换用户状态

#### 2. 医生 (doctor)
- **页面权限**: 患者管理、治疗系统、报告分析
- **操作权限**: 无特殊操作权限

#### 3. 操作员 (operator)
- **页面权限**: 患者管理、治疗系统
- **操作权限**: 无特殊操作权限

### 权限控制范围

#### 需要权限控制的功能
- 用户管理页面访问（仅管理员）
- 系统设置页面访问（仅管理员）
- 用户增删改操作（仅管理员）

#### 不需要权限控制的功能
- 患者管理（所有角色）
- 治疗系统（所有角色）
- 报告分析（医生和管理员）
- 数据查看（所有角色）

## 技术实现

### 核心组件

#### 1. 简化权限管理器 (`utils/simple_permission_manager.py`)
```python
from utils.simple_permission_manager import permission_manager

# 检查页面访问权限
can_access = permission_manager.can_access_page('users')

# 检查操作权限
can_create = permission_manager.can_perform_operation('user_create')

# 获取当前用户角色
role = permission_manager.get_current_user_role()

# 角色检查
is_admin = permission_manager.is_admin()
```

#### 2. 基础页面权限检查 (`ui/pages/base_page.py`)
```python
class BasePage(QWidget):
    def check_page_access(self) -> bool:
        """检查页面访问权限"""
        from utils.simple_permission_manager import permission_manager
        return permission_manager.can_access_page(self.page_id)
```

#### 3. 导航菜单静态控制 (`ui/components/sidebar.py`)
```python
# 根据权限静态创建菜单项
allowed_pages = permission_manager.get_allowed_pages()
if "users" in allowed_pages:
    self._add_nav_item("users", "★", "用户管理")
```

### 权限验证流程

#### 1. 页面访问控制
```python
# 在页面初始化时检查权限
def __init__(self, page_id: str, title: str):
    super().__init__(page_id, title)
    
    # 检查页面访问权限
    if not self.check_page_access():
        self.handle_access_denied()
        return
```

#### 2. 操作权限控制
```python
# 在创建操作按钮时检查权限
if permission_manager.can_perform_operation('user_create'):
    add_btn = QPushButton("新增用户")
    add_btn.clicked.connect(self._create_user)
    layout.addWidget(add_btn)
```

#### 3. 数据访问权限验证
```python
# 在数据操作方法中检查权限
def create_user(self, user_data):
    if not permission_manager.can_perform_operation('user_create'):
        return {'success': False, 'error': '没有权限创建用户'}
    
    # 执行创建用户操作
    ...
```

## 使用方法

### 1. 添加新的权限控制

#### 添加新页面权限
```python
# 在 simple_permission_manager.py 中修改角色权限配置
self._role_permissions = {
    'admin': {
        'pages': ['patients', 'treatment', 'reports', 'users', 'settings', 'new_page'],
        'operations': ['user_create', 'user_edit', 'user_delete', 'user_toggle_status']
    },
    # ...
}
```

#### 添加新操作权限
```python
# 在 simple_permission_manager.py 中添加新操作
self._role_permissions = {
    'admin': {
        'pages': ['patients', 'treatment', 'reports', 'users', 'settings'],
        'operations': ['user_create', 'user_edit', 'user_delete', 'user_toggle_status', 'new_operation']
    },
    # ...
}
```

### 2. 在新页面中使用权限控制

```python
class NewPage(BasePage):
    def __init__(self):
        super().__init__("new_page", "新页面")
        
        # 检查页面访问权限
        if not self.check_page_access():
            self.handle_access_denied()
            return
        
        # 初始化页面内容
        self._init_content()
```

### 3. 在操作中使用权限控制

```python
def create_new_item(self):
    # 检查操作权限
    if not permission_manager.can_perform_operation('new_operation'):
        QMessageBox.warning(self, "权限不足", "您没有权限执行此操作")
        return
    
    # 执行操作
    ...
```

## 性能优化

### 1. 静态权限控制
- 登录时确定权限，避免运行时频繁检查
- 导航菜单根据权限静态创建，无需动态显示/隐藏

### 2. 异常处理
- 权限检查失败时默认允许访问，避免系统崩溃
- 提供清晰的错误信息和用户反馈

### 3. 最小化权限检查
- 只在关键操作时验证权限
- 避免不必要的权限检查开销

## 注意事项

1. **医疗器械认证兼容性**: 系统设计考虑了医疗器械认证的启动速度要求
2. **实时算法友好**: 最小化权限检查对实时算法的影响
3. **系统稳定性**: 权限检查失败时不会导致系统崩溃
4. **用户体验**: 提供清晰的权限不足提示信息

## 故障排除

### 常见问题

1. **权限检查失败**
   - 检查 `simple_permission_manager.py` 是否正确导入
   - 确认用户已正确登录

2. **页面访问被拒绝**
   - 检查用户角色是否正确
   - 确认页面ID在权限配置中正确定义

3. **操作按钮不显示**
   - 检查操作权限是否正确配置
   - 确认权限检查逻辑是否正确

### 调试方法

```python
# 打印当前用户权限信息
summary = permission_manager.get_user_info_summary()
print("用户权限摘要:", summary)

# 检查特定权限
print("用户管理页面权限:", permission_manager.can_access_page('users'))
print("创建用户操作权限:", permission_manager.can_perform_operation('user_create'))
```

## 更新记录

- **v1.0**: 初始版本，实现基础权限控制
- **v1.1**: 优化性能，添加异常处理
- **v1.2**: 完善文档，添加使用示例 