{"project_root": "D:\\NK_Python\\脑机接口康复训练", "total_modules": 77, "entry_points": ["main", "build_package", "build_package_backup"], "reachable_module_count": 55, "unreachable_modules": ["app.__init__", "core.__init__", "license_generator", "services.__init__", "services.bluetooth.__init__", "services.data_protection.__init__", "services.data_protection.hardware_fingerprint_protector", "services.eeg_preprocessing.__init__", "services.eeg_preprocessing.kalman_processor", "services.eeg_preprocessing.preprocessing_pipeline", "services.eeg_preprocessing.rls_filter", "services.eeg_preprocessing.traditional_filter", "services.eeg_processing.__init__", "services.eeg_processing.eeg_data_processor", "services.stimulation.__init__", "tools.analyze_project_usage", "ui.__init__", "ui.components.__init__", "ui.components.interactive_chart", "ui.pages.__init__", "ui.themes.__init__", "utils.__init__"], "unused_imports": {"license_generator": [{"type": "import", "module": "uuid", "line": 13}, {"type": "from", "module": "typing", "name": "List", "line": 17}], "app.application": [{"type": "import", "module": "sys", "line": 9}, {"type": "from", "module": "PySide6.QtCore", "name": "QObject", "line": 11}], "app.background_loader": [{"type": "import", "module": "ui.main_window", "line": 52}, {"type": "import", "module": "ui.pages.patients_page", "line": 53}, {"type": "import", "module": "ui.pages.treatment_page", "line": 54}, {"type": "import", "module": "ui.pages.reports_page", "line": 55}, {"type": "import", "module": "ui.pages.settings_page", "line": 56}, {"type": "import", "module": "ui.pages.users_page", "line": 57}, {"type": "import", "module": "services.patient_service", "line": 65}, {"type": "import", "module": "services.treatment_service", "line": 66}, {"type": "import", "module": "services.report_service", "line": 67}, {"type": "import", "module": "services.user_service", "line": 68}], "app.config": [{"type": "import", "module": "os", "line": 10}, {"type": "from", "module": "pathlib", "name": "Path", "line": 11}], "core.database": [{"type": "from", "module": "typing", "name": "Dict", "line": 11}, {"type": "from", "module": "typing", "name": "Any", "line": 11}, {"type": "from", "module": "datetime", "name": "datetime", "line": 14}], "services.api_client": [{"type": "from", "module": "typing", "name": "Optional", "line": 11}], "services.auth_service": [{"type": "import", "module": "json", "line": 10}, {"type": "from", "module": "pathlib", "name": "Path", "line": 12}, {"type": "from", "module": "datetime", "name": "<PERSON><PERSON><PERSON>", "line": 14}], "services.classifier_training_manager": [{"type": "from", "module": "typing", "name": "Optional", "line": 13}, {"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 13}], "services.license_manager": [{"type": "import", "module": "uuid", "line": 12}, {"type": "import", "module": "os", "line": 17}, {"type": "import", "module": "sys", "line": 18}, {"type": "from", "module": "pathlib", "name": "Path", "line": 19}, {"type": "from", "module": "typing", "name": "List", "line": 20}, {"type": "from", "module": "typing", "name": "Optional", "line": 20}], "services.multi_round_training_manager": [{"type": "import", "module": "time", "line": 13}], "services.patient_service": [{"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 9}, {"type": "import", "module": "sqlite3", "line": 10}, {"type": "from", "module": "PySide6.QtCore", "name": "QObject", "line": 15}, {"type": "from", "module": "utils.db_helpers", "name": "generate_patient_id", "line": 16}, {"type": "from", "module": "utils.db_helpers", "name": "build_where_clause", "line": 16}], "services.plan_a_classifier": [{"type": "from", "module": "__future__", "name": "annotations", "line": 16}, {"type": "import", "module": "json", "line": 18}], "services.plan_a_manager": [{"type": "from", "module": "__future__", "name": "annotations", "line": 7}], "services.reference_data_service": [{"type": "from", "module": "pathlib", "name": "Path", "line": 11}], "services.report_service": [{"type": "from", "module": "typing", "name": "Optional", "line": 9}, {"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 9}], "services.training_session_manager": [{"type": "import", "module": "threading", "line": 11}, {"type": "from", "module": "typing", "name": "List", "line": 12}], "services.treatment_service": [{"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 9}], "services.user_service": [{"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 9}], "services.bluetooth.standard_bleak_manager": [{"type": "import", "module": "os", "line": 10}, {"type": "from", "module": "typing", "name": "Optional", "line": 13}, {"type": "from", "module": "typing", "name": "Callable", "line": 13}], "services.bluetooth.__init__": [{"type": "from", "module": "standard_bleak_manager", "name": "StandardBleakManager", "line": 10}, {"type": "from", "module": "standard_bleak_manager", "name": "ConnectionState", "line": 10}], "services.data_protection.__init__": [{"type": "from", "module": "hardware_fingerprint_protector", "name": "HardwareFingerprintDataProtector", "line": 7}, {"type": "from", "module": "hardware_fingerprint_protector", "name": "RehabilitationDataCollector", "line": 7}], "services.eeg_preprocessing.kalman_processor": [{"type": "from", "module": "typing", "name": "Optional", "line": 16}, {"type": "import", "module": "warnings", "line": 17}], "services.eeg_preprocessing.preprocessing_config": [{"type": "import", "module": "numpy", "line": 12}, {"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 13}], "services.eeg_preprocessing.preprocessing_pipeline": [{"type": "from", "module": "typing", "name": "Optional", "line": 16}, {"type": "from", "module": "typing", "name": "Callable", "line": 16}], "services.eeg_preprocessing.realtime_integration_manager": [{"type": "import", "module": "time", "line": 13}], "services.eeg_preprocessing.rls_filter": [{"type": "from", "module": "typing", "name": "Optional", "line": 9}], "services.eeg_preprocessing.traditional_filter": [{"type": "from", "module": "typing", "name": "Optional", "line": 14}, {"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 14}], "services.eeg_preprocessing.__init__": [{"type": "from", "module": "preprocessing_config", "name": "PreprocessingConfig", "line": 19}, {"type": "from", "module": "preprocessing_config", "name": "QuickConfigs", "line": 19}, {"type": "from", "module": "traditional_filter", "name": "TraditionalFilterProcessor", "line": 20}, {"type": "from", "module": "preprocessing_pipeline", "name": "EEGPreprocessingPipeline", "line": 21}, {"type": "from", "module": "realtime_integration_manager", "name": "RealtimeIntegrationManager", "line": 22}], "services.eeg_processing.eeg_data_processor": [{"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 10}], "services.eeg_processing.__init__": [{"type": "from", "module": "eeg_data_processor", "name": "EEGDataProcessor", "line": 7}], "services.stimulation.__init__": [{"type": "from", "module": "stimulation_device", "name": "StimulationDevice", "line": 7}, {"type": "from", "module": "stimulation_device", "name": "StimulationParameters", "line": 7}, {"type": "from", "module": "stimulation_device", "name": "StimulationDeviceStatus", "line": 7}], "tools.analyze_project_usage": [{"type": "from", "module": "__future__", "name": "annotations", "line": 13}, {"type": "import", "module": "sys", "line": 15}], "ui.login_window": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QWidget", "line": 9}, {"type": "from", "module": "PySide6.QtGui", "name": "<PERSON><PERSON><PERSON><PERSON>", "line": 15}, {"type": "from", "module": "PySide6.QtGui", "name": "QP<PERSON>ter<PERSON><PERSON>", "line": 15}, {"type": "from", "module": "PySide6.QtGui", "name": "QBrush", "line": 15}, {"type": "from", "module": "pathlib", "name": "Path", "line": 19}], "ui.main_window": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QFrame", "line": 10}, {"type": "from", "module": "PySide6.QtCore", "name": "Qt", "line": 14}, {"type": "from", "module": "PySide6.QtGui", "name": "QFont", "line": 15}], "ui.components.chart_widgets": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QScrollArea", "line": 9}], "ui.components.filter_panel": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QHBoxLayout", "line": 9}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QButtonGroup", "line": 9}, {"type": "from", "module": "typing", "name": "List", "line": 17}], "ui.components.interactive_chart": [{"type": "import", "module": "numpy", "line": 15}, {"type": "from", "module": "typing", "name": "<PERSON><PERSON>", "line": 16}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QWidget", "line": 17}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QVBoxLayout", "line": 17}, {"type": "from", "module": "PySide6.QtCore", "name": "QTimer", "line": 18}, {"type": "from", "module": "PySide6.QtGui", "name": "QFont", "line": 19}], "ui.components.no_wheel_widgets": [{"type": "from", "module": "PySide6.QtCore", "name": "Qt", "line": 10}], "ui.components.parameter_adjuster": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QSizePolicy", "line": 10}], "ui.components.pyqtgraph_curves_widget": [{"type": "from", "module": "typing", "name": "Optional", "line": 17}, {"type": "from", "module": "typing", "name": "List", "line": 17}, {"type": "from", "module": "PySide6.QtCore", "name": "QTimer", "line": 20}], "ui.components.sidebar": [{"type": "from", "module": "PySide6.QtCore", "name": "QRect", "line": 14}, {"type": "from", "module": "PySide6.QtCore", "name": "QTimer", "line": 14}, {"type": "from", "module": "PySide6.QtGui", "name": "QPixmap", "line": 15}, {"type": "from", "module": "PySide6.QtGui", "name": "<PERSON><PERSON><PERSON><PERSON>", "line": 15}, {"type": "from", "module": "PySide6.QtGui", "name": "QLinearGradient", "line": 15}, {"type": "from", "module": "PySide6.QtGui", "name": "QBrush", "line": 15}, {"type": "from", "module": "PySide6.QtSvgWidgets", "name": "QSvgWidget", "line": 16}, {"type": "from", "module": "typing", "name": "Dict", "line": 17}, {"type": "from", "module": "typing", "name": "List", "line": 17}], "ui.components.status_indicator": [{"type": "from", "module": "PySide6.QtCore", "name": "Qt", "line": 13}, {"type": "from", "module": "PySide6.QtCore", "name": "QPropertyAnimation", "line": 13}, {"type": "from", "module": "PySide6.QtCore", "name": "QEasingCurve", "line": 13}], "ui.components.themed_message_box": [{"type": "from", "module": "PySide6.QtGui", "name": "<PERSON><PERSON><PERSON><PERSON>", "line": 11}, {"type": "from", "module": "PySide6.QtGui", "name": "QBrush", "line": 11}], "ui.components.topbar": [{"type": "from", "module": "PySide6.QtGui", "name": "QPixmap", "line": 15}], "ui.dialogs.activation_dialog": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QTextEdit", "line": 9}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QProgressBar", "line": 9}, {"type": "from", "module": "PySide6.QtGui", "name": "QP<PERSON>ter<PERSON><PERSON>", "line": 15}, {"type": "from", "module": "PySide6.QtGui", "name": "QBrush", "line": 15}, {"type": "from", "module": "PySide6.QtGui", "name": "<PERSON><PERSON><PERSON><PERSON>", "line": 15}], "ui.pages.patients_page": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QTabWidget", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QListWidget", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QListWidgetItem", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QSplitter", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QMessageBox", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QDialogButtonBox", "line": 10}, {"type": "from", "module": "PySide6.QtCore", "name": "QTimer", "line": 18}, {"type": "from", "module": "PySide6.QtCore", "name": "QThread", "line": 18}, {"type": "from", "module": "PySide6.QtGui", "name": "QPixmap", "line": 19}, {"type": "from", "module": "ui.components.themed_message_box", "name": "show_question", "line": 25}, {"type": "from", "module": "datetime", "name": "datetime", "line": 26}, {"type": "import", "module": "os", "line": 27}], "ui.pages.reports_page": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QLabel", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QFrame", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QGridLayout", "line": 10}, {"type": "from", "module": "PySide6.QtGui", "name": "QFont", "line": 15}, {"type": "from", "module": "ui.components.modern_card", "name": "ModernCard", "line": 23}], "ui.pages.settings_page": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QSpinBox", "line": 12}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QDoubleSpinBox", "line": 12}, {"type": "from", "module": "PySide6.QtCore", "name": "QTimer", "line": 19}, {"type": "from", "module": "PySide6.QtGui", "name": "QIcon", "line": 20}, {"type": "from", "module": "PySide6.QtSvgWidgets", "name": "QSvgWidget", "line": 21}, {"type": "from", "module": "ui.components.themed_message_box", "name": "show_warning", "line": 24}], "ui.pages.treatment_page": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QComboBox", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QRadioButton", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QButtonGroup", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QSpacerItem", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QSizePolicy", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QFrame", "line": 10}, {"type": "from", "module": "PySide6.QtGui", "name": "<PERSON><PERSON><PERSON><PERSON>", "line": 16}, {"type": "from", "module": "PySide6.QtGui", "name": "QColor", "line": 16}, {"type": "from", "module": "PySide6.QtGui", "name": "QLinearGradient", "line": 16}, {"type": "from", "module": "PySide6.QtGui", "name": "QPen", "line": 16}, {"type": "from", "module": "collections", "name": "deque", "line": 21}, {"type": "from", "module": "ui.components.status_indicator", "name": "StatusIndicator", "line": 26}, {"type": "from", "module": "ui.components.status_indicator", "name": "ConnectionStatus", "line": 26}, {"type": "from", "module": "pathlib", "name": "Path", "line": 1422}], "ui.pages.users_page": [{"type": "from", "module": "PySide6.QtWidgets", "name": "QGridLayout", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QSpacerItem", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QSizePolicy", "line": 10}, {"type": "from", "module": "PySide6.QtWidgets", "name": "QDateEdit", "line": 10}, {"type": "from", "module": "PySide6.QtCore", "name": "QTimer", "line": 16}, {"type": "from", "module": "PySide6.QtCore", "name": "QDate", "line": 16}, {"type": "from", "module": "PySide6.QtGui", "name": "QPixmap", "line": 17}, {"type": "from", "module": "PySide6.QtGui", "name": "<PERSON><PERSON><PERSON><PERSON>", "line": 17}, {"type": "from", "module": "PySide6.QtGui", "name": "QColor", "line": 17}, {"type": "from", "module": "PySide6.QtSvgWidgets", "name": "QSvgWidget", "line": 18}, {"type": "from", "module": "typing", "name": "List", "line": 19}, {"type": "from", "module": "typing", "name": "Optional", "line": 19}, {"type": "from", "module": "utils.user_helpers", "name": "validate_user_data", "line": 24}, {"type": "from", "module": "utils.user_helpers", "name": "format_user_for_ui", "line": 24}, {"type": "from", "module": "utils.user_helpers", "name": "get_role_display_name", "line": 24}, {"type": "from", "module": "utils.user_helpers", "name": "get_status_display_name", "line": 24}], "utils.chart_helpers": [{"type": "import", "module": "matplotlib.patches", "line": 12}, {"type": "import", "module": "numpy", "line": 14}], "utils.db_helpers": [{"type": "from", "module": "typing", "name": "Optional", "line": 10}, {"type": "from", "module": "typing", "name": "List", "line": 10}], "utils.path_manager": [{"type": "import", "module": "os", "line": 11}, {"type": "from", "module": "typing", "name": "Optional", "line": 13}], "utils.user_helpers": [{"type": "from", "module": "datetime", "name": "datetime", "line": 11}], "utils.warning_manager": [{"type": "from", "module": "typing", "name": "List", "line": 17}], "utils.__init__": [{"type": "from", "module": "db_helpers", "name": "*", "line": 9}]}, "suspect_unused_defs": {"build_package": {"unused_functions": [["check_dependencies", 27]], "unused_classes": []}, "core.simple_voice": {"unused_functions": [["disable_voice", 251], ["enable_voice", 259]], "unused_classes": []}, "core.udp_communicator": {"unused_functions": [["initialize_udp_communicator", 183]], "unused_classes": []}, "services.training_session_manager": {"unused_functions": [], "unused_classes": [["PersonalizedTrainingConfig", 50]]}, "services.data_protection.hardware_fingerprint_protector": {"unused_functions": [], "unused_classes": [["RehabilitationDataCollector", 228]]}, "services.eeg_preprocessing.kalman_processor": {"unused_functions": [], "unused_classes": [["KalmanFilterProcessor", 22]]}, "services.eeg_preprocessing.preprocessing_config": {"unused_functions": [], "unused_classes": [["QuickConfigs", 160]]}, "services.eeg_preprocessing.preprocessing_pipeline": {"unused_functions": [], "unused_classes": [["EEGPreprocessingPipeline", 24]]}, "services.eeg_preprocessing.rls_filter": {"unused_functions": [], "unused_classes": [["RLS<PERSON>ilter", 13]]}, "services.eeg_preprocessing.traditional_filter": {"unused_functions": [], "unused_classes": [["TraditionalFilterProcessor", 19]]}, "services.eeg_processing.eeg_data_processor": {"unused_functions": [], "unused_classes": [["EEGDataProcessor", 14]]}, "ui.components.chart_widgets": {"unused_functions": [], "unused_classes": [["ComparisonChart<PERSON><PERSON>t", 422]]}, "ui.components.interactive_chart": {"unused_functions": [], "unused_classes": [["InteractiveChartCanvas", 22]]}, "ui.components.stats_card": {"unused_functions": [], "unused_classes": [["TrendStatsCard", 147], ["ProgressStatsCard", 208]]}, "ui.components.status_indicator": {"unused_functions": [], "unused_classes": [["ConnectionStatus", 256]]}, "ui.components.themed_message_box": {"unused_functions": [["_create_themed_message_box", 19], ["_get_global_stylesheet", 42]], "unused_classes": []}, "utils.db_helpers": {"unused_functions": [["generate_patient_id", 167], ["safe_get_value", 173], ["build_where_clause", 180]], "unused_classes": []}, "utils.path_manager": {"unused_functions": [["get_lib_file", 306]], "unused_classes": []}, "utils.user_helpers": {"unused_functions": [["validate_user_data", 14], ["format_user_for_ui", 106], ["format_user_for_db", 150], ["get_role_display_name", 188], ["get_status_display_name", 205], ["generate_username_suggestions", 248], ["safe_get_user_value", 317], ["create_user_search_filter", 334]], "unused_classes": []}, "utils.warning_manager": {"unused_functions": [["restore_warnings", 204]], "unused_classes": []}}, "keyword_hits": {"build_package": [[68, "shutil.rmtree(dir_name)"], [188, "\"--hidden-import=pyqtgraph.parametertree\","], [237, "\"--hidden-import=sklearn.svm\","], [238, "\"--hidden-import=sklearn.ensemble\","], [270, "\"--hidden-import=app.application\","], [276, "\"--hidden-import=core.udp_communicator\","], [287, "# 方案A：包含PlanA模块，移除旧的weighted_voting与feature_extraction模块"], [287, "# 方案A：包含PlanA模块，移除旧的weighted_voting与feature_extraction模块"], [287, "# 方案A：包含PlanA模块，移除旧的weighted_voting与feature_extraction模块"], [464, "shutil.rmtree(mpl_data_dir)"], [542, "critical_files = ["], [543, "# 方案A移除 weighted_voting_config.json"], [551, "for file_name in critical_files:"], [634, "shutil.rmtree(dir_name)"]], "license_generator": [[125, "SECRET_KEY = \"BCI-2024-Medical-Recovery-System\""]], "main": [[66, "from PySide6.QtWidgets import QApplication, QMessageBox"], [71, "from app.application import BCIApplication"], [74, "def setup_application():"], [77, "QApplication.setHighDpiScaleFactorRoundingPolicy("], [85, "QApplication.setFont(font)"], [94, "setup_application()"], [97, "app = BCIApplication(sys.argv)"], [104, "QMessageBox.critical("], [125, "QMessageBox.critical("]], "app.application": [[4, "Application Main Class"], [10, "from PySide6.QtWidgets import QApplication, QMessageBox"], [19, "class BCIApplication(QApplication):"], [23, "application_started = Signal()"], [24, "application_closing = Signal()"], [40, "self.setApplicationName(\"脑机接口康复训练系统\")"], [56, "QMessageBox.critical("], [110, "success, message = auth_service.authenticate(username, self.login_window.password_input.text())"], [207, "self.application_started.emit()"], [224, "from core.udp_communicator import get_udp_communicator"], [225, "udp_comm = get_udp_communicator()"], [246, "self.application_closing.emit()"]], "app.config": [[4, "Application Configuration Management"], [41, "\"theme\": \"tech\",  # medical 或 tech"]], "app.__init__": [[4, "Application Core Module"]], "core.udp_communicator": [[5, "UDP Communicator Module"], [27, "class UDPCommunicator(QObject):"], [170, "_udp_communicator: Optional[UDPCommunicator] = None"], [173, "def get_udp_communicator() -> UDPCommunicator:"], [175, "global _udp_communicator"], [177, "if _udp_communicator is None:"], [178, "_udp_communicator = UDPCommunicator()"], [180, "return _udp_communicator"], [183, "def initialize_udp_communicator(vr_host: str = \"127.0.0.1\", vr_port: int = 3004, local_port: int = 3005) -> UDPCommunicator:"], [193, "UDPCommunicator: UDP通信器实例"], [195, "global _udp_communicator"], [197, "_udp_communicator = UDPCommunicator(vr_host, vr_port, local_port)"], [198, "return _udp_communicator"]], "services.auth_service": [[4, "User Authentication Service"], [73, "def authenticate(self, username: str, password: str) -> <PERSON><PERSON>[bool, str]:"], [119, "def is_authenticated(self) -> bool:"]], "services.classifier_training_manager": [[58, "feature_type_mapping = {"], [59, "'fbcsp_svm': 'fbcsp',"], [59, "'fbcsp_svm': 'fbcsp',"], [63, "'plv_svm': 'plv'"], [66, "for classifier_name, feature_type in feature_type_mapping.items():"], [72, "'feature_type': feature_type"], [80, "'feature_type': feature_type"], [87, "feature_type_mapping = {"], [88, "'fbcsp_svm': 'fbcsp',"], [88, "'fbcsp_svm': 'fbcsp',"], [92, "'plv_svm': 'plv'"], [96, "for classifier_name, feature_type in feature_type_mapping.items():"], [99, "'feature_type': feature_type"], [114, "from sklearn.svm import SVC"], [116, "elif classifier_type == 'RandomForestClassifier':"], [117, "from sklearn.ensemble import RandomForestClassifier"], [117, "from sklearn.ensemble import RandomForestClassifier"], [118, "return RandomForestClassifier(**params)"], [119, "elif classifier_type == 'LogisticRegression':"], [120, "from sklearn.linear_model import LogisticRegression"], [121, "return LogisticRegression(**params)"], [140, "from pyriemann.classification import MeanField"], [148, "from sklearn.svm import SVC"], [153, "if classifier_name == 'fbcsp_svm':"], [153, "if classifier_name == 'fbcsp_svm':"], [154, "return self._create_optimized_svm_classifier()"], [160, "return self._create_optimized_logistic_regression_classifier()"], [164, "def _create_optimized_svm_classifier(self):"], [165, "\"\"\"创建优化的SVM分类器\"\"\""], [175, "logger.warning(f\"优化SVM分类器创建失败: {e}，使用默认配置\")"], [181, "return RandomForestClassifier("], [186, "max_features=0.5,         # 限制特征选择比例"], [193, "return RandomForestClassifier(n_estimators=100, random_state=42)"], [198, "from pyriemann.classification import MeanField"], [208, "logger.warning(\"pyriemann未安装或版本过低，使用优化SVM替代MeanField\")"], [209, "return self._create_optimized_svm_classifier()"], [211, "logger.warning(f\"优化MeanField分类器创建失败: {e}，使用优化SVM替代\")"], [212, "return self._create_optimized_svm_classifier()"], [214, "def _create_optimized_logistic_regression_classifier(self):"], [217, "return LogisticRegression("], [226, "return LogisticRegression(C=1.0, max_iter=1000, random_state=42, solver='lbfgs')"], [231, "from pyriemann.classification import MeanField"], [236, "logger.warning(\"pyriemann未安装或版本过低，使用SVM替代MeanField\")"], [239, "logger.warning(f\"MeanField分类器创建失败: {e}，使用SVM替代\")"], [242, "def _create_logistic_regression_classifier(self):"], [246, "return LogisticRegression("], [253, "logger.warning(f\"逻辑回归分类器创建失败: {e}，使用SVM替代\")"], [256, "# 旧的train_voting_system方法已删除 - 系统仅支持高性能Stacking训练"], [257, "# 请使用train_weighted_voting_system方法"], [259, "def _clean_features(self, features: np.ndarray, feature_type: str) -> np.ndarray:"], [263, "if np.any(np.isinf(features)) or np.any(np.isnan(features)):"], [264, "logger.warning(f\"{feature_type}特征包含无穷值或NaN，进行清理\")"], [267, "features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)"], [270, "if np.all(features == 0):"], [271, "logger.warning(f\"{feature_type}特征全为0，使用随机值替代\")"], [272, "features = np.random.normal(0, 0.01, features.shape)"], [275, "max_val = np.max(np.abs(features))"], [277, "logger.warning(f\"{feature_type}特征值过大({max_val})，进行缩放\")"], [278, "features = features / (max_val / 1000)  # 缩放到合理范围"], [280, "return features.astype(np.float32)"], [283, "logger.error(f\"清理{feature_type}特征失败: {e}\")"], [285, "return features"], [315, "def _evaluate_classifier(self, classifier, features: np.ndarray, y: np.ndarray) -> List[float]:"], [327, "scores = cross_val_score(classifier, features, y, cv=cv, scoring='accuracy')"], [334, "def _save_voting_system(self, voting_system,"], [337, "filename = f\"{patient_id}_{version_suffix}_voting_system.pkl\""], [341, "pickle.dump(voting_system, f)"], [344, "latest_filename = f\"{patient_id}_latest_voting_system.pkl\""], [358, "def _save_performance_report(self, voting_system,"], [371, "for name, performance in voting_system.performance.items():"], [473, "def train_weighted_voting_system(self, X: np.ndar<PERSON>, y: np.ndarray,"], [474, "feature_extractors: Dict[str, Any],"], [474, "feature_extractors: Dict[str, Any],"], [482, "feature_extractors: 特征提取器字典"], [482, "feature_extractors: 特征提取器字典"], [492, "from services.weighted_voting_classifier import WeightedVotingManager"], [495, "voting_manager = WeightedVotingManager()"], [503, "feature_type = config['feature_type']"], [507, "if feature_type not in feature_extractors:"], [507, "if feature_type not in feature_extractors:"], [508, "logger.warning(f\"未找到{feature_type}特征提取器，跳过{name}\")"], [511, "feature_extractor = feature_extractors[feature_type]"], [511, "feature_extractor = feature_extractors[feature_type]"], [514, "logger.info(f\"使用{feature_type}特征提取器提取特征...\")"], [515, "features = feature_extractor.transform(X)"], [515, "features = feature_extractor.transform(X)"], [516, "logger.info(f\"{feature_type}特征形状: {features.shape}\")"], [519, "if feature_type == 'riemannian' and self._is_riemannian_classifier(classifier):"], [521, "features = self._get_covariance_matrices(X)"], [522, "logger.info(f\"Riemannian协方差矩阵形状: {features.shape}\")"], [525, "features = self._clean_features(features, feature_type)"], [528, "cv_scores = self._evaluate_classifier(classifier, features, y)"], [532, "classifier.fit(features, y)"], [537, "accuracy=accuracy_score(y, classifier.predict(features)),"], [548, "'feature_extractor': feature_extractor,"], [548, "'feature_extractor': feature_extractor,"], [565, "voting_manager.fit(X, y, classifiers_data)"], [571, "voting_manager.initialize_dynamic_weights(dynamic_config)"], [583, "voting_manager.initialize_classifier_smoothing(classifier_configs)"], [589, "save_path = self._save_weighted_voting_system(voting_manager, patient_id, version_suffix)"], [590, "saved_files['weighted_voting_system'] = save_path"], [593, "report_path = self._save_weighted_voting_report(voting_manager, patient_id, version_suffix)"], [603, "def _save_weighted_voting_system(self, voting_manager, patient_id: str, version_suffix: str) -> str:"], [606, "filename = f\"weighted_voting_system_{patient_id}_{version_suffix}.pkl\""], [610, "pickle.dump(voting_manager, f)"], [615, "latest_filename = f\"weighted_voting_system_{patient_id}_latest.pkl\""], [642, "def _save_weighted_voting_report(self, voting_manager, patient_id: str, version_suffix: str) -> str:"], [646, "status = voting_manager.get_system_status()"], [674, "for name, weight in status['voting_weights'].items():"], [678, "filename = f\"weighted_voting_report_{patient_id}_{version_suffix}.txt\""], [687, "latest_filename = f\"weighted_voting_report_{patient_id}_latest.txt\""]], "services.license_manager": [[152, "SECRET_KEY = \"BCI-2024-Medical-Recovery-System\""]], "services.multi_round_training_manager": [[28, "feature_files: Dict[str, str]"], [63, "self.features_dir = os.path.join(self.base_dir, \"features\")"], [98, "def load_historical_data(self) -> Tuple[np.ndarray, np.ndarray]:"], [151, "historical_X, historical_y = self.load_historical_data()"], [157, "if historical_X.size > 0:"], [158, "self.accumulated_data['X'].append(historical_X)"], [159, "self.accumulated_data['y'].append(historical_y)"], [160, "logger.info(f\"第{self.current_round}轮训练开始，累积历史样本: {historical_X.shape[0]}\")"], [208, "def complete_round(self, feature_files: Dict[str, str],"], [217, "feature_files: 保存的特征文件路径字典"], [241, "feature_files=feature_files.copy(),"]], "services.plan_a_classifier": [[9, "- 分支B：Tangent Space + Logistic Regression（各带切空间拼接）"], [29, "from pyriemann.classification import MDM"], [37, "from sklearn.linear_model import LogisticRegression"], [130, "self._lr: Optional[LogisticRegression] = None"], [150, "def _stack_tangent_features(self, X: np.ndarray) -> np.ndarray:"], [156, "features: [n_trials, n_features_total]"], [219, "feats = self._stack_tangent_features(X)"], [220, "lr = LogisticRegression(C=1.0, max_iter=2000, solver=\"lbfgs\", random_state=42)"], [256, "feats = self._stack_tangent_features(X)"]], "services.plan_a_manager": [[111, "\"voting_weights\": {},"], [115, "\"classification_details\": ("]], "services.training_session_manager": [[148, "self.feature_manager = None"], [232, "def set_feature_manager(self, feature_manager):"], [234, "self.feature_manager = feature_manager"], [838, "feature_files={},"], [872, "def _load_latest_feature_extractors(self) -> Dict[str, Any]:"], [872, "def _load_latest_feature_extractors(self) -> Dict[str, Any]:"], [875, "# 兼容旧接口，已不再使用BaseFeatureExtractor"], [875, "# 兼容旧接口，已不再使用BaseFeatureExtractor"], [877, "extractors = {}"], [879, "feature_types = self.feature_manager.get_enabled_extractors()"], [879, "feature_types = self.feature_manager.get_enabled_extractors()"], [881, "for feature_type in feature_types:"], [885, "latest_file = str(get_data_file(f\"features/{self.patient_id}_latest_{feature_type}.pkl\"))"], [887, "extractor = None  # 特征提取器已移除，兼容占位"], [888, "extractors[feature_type] = extractor"], [888, "extractors[feature_type] = extractor"], [889, "logger.debug(f\"加载{feature_type}特征提取器成功\")"], [891, "logger.warning(f\"未找到{feature_type}特征提取器: {latest_file}\")"], [894, "logger.error(f\"加载{feature_type}特征提取器失败: {e}\")"], [897, "return extractors"]], "services.user_service": [[300, "def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:"]], "services.bluetooth.standard_bleak_manager": [[791, "await self._enable_data_notifications(client)"], [829, "await self._disable_data_notifications(client)"], [888, "async def _enable_data_notifications(self, client):"], [897, "await client.start_notify(read_char_uuid, self._notification_handler)"], [904, "async def _disable_data_notifications(self, client):"], [915, "def _notification_handler(self, sender, data):"]], "services.eeg_preprocessing.realtime_integration_manager": [[75, "def _extract_window(self):"], [198, "self._extract_window()"], [304, "'can_extract_window': len(self.data_buffer) >= self.window_samples"]], "services.eeg_preprocessing.rls_filter": [[116, "self._check_numerical_stability()"], [225, "def _check_numerical_stability(self):"]], "services.eeg_processing.eeg_data_processor": [[259, "self.packets_extracted = 0"], [261, "self.last_extract_time = 0"], [287, "packets = self._extract_complete_packets()"], [292, "self.last_extract_time = process_time"], [297, "def _extract_complete_packets(self) -> List[bytes]:"], [332, "self.packets_extracted += 1"], [333, "self.logger.debug(f\"成功提取数据包#{self.packets_extracted}\")"], [362, "\"packets_extracted\": self.packets_extracted,"], [365, "\"last_extract_time_ms\": self.last_extract_time,"], [366, "\"extraction_rate\": self.packets_extracted / max(1, self.bytes_processed / 100)"], [372, "self.packets_extracted = 0"], [374, "self.last_extract_time = 0"]], "services.stimulation.__init__": [[4, "Electrical Stimulation Device Service Module"]], "tools.analyze_project_usage": [[119, "tree = ast.parse(src, filename=str(path))"], [121, "for node in ast.walk(tree):"], [124, "UsageVisitor(mi).visit(tree)"], [300, "# keyword scan for legacy voting/feature extraction/classifier"], [300, "# keyword scan for legacy voting/feature extraction/classifier"], [300, "# keyword scan for legacy voting/feature extraction/classifier"], [302, "'voting', 'ensemble', 'bagging', 'boosting',"], [302, "'voting', 'ensemble', 'bagging', 'boosting',"], [302, "'voting', 'ensemble', 'bagging', 'boosting',"], [302, "'voting', 'ensemble', 'bagging', 'boosting',"], [303, "'feature', 'extract', 'csp', 'ica', 'wavelet',"], [303, "'feature', 'extract', 'csp', 'ica', 'wavelet',"], [303, "'feature', 'extract', 'csp', 'ica', 'wavelet',"], [303, "'feature', 'extract', 'csp', 'ica', 'wavelet',"], [303, "'feature', 'extract', 'csp', 'ica', 'wavelet',"], [304, "'spectral', 'spectrum', 'psd', 'bandpower',"], [304, "'spectral', 'spectrum', 'psd', 'bandpower',"], [304, "'spectral', 'spectrum', 'psd', 'bandpower',"], [304, "'spectral', 'spectrum', 'psd', 'bandpower',"], [305, "'svm', 'knn', 'randomforest', 'xgboost',"], [305, "'svm', 'knn', 'randomforest', 'xgboost',"], [305, "'svm', 'knn', 'randomforest', 'xgboost',"], [305, "'svm', 'knn', 'randomforest', 'xgboost',"], [306, "'lda', 'logistic', 'bayes', 'naive', 'tree',"], [306, "'lda', 'logistic', 'bayes', 'naive', 'tree',"], [306, "'lda', 'logistic', 'bayes', 'naive', 'tree',"], [306, "'lda', 'logistic', 'bayes', 'naive', 'tree',"], [306, "'lda', 'logistic', 'bayes', 'naive', 'tree',"], [307, "'multiclass', 'one-vs', 'ovo', 'ovr'"], [307, "'multiclass', 'one-vs', 'ovo', 'ovr'"], [307, "'multiclass', 'one-vs', 'ovo', 'ovr'"], [307, "'multiclass', 'one-vs', 'ovo', 'ovr'"]], "ui.login_window": [[12, "QGraphicsDropShadowEffect, QApplication, QComboBox"], [74, "screen = QApplication.primaryScreen().geometry()"], [453, "success, message = auth_service.authenticate(username, password)"]], "ui.main_window": [[148, "from PySide6.QtWidgets import QApplication"], [149, "screen = QApplication.primaryScreen().geometry()"], [260, "from PySide6.QtWidgets import QApplication"], [261, "QApplication.instance().setStyleSheet(stylesheet)"], [447, "from core.udp_communicator import get_udp_communicator"], [448, "udp_comm = get_udp_communicator()"]], "ui.components.filter_panel": [[61, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"]], "ui.components.interactive_chart": [[62, "if self.theme == 'medical':"]], "ui.components.parameter_adjuster": [[189, "self.layout_direction = layout_direction  # \"horizontal\" 或 \"vertical\""], [200, "if self.layout_direction == \"vertical\":"], [201, "self._init_vertical_ui()"], [247, "def _init_vertical_ui(self):"], [345, "initial_value: int = 3, layout_direction: str = \"vertical\", parent=None):"]], "ui.components.sidebar": [[292, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"]], "ui.components.stats_card": [[159, "self._create_trend_indicator()"], [161, "def _create_trend_indicator(self):"]], "ui.components.status_indicator": [[4, "Status Indicator Component"], [17, "class StatusIndicator(QWidget):"], [77, "self.setObjectName(\"status_indicator\")"], [256, "class ConnectionStatus(StatusIndicator):"]], "ui.components.themed_message_box": [[11, "from PySide6.QtGui import QFont, QColor, QPainter, QBrush, QGuiApplication"], [54, "# 方法2：从QApplication获取"], [56, "from PySide6.QtWidgets import QApplication"], [57, "app = QApplication.instance()"], [67, "from PySide6.QtWidgets import QApplication"], [68, "app = QApplication.instance()"], [110, "from PySide6.QtGui import QGuiApplication"], [115, "geo = QGuiApplication.primaryScreen().geometry()"], [138, "if self.current_theme == 'medical':"], [156, "shadow.setColor(QColor(0, 0, 0, 90 if self.current_theme != 'medical' else 70))"], [170, "'critical': self.theme_config['danger_color'],"], [252, "if self.icon_type in (\"warning\", \"critical\"):"], [274, "keyline = keyline_light if self.current_theme == 'medical' else keyline_dark"], [367, "\"critical\": \"✕\","], [418, "def show_critical(parent, title, text):"], [420, "msg_box = ThemedMessageBox(parent, title, text, \"critical\", QMessageBox.StandardButton.Ok)"], [433, "critical = show_critical"]], "ui.components.topbar": [[98, "elif current_theme == 'medical':"], [251, "self.medical_label = QLabel(\"浅色\")"], [252, "self.medical_label.setObjectName(\"theme_label\")"], [253, "self.medical_label.setCursor(Qt.CursorShape.PointingHandCursor)"], [257, "self.medical_label.setFont(theme_font)"], [258, "self.medical_label.mousePressEvent = lambda event: self._set_theme(\"medical\")"], [259, "layout.addWidget(self.medical_label)"], [284, "self.current_theme = \"medical\" if self.current_theme == \"tech\" else \"tech\""], [303, "self.medical_label.setProperty(\"active\", not is_tech)"], [307, "self.medical_label.style().unpolish(self.medical_label)"], [308, "self.medical_label.style().polish(self.medical_label)"]], "ui.components.unified_timer_manager": [[26, "classification_tick = Signal()  # 每500ms触发（分类）"], [43, "self.classification_callback: Optional[Callable] = None"], [50, "'classification_calls': 0,"], [80, "self.classification_tick.emit()"], [81, "if self.classification_callback:"], [82, "self.classification_callback()"], [83, "self.stats['classification_calls'] += 1"], [102, "def register_classification_callback(self, callback: Callable):"], [104, "self.classification_callback = callback"], [119, "self.classification_callback = None"], [131, "'classification_frequency': self.stats['classification_calls'] / uptime_seconds if uptime_seconds > 0 else 0,"]], "ui.dialogs.activation_dialog": [[12, "QApplication, QProgressBar, QGraphicsDropShadowEffect"], [59, "screen = QApplication.primaryScreen().geometry()"]], "ui.pages.patients_page": [[25, "from ui.components.themed_message_box import show_information, show_warning, show_critical, show_question"], [472, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [546, "self.detail_diagnosis.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [571, "self.detail_history.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [614, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [1107, "self.patients_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [1466, "show_critical(self, \"错误\", f\"创建患者时发生错误：{str(e)}\")"], [1480, "show_critical(self, \"错误\", f\"更新患者信息时发生错误：{str(e)}\")"], [1507, "show_critical(self, \"错误\", f\"出院操作时发生错误：{str(e)}\")"], [2103, "self._create_medical_info_section(form_layout)"], [2172, "def _create_medical_info_section(self, parent_layout):"], [2372, "show_critical(self, \"错误\", f\"检查患者状态时发生错误：{str(e)}\")"], [2404, "show_critical(self, \"错误\", f\"再次入院时发生错误：{str(e)}\")"]], "ui.pages.reports_page": [[103, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"]], "ui.pages.settings_page": [[16, "QMessageBox, QApplication, QDialog"], [24, "from ui.components.themed_message_box import show_information, show_warning, show_critical, show_question"], [212, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [225, "form_grid.setVerticalSpacing(12)"], [363, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [376, "form_grid.setVerticalSpacing(12)"], [597, "scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [610, "form_grid.setVerticalSpacing(12)"], [832, "show_critical(self, \"保存失败\", f\"保存设置时发生错误：{str(e)}\")"], [906, "show_critical(self, \"重置失败\", f\"重置设置时发生错误：{str(e)}\")"], [1041, "show_critical(self, \"密码错误\", \"管理员密码错误，无法解锁\")"], [1252, "# 方法1：从QApplication获取全局样式"], [1253, "from PySide6.QtWidgets import QApplication"], [1254, "app = QApplication.instance()"]], "ui.pages.treatment_page": [[26, "from ui.components.status_indicator import StatusIndicator, ConnectionStatus"], [61, "self.feature_manager = None  # 方案A不再使用特征管理器，仅保留占位避免大改"], [85, "# 🔧 难度等级到触发阈值的映射（与WeightedVotingClassifierSystem完全一致）"], [755, "layout_direction=\"vertical\""], [762, "\"trigger_threshold\", \"触发阈值\", 0.75, 0.50, 1.00, 0.01, 2, \"vertical\""], [1216, "from ui.components.themed_message_box import show_critical"], [1217, "show_critical(self, \"启动失败\", f\"启动VR软件时发生错误：\\n{str(e)}\")"], [1264, "from ui.components.themed_message_box import show_critical"], [1265, "show_critical(self, \"删除失败\", f\"删除患者数据时发生错误：\\n{str(e)}\")"], [1319, "from ui.components.themed_message_box import show_critical"], [1320, "show_critical(self, \"删除过程错误\", f\"删除过程中发生错误：\\n{str(e)}\")"], [1332, "elif 'features' in file_path or 'raw_training_data' in file_path:"], [1985, "self._initialize_feature_extraction()"], [1985, "self._initialize_feature_extraction()"], [1994, "def _initialize_feature_extraction(self):"], [1994, "def _initialize_feature_extraction(self):"], [2022, "# print(f\"启用特征算法: {config.get_enabled_extractors()}\")"], [2054, "if self.feature_manager:"], [2055, "self.session_manager.set_feature_manager(self.feature_manager)"], [2297, "if hasattr(self, 'classification_timer') and self.classification_timer and self.classification_timer.isActive():"], [2298, "self.classification_timer.stop()"], [2361, "features_dir = str(get_data_file(\"features\"))"], [2363, "if not os.path.exists(features_dir):"], [2367, "for filename in os.listdir(features_dir):"], [2524, "self._initialize_realtime_classification()"], [2546, "self._start_udp_communication()"], [2626, "def _start_udp_communication(self):"], [2630, "if not hasattr(self, 'udp_communicator'):"], [2631, "from core.udp_communicator import get_udp_communicator"], [2632, "self.udp_communicator = get_udp_communicator()"], [2635, "if not self.udp_communicator.is_connected:"], [2636, "if not self.udp_communicator.start_listening():"], [2641, "self.udp_communicator.send_treat_command()"], [2905, "self._initialize_realtime_classification()"], [2935, "self._initialize_realtime_classification()"], [3149, "if hasattr(self, 'udp_communicator') and self.udp_communicator:"], [3150, "self.udp_communicator.send_stopall_command()"], [3580, "if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:"], [3582, "self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=value)"], [3585, "actual_threshold = self.weighted_voting_manager.get_current_thresholds()"], [3590, "self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=value)"], [3591, "actual_threshold = self.weighted_voting_manager.get_current_thresholds()"], [3613, "if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:"], [3614, "self.weighted_voting_manager.set_difficulty_level(difficulty_level)"], [3616, "actual_threshold = self.weighted_voting_manager.get_current_thresholds()"], [3620, "self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=trigger_threshold)"], [3626, "if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:"], [3628, "self.weighted_voting_manager.set_difficulty_level(difficulty_level)"], [3749, "if hasattr(self, 'classification_timer') and self.classification_timer:"], [3751, "if self.classification_timer.isActive():"], [3752, "self.classification_timer.stop()"], [4004, "# 治疗时的分类由classification_timer严格控制"], [4007, "if self.pre_training_active and self.feature_manager:"], [4009, "if not hasattr(self.feature_manager, '_is_fitted') or not self.feature_manager._is_fitted:"], [4017, "features = self.feature_manager.transform(input_data)"], [4019, "if features is not None:"], [4020, "# 移除batch维度：[1, n_features] -> [n_features]"], [4021, "features = features[0]"], [4023, "print(f\"预训练窗口处理完成，特征维度: {features.shape if features is not None else 'None'}\")"], [4116, "def _perform_realtime_classification(self, features):"], [4116, "def _perform_realtime_classification(self, features):"], [4120, "if not hasattr(self, 'weighted_voting_manager') or not self.weighted_voting_manager:"], [4122, "self._load_weighted_voting_manager()"], [4124, "if not self.weighted_voting_manager or not self.weighted_voting_manager._is_fitted:"], [4126, "from ui.components.themed_message_box import show_critical"], [4131, "show_critical("], [4146, "# 准备输入数据 - features应该是窗口数据 [8, 250]"], [4147, "if features.ndim == 1:"], [4153, "input_data = features[np.newaxis, :, :] if features.ndim == 2 else features"], [4157, "self._monitor_data_quality(features)"], [4165, "detailed_result = self.weighted_voting_manager.predict_with_details(input_data)"], [4183, "# print(detailed_result['classification_details'])"], [4202, "if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:"], [4204, "self.weighted_voting_manager.reset_temporal_smoother()"], [4246, "voting_manager = training_manager.load_plan_a_model(patient_id, version='latest')"], [4248, "if voting_manager is None:"], [4253, "self.weighted_voting_manager = voting_manager"], [4256, "self.weighted_voting_manager.set_realtime_debug(enabled=True)"], [4258, "if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:"], [4259, "self.weighted_voting_manager.set_temporal_smoothing(True, method='ewma', alpha=0.2)"], [4270, "self.weighted_voting_manager.set_difficulty_level(difficulty_level)"], [4273, "self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=current_trigger_threshold)"], [4276, "actual_threshold = self.weighted_voting_manager.get_current_thresholds()"], [4282, "self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=current_trigger_threshold)"], [4283, "actual_threshold = self.weighted_voting_manager.get_current_thresholds()"], [4295, "self.weighted_voting_manager = None"], [4298, "def _load_weighted_voting_manager(self):"], [4302, "def _initialize_realtime_classification(self):"], [4313, "if hasattr(self, 'classification_timer') and self.classification_timer:"], [4315, "self.classification_timer.stop()"], [4316, "self.classification_timer.timeout.disconnect()"], [4321, "self.classification_timer = None"], [4327, "self.classification_count = 0"], [4328, "self.last_classification_time = 0"], [4363, "self.unified_timer_manager.register_classification_callback(self._perform_sliding_window_classification)"], [4394, "def _perform_sliding_window_classification(self):"], [4399, "if self.classification_count % 20 == 0:  # 减少日志频率"], [4404, "# if self.classification_count % 10 == 0:  # 每10次分类输出一次调试信息"], [4405, "#     print(f\"🔍 分类定时器触发 #{self.classification_count}\")"], [4418, "self._perform_realtime_classification(window_data)"], [4421, "self.classification_count += 1"], [4422, "self.last_classification_time = time.time()"], [4425, "self._update_realtime_curves_with_classification_data(window_data)"], [4429, "if self.classification_count % 20 == 0:  # 每20次输出一次数据不足警告"], [4440, "def _update_realtime_curves_with_classification_data(self, window_data: np.ndarray):"], [4458, "# def _compute_frequency_features(self, data):"], [4464, "# def _compute_topography_features(self, data):"], [4465, "# def _compute_balanced_features(self, data):"], [4576, "if hasattr(self, 'udp_communicator') and self.udp_communicator:"], [4577, "self.udp_communicator.send_start_command()"], [4679, "if hasattr(self, 'udp_communicator') and self.udp_communicator:"], [4680, "self.udp_communicator.send_stop_command()"], [4684, "def cleanup_duplicate(self):"]], "ui.pages.users_page": [[358, "self.users_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"], [1082, "from ui.components.themed_message_box import show_critical"], [1083, "show_critical(self, \"错误\", f\"创建用户时发生错误：{str(e)}\")"], [1103, "from ui.components.themed_message_box import show_critical"], [1104, "show_critical(self, \"错误\", f\"更新用户信息时发生错误：{str(e)}\")"], [1212, "scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)"]], "ui.themes.theme_manager": [[20, "\"medical\": self._get_medical_theme(),"], [24, "def _get_medical_theme(self) -> Dict[str, Any]:"], [622, "QLabel#dialog_icon_critical {{"], [850, "QTextEdit#info_value_text QScrollBar:vertical {{"], [856, "QTextEdit#info_value_text QScrollBar::handle:vertical {{"], [862, "QTextEdit#info_value_text QScrollBar::handle:vertical:hover {{"], [1316, "QRadioButton::indicator {{"], [1324, "QRadioButton::indicator:hover {{"], [1329, "QRadioButton::indicator:checked {{"], [1335, "QRadioButton::indicator:checked:hover {{"], [1341, "QRadioButton::indicator:disabled {{"], [1346, "QRadioButton::indicator:checked:disabled {{"], [1374, "QCheckBox::indicator {{"], [1382, "QCheckBox::indicator:hover {{"], [1387, "QCheckBox::indicator:checked {{"], [1393, "QCheckBox::indicator:checked:hover {{"], [1398, "QCheckBox::indicator:disabled {{"], [1403, "QCheckBox::indicator:checked:disabled {{"], [1483, "QScrollArea#users_scroll QScrollBar:vertical,"], [1484, "QScrollArea#patients_scroll QScrollBar:vertical {{"], [1491, "QScrollArea#users_scroll QScrollBar::handle:vertical,"], [1492, "QScrollArea#patients_scroll QScrollBar::handle:vertical {{"], [1498, "QScrollArea#users_scroll QScrollBar::handle:vertical:hover,"], [1499, "QScrollArea#patients_scroll QScrollBar::handle:vertical:hover {{"], [1503, "QScrollArea#users_scroll QScrollBar::add-line:vertical,"], [1504, "QScrollArea#users_scroll QScrollBar::sub-line:vertical,"], [1505, "QScrollArea#patients_scroll QScrollBar::add-line:vertical,"], [1506, "QScrollArea#patients_scroll QScrollBar::sub-line:vertical {{"], [1875, "QCheckBox#filter_checkbox::indicator {{"], [1883, "QCheckBox#filter_checkbox::indicator:checked {{"], [2017, "QScrollArea#filter_scroll_area QScrollBar:vertical {{"], [2023, "QScrollArea#filter_scroll_area QScrollBar::handle:vertical {{"], [2029, "QScrollArea#filter_scroll_area QScrollBar::handle:vertical:hover {{"], [2038, "RealtimeMetricsPanel {{"], [2044, "RealtimeMetricsPanel QFrame {{"], [2052, "RealtimeMetricsPanel QProgressBar {{"], [2062, "RealtimeMetricsPanel QProgressBar::chunk {{"], [2080, "RealtimeMetricsPanel QLabel {{"], [2089, "if theme_name == \"medical\":"]], "utils.chart_helpers": [[34, "if self.theme == 'medical':"]], "utils.simple_permission_manager": [[90, "return {'authenticated': False}"], [94, "'authenticated': True,"]]}}