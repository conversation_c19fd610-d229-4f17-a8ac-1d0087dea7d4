# -*- coding: utf-8 -*-
"""
禁用滚轮响应的自定义组件
No Wheel Response Widgets

为了防止在页面滚动时意外修改数字选择框和下拉框的值
"""

from PySide6.QtWidgets import QSpinBox, QDoubleSpinBox, QComboBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QWheelEvent


class NoWheelSpinBox(QSpinBox):
    """禁用滚轮响应的数字选择框"""
    
    def wheelEvent(self, event: QWheelEvent):
        """忽略滚轮事件"""
        event.ignore()


class NoWheelDoubleSpinBox(QDoubleSpinBox):
    """禁用滚轮响应的双精度数字选择框"""
    
    def wheelEvent(self, event: QWheelEvent):
        """忽略滚轮事件"""
        event.ignore()


class NoWheelComboBox(QComboBox):
    """禁用滚轮响应的下拉框"""
    
    def wheelEvent(self, event: QWheelEvent):
        """忽略滚轮事件"""
        event.ignore()
