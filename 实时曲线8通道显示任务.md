# Context
Filename: 实时曲线8通道显示任务.md
Created On: 2025-01-18
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
用户要求查看实时曲线时显示所有8个通道，当前只显示了4个通道（C3、C4、F3、F4），需要修改为显示全部8个通道（PZ、P3、P4、C3、CZ、C4、F3、F4）。

# Project Overview
脑机接口康复训练系统，基于PySide6的医疗器械级别应用。系统已经支持8通道EEG数据采集和处理，但在实时曲线显示组件中人为限制为4通道显示，目的是"性能优化"和专注于"运动皮层相关通道"。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 系统架构发现
- 系统完全支持8通道EEG数据：配置文件settings.json中channels设置为8
- 数据处理器EEGDataProcessor处理8通道数据：`CHANNELS = 8`
- 数据流路径完整：8通道数据从蓝牙接收 → 数据处理器 → 实时集成管理器 → 曲线显示组件
- 通道顺序：['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4']

## 当前4通道限制分析
- 文件：`ui/components/pyqtgraph_curves_widget.py`
- 第64-68行：硬编码选择4个通道 `['C3', 'C4', 'F3', 'F4']`
- 索引映射：`[3, 5, 6, 7]` - 从8通道数据中提取特定4通道
- 数据缓冲区：`np.zeros((4, self.buffer_size))` - 只为4通道分配空间
- 颜色配置：只定义了4种颜色
- Y轴偏移：只计算4通道的垂直间距

## 数据传递确认
- `ui/pages/treatment_page.py`的`_update_realtime_curves_only()`方法传递完整8通道数据
- `update_data()`方法接收8通道数据，但通过channel_indices提取4通道

## 性能考虑
- 注释显示"4通道优化版（50%资源节约）"
- 使用共享PlotWidget + 多曲线方案，相比8个独立PlotWidget降低85%资源消耗
- 当前方案已经是性能优化版，8通道显示不会显著影响性能

# Proposed Solution (Populated by INNOVATE mode)

## 解决方案分析

### 方案一：完全替换4通道为8通道显示
**优点**：实现简单直接，保持现有性能优化架构
**缺点**：显示区域需求增加，可能影响界面布局
**适用性**：最符合用户直接需求

### 方案二：可配置通道显示数量  
**优点**：灵活性强，用户可选择显示模式
**缺点**：增加代码复杂度和配置管理负担
**适用性**：过度设计，不适合当前需求

### 方案三：保持性能优化但扩展通道（推荐方案）
**优点**：平衡功能需求和性能，充分利用现有架构
**缺点**：需要仔细调整Y轴布局和颜色配置
**适用性**：最佳平衡方案

### 方案四：分组显示模式
**优点**：在复杂度和功能性之间平衡
**缺点**：不符合"显示所有8个通道"的明确需求
**适用性**：功能过复杂

## 选定方案：方案三

基于用户明确需求和系统现状，选择方案三：在现有共享PlotWidget架构基础上扩展为8通道显示。

### 技术可行性
- ✅ 数据流完整支持8通道
- ✅ 共享PlotWidget可容纳更多曲线  
- ✅ 性能瓶颈不在曲线数量而在数据处理
- ✅ 只需修改显示层配置，不涉及数据处理逻辑

### 核心修改点
1. 扩展通道配置：从4通道改为8通道
2. 调整数据缓冲区：从(4, buffer_size)改为(8, buffer_size)
3. 增加颜色配置：为8个通道定义不同颜色
4. 调整Y轴布局：合理分配8通道的垂直间距
5. 更新索引映射：移除通道筛选逻辑

# Implementation Plan (Generated by PLAN mode)

## 文件修改规格

**目标文件**: `ui/components/pyqtgraph_curves_widget.py`
**修改类型**: 功能扩展 - 4通道显示扩展为8通道显示

### 详细修改项

#### 修改项1: 通道配置扩展
- **文件**: `ui/components/pyqtgraph_curves_widget.py`
- **位置**: 第64-68行
- **理由**: 将硬编码的4通道配置改为完整8通道配置
- **具体变更**: 
  - 修改`self.channel_names`为完整8通道列表
  - 移除`self.channel_indices`索引映射逻辑
  - 移除`self.full_channel_names`（不再需要）

#### 修改项2: 数据缓冲区扩展
- **文件**: `ui/components/pyqtgraph_curves_widget.py` 
- **位置**: 第73行
- **理由**: 为8通道数据分配足够的缓冲空间
- **具体变更**: 数据缓冲区维度从(4, buffer_size)改为(8, buffer_size)

#### 修改项3: 颜色配置扩展
- **文件**: `ui/components/pyqtgraph_curves_widget.py`
- **位置**: 第84-89行  
- **理由**: 为8个通道定义高对比度颜色以确保视觉区分
- **具体变更**: 扩展颜色列表到8种不同颜色

#### 修改项4: Y轴布局优化
- **文件**: `ui/components/pyqtgraph_curves_widget.py`
- **位置**: Y轴偏移相关代码
- **理由**: 调整垂直间距以适应8通道显示
- **具体变更**: 可能需要调整`y_offset_step`参数

#### 修改项5: 数据更新逻辑简化
- **文件**: `ui/components/pyqtgraph_curves_widget.py`
- **位置**: `update_data`方法（第305-383行）
- **理由**: 移除通道筛选逻辑，直接处理8通道数据
- **具体变更**: 
  - 移除`selected_data = new_data[self.channel_indices, :]`
  - 简化数据处理流程
  - 保留数据验证逻辑

#### 修改项6: UI高度调整
- **文件**: `ui/components/pyqtgraph_curves_widget.py`
- **位置**: PlotWidget设置部分
- **理由**: 确保8通道能在界面中适当显示
- **具体变更**: 可能需要调整最小高度设置

## 实现检查清单

### Implementation Checklist:

1. 修改通道配置 - 将`self.channel_names`从4通道改为8通道列表
2. 移除索引映射逻辑 - 删除`self.channel_indices`和`self.full_channel_names`  
3. 扩展数据缓冲区 - 将`self.data_buffer`从(4, buffer_size)改为(8, buffer_size)
4. 扩展颜色配置 - 将`self.channel_colors`从4种颜色扩展为8种高对比度颜色
5. 调整Y轴布局参数 - 检查并调整`y_offset_step`以适应8通道垂直分布
6. 简化数据更新逻辑 - 在`update_data`方法中移除通道筛选，直接处理8通道数据
7. 更新数据验证逻辑 - 确保数据验证适配8通道要求
8. 调整UI高度设置 - 检查PlotWidget高度设置是否适合8通道显示
9. 更新相关注释 - 将代码注释中的"4通道"改为"8通道"
10. 测试验证 - 确认8通道数据能正确显示且性能无明显影响

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "完成代码修改 - 所有检查清单项目已执行完成"

# Task Progress (Appended by EXECUTE mode after each step completion)

* [2025-01-18]
  * Step: 检查清单项目1-9全部完成
  * Modifications: 
    - 修改了ui/components/pyqtgraph_curves_widget.py中的通道配置
    - 将self.channel_names从4通道改为8通道完整列表
    - 移除了self.channel_indices和self.full_channel_names索引映射逻辑
    - 将self.data_buffer从(4, buffer_size)改为(8, buffer_size)
    - 扩展self.channel_colors为8种高对比度颜色
    - 调整y_offset_step从30.0为25.0以适应8通道垂直分布
    - 简化update_data方法，移除通道筛选逻辑，直接处理8通道数据
    - 更新数据验证逻辑，要求严格匹配8通道
    - 确认UI高度设置(420)适合8通道显示
    - 更新所有相关注释从"4通道"改为"8通道"
  * Change Summary: 成功将实时曲线组件从4通道显示扩展为8通道完整显示
  * Reason: 执行计划步骤1-9，完成核心功能修改
  * Blockers: 无
  * Status: 发现问题，已修复

* [2025-01-18] 
  * Step: 修复Y轴偏移计算问题
  * Modifications:
    - 发现_calculate_channel_offsets方法中的关键缺陷
    - 原代码只为4个通道(C3,C4,F3,F4)定义了偏移位置
    - 其他4个通道(PZ,P3,P4,CZ)都使用默认值1.5，全部重叠在Y=0位置
    - 修复为全部8个通道定义不同的垂直偏移位置
    - 按照脑电学位置排列：F3(+87.5) → F4(+62.5) → C3(+37.5) → CZ(+12.5) → C4(-12.5) → P3(-37.5) → P4(-62.5) → PZ(-87.5)
    - Y轴自适应范围从-137.5到+137.5，确保8条曲线都能清晰显示
  * Change Summary: 修复通道重叠问题，现在8条曲线应该均匀分布显示
  * Reason: 解决用户反馈的"只看到5条曲线"问题
  * Blockers: 无  
  * Status: 发现新问题，已修复

* [2025-01-18]
  * Step: 实现动态通道偏移调整
  * Modifications:
    - 用户反馈：虽然8条曲线都能显示，但Y轴自动调整后通道间距太小，曲线挤在一起
    - 根本原因：Y轴范围根据数据动态调整（如-600到+600），但通道偏移量固定（-87.5到+87.5），相对间距太小
    - 解决方案：实现动态通道偏移调整
    - 修改_calculate_channel_offsets方法，支持根据数据范围动态计算间距
    - 通道总跨度占数据范围的40%，确保在任何数据范围下都有合理分离
    - 修改_update_y_range_if_needed方法，在调整Y轴范围时同时重新计算通道偏移
    - 例如：数据范围100μV时，通道间距约5.7μV；数据范围1000μV时，通道间距约57μV
  * Change Summary: 实现了智能动态通道间距，无论数据范围多大，8条曲线都能保持清晰分离
  * Reason: 解决用户反馈的"曲线挤在一起"问题
  * Blockers: 无
  * Status: 发现同步问题，已根本修复

* [2025-01-18]
  * Step: 修复Y轴范围与通道间距同步问题
  * Modifications:
    - 用户反馈：Y轴范围扩大到2000，但通道间距没有同步扩大，8条曲线仍然挤在中间小区域
    - 根本原因：计算逻辑错误 - 基于原始数据范围计算通道间距，但Y轴显示范围远大于数据范围
    - 举例：数据范围100μV，通道间距40μV，但Y轴显示范围280μV，导致40μV间距在280μV空间中显得很小
    - 解决方案：重新设计计算逻辑
      * 总显示范围 = 数据范围 × 4.0 （充分利用屏幕空间）  
      * 通道间距总跨度 = 总显示范围 × 75% （既保证分离又不过于分散）
      * 每个通道间距 = 通道间距总跨度 ÷ 7 （8通道分7个间隔）
    - 修改了_update_y_range_if_needed方法，实现完全同步的间距调整
    - 修改了_calculate_channel_offsets方法，统一计算逻辑
    - 修改了初始化Y轴范围设置，确保与动态调整一致
    - 示例效果：数据范围100μV时通道间距42.9μV；数据范围500μV时通道间距214.3μV
  * Change Summary: 实现了Y轴范围与通道间距的完全同步，通道间距充分利用整个Y轴空间
  * Reason: 解决用户反馈的"间距没有跟着Y轴扩大"问题
  * Blockers: 无
  * Status: 进一步界面优化完成

* [2025-01-18]
  * Step: 界面优化 - Y轴简化和动态横格线系统
  * Modifications:
    - 用户需求1：移除Y轴坐标轴刻度、刻度值和Y轴标签"脑电信号(μV)"
      * 移除了Y轴标签设置
      * 隐藏了Y轴刻度线
      * 隐藏了Y轴刻度值
      * 保留X轴时间标签
    - 用户需求2：横格线和通道名称的动态调整
      * 发现原有横格线固定在初始位置，不跟随动态调整
      * 实现动态横格线系统：
        - 添加channel_divider_lines和channel_divider_labels数组存储引用
        - 修改_add_channel_dividers方法存储横格线和标签引用
        - 新增_update_channel_dividers方法更新横格线位置
        - 在动态调整时同步调用横格线更新
      * 现在横格线和通道标签能完全跟随曲线间距动态调整
      * 用户可以通过横格线和右侧标签清晰识别每条曲线对应的通道
  * Change Summary: Y轴界面简化，横格线智能跟随，提升用户体验和可识别性
  * Reason: 根据用户明确需求进行界面优化
  * Blockers: 无
  * Status: 发现间距问题，已优化

* [2025-01-18]
  * Step: 优化通道间距 - 解决曲线重叠问题
  * Modifications:
    - 用户反馈：从截图看出通道间距不够，8条曲线相互重叠，不美观
    - 问题分析：
      * 通道总跨度只占显示范围的75%，导致曲线挤在中间区域
      * 上下有较多空白空间被浪费
      * 8条曲线集中在75%的空间里，间距不足造成重叠
    - 解决方案：增加通道间距比例
      * 将通道总跨度从75%提升到90%
      * 初始化和动态调整都统一使用90%比例
      * 让8条曲线更充分地分布在整个Y轴范围内
    - 效果对比：
      * 修改前：通道间距占75%，约21.4μV初始间距
      * 修改后：通道间距占90%，约25.7μV初始间距（提升20%）
      * 动态调整时间距也相应增加20%
  * Change Summary: 通道间距增加20%，曲线分布更均匀，减少重叠，提升视觉美观度
  * Reason: 解决用户反馈的"曲线相互重叠，不美观"问题
  * Blockers: 无
  * Status: 发现算法缺陷，已根本重构

* [2025-01-18]
  * Step: 重构间距算法 - 基于实际数据峰谷值的智能间距
  * Modifications:
    - 用户深度反馈：
      * 实际脑电数据经过放大，远超标准200μV范围
      * 之前的算法基于固定倍数（4倍）限制总显示范围，导致间距不足
      * 需要考虑相邻通道共享间距空间，峰值可能与相邻通道谷值重叠
    - 问题根源：
      * 错误逻辑：total_display_range = data_range * 4.0 (人为限制)
      * 间距基于固定比例计算，无法适应实际数据范围
      * 没有考虑相邻通道的峰谷值重叠风险
    - 全新算法设计：
      * 核心理念：通道间距 = 2倍数据峰谷值
      * 移除所有人为的范围限制和比例设置
      * Y轴范围基于通道分布自然适应
      * 每个通道获得足够的"私有空间"
    - 具体实现：
      * dynamic_step = effective_data_range * 2.0 (直接基于实际数据)
      * 移除total_display_range、channel_span_ratio等限制参数
      * data_margin = effective_data_range * 1.0 (简化边距计算)
      * 初始化和动态调整使用统一的2倍峰谷值逻辑
  * Change Summary: 彻底重构间距算法，基于实际数据峰谷值计算，无范围限制，确保相邻通道不重叠
  * Reason: 解决用户指出的算法根本缺陷，适应实际放大后的脑电数据
  * Blockers: 无
  * Status: 算法重构完成，应能完全解决重叠问题
