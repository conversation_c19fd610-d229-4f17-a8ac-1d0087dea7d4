# -*- coding: utf-8 -*-
"""
统计卡片组件
Statistics Card Component

用于显示关键统计指标的卡片组件
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QPainter, QColor, QLinearGradient


class StatsCard(QFrame):
    """统计卡片组件"""
    
    # 卡片点击信号
    card_clicked = Signal(str)  # 传递卡片标识
    
    def __init__(self, title: str = "", value: str = "", 
                 subtitle: str = "", icon: str = "", 
                 card_id: str = "", parent=None):
        super().__init__(parent)
        
        self.title_text = title
        self.value_text = value
        self.subtitle_text = subtitle
        self.icon_text = icon
        self.card_id = card_id
        
        # 设置卡片属性
        self.setObjectName("stats_card")
        self.setFrameStyle(QFrame.Shape.NoFrame)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 初始化UI
        self._init_ui()
        self._setup_layout()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(4, 0, 4,0)  # 进一步减小边距
        self.main_layout.setSpacing(1)  # 减小间距，让布局更紧凑
        
        # 创建头部区域（图标和标题）
        self._create_header()
        
        # 创建数值区域
        self._create_value_area()
        
        # 创建副标题区域
        if self.subtitle_text:
            self._create_subtitle()
    
    def _create_header(self):
        """创建头部区域"""
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)  # 改为垂直布局，图标和标题上下排列
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(4)

        # 图标标签
        if self.icon_text:
            self.icon_label = QLabel(self.icon_text)
            self.icon_label.setObjectName("stats_card_icon")
            self.icon_label.setFont(QFont("Microsoft YaHei", 14))
            self.icon_label.setFixedSize(21, 21)
            self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            header_layout.addWidget(self.icon_label)
        
        # 标题标签
        self.title_label = QLabel(self.title_text)
        self.title_label.setObjectName("stats_card_title")
        self.title_label.setFont(QFont("Microsoft YaHei", 8))  # 进一步减小字体
        self.title_label.setWordWrap(False)  # 禁止换行
        self.title_label.setTextInteractionFlags(Qt.TextInteractionFlag.NoTextInteraction)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 居中对齐
        header_layout.addWidget(self.title_label)

        self.main_layout.addWidget(header_widget)
    
    def _create_value_area(self):
        """创建数值区域"""
        self.value_label = QLabel(self.value_text)
        self.value_label.setObjectName("stats_card_value")
        self.value_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))  # 进一步减小字体
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 居中对齐
        self.value_label.setWordWrap(False)  # 禁止换行

        self.main_layout.addWidget(self.value_label, 1)  # 添加拉伸因子，让数值区域占据更多空间
    
    def _create_subtitle(self):
        """创建副标题区域"""
        self.subtitle_label = QLabel(self.subtitle_text)
        self.subtitle_label.setObjectName("stats_card_subtitle")
        self.subtitle_label.setFont(QFont("Microsoft YaHei", 9))
        self.subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 居中对齐
        
        self.main_layout.addWidget(self.subtitle_label)
    
    def _setup_layout(self):
        """设置布局属性"""
        # 设置固定高度
        self.setFixedHeight(66)

        # 设置更小的最小宽度，让6个卡片能在一个窗口中显示
        self.setMinimumWidth(50)  # 进一步减小到50px
        # 移除最大宽度限制，让卡片能够充分利用空间

        # 设置尺寸策略，允许水平方向压缩和拉伸
        self.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
    
    def update_data(self, title: str = None, value: str = None, 
                   subtitle: str = None, icon: str = None):
        """更新卡片数据"""
        if title is not None:
            self.title_text = title
            self.title_label.setText(title)
        
        if value is not None:
            self.value_text = value
            self.value_label.setText(value)
        
        if subtitle is not None:
            self.subtitle_text = subtitle
            if hasattr(self, 'subtitle_label'):
                self.subtitle_label.setText(subtitle)
            elif subtitle:  # 如果之前没有副标题，现在需要创建
                self._create_subtitle()
        
        if icon is not None:
            self.icon_text = icon
            if hasattr(self, 'icon_label'):
                self.icon_label.setText(icon)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.card_clicked.emit(self.card_id)
        super().mousePressEvent(event)


class TrendStatsCard(StatsCard):
    """带趋势指示的统计卡片"""
    
    def __init__(self, title: str = "", value: str = "", 
                 trend_value: float = 0.0, trend_text: str = "",
                 icon: str = "", card_id: str = "", parent=None):
        self.trend_value = trend_value
        self.trend_text = trend_text
        
        super().__init__(title, value, "", icon, card_id, parent)
        
        # 添加趋势指示器
        self._create_trend_indicator()
    
    def _create_trend_indicator(self):
        """创建趋势指示器"""
        trend_widget = QWidget()
        trend_layout = QHBoxLayout(trend_widget)
        trend_layout.setContentsMargins(0, 0, 0, 0)
        trend_layout.setSpacing(4)

        # 不显示趋势箭头，只保留颜色逻辑用于文字
        if self.trend_value > 0:
            trend_color = "stats_card_trend_up"
        elif self.trend_value < 0:
            trend_color = "stats_card_trend_down"
        else:
            trend_color = "stats_card_trend_neutral"

        # 趋势文字
        self.trend_text_label = QLabel(self.trend_text)
        self.trend_text_label.setObjectName(trend_color)
        self.trend_text_label.setFont(QFont("Microsoft YaHei", 9))  # 减小字体以节省空间
        self.trend_text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 居中对齐
        trend_layout.addWidget(self.trend_text_label)

        # 添加趋势指示器，但不给它拉伸因子，让它占用最小空间
        self.main_layout.addWidget(trend_widget, 0)
    
    def update_trend(self, trend_value: float, trend_text: str):
        """更新趋势数据"""
        self.trend_value = trend_value
        self.trend_text = trend_text
        
        # 更新趋势颜色（不显示图标）
        if trend_value > 0:
            trend_color = "stats_card_trend_up"
        elif trend_value < 0:
            trend_color = "stats_card_trend_down"
        else:
            trend_color = "stats_card_trend_neutral"
        
        # 更新趋势文字
        self.trend_text_label.setText(trend_text)
        self.trend_text_label.setObjectName(trend_color)
        
        # 刷新样式
        self.trend_text_label.style().unpolish(self.trend_text_label)
        self.trend_text_label.style().polish(self.trend_text_label)


class ProgressStatsCard(StatsCard):
    """带进度条的统计卡片"""
    
    def __init__(self, title: str = "", value: str = "", 
                 progress: float = 0.0, max_value: float = 100.0,
                 icon: str = "", card_id: str = "", parent=None):
        self.progress = progress
        self.max_value = max_value
        
        super().__init__(title, value, "", icon, card_id, parent)
        
        # 添加进度条
        self._create_progress_bar()
    
    def _create_progress_bar(self):
        """创建进度条"""
        progress_widget = QWidget()
        progress_widget.setFixedHeight(8)
        progress_widget.setObjectName("stats_card_progress_container")
        
        self.progress_bar = ProgressBar(self.progress, self.max_value)
        
        progress_layout = QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.addWidget(self.progress_bar)
        
        self.main_layout.addWidget(progress_widget)
    
    def update_progress(self, progress: float, max_value: float = None):
        """更新进度"""
        self.progress = progress
        if max_value is not None:
            self.max_value = max_value
        
        self.progress_bar.update_progress(self.progress, self.max_value)


class ProgressBar(QWidget):
    """简单的进度条组件"""
    
    def __init__(self, progress: float = 0.0, max_value: float = 100.0, parent=None):
        super().__init__(parent)
        self.progress = progress
        self.max_value = max_value
        self.setFixedHeight(6)
    
    def update_progress(self, progress: float, max_value: float = 100.0):
        """更新进度"""
        self.progress = progress
        self.max_value = max_value
        self.update()
    
    def paintEvent(self, event):
        """绘制进度条"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 获取绘制区域
        rect = self.rect()
        
        # 绘制背景
        painter.setBrush(QColor("#e2e8f0"))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(rect, 3, 3)
        
        # 计算进度宽度
        if self.max_value > 0:
            progress_width = int((self.progress / self.max_value) * rect.width())
            progress_rect = rect.adjusted(0, 0, progress_width - rect.width(), 0)
            
            # 绘制进度
            gradient = QLinearGradient(0, 0, progress_width, 0)
            gradient.setColorAt(0, QColor("#06b6d4"))
            gradient.setColorAt(1, QColor("#0891b2"))
            
            painter.setBrush(gradient)
            painter.drawRoundedRect(progress_rect, 3, 3)
