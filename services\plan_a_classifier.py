#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plan A Classifier: Filter-Bank Riemannian + Tangent Space LR (with simple fusion)

设计目标：
- 6子带带通 → 每带协方差（OAS/LW收缩）
- 分支A：MDM（按带训练，预测时跨带距离求和后softmax）
- 分支B：Tangent Space + Logistic Regression（各带切空间拼接）
- 概率校准（占位：温度缩放，初始T=1.0）
- 融合：加权平均（默认 mdm:tslr = 0.6:0.4）

说明：为保证与现有系统快速集成，本实现不依赖原先的特征提取模块，完全自包含。
"""

from __future__ import annotations

import json
import pickle
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Sequence, Tuple

import numpy as np
from scipy import signal

try:
    from pyriemann.estimation import Covariances
    from pyriemann.tangentspace import TangentSpace
    from pyriemann.classification import MDM
    PYRIEMANN_AVAILABLE = True
except Exception:  # pragma: no cover - 环境兜底
    PYRIEMANN_AVAILABLE = False
    Covariances = None  # type: ignore
    TangentSpace = None  # type: ignore
    MDM = None  # type: ignore

from sklearn.linear_model import LogisticRegression


@dataclass
class PlanAConfig:
    sampling_rate: int = 125
    window_samples: int = 250  # 2秒窗口
    # 6子带（可按需调整）
    subbands: List[Tuple[float, float]] = (
        (8.0, 12.0), (12.0, 16.0), (16.0, 20.0), (20.0, 24.0), (24.0, 28.0), (28.0, 32.0)
    )
    covariance_estimator: str = "oas"  # 'oas' | 'lwf' | 'cov'
    riemann_metric: str = "riemann"  # 'riemann' | 'logeuclid'
    fusion_weights: Dict[str, float] = None  # {'mdm': 0.6, 'tslr': 0.4}

    def __post_init__(self) -> None:
        if self.fusion_weights is None:
            self.fusion_weights = {"mdm": 0.6, "tslr": 0.4}


class _TemperatureScaler:
    """简单温度缩放（占位实现：T固定为1.0，可后续扩展为基于CV优化）。"""

    def __init__(self, temperature: float = 1.0) -> None:
        self.temperature = max(1e-6, float(temperature))

    def fit(self, probs: np.ndarray, y: np.ndarray) -> None:
        # 可扩展：通过最小化NLL优化T；当前先保持T=1.0以保证稳定
        self.temperature = 1.0

    def transform(self, probs: np.ndarray) -> np.ndarray:
        # 将概率通过softmax温度缩放；此处直接对logits近似处理：p^(1/T)/Z
        p = np.clip(probs, 1e-9, 1 - 1e-9)
        if self.temperature == 1.0:
            return p
        # 简单幂缩放（近似）：
        p1 = p ** (1.0 / self.temperature)
        p0 = (1.0 - p) ** (1.0 / self.temperature)
        z = p1 + p0
        return p1 / np.clip(z, 1e-9, None)


class _SubbandFilterBank:
    """IIR子带滤波器组（Butterworth，sos实现）。"""

    def __init__(self, fs: int, bands: Sequence[Tuple[float, float]], order: int = 4) -> None:
        self.fs = fs
        self.bands = list(bands)
        self.order = order
        self._sos_list: List[np.ndarray] = []
        for low, high in self.bands:
            sos = signal.butter(
                N=self.order,
                Wn=[low, high],
                btype="bandpass",
                fs=self.fs,
                output="sos",
            )
            self._sos_list.append(sos)

    def transform(self, x: np.ndarray) -> List[np.ndarray]:
        """
        Args:
            x: [n_channels, n_samples]
        Returns:
            List of bandpassed arrays, each shape [n_channels, n_samples]
        """
        outputs: List[np.ndarray] = []
        for sos in self._sos_list:
            # 使用filtfilt以获得零相位失真
            y = signal.sosfiltfilt(sos, x, axis=1)
            outputs.append(y.astype(np.float32, copy=False))
        return outputs


class PlanAClassifier:
    """自包含的方案A分类器实现。"""

    def __init__(self, config: Optional[PlanAConfig] = None) -> None:
        if config is None:
            config = PlanAConfig()
        self.config = config

        if not PYRIEMANN_AVAILABLE:
            raise ImportError("需要安装pyriemann库以使用方案A分类器")

        self._bank = _SubbandFilterBank(fs=config.sampling_rate, bands=config.subbands)
        self._cov = Covariances(estimator=config.covariance_estimator)
        self._ts = TangentSpace(metric=config.riemann_metric)

        # 分支A：每个子带一个MDM
        self._mdm_models: List[MDM] = []
        # 分支B：切空间+LR（拼接全部子带）
        self._lr: Optional[LogisticRegression] = None

        # 温度缩放器
        self._temp_mdm = _TemperatureScaler(1.0)
        self._temp_tslr = _TemperatureScaler(1.0)

        # 训练状态
        self._is_fitted = False

    # -------------------- 工具函数 --------------------
    @staticmethod
    def _softmax_neg_dist(dist_2d: np.ndarray) -> np.ndarray:
        """将每样本[2类距离]转为概率，使用softmax(-d)。"""
        logits = -np.clip(dist_2d, -1e6, 1e6)
        logits = logits - np.max(logits, axis=1, keepdims=True)
        e = np.exp(logits)
        p = e / np.sum(e, axis=1, keepdims=True)
        # 返回MI类别概率（索引1）
        return p[:, 1]

    def _stack_tangent_features(self, X: np.ndarray) -> np.ndarray:
        """对每个trial做6子带切空间特征并拼接。

        Args:
            X: [n_trials, n_channels, n_samples]
        Returns:
            features: [n_trials, n_features_total]
        """
        n_trials, n_channels, _ = X.shape
        feats: List[np.ndarray] = []
        for i in range(n_trials):
            band_signals = self._bank.transform(X[i])  # List[[C, T]]
            band_feats: List[np.ndarray] = []
            for b in band_signals:
                cov = self._cov.fit_transform(b[np.newaxis, :, :])  # [1, C, C]
                # 使用已拟合的切空间进行变换（避免fit_transform导致零向量）
                f = self._ts.transform(cov)  # [1, C*(C+1)/2]
                band_feats.append(f)
            feats.append(np.concatenate(band_feats, axis=1))
        return np.vstack(feats)

    def _compute_mdm_distances(self, X: np.ndarray) -> np.ndarray:
        """跨子带求和的MDM距离。

        Returns:
            dist_sum: [n_trials, 2]  两类距离之和
        """
        n_trials = X.shape[0]
        dist_sum = np.zeros((n_trials, 2), dtype=np.float64)
        for i in range(n_trials):
            band_signals = self._bank.transform(X[i])
            # 每带一个MDM
            for model, b in zip(self._mdm_models, band_signals):
                cov = self._cov.transform(b[np.newaxis, :, :])  # [1, C, C]
                d = model._predict_distances(cov)  # type: ignore  # [1, n_classes]
                dist_sum[i] += d[0]
        return dist_sum

    # -------------------- 训练/预测接口 --------------------
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """训练方案A分类器。

        Args:
            X: [n_trials, n_channels, window_samples]
            y: [n_trials], 二分类 {0: rest, 1: motor imagery}
        """
        if X.ndim != 3 or X.shape[2] != self.config.window_samples:
            raise ValueError("输入X维度应为 [n_trials, n_channels, window_samples]")
        if not set(np.unique(y)).issubset({0, 1}):
            raise ValueError("y 仅支持二分类 {0,1}")

        # 分支A：为每个子带训练一个MDM
        self._mdm_models = []
        for _ in self.config.subbands:
            self._mdm_models.append(MDM(metric=self.config.riemann_metric))

        # 逐带协方差 → 拟合各自MDM
        # 为避免多次重复滤波，按trial遍历（样本量通常不大）
        covs_per_band: List[List[np.ndarray]] = [[] for _ in self.config.subbands]
        for i in range(X.shape[0]):
            band_signals = self._bank.transform(X[i])
            for b_idx, b in enumerate(band_signals):
                cov = self._cov.fit_transform(b[np.newaxis, :, :])  # [1, C, C]
                covs_per_band[b_idx].append(cov[0])
        # 堆叠到数组并拟合
        for b_idx, model in enumerate(self._mdm_models):
            cov_band = np.stack(covs_per_band[b_idx], axis=0)  # [n_trials, C, C]
            model.fit(cov_band, y)

        # 分支B：切空间+LR（拼接所有子带的切空间特征）
        # 先在训练集上拟合切空间参考（使用每带协方差）
        # 注意：_stack_tangent_features 已改用 transform，需在此处先 fit TangentSpace
        # 我们用第一带的协方差来建立切空间参考（等价也可用全部带的均值参考）
        try:
            # 使用第一带样本建立切空间参考
            sample_band_covs: List[np.ndarray] = []
            for i in range(X.shape[0]):
                b = self._bank.transform(X[i])[0]  # 第1带
                cov = self._cov.fit_transform(b[np.newaxis, :, :])
                sample_band_covs.append(cov[0])
            ref_cov_stack = np.stack(sample_band_covs, axis=0)
            self._ts.fit(ref_cov_stack)
        except Exception:
            # 兜底：若失败，保持原先在特征阶段fit_transform的行为
            pass

        feats = self._stack_tangent_features(X)

        # 标准化特征以避免概率塌缩到0.5
        from sklearn.preprocessing import StandardScaler
        self._scaler = StandardScaler()
        feats_scaled = self._scaler.fit_transform(feats)

        lr = LogisticRegression(C=1.0, max_iter=2000, solver="lbfgs", random_state=42)
        lr.fit(feats_scaled, y)
        self._lr = lr

        # 温度缩放（占位：T=1.0）
        # 可扩展为：在简单CV上拟合T
        # ---> self._temp_mdm.fit(mdm_p_train, y)
        # ---> self._temp_tslr.fit(tslr_p_train, y)

        self._is_fitted = True

    def predict_proba(self, X_window: np.ndarray) -> float:
        """返回单窗口MI概率（融合后，不含平滑）。

        Args:
            X_window: [n_channels, window_samples]
        Returns:
            p_mi: float in [0,1]
        """
        comps = self.predict_proba_components(X_window)
        return comps["p"]

    def predict_proba_components(self, X_window: np.ndarray) -> Dict[str, float]:
        """返回单窗口的分支概率与融合概率。

        Args:
            X_window: [n_channels, window_samples] 或 [1, n_channels, window_samples]
        Returns:
            dict: { 'p_mdm': float, 'p_tslr': float, 'p': float }
        """
        if not self._is_fitted:
            raise ValueError("PlanAClassifier 未训练")
        # 兼容输入：[8, 250] 或 [1, 8, 250]
        xw = X_window
        if xw.ndim == 3 and xw.shape[0] == 1:
            xw = xw[0]
        if xw.ndim != 2 or xw.shape[1] != self.config.window_samples:
            raise ValueError("输入窗口维度应为 [n_channels, window_samples]")

        X = xw[np.newaxis, :, :]

        # 分支A：MDM（跨带距离求和→softmax）
        dist = self._compute_mdm_distances(X)  # [1, 2]
        p_mdm = self._softmax_neg_dist(dist)[0]
        p_mdm = float(self._temp_mdm.transform(np.array([p_mdm]))[0])

        # 分支B：TSLR（拼接切空间特征→LR概率）
        feats = self._stack_tangent_features(X)
        if hasattr(self, "_scaler") and self._scaler is not None:
            feats = self._scaler.transform(feats)
        p_tslr = float(self._lr.predict_proba(feats)[0, 1]) if self._lr is not None else 0.5
        p_tslr = float(self._temp_tslr.transform(np.array([p_tslr]))[0])

        # 融合
        w_mdm = float(self.config.fusion_weights.get("mdm", 0.8))
        w_tslr = float(self.config.fusion_weights.get("tslr", 0.2))
        w_sum = max(1e-9, w_mdm + w_tslr)
        p = (w_mdm * p_mdm + w_tslr * p_tslr) / w_sum
        p = float(np.clip(p, 0.0, 1.0))

        return {"p_mdm": float(p_mdm), "p_tslr": float(p_tslr), "p": p}

    # -------------------- 持久化与状态 --------------------
    def get_status(self) -> Dict[str, Any]:
        return {
            "subbands": list(self.config.subbands),
            "covariance_estimator": self.config.covariance_estimator,
            "riemann_metric": self.config.riemann_metric,
            "fusion_weights": dict(self.config.fusion_weights),
            "is_fitted": self._is_fitted,
        }

    def save(self, filepath: str) -> None:
        with open(filepath, "wb") as f:
            pickle.dump(self, f)

    @staticmethod
    def load(filepath: str) -> "PlanAClassifier":
        with open(filepath, "rb") as f:
            obj = pickle.load(f)
        if not isinstance(obj, PlanAClassifier):
            raise TypeError("加载的对象类型不匹配：期望 PlanAClassifier")
        return obj



