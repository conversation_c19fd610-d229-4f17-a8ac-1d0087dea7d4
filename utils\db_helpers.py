# -*- coding: utf-8 -*-
"""
数据库辅助工具
Database Helper Functions

提供数据验证、格式化和转换功能
"""

import re
from typing import Dict, Any, Optional, List
from datetime import datetime
import sqlite3

def validate_patient_data(data: Dict[str, Any]) -> Dict[str, str]:
    """
    验证患者数据
    返回错误信息字典，空字典表示验证通过
    """
    errors = {}
    
    # 必填字段验证
    required_fields = {
        'bianhao': '患者编号',
        'name': '姓名',
        'age': '年龄',
        'xingbie': '性别'
    }
    
    for field, field_name in required_fields.items():
        if not data.get(field):
            errors[field] = f"{field_name}不能为空"
    
    # 姓名验证
    if data.get('name'):
        name = str(data['name']).strip()
        if len(name) < 2 or len(name) > 20:
            errors['name'] = "姓名长度应在2-20个字符之间"
        elif not re.match(r'^[\u4e00-\u9fa5a-zA-Z\s]+$', name):
            errors['name'] = "姓名只能包含中文、英文和空格"
    
    # 年龄验证
    if data.get('age'):
        try:
            age = int(data['age'])
            if age < 0 or age > 150:
                errors['age'] = "年龄应在0-150之间"
        except (ValueError, TypeError):
            errors['age'] = "年龄必须是数字"
    
    # 性别验证
    if data.get('xingbie'):
        if data['xingbie'] not in ['男', '女']:
            errors['xingbie'] = "性别只能是'男'或'女'"
    
    # 患者编号验证
    if data.get('bianhao'):
        bianhao = str(data['bianhao']).strip()
        if len(bianhao) < 1 or len(bianhao) > 50:
            errors['bianhao'] = "患者编号长度应在1-50个字符之间"

    # 证件号码验证（可选，不限制格式）
    if data.get('cardid'):
        cardid = str(data['cardid']).strip()
        if len(cardid) > 50:
            errors['cardid'] = "证件号码长度不能超过50个字符"

    # 电话号码验证（可选，支持手机和座机）
    if data.get('phone'):
        phone = str(data['phone']).strip()
        if phone and len(phone) > 20:
            errors['phone'] = "电话号码长度不能超过20个字符"
    
    return errors

def format_patient_for_ui(row: sqlite3.Row) -> Dict[str, Any]:
    """将数据库记录转换为UI显示格式"""
    if not row:
        return {}

    # 转换为字典
    data = dict(row)

    # 获取治疗次数和最后访问时间（延迟导入避免循环依赖）
    treatment_count = 0
    last_visit_date = data.get('lrshijian', '')  # 默认使用录入时间
    try:
        from services.treatment_service import treatment_service
        patient_id = data.get('bianhao', '')
        if patient_id:
            treatment_count = treatment_service.get_treatment_count_by_patient(patient_id)
            # 获取最新治疗记录的日期
            latest_treatment_date = treatment_service.get_latest_treatment_date(patient_id)
            if latest_treatment_date:
                # 如果有治疗记录，使用治疗记录的日期（只取日期部分）
                if len(latest_treatment_date) > 10:
                    last_visit_date = latest_treatment_date[:10]
                else:
                    last_visit_date = latest_treatment_date
            elif last_visit_date and len(last_visit_date) > 10:
                # 如果没有治疗记录，使用录入时间（只取日期部分）
                last_visit_date = last_visit_date[:10]
    except Exception as e:
        print(f"获取治疗信息失败: {e}")
        treatment_count = 0
        # 保持原有的录入时间
        if last_visit_date and len(last_visit_date) > 10:
            last_visit_date = last_visit_date[:10]

    # 格式化显示字段
    formatted_data = {
        'bianhao': data.get('bianhao', ''),
        'name': data.get('name', ''),
        'xingbie': data.get('xingbie', ''),
        'age': data.get('age', 0),
        'cardid': data.get('cardid', ''),
        'zhenduan': data.get('zhenduan', ''),
        'bingshi': data.get('bingshi', ''),
        'hardid': data.get('hardid', ''),
        'yiyuanid': data.get('yiyuanid', 0),
        'keshi': data.get('keshi', ''),
        'zhuzhi': data.get('zhuzhi', ''),
        'czy': data.get('czy', ''),
        'lrshijian': last_visit_date,
        'status': data.get('status', ''),
        'phone': data.get('phone', ''),
        'address': data.get('address', ''),

        # UI专用字段
        'avatar': data.get('name', '')[:1] if data.get('name') else 'P',
        'treatment_count': treatment_count,  # 从治疗记录表查询
        'totalTime': 0,        # 需要从治疗记录表查询
        'avgAccuracy': 0.0,    # 需要从治疗记录表查询
    }

    return formatted_data

def format_patient_for_db(data: Dict[str, Any]) -> Dict[str, Any]:
    """将UI数据转换为数据库存储格式"""
    # 清理和转换数据
    db_data = {}
    
    # 字符串字段（包括患者编号，现在是TEXT类型，支持前导0）
    string_fields = ['bianhao', 'name', 'xingbie', 'cardid', 'zhenduan', 'bingshi',
                    'hardid', 'keshi', 'zhuzhi', 'czy', 'status', 'phone', 'address']
    for field in string_fields:
        if field in data:
            value = str(data[field]).strip() if data[field] is not None else ''
            db_data[field] = value if value else None
    
    # 整数字段
    int_fields = ['age', 'yiyuanid']
    for field in int_fields:
        if field in data:
            try:
                db_data[field] = int(data[field]) if data[field] is not None else None
            except (ValueError, TypeError):
                db_data[field] = None
    
    # 添加时间戳
    if 'lrshijian' not in data or not data['lrshijian']:
        db_data['lrshijian'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    else:
        db_data['lrshijian'] = data['lrshijian']
    
    return db_data

def generate_patient_id() -> str:
    """生成患者编号"""
    # 使用时间戳生成唯一编号
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return timestamp

def safe_get_value(row: sqlite3.Row, key: str, default: Any = None) -> Any:
    """安全获取数据库记录值"""
    try:
        return row[key] if row and key in row.keys() else default
    except (IndexError, KeyError, TypeError):
        return default

def build_where_clause(filters: Dict[str, Any]) -> tuple:
    """构建WHERE子句和参数"""
    conditions = []
    params = []
    
    for field, value in filters.items():
        if value is not None and value != '':
            if field in ['name', 'zhenduan', 'bingshi']:
                # 模糊查询
                conditions.append(f"{field} LIKE ?")
                params.append(f"%{value}%")
            else:
                # 精确查询
                conditions.append(f"{field} = ?")
                params.append(value)
    
    where_clause = " AND ".join(conditions) if conditions else ""
    return where_clause, tuple(params)

def paginate_query(base_sql: str, page: int, limit: int, params: tuple = ()) -> tuple:
    """为查询添加分页"""
    offset = (page - 1) * limit
    paginated_sql = f"{base_sql} LIMIT ? OFFSET ?"
    paginated_params = params + (limit, offset)
    return paginated_sql, paginated_params
