# 用户管理功能升级说明

## 概述

本次升级将原有的简单操作员表(operator)扩展为完整的用户管理系统，支持用户认证、权限管理、活动追踪等功能。升级后的系统可以满足医疗设备系统对用户管理的全部需求。

## 数据库升级

### 原有表结构
```sql
CREATE TABLE operator (
    name TEXT
);
```

### 新表结构
```sql
CREATE TABLE operator (
    -- 基础身份字段
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    email TEXT UNIQUE,
    name TEXT,
    role TEXT NOT NULL DEFAULT 'operator' CHECK (role IN ('admin', 'doctor', 'operator')),
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    
    -- 时间追踪字段
    created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
    last_login_at TEXT,
    updated_at TEXT DEFAULT (datetime('now', 'localtime')),
    
    -- 活动统计字段
    login_count INTEGER DEFAULT 0,
    total_online_minutes INTEGER DEFAULT 0,
    last_login_ip TEXT,
    last_device_type TEXT,
    
    -- 医疗系统相关字段
    hospital_id INTEGER,
    department TEXT,
    employee_id TEXT,
    phone TEXT,
    notes TEXT,
    
    -- 安全相关字段
    reset_token TEXT,
    reset_token_expires_at TEXT,
    is_locked INTEGER DEFAULT 0,
    locked_at TEXT,
    failed_login_attempts INTEGER DEFAULT 0,
    
    -- 外键约束
    FOREIGN KEY (hospital_id) REFERENCES yiyuan(id)
);

-- 索引
CREATE INDEX idx_operator_username ON operator(username);
CREATE INDEX idx_operator_email ON operator(email);
CREATE INDEX idx_operator_role ON operator(role);
CREATE INDEX idx_operator_status ON operator(status);
CREATE INDEX idx_operator_hospital ON operator(hospital_id);
```

## 新增功能

### 1. 用户认证
- 用户名/密码登录系统
- 密码安全哈希存储
- 登录失败计数和账户锁定
- 密码重置功能

### 2. 用户管理
- 创建新用户
- 编辑用户信息
- 启用/停用用户
- 重置用户密码
- 查看用户活动日志

### 3. 角色与权限
- 三级角色系统：管理员、医生、操作员
- 基于角色的权限控制
- 系统权限和数据权限分离
- 动态权限显示

### 4. 活动追踪
- 登录次数统计
- 在线时长记录
- 最后登录IP和设备类型
- 登录时间记录

## 技术实现

### 1. 数据库迁移
- 创建了安全的数据库迁移脚本 `database_migration.py`
- 自动备份原有数据库
- 平滑迁移现有数据
- 验证迁移结果

### 2. 用户服务
- 创建了用户管理服务 `services/user_service.py`
- 提供完整的CRUD操作
- 实现用户认证和密码验证
- 支持状态管理和权限控制

### 3. 数据工具
- 创建了用户数据工具 `utils/user_helpers.py`
- 提供数据验证和格式化
- 支持权限管理和角色转换
- 实现安全的数据处理

### 4. UI集成
- 更新了用户管理页面 `ui/pages/users_page.py`
- 集成真实数据库数据
- 移除模拟数据依赖
- 实现真实的状态切换

## 默认用户

系统已预置以下用户：

1. **管理员**
   - 用户名: `testadmin`
   - 密码: `123456`
   - 角色: 管理员
   - 权限: 全部系统和数据权限

2. **操作员1**
   - 用户名: `operator1`
   - 密码: `123456`
   - 角色: 操作员
   - 权限: 治疗操作、患者数据

3. **操作员2**
   - 用户名: `operator2`
   - 密码: `123456`
   - 角色: 操作员
   - 权限: 治疗操作、患者数据

## 待实现功能

以下功能已在UI中预留，但尚未实现：

1. **新增用户**
   - 界面已准备，点击"新增用户"按钮显示"新增用户功能 - 待实现"
   - 需要实现用户创建表单和数据验证

2. **编辑用户**
   - 界面已准备，点击"编辑"按钮显示"编辑用户: xxx - 待实现"
   - 需要实现用户编辑表单和数据验证

3. **重置密码**
   - 界面已准备，点击"重置密码"按钮显示"重置用户密码: xxx - 待实现"
   - 需要实现密码重置确认和邮件通知

4. **查看日志**
   - 界面已准备，点击"查看日志"按钮显示"查看用户日志: xxx - 待实现"
   - 需要实现用户活动日志记录和展示

## 安全建议

1. **首次登录后修改密码**
   - 所有预置用户使用相同的默认密码，建议首次登录后立即修改

2. **定期密码更新**
   - 建议每3个月更新一次密码
   - 密码应包含字母、数字和特殊字符

3. **权限最小化**
   - 为用户分配最小必要权限
   - 定期审核用户权限设置

4. **活动监控**
   - 定期检查用户活动日志
   - 监控异常登录行为

## 后续开发计划

1. **完善用户表单**
   - 实现新增用户表单
   - 实现编辑用户表单
   - 实现密码重置功能

2. **增强安全性**
   - 实现更强的密码策略
   - 添加双因素认证
   - 完善账户锁定机制

3. **审计日志**
   - 记录用户所有操作
   - 提供审计日志查询
   - 支持日志导出

4. **用户组管理**
   - 实现用户组功能
   - 基于组的权限分配
   - 批量用户管理
