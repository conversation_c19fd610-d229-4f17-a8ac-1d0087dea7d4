# -*- coding: utf-8 -*-
"""
用户数据处理工具
User Data Helpers

提供用户数据验证、格式化、转换等工具函数
"""

import sqlite3
from typing import Dict, Any, List
from datetime import datetime
import re

def validate_user_data(data: Dict[str, Any], is_update: bool = False) -> Dict[str, List[str]]:
    """
    验证用户数据
    
    Args:
        data: 用户数据字典
        is_update: 是否为更新操作
        
    Returns:
        验证错误字典
    """
    errors = {}
    
    # 用户名验证
    if not is_update or 'username' in data:
        username = data.get('username', '').strip()
        if not username:
            errors['username'] = '用户名不能为空'
        elif len(username) < 3:
            errors['username'] = '用户名至少3个字符'
        elif len(username) > 50:
            errors['username'] = '用户名不能超过50个字符'
        elif not re.match(r'^[a-zA-Z0-9_]+$', username):
            errors['username'] = '用户名只能包含字母、数字和下划线'
    
    # 密码验证（创建时必需，更新时可选）
    if not is_update and 'password' in data:
        password = data.get('password', '')
        if not password:
            errors['password'] = '密码不能为空'
        elif len(password) < 6:
            errors['password'] = '密码至少6个字符'
        elif len(password) > 100:
            errors['password'] = '密码不能超过100个字符'
    
    # 邮箱验证（可选）
    if data.get('email'):
        email = data['email'].strip()
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            errors['email'] = '邮箱格式不正确'
        elif len(email) > 100:
            errors['email'] = '邮箱长度不能超过100个字符'
    
    # 姓名验证
    if data.get('name'):
        name = data['name'].strip()
        if len(name) > 50:
            errors['name'] = '姓名长度不能超过50个字符'
    
    # 角色验证
    if 'role' in data:
        valid_roles = ['admin', 'doctor', 'operator']
        if data['role'] not in valid_roles:
            errors['role'] = f'角色必须是以下之一: {", ".join(valid_roles)}'
    
    # 状态验证
    if 'status' in data:
        valid_statuses = ['active', 'inactive']
        if data['status'] not in valid_statuses:
            errors['status'] = f'状态必须是以下之一: {", ".join(valid_statuses)}'
    
    # 电话验证（可选）
    if data.get('phone'):
        phone = data['phone'].strip()
        if len(phone) > 20:
            errors['phone'] = '电话号码长度不能超过20个字符'
        # 简单的电话号码格式验证
        phone_pattern = r'^[\d\-\+\(\)\s]+$'
        if not re.match(phone_pattern, phone):
            errors['phone'] = '电话号码格式不正确'
    
    # 工号验证（可选）
    if data.get('employee_id'):
        employee_id = data['employee_id'].strip()
        if len(employee_id) > 50:
            errors['employee_id'] = '工号长度不能超过50个字符'
    
    # 科室验证（可选）
    if data.get('department'):
        department = data['department'].strip()
        if len(department) > 50:
            errors['department'] = '科室名称长度不能超过50个字符'
    
    # 备注验证（可选）
    if data.get('notes'):
        notes = data['notes'].strip()
        if len(notes) > 500:
            errors['notes'] = '备注长度不能超过500个字符'
    
    return errors

def format_user_for_ui(row: sqlite3.Row) -> Dict[str, Any]:
    """
    将数据库记录转换为UI显示格式
    
    Args:
        row: 数据库记录
        
    Returns:
        格式化的用户数据
    """
    if not row:
        return {}
    
    # 转换为字典
    data = dict(row)
    
    # 格式化显示字段
    formatted_data = {
        'id': data.get('id', 0),
        'username': data.get('username', ''),
        'email': data.get('email', ''),
        'name': data.get('name', ''),
        'role': data.get('role', 'operator'),
        'status': data.get('status', 'active'),
        'created': data.get('created_at', ''),
        'lastLogin': data.get('last_login_at', 'Never'),
        'loginCount': data.get('login_count', 0),
        'onlineTime': _format_online_time(data.get('total_online_minutes', 0)),
        'lastIP': data.get('last_login_ip', 'N/A'),
        'deviceType': data.get('last_device_type', 'Unknown'),
        'hospital_id': data.get('hospital_id'),
        'department': data.get('department', ''),
        'employee_id': data.get('employee_id', ''),
        'phone': data.get('phone', ''),
        'notes': data.get('notes', ''),
        'is_locked': data.get('is_locked', 0),
        'failed_login_attempts': data.get('failed_login_attempts', 0),
        
        # UI专用字段
        'avatar': _generate_avatar(data.get('name'), data.get('username')),
    }
    
    return formatted_data

def format_user_for_db(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将UI数据转换为数据库存储格式
    
    Args:
        data: UI数据
        
    Returns:
        数据库格式的数据
    """
    # 清理和转换数据
    db_data = {}
    
    # 字符串字段
    string_fields = [
        'username', 'email', 'name', 'role', 'status',
        'department', 'employee_id', 'phone', 'notes'
    ]
    for field in string_fields:
        if field in data:
            value = str(data[field]).strip() if data[field] is not None else ''
            db_data[field] = value if value else None
    
    # 整数字段
    int_fields = ['hospital_id']
    for field in int_fields:
        if field in data:
            try:
                db_data[field] = int(data[field]) if data[field] is not None else None
            except (ValueError, TypeError):
                db_data[field] = None
    
    # 密码字段（如果提供）
    if 'password' in data and data['password']:
        db_data['password'] = data['password']
    
    return db_data

def get_role_display_name(role: str) -> str:
    """
    获取角色显示名称
    
    Args:
        role: 角色代码
        
    Returns:
        角色显示名称
    """
    role_map = {
        'admin': '管理员',
        'doctor': '医生',
        'operator': '操作员'
    }
    return role_map.get(role, role)

def get_status_display_name(status: str) -> str:
    """
    获取状态显示名称
    
    Args:
        status: 状态代码
        
    Returns:
        状态显示名称
    """
    status_map = {
        'active': '启用',
        'inactive': '停用'
    }
    return status_map.get(status, status)

def get_user_permissions(role: str) -> Dict[str, List[str]]:
    """
    根据角色获取用户权限
    
    Args:
        role: 用户角色
        
    Returns:
        权限字典
    """
    permissions = {
        'admin': {
            'system': ['用户管理', '系统设置', '数据分析', '治疗操作'],
            'data': ['患者数据', '治疗记录', '系统日志']
        },
        'doctor': {
            'system': ['数据分析', '治疗操作'],
            'data': ['患者数据', '治疗记录']
        },
        'operator': {
            'system': ['治疗操作'],
            'data': ['患者数据']
        }
    }
    
    return permissions.get(role, {'system': [], 'data': []})

def generate_username_suggestions(name: str) -> List[str]:
    """
    根据姓名生成用户名建议
    
    Args:
        name: 姓名
        
    Returns:
        用户名建议列表
    """
    if not name:
        return []
    
    suggestions = []
    
    # 移除空格和特殊字符
    clean_name = re.sub(r'[^a-zA-Z0-9\u4e00-\u9fff]', '', name)
    
    if clean_name:
        # 拼音首字母 + 数字
        suggestions.extend([
            f"{clean_name.lower()}",
            f"{clean_name.lower()}01",
            f"{clean_name.lower()}123",
        ])
    
    return suggestions[:3]  # 返回前3个建议

def _format_online_time(minutes: int) -> str:
    """
    格式化在线时长
    
    Args:
        minutes: 分钟数
        
    Returns:
        格式化的时长字符串
    """
    if minutes < 60:
        return f"{minutes} 分钟"
    elif minutes < 1440:  # 24小时
        hours = minutes // 60
        remaining_minutes = minutes % 60
        if remaining_minutes > 0:
            return f"{hours} 小时 {remaining_minutes} 分钟"
        else:
            return f"{hours} 小时"
    else:
        hours = minutes // 60
        return f"{hours:,} 小时"

def _generate_avatar(name: str, username: str) -> str:
    """
    生成用户头像字符
    
    Args:
        name: 姓名
        username: 用户名
        
    Returns:
        头像字符
    """
    if name and name.strip():
        return name.strip()[:1].upper()
    elif username and username.strip():
        return username.strip()[:1].upper()
    else:
        return 'U'

def safe_get_user_value(row: sqlite3.Row, key: str, default: Any = None) -> Any:
    """
    安全获取用户数据库记录值
    
    Args:
        row: 数据库记录
        key: 字段名
        default: 默认值
        
    Returns:
        字段值或默认值
    """
    try:
        return row[key] if row and key in row.keys() else default
    except (IndexError, KeyError, TypeError):
        return default

def create_user_search_filter(search_text: str, role_filter: str = None) -> tuple:
    """
    创建用户搜索过滤条件
    
    Args:
        search_text: 搜索文本
        role_filter: 角色过滤
        
    Returns:
        (WHERE子句, 参数元组)
    """
    conditions = []
    params = []
    
    if search_text and search_text.strip():
        search_text = search_text.strip().lower()
        conditions.append("""
            (LOWER(username) LIKE ? OR 
             LOWER(email) LIKE ? OR 
             LOWER(name) LIKE ? OR
             LOWER(employee_id) LIKE ?)
        """)
        search_param = f"%{search_text}%"
        params.extend([search_param, search_param, search_param, search_param])
    
    if role_filter and role_filter != "全部角色":
        role_map = {"管理员": "admin", "医生": "doctor", "操作员": "operator"}
        if role_filter in role_map:
            conditions.append("role = ?")
            params.append(role_map[role_filter])
    
    where_clause = ""
    if conditions:
        where_clause = "WHERE " + " AND ".join(conditions)
    
    return where_clause, tuple(params)
