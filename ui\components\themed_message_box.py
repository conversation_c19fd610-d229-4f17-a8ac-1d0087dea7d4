# -*- coding: utf-8 -*-
"""
主题化消息框组件
Themed Message Box Component

提供支持主题切换的消息框
"""

from PySide6.QtWidgets import QMessageBox, QWidget, QFrame, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QDialog
from PySide6.QtCore import Qt, QEasingCurve, QPropertyAnimation, QParallelAnimationGroup, QEvent, QPoint, QTimer
from PySide6.QtGui import QFont, QColor, QPainter, QBrush, QGuiApplication
from PySide6.QtWidgets import QGraphicsDropShadowEffect

from app.config import AppConfig
from ui.themes.theme_manager import ThemeManager



def _create_themed_message_box(parent):
    """创建主题化消息框 - 使用圆角容器"""
    msg_box = QMessageBox(parent)

    # 设置无边框窗口 - 与其他对话框保持一致
    msg_box.setWindowFlags(
        Qt.WindowType.FramelessWindowHint |
        Qt.WindowType.Dialog |
        Qt.WindowType.WindowStaysOnTopHint
    )
    msg_box.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
    msg_box.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

    # 应用全局样式
    if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
        msg_box.setStyleSheet(parent.styleSheet())

    # 设置字体
    font = QFont("Microsoft YaHei", 12)
    msg_box.setFont(font)

    return msg_box

def _get_global_stylesheet(parent):
    """获取全局样式表"""
    # 方法1：从父窗口获取
    if parent:
        parent_widget = parent
        level = 0
        while parent_widget and level < 10:
            if hasattr(parent_widget, 'theme_manager'):
                return parent_widget.theme_manager.get_current_stylesheet()
            parent_widget = parent_widget.parent()
            level += 1

    # 方法2：从QApplication获取
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'theme_manager'):
                    return widget.theme_manager.get_current_stylesheet()
    except:
        pass

    # 方法3：返回应用程序的样式表
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            return app.styleSheet()
    except:
        pass

    return ""


class ThemedMessageBox(QDialog):
    """自定义圆角消息框"""

    def __init__(self, parent=None, title="", text="", icon_type="information", buttons=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)

        # 参数保存
        self.icon_type = icon_type

        # 主题/配置
        self.config = AppConfig()
        self.theme_manager = ThemeManager()
        self.current_theme = self.config.get('theme', 'tech')
        self.theme_config = self.theme_manager.get_theme_config(self.current_theme)

        # 记录父级顶层窗口，追踪移动/缩放
        self._top_window = parent.window() if parent is not None else None
        if self._top_window is not None:
            self._top_window.installEventFilter(self)

        # 设置无边框窗口（透明背景，便于绘制遮罩）
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 将对话框同步为父窗口的全区域，以便绘制全屏遮罩（不改变无边框风格）
        try:
            from PySide6.QtGui import QGuiApplication
            if parent is not None:
                top = parent.window()
                self.setGeometry(top.geometry())
            else:
                geo = QGuiApplication.primaryScreen().geometry()
                self.setGeometry(geo)
        except Exception:
            self.setGeometry(0, 0, 900, 700)

        # 结果值
        self.result_value = QMessageBox.StandardButton.NoButton

        # 初始化UI
        self._init_ui(title, text, icon_type, buttons)

        # 初始透明用于入场动效
        self.setWindowOpacity(0.0)

    def _init_ui(self, title, text, icon_type, buttons):
        """初始化UI（方案C：不透明卡片 + 强阴影 + 顶条状态色 + 动效）"""

        # 1) 背景遮罩 + 中央卡片容器
        self.overlay = QWidget(self)
        self.overlay.setObjectName("dialog_overlay")
        self.overlay.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, False)

        # 遮罩颜色根据主题设置（亮->黑半透明，暗->更深一些）
        if self.current_theme == 'medical':
            scrim = "rgba(0, 0, 0, 0.32)"
        else:
            scrim = "rgba(0, 0, 0, 0.52)"
        self.overlay.setStyleSheet(f"QWidget#dialog_overlay {{ background-color: {scrim}; border-radius: 0px; }}")

        # 中央对话框卡片（真正的内容容器）
        self.card = QFrame(self.overlay)
        self.card.setObjectName("dialog_card")
        self.card.setMinimumSize(420, 220)
        self.card.setMaximumWidth(520)

        # 强阴影
        shadow = QGraphicsDropShadowEffect(self.card)
        shadow.setBlurRadius(40)
        shadow.setXOffset(0)
        shadow.setYOffset(16)
        # 暗主题更重一些
        shadow.setColor(QColor(0, 0, 0, 90 if self.current_theme != 'medical' else 70))
        self.card.setGraphicsEffect(shadow)

        # 卡片布局
        layout = QVBoxLayout(self.card)
        layout.setContentsMargins(0, 0, 0, 24)
        layout.setSpacing(0)

        # 顶部状态色条（3-4px）
        self.top_strip = QWidget(self.card)
        self.top_strip.setFixedHeight(4)
        strip_color = {
            'information': self.theme_config['accent_color'],
            'warning': self.theme_config['warning_color'],
            'critical': self.theme_config['danger_color'],
            'question': self.theme_config['primary_color']
        }.get(icon_type, self.theme_config['primary_color'])
        self.top_strip.setStyleSheet(f"background-color: {strip_color}; border-top-left-radius: 12px; border-top-right-radius: 12px;")
        layout.addWidget(self.top_strip)

        # 内容区容器
        content = QWidget(self.card)
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(24, 24, 24, 8)
        content_layout.setSpacing(16)

        # 标题和图标
        title_layout = QHBoxLayout()
        title_layout.setSpacing(12)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        icon_label = QLabel()
        icon_label.setObjectName(f"dialog_icon_{icon_type}")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setFixedSize(36, 36)
        icon_text = self._get_icon_text(icon_type)
        icon_label.setText(icon_text)
        icon_label.setFont(QFont("Microsoft YaHei", 22, QFont.Weight.Bold))

        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        content_layout.addLayout(title_layout)

        # 消息内容
        message_label = QLabel(text)
        message_label.setFont(QFont("Microsoft YaHei", 12))
        message_label.setObjectName("dialog_info")
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)

        # 按钮区
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        if buttons is None:
            buttons = QMessageBox.StandardButton.Ok
        button_width, button_height = 96, 36

        if buttons & QMessageBox.StandardButton.Ok:
            ok_btn = QPushButton("确定")
            ok_btn.setObjectName("btn_primary")
            ok_btn.setFont(QFont("Microsoft YaHei", 12))
            ok_btn.setFixedSize(button_width, button_height)
            ok_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.Ok))
            button_layout.addWidget(ok_btn)

        if buttons & QMessageBox.StandardButton.Yes:
            yes_btn = QPushButton("是")
            yes_btn.setObjectName("btn_primary")
            yes_btn.setFont(QFont("Microsoft YaHei", 12))
            yes_btn.setFixedSize(button_width, button_height)
            yes_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.Yes))
            button_layout.addWidget(yes_btn)

        if buttons & QMessageBox.StandardButton.No:
            no_btn = QPushButton("否")
            no_btn.setObjectName("btn_secondary")
            no_btn.setFont(QFont("Microsoft YaHei", 12))
            no_btn.setFixedSize(button_width, button_height)
            no_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.No))
            button_layout.addWidget(no_btn)

        if buttons & QMessageBox.StandardButton.Cancel:
            cancel_btn = QPushButton("取消")
            cancel_btn.setObjectName("btn_secondary")
            cancel_btn.setFont(QFont("Microsoft YaHei", 12))
            cancel_btn.setFixedSize(button_width, button_height)
            cancel_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.Cancel))
            button_layout.addWidget(cancel_btn)

        # 警告/错误时增加一次轻微抖动提示（入场后触发）
        if self.icon_type in ("warning", "critical"):
            QTimer.singleShot(240, self._shake_card)

        button_layout.addStretch()
        content_layout.addLayout(button_layout)

        layout.addWidget(content)

        # 主布局 - 填充全屏遮罩
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.overlay)

        # 将卡片置于中央
        self._reposition_card()
        self.overlay.resize(self.size())

        # 应用卡片样式（无边框但清晰）
        bg = self.theme_config['bg_secondary']
        text_primary = self.theme_config['text_primary']
        keyline_light = 'rgba(0,0,0,0.08)'
        keyline_dark = 'rgba(255,255,255,0.18)'
        keyline = keyline_light if self.current_theme == 'medical' else keyline_dark
        self.card.setStyleSheet(
            f"QFrame#dialog_card {{ background-color: {bg}; border-radius: 12px; border: 1px solid {keyline}; }}"
        )

        # 启动入场动画
        self._play_enter_animation()


    def resizeEvent(self, event):
        """确保遮罩和卡片始终正确居中"""
        super().resizeEvent(event)
        if hasattr(self, 'overlay'):
            self.overlay.resize(self.size())
            self._reposition_card()

    def _reposition_card(self):
        if hasattr(self, 'card'):
            # 将卡片放在中央位置
            w, h = self.card.sizeHint().width(), self.card.sizeHint().height()
            if w < 420:
                w = 420
            if h < 220:
                h = 220
            self.card.resize(w, h)
            cx = (self.width() - w) // 2
            cy = (self.height() - h) // 2
            self.card.move(max(0, cx), max(0, cy))

    def _play_enter_animation(self):
        """入场动效：遮罩淡入 + 卡片缩放淡入 + 阴影渐强"""
        self.overlay.setWindowOpacity(1.0)
        # 对话框整体透明度
        anim_opacity = QPropertyAnimation(self, b"windowOpacity")
        anim_opacity.setDuration(200)
        anim_opacity.setStartValue(0.0)
        anim_opacity.setEndValue(1.0)
        anim_opacity.setEasingCurve(QEasingCurve.Type.OutCubic)

        # 卡片缩放通过几何动画模拟
        start_rect = self.card.geometry()
        scale = 0.96
        dw = int(start_rect.width() * (1 - scale))
        dh = int(start_rect.height() * (1 - scale))
        start_geo = start_rect.adjusted(dw//2, dh//2, -dw//2, -dh//2)

        anim_geo = QPropertyAnimation(self.card, b"geometry")
        anim_geo.setDuration(200)
        anim_geo.setStartValue(start_geo)
        anim_geo.setEndValue(start_rect)
        anim_geo.setEasingCurve(QEasingCurve.Type.OutCubic)

        group = QParallelAnimationGroup(self)
        group.addAnimation(anim_opacity)
        group.addAnimation(anim_geo)
        group.start()
        self._enter_anim_group = group


    def eventFilter(self, obj, event):
        """跟随父窗口移动/尺寸变化，保持遮罩全屏覆盖"""
        if obj is self._top_window and event.type() in (QEvent.Type.Move, QEvent.Type.Resize):
            self.setGeometry(self._top_window.geometry())
            if hasattr(self, 'overlay'):
                self.overlay.resize(self.size())
                self._reposition_card()
        return super().eventFilter(obj, event)

    def _set_result_and_close(self, result):
        """设置结果并关闭对话框（带退场动效）"""
        self.result_value = result

        # 出场动画
        anim = QPropertyAnimation(self, b"windowOpacity")
        anim.setDuration(160)
        anim.setStartValue(1.0)
        anim.setEndValue(0.0)
        anim.setEasingCurve(QEasingCurve.Type.InOutCubic)

        def _finish():
            if result in [QMessageBox.StandardButton.Ok, QMessageBox.StandardButton.Yes]:
                self.accept()
            else:
                self.reject()
        anim.finished.connect(_finish)
        anim.start()
        self._exit_anim = anim

    def _get_icon_text(self, icon_type):
        """根据图标类型获取图标文本"""
        icon_map = {
            "information": "ℹ",
            "warning": "⚠",
            "critical": "✕",
            "question": "?"
        }
        return icon_map.get(icon_type, "ℹ")

    def paintEvent(self, event):
        """将背景保持透明，由QSS绘制卡片；此处无需额外绘制"""
        super().paintEvent(event)

    def _shake_card(self):
        """轻微抖动（警告/错误时调用），幅度小于8px，时长 ~ 220ms"""
        seq = []
        origin = self.card.pos()
        dx = 6
        # 5段左右位移
        offsets = [QPoint(-dx, 0), QPoint(dx, 0), QPoint(-dx//2, 0), QPoint(dx//2, 0), QPoint(0, 0)]
        animations = []
        start = origin
        for off in offsets:
            anim = QPropertyAnimation(self.card, b"pos")
            anim.setDuration(40)
            anim.setStartValue(start)
            anim.setEndValue(origin + off)
            anim.setEasingCurve(QEasingCurve.Type.InOutSine)
            animations.append(anim)
            start = origin + off
        # 串行动画
        group = QParallelAnimationGroup(self)
        # 用顺序播放手动串联
        def play_next(i=0):
            if i < len(animations):
                animations[i].finished.connect(lambda: play_next(i+1))
                animations[i].start()
        play_next(0)
        self._shake_anims = animations



# 便捷函数
def show_information(parent, title, text):
    """显示信息消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "information", QMessageBox.StandardButton.Ok)
    msg_box.exec()
    return msg_box.result_value

def show_warning(parent, title, text):
    """显示警告消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "warning", QMessageBox.StandardButton.Ok)
    msg_box.exec()
    return msg_box.result_value

def show_critical(parent, title, text):
    """显示错误消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "critical", QMessageBox.StandardButton.Ok)
    msg_box.exec()
    return msg_box.result_value

def show_question(parent, title, text, buttons=QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No):
    """显示询问消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "question", buttons)
    msg_box.exec()
    return msg_box.result_value

# 兼容性别名
information = show_information
warning = show_warning
critical = show_critical
question = show_question
