# -*- coding: utf-8 -*-
"""
治疗记录服务
Treatment Record Service

提供治疗记录的查询和统计功能
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from core.database import db_manager

class TreatmentService:
    """治疗记录服务类"""
    
    def __init__(self):
        self.table_name = "zhiliao"
    
    def get_treatment_count_by_patient(self, patient_id: str) -> int:
        """
        根据患者编号获取治疗次数
        
        Args:
            patient_id: 患者编号
            
        Returns:
            治疗次数
        """
        try:
            sql = f"SELECT COUNT(*) as count FROM {self.table_name} WHERE bianh = ?"
            result = db_manager.execute_one(sql, (patient_id,))
            return result['count'] if result else 0
        except Exception as e:
            print(f"获取患者治疗次数失败: {e}")
            return 0

    def get_latest_treatment_date(self, patient_id: str) -> Optional[str]:
        """
        根据患者编号获取最新治疗记录的日期

        Args:
            patient_id: 患者编号

        Returns:
            最新治疗日期字符串，如果没有记录则返回None
        """
        try:
            sql = f"SELECT rq FROM {self.table_name} WHERE bianh = ? ORDER BY rq DESC LIMIT 1"
            result = db_manager.execute_one(sql, (patient_id,))
            return result['rq'] if result else None
        except Exception as e:
            print(f"获取患者最新治疗日期失败: {e}")
            return None

    def get_treatments_by_patient(self, patient_id: str, time_filter: str = "全部记录") -> List[Dict[str, Any]]:
        """
        根据患者编号获取治疗记录列表

        Args:
            patient_id: 患者编号
            time_filter: 时间筛选条件

        Returns:
            治疗记录列表
        """
        try:
            # 构建时间筛选条件 - 使用本机时间计算
            from datetime import datetime, timedelta
            time_condition = ""
            params = [patient_id]

            if time_filter == "最近7天":
                cutoff_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
                time_condition = " AND rq >= ?"
                params.append(cutoff_date)
            elif time_filter == "最近30天":
                cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                time_condition = " AND rq >= ?"
                params.append(cutoff_date)
            elif time_filter == "最近3个月":
                cutoff_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
                time_condition = " AND rq >= ?"
                params.append(cutoff_date)

            sql = f"SELECT * FROM {self.table_name} WHERE bianh = ?{time_condition} ORDER BY rq DESC"
            rows = db_manager.execute_query(sql, tuple(params))
            
            treatments = []
            for row in rows:
                treatment = {
                    'id': row['zhiliaobh'] if 'zhiliaobh' in row.keys() else '',
                    'patient_id': row['bianh'] if 'bianh' in row.keys() else '',
                    'date': row['rq'] if 'rq' in row.keys() else '',
                    'duration': row['shijian'] if 'shijian' in row.keys() else 0,
                    'effect': row['xiaoguo'] if 'xiaoguo' in row.keys() else '',
                    'required_count': row['yaoqiucs'] if 'yaoqiucs' in row.keys() else 0,
                    'actual_count': row['shijics'] if 'shijics' in row.keys() else 0,
                    'score': row['defen'] if 'defen' in row.keys() else 0,
                    'hospital_id': row['yiyuanid'] if 'yiyuanid' in row.keys() else 0,
                    'department': row['keshiming'] if 'keshiming' in row.keys() else '',
                    'doctor': row['zhuzhiyis'] if 'zhuzhiyis' in row.keys() else '',
                    'operator': row['czy'] if 'czy' in row.keys() else '',
                    'id_card': row['shenfenzh'] if 'shenfenzh' in row.keys() else '',
                    'device_id': row['shebeih'] if 'shebeih' in row.keys() else '',
                    'status': row['status'] if 'status' in row.keys() else '',
                    'eeg_channels': row['eeg_channels'] if 'eeg_channels' in row.keys() else 8,
                    'stimulation_params': row['stimulation_params'] if 'stimulation_params' in row.keys() else ''
                }
                treatments.append(treatment)
            
            return treatments
        except Exception as e:
            print(f"获取患者治疗记录失败: {e}")
            return []
    
    def get_patient_statistics(self, patient_id: str) -> Dict[str, Any]:
        """
        获取患者治疗统计信息

        Args:
            patient_id: 患者编号

        Returns:
            统计信息字典
        """
        try:
            sql = f"""
                SELECT 
                    COUNT(*) as total_treatments,
                    SUM(shijian) as total_duration,
                    AVG(defen) as avg_score,
                    MAX(rq) as last_treatment_date
                FROM {self.table_name} 
                WHERE bianh = ?
            """
            result = db_manager.execute_one(sql, (patient_id,))
            
            if result:
                return {
                    'total_treatments': result['total_treatments'] or 0,
                    'total_duration': result['total_duration'] or 0,
                    'avg_score': round(result['avg_score'] or 0, 1),
                    'last_treatment_date': result['last_treatment_date'] or ''
                }
            else:
                return {
                    'total_treatments': 0,
                    'total_duration': 0,
                    'avg_score': 0.0,
                    'last_treatment_date': ''
                }
        except Exception as e:
            print(f"获取患者治疗统计失败: {e}")
            return {
                'total_treatments': 0,
                'total_duration': 0,
                'avg_score': 0.0,
                'last_treatment_date': ''
            }
    
    def get_recent_treatments(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的治疗记录
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            最近治疗记录列表
        """
        try:
            sql = f"""
                SELECT z.*, b.name as patient_name 
                FROM {self.table_name} z
                LEFT JOIN bingren b ON z.bianh = b.bianhao
                ORDER BY z.rq DESC 
                LIMIT ?
            """
            rows = db_manager.execute_query(sql, (limit,))
            
            treatments = []
            for row in rows:
                treatment = {
                    'id': row['zhiliaobh'] if 'zhiliaobh' in row.keys() else '',
                    'patient_id': row['bianh'] if 'bianh' in row.keys() else '',
                    'patient_name': row['patient_name'] if 'patient_name' in row.keys() else '未知',
                    'date': row['rq'] if 'rq' in row.keys() else '',
                    'duration': row['shijian'] if 'shijian' in row.keys() else 0,
                    'effect': row['xiaoguo'] if 'xiaoguo' in row.keys() else '',
                    'score': row['defen'] if 'defen' in row.keys() else 0,
                    'status': row['status'] if 'status' in row.keys() else ''
                }
                treatments.append(treatment)
            
            return treatments
        except Exception as e:
            print(f"获取最近治疗记录失败: {e}")
            return []

    def get_treatment_statistics(self, start_date: str = None, end_date: str = None,
                               patient_ids: List[str] = None, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        获取治疗统计数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD) - 已弃用，使用filters参数
            end_date: 结束日期 (YYYY-MM-DD) - 已弃用，使用filters参数
            patient_ids: 患者ID列表 - 已弃用，使用filters参数
            filters: 筛选条件字典

        Returns:
            统计数据字典
        """
        try:
            # 优先使用filters参数，兼容旧的参数方式
            if filters:
                start_date = filters.get('start_date')
                end_date = filters.get('end_date')
                patient_search = filters.get('patient_search', '')
                age_range = filters.get('age_range', '全部年龄')
                gender = filters.get('gender', '全部')
                effects = filters.get('effects', ['优', '良', '中', '差'])
            else:
                # 使用传统参数或默认值
                patient_search = ''
                age_range = '全部年龄'
                gender = '全部'
                effects = ['优', '良', '中', '差']

            # 构建WHERE条件
            where_conditions = []
            params = []

            # 时间筛选
            if start_date:
                where_conditions.append("DATE(z.rq) >= ?")
                params.append(start_date)

            if end_date:
                where_conditions.append("DATE(z.rq) <= ?")
                params.append(end_date)

            # 患者筛选
            if patient_ids:
                placeholders = ','.join(['?' for _ in patient_ids])
                where_conditions.append(f"z.bianh IN ({placeholders})")
                params.extend(patient_ids)
            elif patient_search:
                # 根据患者姓名或编号搜索
                where_conditions.append("(b.name LIKE ? OR b.bianhao LIKE ?)")
                params.extend([f"%{patient_search}%", f"%{patient_search}%"])

            # 年龄筛选
            if age_range != '全部年龄':
                if age_range == '未成年 (<18)':
                    where_conditions.append("b.age < 18")
                elif age_range == '青年 (18-30)':
                    where_conditions.append("b.age BETWEEN 18 AND 30")
                elif age_range == '中年 (31-50)':
                    where_conditions.append("b.age BETWEEN 31 AND 50")
                elif age_range == '中老年 (51-70)':
                    where_conditions.append("b.age BETWEEN 51 AND 70")
                elif age_range == '老年 (>70)':
                    where_conditions.append("b.age > 70")

            # 性别筛选
            if gender != '全部':
                where_conditions.append("b.xingbie = ?")
                params.append(gender)

            # 治疗效果筛选
            if effects and len(effects) < 4:  # 如果不是全选
                effect_placeholders = ','.join(['?' for _ in effects])
                where_conditions.append(f"z.xiaoguo IN ({effect_placeholders})")
                params.extend(effects)

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 基础统计查询 - 需要JOIN患者表以支持患者信息筛选
            sql = f"""
                SELECT
                    COUNT(*) as total_treatments,
                    COUNT(DISTINCT z.bianh) as total_patients,
                    SUM(z.shijian) as total_duration,
                    AVG(z.defen) as avg_score,
                    MIN(z.rq) as first_date,
                    MAX(z.rq) as last_date
                FROM {self.table_name} z
                LEFT JOIN bingren b ON z.bianh = b.bianhao
                {where_clause}
            """

            basic_stats = db_manager.execute_one(sql, tuple(params))

            # 治疗效果分布统计
            effect_sql = f"""
                SELECT
                    z.xiaoguo as effect,
                    COUNT(*) as count,
                    AVG(z.defen) as avg_score
                FROM {self.table_name} z
                LEFT JOIN bingren b ON z.bianh = b.bianhao
                {where_clause}
                GROUP BY z.xiaoguo
                ORDER BY count DESC
            """

            effect_stats = db_manager.execute_query(effect_sql, tuple(params))

            # 按日期统计（用于趋势分析）
            daily_sql = f"""
                SELECT
                    DATE(z.rq) as date,
                    COUNT(*) as treatment_count,
                    AVG(z.defen) as avg_score,
                    COUNT(DISTINCT z.bianh) as patient_count
                FROM {self.table_name} z
                LEFT JOIN bingren b ON z.bianh = b.bianhao
                {where_clause}
                GROUP BY DATE(z.rq)
                ORDER BY DATE(z.rq)
            """

            daily_stats = db_manager.execute_query(daily_sql, tuple(params))

            return {
                'basic_stats': {
                    'total_treatments': basic_stats['total_treatments'] or 0,
                    'total_patients': basic_stats['total_patients'] or 0,
                    'total_duration': basic_stats['total_duration'] or 0,
                    'avg_score': round(basic_stats['avg_score'] or 0, 1),
                    'first_date': basic_stats['first_date'] or '',
                    'last_date': basic_stats['last_date'] or ''
                },
                'effect_distribution': [
                    {
                        'effect': row['effect'] or '未知',
                        'count': row['count'],
                        'avg_score': round(row['avg_score'] or 0, 1)
                    } for row in effect_stats
                ],
                'daily_trends': [
                    {
                        'date': row['date'],
                        'treatment_count': row['treatment_count'],
                        'avg_score': round(row['avg_score'] or 0, 1),
                        'patient_count': row['patient_count']
                    } for row in daily_stats
                ]
            }
        except Exception as e:
            print(f"获取治疗统计数据失败: {e}")
            return {
                'basic_stats': {
                    'total_treatments': 0,
                    'total_patients': 0,
                    'total_duration': 0,
                    'avg_score': 0.0,
                    'first_date': '',
                    'last_date': ''
                },
                'effect_distribution': [],
                'daily_trends': []
            }



# 全局治疗服务实例
treatment_service = TreatmentService()
