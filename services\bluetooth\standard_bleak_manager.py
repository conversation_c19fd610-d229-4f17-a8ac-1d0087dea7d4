"""
标准Bleak蓝牙管理器
基于官方最佳实践，使用async with模式确保服务发现完成
专门针对BLE5201医疗设备优化
"""

import asyncio
import logging
import json
import os
import time
from enum import Enum
from typing import Optional, Callable, Dict, Any
from PySide6.QtCore import QObject, Signal, QThread

# 检查Bleak可用性
try:
    from bleak import BleakClient, BleakScanner
    BLEAK_AVAILABLE = True
except ImportError:
    BLEAK_AVAILABLE = False
    BleakClient = None
    BleakScanner = None


class ConnectionState(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    SCANNING = "scanning"
    CONNECTING = "connecting"
    CONNECTED = "connected"


class BluetoothErrorType(Enum):
    """蓝牙错误类型枚举"""
    DEVICE_NOT_FOUND = "device_not_found"
    CONNECTION_TIMEOUT = "connection_timeout"
    SERVICE_DISCOVERY_FAILED = "service_discovery_failed"
    CHARACTERISTIC_NOT_FOUND = "characteristic_not_found"
    GATT_ERROR = "gatt_error"
    GATT_UNREACHABLE = "gatt_unreachable"  # 新增：GATT服务不可达
    WINDOWS_CATASTROPHIC_FAILURE = "windows_catastrophic_failure"  # 新增：Windows灾难性故障
    SYSTEM_ERROR = "system_error"
    WINDOWS_BLUETOOTH_ERROR = "windows_bluetooth_error"
    PERMISSION_ERROR = "permission_error"


class StandardBleakManager(QObject):
    """
    标准Bleak蓝牙管理器
    使用官方推荐的async with模式，确保服务发现完成后再进行GATT操作
    """

    # Qt信号定义
    state_changed = Signal(str)  # 状态变化信号
    device_found = Signal(str, str, int)  # 设备发现信号 (name, address, rssi)
    connected = Signal(str)  # 连接成功信号 (address)
    disconnected = Signal(str)  # 断开连接信号 (address)
    connection_failed = Signal(str, str)  # 连接失败信号 (address, error)
    data_received = Signal(bytes)  # 数据接收信号

    def __init__(self):
        super().__init__()

        self.logger = logging.getLogger(__name__)

        # 基础配置 - 针对Windows环境和BLE5201设备优化
        self.config = {
            "device_name": "BLE5201",
            "scan_timeout": 15,                # 延长扫描超时，提高设备发现成功率
            "connect_timeout": 30,             # 延长连接超时，适应Windows蓝牙栈
            "address_type": "public",          # 🔧 修复：恢复默认的public地址类型，大多数BLE设备使用public地址
            "max_retry_attempts": 3,           # 连接重试次数
            "retry_delays": [1, 2, 5],         # 重试间隔（秒），递增延迟
            "service_discovery_delay": 2,      # 服务发现后额外延迟，确保服务完全可用
            "connection_check_interval": 0.5,  # 连接状态检查间隔
            "heartbeat_timeout": 2.0,          # 心跳超时（秒）：超过该时间未收到数据则判定断开
            "connection_monitor_enabled": True, # 启用连接监控
            "reconnect_enabled": True,         # 启用自动重连
            "quality_threshold": 0.7,          # 连接质量阈值
            # GATT错误恢复相关配置
            "gatt_recovery_base_time": 5.0,    # GATT恢复基础等待时间（秒）
            "gatt_recovery_increment": 2.0,    # 每次重试的额外等待时间（秒）
            "enable_windows_cache_cleanup": True,  # 启用Windows蓝牙缓存清理
        }

        # 状态管理
        self.state = ConnectionState.DISCONNECTED
        self.current_device_address = None
        self.is_running = False

        # 工作线程
        self.worker_thread = None

        # 连接监控和质量管理
        self.connection_monitor_active = False
        self.last_data_time = None
        self.connection_quality_score = 0.0
        self.connection_attempt_count = 0
        self.last_connection_attempt_time = None
        self.connection_statistics = {
            'total_attempts': 0,
            'successful_connections': 0,
            'connection_failures': {},
            'average_connection_time': 0.0
        }
        # 心跳与数据质量监控
        self.last_data_time = None
        self.invalid_packet_streak = 0  # 连续无效包计数（包头/包尾缺失）
        self.invalid_packet_threshold = 10  # 连续无效包超过阈值判定连接异常




        # 加载配置
        self._load_config()

        self.logger.info("标准Bleak蓝牙管理器初始化成功")

    def _load_config(self):
        """从settings.json加载配置"""
        try:
            from utils.path_manager import get_config_file_in_dir
            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # 从eeg配置中读取参数
                eeg_config = settings.get("eeg", {})

                self.config['device_name'] = eeg_config.get('device_name', 'BLE5201')
                self.config['scan_timeout'] = eeg_config.get('scan_timeout', 10)
                self.config['connect_timeout'] = eeg_config.get('connect_timeout', 20)

                self.logger.info(f"配置加载成功: 设备名称={self.config['device_name']}, "
                               f"扫描超时={self.config['scan_timeout']}s, "
                               f"连接超时={self.config['connect_timeout']}s")

        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认配置: {e}")

    def _set_state(self, new_state: ConnectionState):
        """设置连接状态"""
        if self.state != new_state:
            self.state = new_state
            self.logger.debug(f"状态变更: {new_state.value}")

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.state == ConnectionState.CONNECTED

    def is_connecting(self) -> bool:
        """检查是否正在连接"""
        return self.state == ConnectionState.CONNECTING

    def _classify_error(self, exception: Exception) -> BluetoothErrorType:
        """
        错误分类方法，根据异常类型和消息内容分类错误

        Args:
            exception: 捕获的异常

        Returns:
            BluetoothErrorType: 错误类型
        """
        error_msg = str(exception).lower()

        # 检查GATT服务不可达错误（BLE5201特有问题）
        if "gatt services" in error_msg and "unreachable" in error_msg:
            return BluetoothErrorType.GATT_UNREACHABLE
        if "could not get gatt" in error_msg or "gatt unreachable" in error_msg:
            return BluetoothErrorType.GATT_UNREACHABLE

        # 检查连接超时错误
        if "timeout" in error_msg or "timed out" in error_msg:
            return BluetoothErrorType.CONNECTION_TIMEOUT

        # 检查设备未找到错误
        if "device not found" in error_msg or "no device found" in error_msg:
            return BluetoothErrorType.DEVICE_NOT_FOUND

        # 检查服务发现失败
        if "service" in error_msg and ("not found" in error_msg or "failed" in error_msg):
            return BluetoothErrorType.SERVICE_DISCOVERY_FAILED

        # 检查特征值未找到
        if "characteristic" in error_msg and "not found" in error_msg:
            return BluetoothErrorType.CHARACTERISTIC_NOT_FOUND

        # 检查GATT错误
        if "gatt" in error_msg or "characteristic" in error_msg or "write" in error_msg:
            return BluetoothErrorType.GATT_ERROR

        # 检查权限错误
        if "permission" in error_msg or "access" in error_msg:
            return BluetoothErrorType.PERMISSION_ERROR

        # 检查Windows特定错误
        if "windows" in error_msg or "winrt" in error_msg or "win32" in error_msg:
            return BluetoothErrorType.WINDOWS_BLUETOOTH_ERROR

        # 检查Windows灾难性故障
        if "winerror" in error_msg and "2147418113" in error_msg:
            return BluetoothErrorType.WINDOWS_CATASTROPHIC_FAILURE
        if "灾难性故障" in error_msg or "catastrophic" in error_msg:
            return BluetoothErrorType.WINDOWS_CATASTROPHIC_FAILURE

        # 默认为系统错误
        return BluetoothErrorType.SYSTEM_ERROR

    def get_connection_statistics(self) -> Dict[str, Any]:
        """
        获取连接统计信息

        Returns:
            Dict: 连接统计数据
        """
        return {
            'connection_state': self.state.value,
            'device_address': self.current_device_address,
            'connection_quality_score': self.connection_quality_score,
            'total_attempts': self.connection_statistics['total_attempts'],
            'successful_connections': self.connection_statistics['successful_connections'],
            'success_rate': (self.connection_statistics['successful_connections'] /
                           max(1, self.connection_statistics['total_attempts'])) * 100,
            'connection_failures': self.connection_statistics['connection_failures'],
            'average_connection_time': self.connection_statistics['average_connection_time'],
            'last_data_time': self.last_data_time
        }

    def start_scan_and_connect(self):
        """开始扫描并连接设备"""
        if not BLEAK_AVAILABLE:
            self.connection_failed.emit("", "Bleak库不可用")
            return

        if self.is_connecting() or self.is_connected():
            self.logger.warning("设备已连接或正在连接中")
            return

        if self.worker_thread and self.worker_thread.isRunning():
            self.logger.warning("工作线程正在运行")
            return

        # 启动工作线程
        self._start_worker_thread()

    def disconnect_device(self):
        """断开连接"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.logger.info("用户请求断开连接")
            self.is_running = False
            # 工作线程会检测到is_running=False并自动断开

    def _start_worker_thread(self):
        """启动工作线程"""
        self.is_running = True
        self.worker_thread = StandardBleakWorkerThread(self.config, self)
        self.worker_thread.finished.connect(self._on_worker_finished)
        self.worker_thread.start()

    def _on_worker_finished(self):
        """工作线程完成回调"""
        self.is_running = False
        if self.state == ConnectionState.CONNECTED:
            self._set_state(ConnectionState.DISCONNECTED)
            self.state_changed.emit("连接已断开")
            if self.current_device_address:
                self.disconnected.emit(self.current_device_address)

        self.current_device_address = None
        self.logger.info("工作线程已结束")

    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理标准Bleak管理器")

        # 停止运行标志
        self.is_running = False

        # 等待工作线程结束
        if self.worker_thread and self.worker_thread.isRunning():
            self.logger.info("正在停止工作线程...")
            self.worker_thread.quit()

            # 等待线程正常退出
            if not self.worker_thread.wait(3000):  # 等待3秒
                self.logger.warning("工作线程未能正常退出，强制终止")
                self.worker_thread.terminate()
                self.worker_thread.wait(1000)  # 再等待1秒确保终止

            self.logger.info("工作线程已停止")

        # 重置状态
        self._set_state(ConnectionState.DISCONNECTED)
        self.current_device_address = None

        self.logger.info("标准Bleak管理器清理完成")


class StandardBleakWorkerThread(QThread):
    """
    标准Bleak工作线程
    使用async with模式确保服务发现完成
    """

    def __init__(self, config: dict, manager: StandardBleakManager):
        super().__init__()
        self.config = config
        self.manager = manager
        self.logger = logging.getLogger(__name__)

    def run(self):
        """线程主函数"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 运行异步连接流程
            loop.run_until_complete(self._async_main())

        except Exception as e:
            # 只捕获事件循环相关的严重错误，不要捕获连接相关的异常
            # 连接相关的异常应该在_async_main中处理
            if "event loop" in str(e).lower() or "asyncio" in str(e).lower():
                self.logger.error(f"事件循环异常: {e}")
                self.manager.connection_failed.emit("", f"事件循环错误: {str(e)}")
            else:
                # 其他异常重新抛出，让_async_main处理
                self.logger.error(f"工作线程严重异常: {e}")
                self.manager.connection_failed.emit("", f"工作线程异常: {str(e)}")
        finally:
            # 清理事件循环
            try:
                loop.close()
            except:
                pass

    async def _async_main(self):
        """异步主流程"""
        # 扫描设备
        try:
            device = await self._scan_for_device()
        except Exception as e:
            self.logger.error(f"设备扫描失败: {e}")
            self.manager._set_state(ConnectionState.DISCONNECTED)
            self.manager.state_changed.emit("设备扫描失败")
            self.manager.connection_failed.emit("", f"扫描失败: {str(e)}")
            return

        if not device:
            self.manager._set_state(ConnectionState.DISCONNECTED)
            self.manager.state_changed.emit("未找到目标设备")
            self.manager.connection_failed.emit("", "未找到目标设备")
            return

        # 使用重试机制连接设备
        # _retry_connection内部已经处理了所有异常，不需要在这里再次捕获
        connection_success = await self._retry_connection(device)
        if not connection_success:
            self.manager._set_state(ConnectionState.DISCONNECTED)
            self.manager.state_changed.emit("连接失败，已尝试所有重试")
            self.manager.connection_failed.emit(device.address, "重试次数已耗尽")

    async def _retry_connection(self, device, max_attempts: int = None) -> bool:
        """
        重试连接机制

        Args:
            device: 蓝牙设备对象
            max_attempts: 最大重试次数，如果为None则使用配置中的值

        Returns:
            bool: 连接是否成功
        """
        if max_attempts is None:
            max_attempts = self.config.get('max_retry_attempts', 3)

        retry_delays = self.config.get('retry_delays', [1, 2, 5])

        self.logger.info(f"=== 开始重试连接逻辑 ===")
        self.logger.info(f"设备地址: {device.address}")
        self.logger.info(f"最大重试次数: {max_attempts}")
        self.logger.info(f"重试延迟配置: {retry_delays}")

        for attempt in range(max_attempts):
            try:
                self.logger.info(f"尝试连接设备 {device.address}，第 {attempt + 1}/{max_attempts} 次")

                # 记录连接尝试统计
                self.manager.connection_statistics['total_attempts'] += 1
                connection_start_time = time.time()

                # 尝试连接
                await self._connect_with_async_context(device)

                # 连接成功，更新统计信息
                connection_time = time.time() - connection_start_time
                self.manager.connection_statistics['successful_connections'] += 1

                # 更新平均连接时间
                total_successful = self.manager.connection_statistics['successful_connections']
                current_avg = self.manager.connection_statistics['average_connection_time']
                new_avg = ((current_avg * (total_successful - 1)) + connection_time) / total_successful
                self.manager.connection_statistics['average_connection_time'] = new_avg

                self.logger.info(f"连接成功，耗时: {connection_time:.2f}秒")
                return True

            except Exception as e:
                # 分类错误并记录统计
                error_type = self.manager._classify_error(e)
                error_key = error_type.value

                if error_key not in self.manager.connection_statistics['connection_failures']:
                    self.manager.connection_statistics['connection_failures'][error_key] = 0
                self.manager.connection_statistics['connection_failures'][error_key] += 1

                self.logger.warning(f"连接尝试 {attempt + 1} 失败: {e} (类型: {error_type.value})")

                # 如果不是最后一次尝试，则等待重试
                if attempt < max_attempts - 1:
                    self.logger.info(f"=== 准备重试 {attempt + 2}/{max_attempts} ===")
                    # 针对GATT_UNREACHABLE错误的特殊处理
                    if error_type == BluetoothErrorType.GATT_UNREACHABLE:
                        self.logger.warning("检测到GATT服务不可达错误，执行设备重置序列...")
                        delay = await self._handle_gatt_unreachable_error(attempt)
                        self.logger.info(f"GATT错误处理完成，延迟时间: {delay}秒")
                    # 针对Windows灾难性故障的特殊处理
                    elif error_type == BluetoothErrorType.WINDOWS_CATASTROPHIC_FAILURE:
                        self.logger.error("检测到Windows蓝牙灾难性故障，执行系统级恢复...")
                        delay = await self._handle_windows_catastrophic_failure(attempt)
                        self.logger.info(f"灾难性故障处理完成，延迟时间: {delay}秒")
                    else:
                        delay = retry_delays[min(attempt, len(retry_delays) - 1)]
                        self.logger.info(f"使用标准重试延迟: {delay}秒")

                    self.logger.info(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)

                    # 清理状态，为下次重试做准备
                    self.logger.info("执行重试前清理...")
                    await self._cleanup_before_retry()
                    self.logger.info("清理完成，即将开始下次重试")
                else:
                    self.logger.error(f"=== 所有重试尝试均已耗尽 ===")
                    self.logger.error(f"所有连接尝试均失败，最后错误: {e}")

        return False

    async def _cleanup_before_retry(self):
        """
        重试前的清理操作
        """
        try:
            # 重置连接状态
            self.manager._set_state(ConnectionState.DISCONNECTED)

            # 可以在这里添加其他清理操作，如清理蓝牙缓存等
            await asyncio.sleep(0.1)  # 短暂延迟，让系统稳定

        except Exception as e:
            self.logger.warning(f"重试前清理操作失败: {e}")

    async def _handle_gatt_unreachable_error(self, attempt: int) -> float:
        """
        处理GATT服务不可达错误的特殊恢复序列

        Args:
            attempt: 当前重试次数

        Returns:
            float: 建议的等待时间（秒）
        """
        try:
            self.logger.info("🔧 开始BLE5201设备GATT恢复序列...")

            # 步骤1：强制重置连接状态
            self.manager._set_state(ConnectionState.DISCONNECTED)
            await asyncio.sleep(1.0)

            # 步骤2：清理系统蓝牙缓存（Windows特定）
            try:
                await self._clear_windows_bluetooth_cache()
            except Exception as cache_error:
                self.logger.warning(f"清理蓝牙缓存失败: {cache_error}")

            # 步骤3：给BLE5201设备足够的恢复时间
            # 根据蓝牙说明书，设备在断开后可能需要时间来重置内部状态
            base_recovery_time = self.config.get('gatt_recovery_base_time', 5.0)
            additional_time = attempt * self.config.get('gatt_recovery_increment', 2.0)
            total_recovery_time = base_recovery_time + additional_time

            self.logger.info(f"⏳ 等待BLE5201设备恢复，预计 {total_recovery_time} 秒...")
            self.logger.info("💡 建议检查：1)设备是否正常供电 2)是否有其他程序占用设备 3)设备是否需要手动重启")

            return total_recovery_time

        except Exception as e:
            self.logger.error(f"GATT恢复序列执行失败: {e}")
            # 返回默认较长的等待时间
            return 10.0

    async def _handle_windows_catastrophic_failure(self, attempt: int) -> float:
        """
        处理Windows蓝牙灾难性故障的特殊恢复序列

        Args:
            attempt: 当前重试次数

        Returns:
            float: 建议的等待时间（秒）
        """
        try:
            self.logger.error("💥 开始Windows蓝牙灾难性故障恢复序列...")

            # 步骤1：强制重置连接状态和资源
            self.logger.info("1. 强制重置连接状态...")
            self.manager._set_state(ConnectionState.DISCONNECTED)
            await asyncio.sleep(2.0)

            # 步骤2：尝试温和的蓝牙资源清理
            self.logger.info("2. 清理蓝牙资源...")
            await self._emergency_bluetooth_cleanup()

            # 步骤3：给Windows蓝牙栈足够的恢复时间
            base_recovery_time = 15.0  # 灾难性故障需要更长恢复时间
            additional_time = attempt * 5.0  # 每次重试额外增加5秒
            total_recovery_time = base_recovery_time + additional_time

            self.logger.error(f"⚠️  Windows蓝牙发生灾难性故障，需要 {total_recovery_time} 秒恢复时间")
            self.logger.error("💡 强烈建议：")
            self.logger.error("   1. 检查Windows蓝牙服务是否正常")
            self.logger.error("   2. 重启应用程序")
            self.logger.error("   3. 如问题持续，请重启计算机")
            self.logger.error("   4. 检查蓝牙驱动程序是否需要更新")

            return total_recovery_time

        except Exception as e:
            self.logger.error(f"Windows灾难性故障恢复序列执行失败: {e}")
            return 30.0  # 返回长等待时间

    async def _emergency_bluetooth_cleanup(self):
        """紧急蓝牙资源清理"""
        try:
            self.logger.info("🧹 执行紧急蓝牙资源清理...")

            # 清理Python层面的资源
            import gc
            gc.collect()  # 强制垃圾回收

            # 给系统更多时间释放蓝牙资源
            await asyncio.sleep(3.0)

            self.logger.info("✅ 紧急蓝牙资源清理完成")

        except Exception as e:
            self.logger.warning(f"紧急蓝牙资源清理异常: {e}")

    async def _restart_bluetooth_service(self):
        """重启Windows蓝牙服务（已弃用，太激进）"""
        # 这个方法现在只是一个占位符，不执行实际操作
        pass

    async def _restart_bluetooth_adapter(self):
        """重启Windows蓝牙适配器（已弃用，太激进）"""
        # 这个方法现在只是一个占位符，不执行实际操作
        pass

    async def _restart_windows_system(self):
        """重启Windows系统（已弃用，太危险）"""
        # 这个方法现在只是一个占位符，不执行实际操作
        pass

    async def _clear_windows_bluetooth_cache(self):
        """
        清理Windows蓝牙缓存的尝试
        """
        try:
            # 检查是否启用了Windows缓存清理
            if not self.config.get('enable_windows_cache_cleanup', True):
                self.logger.debug("Windows蓝牙缓存清理已禁用")
                return

            import platform
            if platform.system().lower() == 'windows':
                self.logger.info("🧹 尝试清理Windows蓝牙缓存...")

                # 方法1：短暂延迟让Windows释放资源
                await asyncio.sleep(2.0)

                # 方法2：可以在这里添加更多Windows特定的清理操作
                # 例如：重启蓝牙适配器、清理注册表缓存等
                # 但需要谨慎，避免影响系统稳定性

                self.logger.info("✅ Windows蓝牙缓存清理完成")
            else:
                self.logger.debug("非Windows系统，跳过蓝牙缓存清理")

        except Exception as e:
            self.logger.warning(f"Windows蓝牙缓存清理异常: {e}")
            # 不抛出异常，继续执行其他恢复步骤

    async def _verify_services_with_retry(self, client: BleakClient, max_attempts: int = 3) -> bool:
        """
        带重试的服务验证

        Args:
            client: Bleak客户端
            max_attempts: 最大重试次数

        Returns:
            bool: 验证是否成功
        """
        required_char_uuids = [
            "0000fff1-0000-1000-8000-00805f9b34fb",  # 读取/通知特征值
            "0000fff2-0000-1000-8000-00805f9b34fb"   # 写入特征值
        ]

        for attempt in range(max_attempts):
            try:
                self.logger.debug(f"服务验证尝试 {attempt + 1}/{max_attempts}")

                # 验证连接状态
                if not client.is_connected:
                    self.logger.warning("验证期间连接断开")
                    return False

                # 统计服务和特征值
                service_count = 0
                found_chars = []

                for service in client.services:
                    service_count += 1
                    self.logger.debug(f"验证服务: {service.uuid}")

                    # 检查每个特征值
                    for char in service.characteristics:
                        char_uuid = char.uuid
                        if char_uuid in required_char_uuids:
                            found_chars.append(char_uuid)
                            self.logger.debug(f"找到目标特征值: {char_uuid} "
                                           f"(属性: {', '.join(char.properties)})")

                self.logger.info(f"服务验证结果: 服务数量={service_count}, "
                               f"找到特征值={len(found_chars)}/2")

                # 检查是否找到所有必需的特征值
                if len(found_chars) >= 2:
                    # 验证特征值属性
                    char_properties_valid = await self._verify_characteristic_properties(client, found_chars)
                    if char_properties_valid:
                        self.logger.info("服务验证成功，所有必需的服务和特征值均可用")
                        return True
                    else:
                        self.logger.warning("特征值属性验证失败")
                else:
                    self.logger.warning(f"特征值不完整，仅找到: {found_chars}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_attempts - 1:
                    retry_delay = 1.0  # 服务验证重试间隔较短
                    self.logger.info(f"等待 {retry_delay} 秒后重新验证服务...")
                    await asyncio.sleep(retry_delay)

            except Exception as e:
                self.logger.warning(f"服务验证尝试 {attempt + 1} 异常: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(1.0)

        self.logger.error("服务验证失败，所有重试均未成功")
        return False

    async def _verify_characteristic_properties(self, client: BleakClient, found_chars: list) -> bool:
        """
        验证特征值属性是否符合BLE5201要求

        Args:
            client: Bleak客户端
            found_chars: 找到的特征值UUID列表

        Returns:
            bool: 属性验证是否成功
        """
        try:
            read_char_uuid = "0000fff1-0000-1000-8000-00805f9b34fb"
            write_char_uuid = "0000fff2-0000-1000-8000-00805f9b34fb"

            # 验证读取特征值属性
            if read_char_uuid in found_chars:
                try:
                    char = client.services.get_characteristic(read_char_uuid)
                    if char and ("read" in char.properties or "notify" in char.properties):
                        self.logger.debug(f"读取特征值属性验证通过: {char.properties}")
                    else:
                        self.logger.warning(f"读取特征值属性不符合要求: {char.properties if char else 'None'}")
                        return False
                except Exception as e:
                    self.logger.warning(f"读取特征值属性验证异常: {e}")
                    return False

            # 验证写入特征值属性
            if write_char_uuid in found_chars:
                try:
                    char = client.services.get_characteristic(write_char_uuid)
                    if char and ("write" in char.properties or "write-without-response" in char.properties):
                        self.logger.debug(f"写入特征值属性验证通过: {char.properties}")
                    else:
                        self.logger.warning(f"写入特征值属性不符合要求: {char.properties if char else 'None'}")
                        return False
                except Exception as e:
                    self.logger.warning(f"写入特征值属性验证异常: {e}")
                    return False

            return True

        except Exception as e:
            self.logger.warning(f"特征值属性验证失败: {e}")
            return False

    async def _scan_for_device(self):
        """扫描目标设备"""
        self.manager._set_state(ConnectionState.SCANNING)
        self.manager.state_changed.emit("正在扫描蓝牙设备...")

        found_device = None

        def detection_callback(device, advertisement_data):
            nonlocal found_device
            if device.name == self.config['device_name'] and not found_device:
                found_device = device
                rssi = advertisement_data.rssi if advertisement_data else -100
                self.logger.info(f"发现设备: {device.name} ({device.address}) RSSI: {rssi}dBm")
                self.manager.device_found.emit(device.name, device.address, rssi)

        # 使用官方推荐的扫描模式
        async with BleakScanner(detection_callback) as scanner:
            scan_start = asyncio.get_event_loop().time()
            while (not found_device and
                   (asyncio.get_event_loop().time() - scan_start) < self.config['scan_timeout'] and
                   self.manager.is_running):
                await asyncio.sleep(0.2)

        return found_device

    async def _connect_with_async_context(self, device):
        """使用async with模式连接设备 - 简化版本，只使用public地址类型"""
        device_address = device.address
        device_name = device.name or "Unknown"

        self.manager._set_state(ConnectionState.CONNECTING)
        self.manager.state_changed.emit(f"正在连接 {device_name}...")

        try:
            # 🎯 核心：使用官方推荐的 async with 模式，只使用public地址类型
            async with BleakClient(
                device_address,
                timeout=self.config['connect_timeout'],
                address_type='public'  # 🔧 简化：只使用public地址类型
            ) as client:

                # 连接成功，等待服务发现稳定
                service_discovery_delay = self.config.get('service_discovery_delay', 2)
                self.logger.info(f"连接建立，等待 {service_discovery_delay} 秒确保服务发现完成...")
                await asyncio.sleep(service_discovery_delay)

                # 验证连接状态
                if not client.is_connected:
                    raise Exception("服务发现期间连接已断开")

                # 使用带重试的服务验证
                services_verified = await self._verify_services_with_retry(client)
                if not services_verified:
                    raise Exception("服务验证失败，BLE5201设备可能不兼容")

                # 连接和服务验证都成功
                self.manager.current_device_address = device_address
                self.manager._set_state(ConnectionState.CONNECTED)
                self.manager.state_changed.emit("设备连接成功")
                self.manager.connected.emit(device_address)

                self.logger.info(f"BLE连接和服务验证成功: {device_address}")

                # 发送START指令
                await self._send_start_command(client)

                # 启用数据通知
                await self._enable_data_notifications(client)

                # 注册断开回调（即时响应）
                try:
                    client.set_disconnected_callback(lambda _: self._on_client_disconnected(device_address))
                except Exception:
                    pass

                # 保持连接直到用户断开（加入连接/心跳检查，间隔由配置控制）
                self.logger.info("连接已建立，等待用户断开...")
                check_interval = float(self.manager.config.get('connection_check_interval', 0.5))
                while self.manager.is_running:
                    await asyncio.sleep(check_interval)

                    # 1) 连接状态兜底检测
                    if not client.is_connected:
                        self.logger.warning("检测到BLE客户端已断开")
                        self._on_client_disconnected(device_address)
                        break

                    # 2) 心跳超时检测（>heartbeat_timeout无数据）
                    hb_timeout = float(self.manager.config.get('heartbeat_timeout', 2.0))
                    if self.manager.last_data_time is not None:
                        if (time.time() - self.manager.last_data_time) > hb_timeout:
                            self.logger.warning("心跳超时，判定连接异常")
                            self._on_client_disconnected(device_address)
                            break

                    # 3) 连续无效包阈值（交由通知回调维护计数；此处只做判定）
                    if self.manager.invalid_packet_streak >= self.manager.invalid_packet_threshold:
                        self.logger.warning("连续无效包阈值触发，判定连接异常")
                        self._on_client_disconnected(device_address)
                        break

                self.logger.info("开始断开连接...")

                # 清理操作
                await self._send_stop_command(client)
                await self._disable_data_notifications(client)

            # async with 自动处理断开连接
            self.logger.info("连接已断开")

        except Exception as e:
            self.logger.error(f"连接失败: {e}")
            self.manager._set_state(ConnectionState.DISCONNECTED)
            # 注意：这里不直接发送connection_failed信号，让上层的重试逻辑处理
            raise e

    async def _send_start_command(self, client):
        """发送START指令"""
        try:
            # 验证连接状态
            if not client.is_connected:
                raise Exception("设备未连接")

            write_char_uuid = "0000fff2-0000-1000-8000-00805f9b34fb"
            start_command = b'START\n'

            # 发送START指令
            await client.write_gatt_char(write_char_uuid, start_command)
            self.logger.info("START指令发送成功")

        except Exception as e:
            self.logger.warning(f"START指令发送失败: {e}")
            raise  # 重新抛出异常以便上层处理

    async def _send_stop_command(self, client):
        """发送STOP指令"""
        try:
            write_char_uuid = "0000fff2-0000-1000-8000-00805f9b34fb"
            stop_command = b'STOP\n'

            await client.write_gatt_char(write_char_uuid, stop_command)
            self.logger.info("STOP指令发送成功")
            await asyncio.sleep(0.5)  # 等待处理

        except Exception as e:
            self.logger.warning(f"STOP指令发送失败: {e}")

    def _on_client_disconnected(self, device_address: str):
        """Bleak断开回调 - 在工作线程上下文中安全触发（幂等）"""
        try:
            # 幂等保护：若已是断开状态，避免重复触发
            if self.manager.state == ConnectionState.DISCONNECTED:
                return

            # 结束工作循环并更新状态
            self.manager.is_running = False
            self.manager._set_state(ConnectionState.DISCONNECTED)
            self.manager.state_changed.emit("连接已断开")
            if device_address:
                self.manager.disconnected.emit(device_address)
            self.logger.info("接收到Bleak断开回调，已更新状态")
        except Exception as e:
            self.logger.warning(f"处理断开回调失败: {e}")

    async def _enable_data_notifications(self, client):
        """启用数据通知"""
        try:
            # 验证连接状态
            if not client.is_connected:
                raise Exception("设备未连接")

            read_char_uuid = "0000fff1-0000-1000-8000-00805f9b34fb"

            await client.start_notify(read_char_uuid, self._notification_handler)
            self.logger.info("数据通知已启用")

        except Exception as e:
            self.logger.warning(f"启用数据通知失败: {e}")
            raise  # 重新抛出异常以便上层处理

    async def _disable_data_notifications(self, client):
        """停止数据通知"""
        try:
            read_char_uuid = "0000fff1-0000-1000-8000-00805f9b34fb"

            await client.stop_notify(read_char_uuid)
            self.logger.info("数据通知已停止")

        except Exception as e:
            self.logger.warning(f"停止数据通知失败: {e}")

    def _notification_handler(self, sender, data):
        """数据通知处理器 - 移除数据包校验，只维护心跳并发出数据信号"""
        try:
            # 若已不在运行或已非连接状态，忽略进一步的数据回调
            if not self.manager.is_running or self.manager.state != ConnectionState.CONNECTED:
                return

            if data is None or len(data) == 0:
                return

            # 更新心跳时间戳
            self.manager.last_data_time = time.time()

            # 直接上抛数据（严格数据校验由上层处理器负责）
            self.manager.data_received.emit(data)

        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
