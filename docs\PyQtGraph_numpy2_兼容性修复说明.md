# PyQtGraph与numpy 2.0+兼容性修复说明

## 问题描述

在升级到numpy 2.0+版本后，PyQtGraph出现了两个主要问题：

1. **RuntimeWarning警告**：大量"overflow encountered in cast"警告
2. **颜色配置错误**：实时曲线显示"Unable to convert default to QColor"错误

## 问题根源分析

### 1. numpy 2.0+警告问题
- **原因**：numpy 2.0引入了更严格的数据类型转换警告机制
- **触发点**：PyQtGraph的ViewBox在处理坐标转换时进行大量数值类型转换
- **影响**：控制台输出大量警告信息，影响用户体验

### 2. PyQtGraph颜色配置问题
- **原因**：使用了`'default'`字符串作为颜色值
- **错误位置**：`foreground='default'`和`background='default'`配置
- **影响**：PyQtGraph初始化失败，实时曲线无法显示

## 解决方案

### 1. 创建专门的警告管理器

**文件**：`utils/warning_manager.py`

**功能**：
- 统一管理所有警告配置
- 支持调试/生产模式切换
- 针对numpy和PyQtGraph的特定优化

**核心配置**：
```python
# numpy警告处理
np.seterr(
    divide='ignore',    # 忽略除零警告
    over='ignore',      # 忽略溢出警告  
    under='ignore',     # 忽略下溢警告
    invalid='ignore'    # 忽略无效值警告
)

# Python warnings过滤
warnings.filterwarnings("ignore", category=RuntimeWarning, 
                       message=".*overflow encountered in cast.*")
warnings.filterwarnings("ignore", category=RuntimeWarning, 
                       module="pyqtgraph.*")
```

### 2. 修复PyQtGraph颜色配置

**修改文件**：
- `utils/warning_manager.py`
- `ui/components/pyqtgraph_curves_widget.py`

**修复内容**：
```python
# 修复前（有问题）
pg.setConfigOptions(
    foreground='default',  # ❌ 无效颜色值
    background='default'   # ❌ 无效颜色值
)

# 修复后（正确）
pg.setConfigOptions(
    antialias=True,
    useOpenGL=False,
    enableExperimental=False,
    crashWarning=False
    # ✅ 不设置foreground和background，让PyQtGraph使用默认值
)
```

### 3. 增强错误处理

**改进点**：
- 添加颜色设置的异常处理
- 提供降级方案
- 详细的错误日志记录

```python
try:
    plot_widget.setBackground(self._bg_color)
except Exception as bg_error:
    self.logger.warning(f"设置背景色失败，使用默认背景: {bg_error}")
    plot_widget.setBackground(None)
```

## 修复效果验证

### 测试结果
✅ **numpy警告抑制**：成功消除"overflow encountered in cast"警告  
✅ **PyQtGraph初始化**：成功解决"Unable to convert default to QColor"错误  
✅ **实时曲线显示**：PyQtGraph组件正常工作  
✅ **应用程序启动**：无错误信息，正常连接设备  

### 兼容性确认
- ✅ numpy 2.2.6
- ✅ pyqtgraph 0.13.7
- ✅ PySide6 6.6.1
- ✅ Python 3.9/3.11/3.13

## 使用说明

### 1. 自动配置
警告管理器在应用启动时自动配置，无需手动干预。

### 2. 环境变量控制
```bash
# 启用调试模式（显示更多警告信息）
set BCI_DEBUG=1

# 生产模式（默认，抑制大部分警告）
set BCI_DEBUG=0
```

### 3. 手动配置
```python
from utils.warning_manager import configure_warnings

# 配置警告处理
configure_warnings(debug_mode=False)
```

## 技术细节

### 警告过滤规则
```python
# numpy数值计算警告
warnings.filterwarnings("ignore", category=RuntimeWarning, 
                       message=".*overflow encountered.*")
warnings.filterwarnings("ignore", category=RuntimeWarning, 
                       message=".*invalid value encountered.*")

# PyQtGraph模块警告
warnings.filterwarnings("ignore", category=RuntimeWarning, 
                       module="pyqtgraph.*")
warnings.filterwarnings("ignore", category=UserWarning, 
                       module="pyqtgraph.*")
```

### PyQtGraph安全配置
```python
# 安全的PyQtGraph配置选项
safe_config = {
    'antialias': True,          # 抗锯齿
    'useOpenGL': False,         # 禁用OpenGL（兼容性更好）
    'enableExperimental': False, # 禁用实验性功能
    'crashWarning': False       # 禁用崩溃警告
}
```

## 维护建议

1. **定期检查**：关注numpy和PyQtGraph的版本更新
2. **测试验证**：在升级依赖后运行兼容性测试
3. **日志监控**：注意应用日志中的新警告信息
4. **配置调整**：根据实际使用情况调整警告过滤规则

## 相关文件

- `utils/warning_manager.py` - 警告管理器核心模块
- `main.py` - 应用启动时的警告配置
- `ui/components/pyqtgraph_curves_widget.py` - PyQtGraph曲线组件
- `docs/PyQtGraph_numpy2_兼容性修复说明.md` - 本文档

---

**修复完成时间**：2025-01-30  
**修复版本**：v1.0  
**测试状态**：✅ 通过  
**兼容性**：numpy 2.0+ & pyqtgraph 0.13+
