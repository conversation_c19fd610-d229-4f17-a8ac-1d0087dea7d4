# -*- coding: utf-8 -*-
"""
后台加载器
Background Loader

在用户登录期间异步加载主窗口和服务，提高启动速度
"""

from PySide6.QtCore import QThread, Signal, QObject
import time


class BackgroundLoader(QThread):
    """后台加载器线程"""
    
    # 加载进度信号
    progress_updated = Signal(str, int)  # 消息, 进度百分比
    loading_completed = Signal(object)   # 传递加载完成的主窗口对象
    loading_failed = Signal(str)         # 传递错误信息
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.main_window = None
        self._should_stop = False
    
    def run(self):
        """后台加载主线程 - 真正的模块预加载"""
        try:
            self.progress_updated.emit("正在准备系统...", 10)

            if self._should_stop:
                return
            time.sleep(0.1)

            self.progress_updated.emit("正在加载主题系统...", 20)

            # 导入主题管理器
            if self._should_stop:
                return

            from ui.themes.theme_manager import ThemeManager
            theme_manager = ThemeManager()

            self.progress_updated.emit("正在预加载界面模块...", 40)

            # 预加载主窗口模块（只导入，不创建对象）
            if self._should_stop:
                return

            # 这里进行真正的重型模块导入，但不创建Qt对象
            import ui.main_window  # 导入主窗口模块
            import ui.pages.patients_page  # 导入页面模块
            import ui.pages.treatment_page
            import ui.pages.reports_page
            import ui.pages.settings_page
            import ui.pages.users_page

            self.progress_updated.emit("正在加载核心服务...", 60)

            if self._should_stop:
                return

            # 预加载核心服务模块
            import services.patient_service
            import services.treatment_service
            import services.report_service
            import services.user_service

            self.progress_updated.emit("正在准备配置...", 80)

            if self._should_stop:
                return

            # 准备配置和主题
            theme_name = self.config.get('theme', 'tech')
            self.prepared_stylesheet = theme_manager.get_stylesheet(theme_name)

            self.progress_updated.emit("模块预加载完成", 100)
            time.sleep(0.1)

            if not self._should_stop:
                # 传递配置信息和预加载状态
                self.loading_completed.emit({
                    'config': self.config,
                    'stylesheet': self.prepared_stylesheet,
                    'modules_preloaded': True,
                    'ready': True
                })

        except Exception as e:
            if not self._should_stop:
                self.loading_failed.emit(f"预加载失败: {str(e)}")
    
    def _preload_services(self):
        """预加载服务"""
        try:
            # 这里可以预加载一些服务，比如数据库连接、设备管理器等
            # 但要注意不要在子线程中进行UI操作
            
            # 示例：预加载数据库服务
            # from services.database import DatabaseService
            # db_service = DatabaseService()
            
            # 示例：预加载设备管理器
            # from services.device_manager import DeviceManager
            # device_manager = DeviceManager()
            
            pass
            
        except Exception as e:
            print(f"预加载服务失败: {e}")
    
    def stop_loading(self):
        """停止加载"""
        self._should_stop = True
        self.quit()
        self.wait()


class LoadingManager(QObject):
    """加载管理器"""
    
    # 管理器信号
    main_window_ready = Signal(object)  # 主窗口准备就绪
    loading_progress = Signal(str, int)  # 加载进度
    loading_error = Signal(str)         # 加载错误
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.loader = None
        self.main_window = None
    
    def start_background_loading(self):
        """开始后台加载"""
        if self.loader and self.loader.isRunning():
            return
        
        self.loader = BackgroundLoader(self.config)
        self.loader.progress_updated.connect(self.loading_progress.emit)
        self.loader.loading_completed.connect(self._on_loading_completed)
        self.loader.loading_failed.connect(self.loading_error.emit)
        
        self.loader.start()
    
    def _on_loading_completed(self, loading_result):
        """加载完成处理"""
        # loading_result是一个字典，包含config和stylesheet
        self.loading_result = loading_result
        self.main_window_ready.emit(loading_result)
    
    def stop_loading(self):
        """停止加载"""
        if self.loader:
            self.loader.stop_loading()
    
    def is_loading_complete(self) -> bool:
        """检查是否加载完成"""
        return hasattr(self, 'loading_result') and self.loading_result is not None

    def get_main_window(self):
        """获取主窗口"""
        # 这个方法现在返回None，因为主窗口在主线程中创建
        return None
