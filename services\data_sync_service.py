# -*- coding: utf-8 -*-
"""
数据同步服务
Data Sync Service

提供未上传数据的一次性重传功能
"""

from typing import List, Dict, Any
from PySide6.QtCore import QThread, Signal, QObject
from core.database import db_manager
from core.network_config import SYNC_STATUS
from services.api_client import api_client


class DataSyncThread(QThread):
    """数据同步线程"""
    
    # 同步进度信号
    sync_progress = Signal(str, int, int)  # 消息, 当前进度, 总数
    sync_completed = Signal(int, int, int, int)  # 患者成功数, 患者总数, 治疗成功数, 治疗总数
    
    def __init__(self):
        super().__init__()
        self._should_stop = False
    
    def run(self):
        """执行数据同步"""
        try:
            if self._should_stop:
                return
            
            print("🔄 开始数据同步...")
            
            # 获取未同步的数据
            sync_service = DataSyncService()
            unsynced_patients = sync_service.get_unsynced_patients()
            unsynced_treatments = sync_service.get_unsynced_treatments()
            
            total_patients = len(unsynced_patients)
            total_treatments = len(unsynced_treatments)
            
            if total_patients == 0 and total_treatments == 0:
                print("✅ 没有需要同步的数据")
                self.sync_completed.emit(0, 0, 0, 0)
                return
            
            print(f"📊 发现 {total_patients} 个未同步患者，{total_treatments} 个未同步治疗记录")

            patient_success_count = 0
            treatment_success_count = 0
            patient_failed_count = 0
            treatment_failed_count = 0
            
            # 同步患者数据
            for i, patient in enumerate(unsynced_patients):
                if self._should_stop:
                    return
                
                self.sync_progress.emit(f"正在同步患者数据 {i+1}/{total_patients}", i+1, total_patients)
                
                success = sync_service.sync_patient(patient)
                if success:
                    patient_success_count += 1
                else:
                    patient_failed_count += 1
                
                # 避免过快请求
                self.msleep(100)
            
            # 同步治疗数据
            for i, treatment in enumerate(unsynced_treatments):
                if self._should_stop:
                    return
                
                self.sync_progress.emit(f"正在同步治疗记录 {i+1}/{total_treatments}", i+1, total_treatments)
                
                success = sync_service.sync_treatment(treatment)
                if success:
                    treatment_success_count += 1
                else:
                    treatment_failed_count += 1
                
                # 避免过快请求
                self.msleep(100)
            
            # 汇总输出，避免重复的失败信息
            if patient_failed_count > 0:
                print(f"❌ 患者数据上传失败 {patient_failed_count} 个")
            if treatment_failed_count > 0:
                print(f"❌ 治疗数据上传失败 {treatment_failed_count} 个")

            print(f"✅ 数据同步完成：患者 {patient_success_count}/{total_patients}，治疗 {treatment_success_count}/{total_treatments}")
            self.sync_completed.emit(patient_success_count, total_patients, treatment_success_count, total_treatments)
            
        except Exception as e:
            # print(f"❌ 数据同步异常: {e}")
            self.sync_completed.emit(0, 0, 0, 0)
    
    def stop_sync(self):
        """停止同步"""
        self._should_stop = True


class DataSyncService:
    """数据同步服务类"""
    
    def __init__(self):
        self.patient_table = "bingren"
        self.treatment_table = "zhiliao"
    
    def get_unsynced_patients(self) -> List[Dict[str, Any]]:
        """获取未同步的患者列表"""
        try:
            sql = f"SELECT * FROM {self.patient_table} WHERE status = ?"
            rows = db_manager.execute_query(sql, (SYNC_STATUS['NOT_UPLOADED'],))
            
            patients = []
            for row in rows:
                # 转换为字典格式
                patient = dict(row)
                patients.append(patient)
            
            return patients
        except Exception as e:
            # print(f"获取未同步患者失败: {e}")
            return []
    
    def get_unsynced_treatments(self) -> List[Dict[str, Any]]:
        """获取未同步的治疗记录列表"""
        try:
            sql = f"SELECT * FROM {self.treatment_table} WHERE status = ?"
            rows = db_manager.execute_query(sql, ('0',))  # 治疗记录使用字符串'0'
            
            treatments = []
            for row in rows:
                # 转换为字典格式
                treatment = dict(row)
                treatments.append(treatment)
            
            return treatments
        except Exception as e:
            # print(f"获取未同步治疗记录失败: {e}")
            return []
    
    def sync_patient(self, patient_data: Dict[str, Any]) -> bool:
        """同步单个患者数据"""
        try:
            # 上传患者数据
            success = api_client.upload_patient(patient_data, is_update=False)
            
            if success:
                # 更新同步状态
                patient_id = patient_data.get('bianhao', '')
                self._update_patient_sync_status(patient_id, SYNC_STATUS['UPLOADED'])
                # print(f"✅ 患者 {patient_id} 同步成功")
            # 失败时不打印个别错误信息，由调用方统一汇总
            
            return success
        except Exception as e:
            patient_id = patient_data.get('bianhao', '')
            # print(f"❌ 患者 {patient_id} 同步异常: {e}")
            return False
    
    def sync_treatment(self, treatment_data: Dict[str, Any]) -> bool:
        """同步单个治疗记录"""
        try:
            # 将数据库格式转换为API格式
            api_format_data = self._convert_db_treatment_to_api_format(treatment_data)

            # 上传治疗数据
            success = api_client.upload_treatment(api_format_data)
            
            if success:
                # 更新同步状态
                treatment_id = treatment_data.get('zhiliaobh', '')
                self._update_treatment_sync_status(treatment_id, '1')  # 治疗记录使用字符串'1'
                # print(f"✅ 治疗记录 {treatment_id} 同步成功")
            # 失败时不打印个别错误信息，由调用方统一汇总
            
            return success
        except Exception as e:
            treatment_id = treatment_data.get('zhiliaobh', '')
            # print(f"❌ 治疗记录 {treatment_id} 同步异常: {e}")
            return False
    
    def _update_patient_sync_status(self, patient_id: str, status: int):
        """更新患者同步状态"""
        try:
            sql = f"UPDATE {self.patient_table} SET status = ? WHERE bianhao = ?"
            db_manager.execute_update(sql, (status, patient_id))
        except Exception as e:
            print(f"更新患者同步状态失败: {e}")
    
    def _update_treatment_sync_status(self, treatment_id: str, status: str):
        """更新治疗记录同步状态"""
        try:
            sql = f"UPDATE {self.treatment_table} SET status = ? WHERE zhiliaobh = ?"
            db_manager.execute_update(sql, (status, treatment_id))
        except Exception as e:
            print(f"更新治疗记录同步状态失败: {e}")

    def _convert_db_treatment_to_api_format(self, db_treatment: Dict[str, Any]) -> Dict[str, Any]:
        """将数据库治疗记录格式转换为API格式"""
        try:
            # 严格按照API客户端_convert_treatment_data方法的字段顺序和默认值
            api_data = {
                "actualTimes": db_treatment.get("shijics", 0),  # 实际成功次数
                "commentsOfTreatment": db_treatment.get("xiaoguo") or "",  # 治疗效果，处理None
                "timesOfImagination": db_treatment.get("yaoqiucs", 0),  # 要求想象次数
                "treatScore": db_treatment.get("defen", 0),  # 治疗得分
                "treatTime": db_treatment.get("shijian", 0),  # 治疗时长(分钟)
                "usageTime": db_treatment.get("rq") or "",  # 治疗时间，处理None
                "patientNum": db_treatment.get("bianh") or "",  # 患者编号，处理None
                "treatNum": str(db_treatment.get("zhiliaobh") or ""),  # 治疗编号，处理None
                "hospitalID": db_treatment.get("yiyuanid", 3),  # 医院ID，保持与治疗完成时一致
                "department": db_treatment.get("keshiming") or "",  # 科室名称，处理None
                "equipmentNum": db_treatment.get("shebeih") or "",  # 设备编号，处理None
                "attdoctor": db_treatment.get("zhuzhiyis"),  # 主治医师，保持None值
                "operator": db_treatment.get("czy") or "",  # 操作员，处理None
                "idCard": db_treatment.get("shenfenzh")  # 身份证号，保持None值
            }

            return api_data

        except Exception as e:
            print(f"❌ 治疗数据格式转换失败: {e}")
            # 返回原始数据作为备选
            return db_treatment


class DataSyncManager(QObject):
    """数据同步管理器"""
    
    # 管理器信号
    sync_started = Signal()
    sync_finished = Signal(int, int, int, int)  # 患者成功数, 患者总数, 治疗成功数, 治疗总数
    
    def __init__(self):
        super().__init__()
        self.sync_thread = None
    
    def start_sync(self):
        """启动数据同步"""
        if self.sync_thread and self.sync_thread.isRunning():
            print("⚠️ 数据同步已在进行中")
            return
        
        print("🚀 启动数据同步服务...")
        self.sync_thread = DataSyncThread()
        self.sync_thread.sync_completed.connect(self._on_sync_completed)
        self.sync_thread.finished.connect(self._cleanup_thread)
        
        self.sync_started.emit()
        self.sync_thread.start()
    
    def _on_sync_completed(self, patient_success: int, patient_total: int, 
                          treatment_success: int, treatment_total: int):
        """同步完成处理"""
        print(f"📊 同步结果：患者 {patient_success}/{patient_total}，治疗 {treatment_success}/{treatment_total}")
        self.sync_finished.emit(patient_success, patient_total, treatment_success, treatment_total)
    
    def _cleanup_thread(self):
        """清理线程"""
        if self.sync_thread:
            self.sync_thread.deleteLater()
            self.sync_thread = None
    
    def stop_sync(self):
        """停止同步"""
        if self.sync_thread and self.sync_thread.isRunning():
            self.sync_thread.stop_sync()
            self.sync_thread.quit()
            self.sync_thread.wait()


# 全局数据同步管理器实例
data_sync_manager = DataSyncManager()
