#
# This file is autogenerated by pip-compile with Python 3.13
# by the following command:
#
#    pip-compile --output-file=requirements3.13.txt requirements.in
#
altgraph==0.17.4
    # via pyinstaller
bleak==0.22.3
    # via -r requirements.in
certifi==2025.7.14
    # via requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
colorama==0.4.6
    # via tqdm
comtypes==1.4.11
    # via pyttsx3
contourpy==1.3.3
    # via matplotlib
cryptography==43.0.3
    # via -r requirements.in
cycler==0.12.1
    # via matplotlib
decorator==5.2.1
    # via mne
fonttools==4.59.0
    # via matplotlib
idna==3.10
    # via requests
jinja2==3.1.6
    # via mne
joblib==1.5.1
    # via
    #   pyriemann
    #   scikit-learn
kiwisolver==1.4.8
    # via matplotlib
lazy-loader==0.4
    # via mne
markupsafe==3.0.2
    # via jinja2
matplotlib==3.10.3
    # via
    #   -r requirements.in
    #   mne
    #   pyriemann
mne==1.10.0
    # via -r requirements.in
nuitka==2.7.12
    # via -r requirements.in
numpy==2.2.6
    # via
    #   -r requirements.in
    #   contourpy
    #   matplotlib
    #   mne
    #   pandas
    #   pyqtgraph
    #   pyriemann
    #   scikit-learn
    #   scipy
ordered-set==4.1.0
    # via nuitka
packaging==25.0
    # via
    #   lazy-loader
    #   matplotlib
    #   mne
    #   pooch
    #   pyinstaller
    #   pyinstaller-hooks-contrib
pandas==2.3.1
    # via -r requirements.in
pefile==2023.2.7
    # via pyinstaller
pillow==10.4.0
    # via
    #   -r requirements.in
    #   matplotlib
platformdirs==4.3.8
    # via pooch
pooch==1.8.2
    # via mne
pycparser==2.22
    # via cffi
pyinstaller==6.14.2
    # via -r requirements.in
pyinstaller-hooks-contrib==2025.8
    # via pyinstaller
pyparsing==3.2.3
    # via matplotlib
pypiwin32==223
    # via pyttsx3
pyqtgraph==0.13.7
    # via -r requirements.in
pyriemann==0.8
    # via -r requirements.in
pyserial==3.5
    # via -r requirements.in
pyside6==6.8.3
    # via -r requirements.in
pyside6-addons==6.8.3
    # via pyside6
pyside6-essentials==6.8.3
    # via
    #   pyside6
    #   pyside6-addons
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.in
    #   matplotlib
    #   pandas
pyttsx3==2.99
    # via -r requirements.in
pytz==2025.2
    # via pandas
pywin32==311
    # via
    #   pypiwin32
    #   pyttsx3
pywin32-ctypes==0.2.3
    # via pyinstaller
requests==2.32.4
    # via
    #   -r requirements.in
    #   pooch
scikit-learn==1.5.2
    # via
    #   -r requirements.in
    #   pyriemann
scipy==1.14.1
    # via
    #   -r requirements.in
    #   mne
    #   pyriemann
    #   scikit-learn
shiboken6==6.8.3
    # via
    #   pyside6
    #   pyside6-addons
    #   pyside6-essentials
six==1.17.0
    # via python-dateutil
threadpoolctl==3.6.0
    # via scikit-learn
tqdm==4.67.1
    # via mne
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via requests
winrt-runtime==2.3.0
    # via
    #   bleak
    #   winrt-windows-devices-bluetooth
    #   winrt-windows-devices-bluetooth-advertisement
    #   winrt-windows-devices-bluetooth-genericattributeprofile
    #   winrt-windows-devices-enumeration
    #   winrt-windows-foundation
    #   winrt-windows-foundation-collections
    #   winrt-windows-storage-streams
winrt-windows-devices-bluetooth==2.3.0
    # via bleak
winrt-windows-devices-bluetooth-advertisement==2.3.0
    # via bleak
winrt-windows-devices-bluetooth-genericattributeprofile==2.3.0
    # via bleak
winrt-windows-devices-enumeration==2.3.0
    # via bleak
winrt-windows-foundation==2.3.0
    # via bleak
winrt-windows-foundation-collections==2.3.0
    # via bleak
winrt-windows-storage-streams==2.3.0
    # via bleak
zstandard==0.23.0
    # via nuitka

# The following packages are considered to be unsafe in a requirements file:
# setuptools
