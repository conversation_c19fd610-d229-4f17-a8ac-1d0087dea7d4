# -*- coding: utf-8 -*-
"""
系统设置页面
System Settings Page

完全按照HTML设计实现的系统设置页面
包含基本设置、脑电设备、信号处理、电刺激设备等四个标签页
"""

import json
from pathlib import Path
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QLineEdit, QComboBox, QSpinBox,
    QDoubleSpinBox, QStackedWidget, QFrame, QScrollArea,
    QMessageBox, QApplication, QDialog
)
from ui.components.no_wheel_widgets import NoWheelSpinBox, NoWheelDoubleSpinBox, NoWheelComboBox
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QIcon, QPainter, QPainterPath, QColor
from PySide6.QtSvgWidgets import QSvgWidget

from ui.pages.base_page import BasePage
from ui.components.themed_message_box import show_information, show_warning, show_critical, show_question


class SettingsPage(BasePage):
    """系统设置页面"""

    # 设置信号
    settings_saved = Signal(dict)
    settings_reset = Signal()

    def __init__(self):
        # 当前活跃标签页
        self.current_tab = "basic"

        # 初始化设置数据
        self.settings_data = {}
        self.basic_inputs = {}
        self.eeg_inputs = {}
        self.stimulation_inputs = {}
        self.tab_buttons = {}

        # 锁定字段管理
        self.lock_icons = {}  # 存储锁图标
        self.locked_fields = ["hospital_id", "hospital_name", "department_name", "device_id"]  # 需要锁定的字段
        self.admin_password = "sdht123"  # 管理员密码

        # 加载默认设置
        self.settings_data = self._load_default_settings()

        # 加载配置文件
        self._load_settings_from_file()

        # 调用父类初始化（这会调用_init_content）
        super().__init__("settings", "系统设置")
        
        # 检查页面访问权限
        if not self.check_page_access():
            self.handle_access_denied()
            return

    def _init_content(self):
        """初始化页面内容"""
        # 清除基类的占位内容
        for i in reversed(range(self.main_layout.count())):
            self.main_layout.itemAt(i).widget().setParent(None)

        # 创建主容器
        self._create_main_container()

        # 创建标签页导航
        self._create_tab_navigation()

        # 创建标签页内容区域
        self._create_tab_content_area()

        # 创建底部操作按钮
        self._create_bottom_actions()

        # 设置默认标签页
        self._switch_to_tab("basic")

    def _create_main_container(self):
        """创建主容器"""
        # 主容器 - 垂直布局
        self.main_container = QFrame()
        self.main_container.setObjectName("settings_main_container")

        # 设置固定高度以匹配HTML设计
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        self.main_layout.addWidget(self.main_container)

    def _create_tab_navigation(self):
        """创建标签页导航"""
        # 导航容器
        nav_container = QFrame()
        nav_container.setObjectName("settings_nav_container")
        nav_container.setFixedHeight(80)  # 按照HTML设计

        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距，让按钮左对齐
        nav_layout.setSpacing(8)  # 设置按钮间距

        # 标签页按钮数据
        tab_data = [
            ("basic", "基本设置", "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"),
            ("eeg", "脑电设备", "M9 11H7v9a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-5l-2-3H9a1 1 0 0 0-1 1z"),
            ("stimulation", "电刺激设备", "M13 3a9 9 0 0 0-9 9H1l3.5 4L8 12H5a7 7 0 1 1 7 7v2a9 9 0 0 0 0-18z")
        ]

        # 创建标签页按钮
        self.tab_buttons = {}
        for tab_id, tab_name, icon_path in tab_data:
            btn = self._create_tab_button(tab_id, tab_name, icon_path)
            self.tab_buttons[tab_id] = btn
            nav_layout.addWidget(btn)

        # 添加弹性空间，让按钮靠左对齐
        nav_layout.addStretch()

        # 添加到主容器
        self.main_container.layout().addWidget(nav_container)

    def _create_tab_button(self, tab_id: str, tab_name: str, icon_path: str) -> QPushButton:
        """创建标签页按钮"""
        btn = QPushButton()
        btn.setObjectName("settings_tab_btn")
        btn.setText(tab_name)
        btn.setProperty("tab_id", tab_id)
        btn.setProperty("active", tab_id == "basic")  # 默认激活基本设置

        # 设置按钮样式和尺寸 - 调整为更紧凑的布局
        btn.setFixedHeight(80)
        btn.setMinimumWidth(120)  # 减小最小宽度
        btn.setMaximumWidth(160)  # 设置最大宽度

        # 连接点击事件
        btn.clicked.connect(lambda: self._switch_to_tab(tab_id))

        return btn

    def _create_tab_content_area(self):
        """创建标签页内容区域"""
        # 内容区域容器
        content_container = QFrame()
        content_container.setObjectName("settings_content_container")

        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(0, 9, 0, 9)  # 顶部和底部间距
        content_layout.setSpacing(0)

        # 创建堆叠窗口部件（不使用滚动区域包装）
        self.tab_stack = QStackedWidget()
        self.tab_stack.setObjectName("settings_tab_stack")

        # 创建各个标签页
        self._create_basic_settings_tab()
        self._create_eeg_settings_tab()
        self._create_stimulation_settings_tab()

        # 直接添加堆叠窗口到布局
        content_layout.addWidget(self.tab_stack)

        # 添加到主容器
        self.main_container.layout().addWidget(content_container)

    def _create_basic_settings_tab(self):
        """创建基本设置标签页"""
        # 创建标签页容器
        tab_widget = QWidget()
        tab_widget.setObjectName("basic_settings_tab")

        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距，让卡片左对齐
        tab_layout.setSpacing(0)

        # 创建完整的卡片容器
        card_container = QFrame()
        card_container.setObjectName("settings_card")

        card_layout = QVBoxLayout(card_container)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(0)

        # 创建固定的卡片头部
        header = QFrame()
        header.setObjectName("settings_card_header")
        header.setFixedHeight(0)

        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(32, 16, 32, 16)
        header_layout.setSpacing(16)

        # 标题标签
        # title_label = QLabel("基本设置")
        # title_label.setObjectName("settings_card_title")
        # title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))

        # header_layout.addWidget(title_label)
        header_layout.addStretch()

        # 创建可滚动的内容区域
        scroll_area = QScrollArea()
        scroll_area.setObjectName("settings_scroll_area")
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建内容容器
        content_widget = QWidget()
        content_widget.setObjectName("settings_card_content")

        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(32, 16, 32, 16)
        content_layout.setSpacing(16)

        # 创建表单网格
        form_grid = QGridLayout()
        form_grid.setHorizontalSpacing(32)
        form_grid.setVerticalSpacing(12)

        # 设置3列等宽
        form_grid.setColumnStretch(0, 1)
        form_grid.setColumnStretch(1, 1)
        form_grid.setColumnStretch(2, 1)

        # 基本设置字段 - 包含文本字段和数值字段
        basic_fields = [
            # 文本字段：(field_id, label_text, default_value, field_type)
            ("system_name", "系统名称", "脑机接口康复训练系统", "text"),
            ("hospital_id", "医院编号", "3", "text"),
            ("hospital_name", "医院名称", "海天智能", "text"),
            ("department_name", "科室名称", "康复科", "text"),
            ("device_id", "设备编号", "KFY160701", "text"),
            # 治疗配置字段：(field_id, label_text, default_value, field_type, min_val, max_val, unit)
            ("stimulation_duration", "刺激时长", 10, "number", 10, 60, "秒"),
            ("treatment_duration", "治疗时长", 20, "number", 10, 60, "分钟"),
            ("encouragement_interval", "鼓励间隔", 30, "number", 20, 60, "秒"),
            ("min_save_duration", "最小保存时长", 5, "number", 1, 15, "分钟")
        ]

        self.basic_inputs = {}
        for i, field_data in enumerate(basic_fields):
            field_id, label_text = field_data[0], field_data[1]
            default_value, field_type = field_data[2], field_data[3]

            row = i // 3
            col = i % 3

            # 创建标签容器（用于添加锁图标）
            label_container = QWidget()
            label_layout = QHBoxLayout(label_container)
            label_layout.setContentsMargins(0, 0, 0, 0)
            label_layout.setSpacing(5)

            # 创建标签
            if field_type == "number" and len(field_data) > 6:
                unit = field_data[6]
                label = QLabel(f"{label_text}({unit})")
            else:
                label = QLabel(label_text)
            label.setObjectName("settings_field_label")
            label_layout.addWidget(label)

            # 如果是需要锁定的字段，添加锁图标
            if field_id in self.locked_fields:
                lock_icon = LockIcon()
                lock_icon.clicked.connect(lambda checked=False, fid=field_id: self._toggle_field_lock(fid))
                self.lock_icons[field_id] = lock_icon
                label_layout.addWidget(lock_icon)

            label_layout.addStretch()

            # 根据字段类型创建输入框
            if field_type == "text":
                input_field = QLineEdit()
                input_field.setObjectName("settings_input")
                input_field.setText(self.settings_data.get("basic", {}).get(field_id, default_value))
                input_field.setPlaceholderText(f"请输入{label_text}")

                # 如果是需要锁定的字段，设置为只读状态
                if field_id in self.locked_fields:
                    input_field.setReadOnly(True)
                    input_field.setFocusPolicy(Qt.FocusPolicy.NoFocus)
                    input_field.setProperty("readOnly", "true")
                    input_field.style().unpolish(input_field)
                    input_field.style().polish(input_field)
            else:  # number type
                min_val, max_val = field_data[4], field_data[5]
                input_field = NoWheelSpinBox()
                input_field.setObjectName("settings_spinbox")
                input_field.setRange(min_val, max_val)
                input_field.setValue(self.settings_data.get("basic", {}).get(field_id, default_value))

            self.basic_inputs[field_id] = input_field

            # 添加到网格
            form_grid.addWidget(label_container, row * 2, col)
            form_grid.addWidget(input_field, row * 2 + 1, col)

        # 添加表单到内容布局
        content_layout.addLayout(form_grid)
        content_layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)

        # 组装卡片
        card_layout.addWidget(header)
        card_layout.addWidget(scroll_area)

        # 组装标签页
        tab_layout.addWidget(card_container)

        # 添加到堆叠窗口
        self.tab_stack.addWidget(tab_widget)

    def _create_eeg_settings_tab(self):
        """创建脑电设备标签页"""
        # 创建标签页容器
        tab_widget = QWidget()
        tab_widget.setObjectName("eeg_settings_tab")

        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距，让卡片左对齐
        tab_layout.setSpacing(0)

        # 创建完整的卡片容器
        card_container = QFrame()
        card_container.setObjectName("settings_card")

        card_layout = QVBoxLayout(card_container)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(0)

        # 创建固定的卡片头部
        header = QFrame()
        header.setObjectName("settings_card_header")
        header.setFixedHeight(0)

        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(32, 16, 32, 16)
        header_layout.setSpacing(16)

        # 标题标签
        # title_label = QLabel("脑电设备配置")
        # title_label.setObjectName("settings_card_title")
        # title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))

        # header_layout.addWidget(title_label)
        header_layout.addStretch()

        # 创建可滚动的内容区域
        scroll_area = QScrollArea()
        scroll_area.setObjectName("settings_scroll_area")
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建内容容器
        content_widget = QWidget()
        content_widget.setObjectName("settings_card_content")

        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(32, 16, 32,16)
        content_layout.setSpacing(16)

        # 创建表单网格
        form_grid = QGridLayout()
        form_grid.setHorizontalSpacing(32)
        form_grid.setVerticalSpacing(12)

        # 设置3列等宽
        form_grid.setColumnStretch(0, 1)
        form_grid.setColumnStretch(1, 1)
        form_grid.setColumnStretch(2, 1)

        self.eeg_inputs = {}



        # 目标设备名称
        label = QLabel("目标设备名称")
        label.setObjectName("settings_field_label")
        device_name = QLineEdit()
        device_name.setObjectName("settings_input")
        device_name.setText(self.settings_data.get("eeg", {}).get("device_name", "BLE5201"))
        device_name.setPlaceholderText("输入要连接的蓝牙设备名称")
        self.eeg_inputs["device_name"] = device_name
        form_grid.addWidget(label, 0, 0)
        form_grid.addWidget(device_name, 1, 0)

        # 自动连接开关
        label = QLabel("自动连接")
        label.setObjectName("settings_field_label")
        auto_connect = NoWheelComboBox()
        auto_connect.setObjectName("settings_combo")
        auto_connect.addItems(["启用", "禁用"])
        auto_connect.setCurrentText("启用" if self.settings_data.get("eeg", {}).get("auto_connect", True) else "禁用")
        self.eeg_inputs["auto_connect"] = auto_connect
        form_grid.addWidget(label, 0, 1)
        form_grid.addWidget(auto_connect, 1, 1)

        # 连接超时时间
        label = QLabel("连接超时(秒)")
        label.setObjectName("settings_field_label")
        connect_timeout = NoWheelSpinBox()
        connect_timeout.setObjectName("settings_spinbox")
        connect_timeout.setRange(5, 30)
        connect_timeout.setValue(self.settings_data.get("eeg", {}).get("connect_timeout", 10))
        self.eeg_inputs["connect_timeout"] = connect_timeout
        form_grid.addWidget(label, 0, 2)
        form_grid.addWidget(connect_timeout, 1, 2)

        # 扫描超时时间
        label = QLabel("扫描超时(秒)")
        label.setObjectName("settings_field_label")
        scan_timeout = NoWheelSpinBox()
        scan_timeout.setObjectName("settings_spinbox")
        scan_timeout.setRange(5, 15)
        scan_timeout.setValue(self.settings_data.get("eeg", {}).get("scan_timeout", 10))
        self.eeg_inputs["scan_timeout"] = scan_timeout
        form_grid.addWidget(label, 2, 0)
        form_grid.addWidget(scan_timeout, 3, 0)



        # 最大重连次数
        label = QLabel("最大重连次数")
        label.setObjectName("settings_field_label")
        max_retries = NoWheelSpinBox()
        max_retries.setObjectName("settings_spinbox")
        max_retries.setRange(1, 10)
        max_retries.setValue(self.settings_data.get("eeg", {}).get("max_retries", 3))
        self.eeg_inputs["max_retries"] = max_retries
        form_grid.addWidget(label, 2, 1)
        form_grid.addWidget(max_retries, 3, 1)

        # 重连间隔时间
        label = QLabel("重连间隔(秒)")
        label.setObjectName("settings_field_label")
        retry_interval = NoWheelSpinBox()
        retry_interval.setObjectName("settings_spinbox")
        retry_interval.setRange(1, 10)
        retry_interval.setValue(self.settings_data.get("eeg", {}).get("retry_interval", 3))
        self.eeg_inputs["retry_interval"] = retry_interval
        form_grid.addWidget(label, 2, 2)
        form_grid.addWidget(retry_interval, 3, 2)

        # 信号强度阈值
        label = QLabel("信号强度阈值(dBm)")
        label.setObjectName("settings_field_label")
        rssi_threshold = NoWheelSpinBox()
        rssi_threshold.setObjectName("settings_spinbox")
        rssi_threshold.setRange(-90, -30)
        rssi_threshold.setValue(self.settings_data.get("eeg", {}).get("rssi_threshold", -70))
        self.eeg_inputs["rssi_threshold"] = rssi_threshold
        form_grid.addWidget(label, 4, 0)
        form_grid.addWidget(rssi_threshold, 5, 0)

        # 连接优先级
        label = QLabel("连接优先级")
        label.setObjectName("settings_field_label")
        connection_priority = NoWheelComboBox()
        connection_priority.setObjectName("settings_combo")
        connection_priority.addItems(["优先已知设备", "总是扫描新设备"])
        priority_value = self.settings_data.get("eeg", {}).get("connection_priority", "优先已知设备")
        connection_priority.setCurrentText(priority_value)
        self.eeg_inputs["connection_priority"] = connection_priority
        form_grid.addWidget(label, 4, 1)
        form_grid.addWidget(connection_priority, 5, 1)

        # GATT服务模式
        label = QLabel("GATT服务模式")
        label.setObjectName("settings_field_label")
        gatt_mode = NoWheelComboBox()
        gatt_mode.setObjectName("settings_combo")
        gatt_mode.addItems(["仅连接(推荐)", "完整服务发现"])
        current_mode = "仅连接(推荐)" if self.settings_data.get("eeg", {}).get("skip_gatt", True) else "完整服务发现"
        gatt_mode.setCurrentText(current_mode)
        self.eeg_inputs["gatt_mode"] = gatt_mode
        form_grid.addWidget(label, 4, 2)
        form_grid.addWidget(gatt_mode, 5, 2)



        # 采样率
        label = QLabel("采样率(Hz)")
        label.setObjectName("settings_field_label")
        sample_rate = NoWheelDoubleSpinBox()
        sample_rate.setObjectName("settings_spinbox")
        sample_rate.setRange(1.0, 1000.0)
        sample_rate.setDecimals(2)
        sample_rate.setValue(self.settings_data.get("eeg", {}).get("sample_rate", 125.0))
        self.eeg_inputs["sample_rate"] = sample_rate
        form_grid.addWidget(label, 6, 0)
        form_grid.addWidget(sample_rate, 7, 0)

        # 通道数
        label = QLabel("通道数")
        label.setObjectName("settings_field_label")
        channels = NoWheelSpinBox()
        channels.setObjectName("settings_spinbox")
        channels.setRange(1, 64)
        channels.setValue(self.settings_data.get("eeg", {}).get("channels", 8))
        self.eeg_inputs["channels"] = channels
        form_grid.addWidget(label, 6, 1)
        form_grid.addWidget(channels, 7, 1)



        # 设备管理按钮
        device_buttons_layout = QHBoxLayout()

        scan_devices_btn = QPushButton("扫描设备")
        scan_devices_btn.setObjectName("btn_secondary")
        scan_devices_btn.setMinimumWidth(120)  # 设置最小宽度确保按钮大小一致
        scan_devices_btn.clicked.connect(self._scan_bluetooth_devices)
        device_buttons_layout.addWidget(scan_devices_btn)

        clear_history_btn = QPushButton("清除配对历史")
        clear_history_btn.setObjectName("btn_secondary")
        clear_history_btn.setMinimumWidth(120)  # 设置最小宽度确保按钮大小一致
        clear_history_btn.clicked.connect(self._clear_device_history)
        device_buttons_layout.addWidget(clear_history_btn)

        device_buttons_layout.addStretch()

        device_buttons_widget = QWidget()
        device_buttons_widget.setLayout(device_buttons_layout)
        form_grid.addWidget(device_buttons_widget, 8, 0, 1, 3)

        # 添加表单到内容布局
        content_layout.addLayout(form_grid)
        content_layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)

        # 组装卡片
        card_layout.addWidget(header)
        card_layout.addWidget(scroll_area)

        # 组装标签页
        tab_layout.addWidget(card_container)

        # 添加到堆叠窗口
        self.tab_stack.addWidget(tab_widget)



    def _create_stimulation_settings_tab(self):
        """创建电刺激设备标签页"""
        # 创建标签页容器
        tab_widget = QWidget()
        tab_widget.setObjectName("stimulation_settings_tab")

        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距，让卡片左对齐
        tab_layout.setSpacing(0)

        # 创建完整的卡片容器
        card_container = QFrame()
        card_container.setObjectName("settings_card")

        card_layout = QVBoxLayout(card_container)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(0)

        # 创建固定的卡片头部
        header = QFrame()
        header.setObjectName("settings_card_header")
        header.setFixedHeight(0)

        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(32, 16, 32, 16)
        header_layout.setSpacing(16)

        # 标题标签
        # title_label = QLabel("电刺激设备配置")
        # title_label.setObjectName("settings_card_title")
        # title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))

        # header_layout.addWidget(title_label)
        header_layout.addStretch()

        # 创建可滚动的内容区域
        scroll_area = QScrollArea()
        scroll_area.setObjectName("settings_scroll_area")
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建内容容器
        content_widget = QWidget()
        content_widget.setObjectName("settings_card_content")

        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(32, 16, 32, 16)
        content_layout.setSpacing(16)

        # 创建表单网格
        form_grid = QGridLayout()
        form_grid.setHorizontalSpacing(32)
        form_grid.setVerticalSpacing(12)

        # 设置3列等宽
        form_grid.setColumnStretch(0, 1)
        form_grid.setColumnStretch(1, 1)
        form_grid.setColumnStretch(2, 1)

        self.stimulation_inputs = {}

        # 端口号选择
        label = QLabel("端口号")
        label.setObjectName("settings_field_label")

        port_combo = NoWheelComboBox()
        port_combo.setObjectName("settings_combo")
        port_combo.addItems(["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8","COM9", "COM10", "COM11", "COM12", "COM13", "COM14", "COM15", "COM16","COM17", "COM18", "COM19"])
        port_combo.setCurrentText(self.settings_data.get("stimulation", {}).get("port", "COM7"))
        self.stimulation_inputs["port"] = port_combo

        form_grid.addWidget(label, 0, 0)
        form_grid.addWidget(port_combo, 1, 0)

        # 波形类型选择
        label = QLabel("默认波形类型")
        label.setObjectName("settings_field_label")

        waveform_combo = NoWheelComboBox()
        waveform_combo.setObjectName("settings_combo")
        waveform_combo.addItems(["双相波", "单相波"])  # 0=双相波, 1=单相波
        waveform_combo.setCurrentText(self.settings_data.get("stimulation", {}).get("waveform", "双相波"))
        self.stimulation_inputs["waveform"] = waveform_combo

        form_grid.addWidget(label, 0, 1)
        form_grid.addWidget(waveform_combo, 1, 1)

        # 电流参数 - 按照文档要求调整范围
        current_fields = [
            ("max_current", "最大电流(mA)", 50, 1, 100),  # 文档建议1-100mA
            ("min_current", "最小电流(mA)", 1, 1, 100),
            ("current_step", "电流步长(mA)", 1.0, 0.1, 5.0),  # 文档支持0.1-5.0mA步长
            ("default_frequency", "默认频率(Hz)", 20, 2, 160),  # 文档要求2-160Hz
            ("default_pulse_width", "默认脉宽(μs)", 200, 10, 500),  # 文档要求10-500μs
            ("default_relax_time", "默认休息时间(s)", 5.0, 0.0, 16.0),  # 文档要求0-16s
            ("default_climb_time", "默认上升时间(s)", 2.0, 0.0, 5.0),  # 文档要求0-5s
            ("default_work_time", "默认工作时间(s)", 10, 0, 30),  # 文档要求0-30s
            ("default_fall_time", "默认下降时间(s)", 2.0, 0.0, 5.0)  # 文档要求0-5s
        ]

        for i, (field_id, label_text, default_value, min_val, max_val) in enumerate(current_fields):
            # 从第三列开始排列（前两列已被端口号和波形类型占用）
            total_index = i + 2  # 加2是因为前面有端口号和波形类型
            row = total_index // 3
            col = total_index % 3

            # 创建标签
            label = QLabel(label_text)
            label.setObjectName("settings_field_label")

            # 创建输入框 - 使用禁用滚轮响应的组件
            if isinstance(default_value, float):
                input_field = NoWheelDoubleSpinBox()
                input_field.setRange(min_val, max_val)
                input_field.setDecimals(1)
                input_field.setValue(self.settings_data.get("stimulation", {}).get(field_id, default_value))
            else:
                input_field = NoWheelSpinBox()
                input_field.setRange(int(min_val), int(max_val))
                input_field.setValue(self.settings_data.get("stimulation", {}).get(field_id, int(default_value)))

            input_field.setObjectName("settings_spinbox")
            self.stimulation_inputs[field_id] = input_field

            # 添加到网格
            form_grid.addWidget(label, row * 2, col)
            form_grid.addWidget(input_field, row * 2 + 1, col)

        # 添加表单到内容布局
        content_layout.addLayout(form_grid)
        content_layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)

        # 组装卡片
        card_layout.addWidget(header)
        card_layout.addWidget(scroll_area)

        # 组装标签页
        tab_layout.addWidget(card_container)

        # 添加到堆叠窗口
        self.tab_stack.addWidget(tab_widget)



    def _create_bottom_actions(self):
        """创建底部操作按钮"""
        # 底部容器
        bottom_container = QFrame()
        bottom_container.setObjectName("settings_bottom_container")
        bottom_container.setFixedHeight(80)  # 减少高度

        bottom_layout = QHBoxLayout(bottom_container)
        bottom_layout.setContentsMargins(32, 16, 32, 16)  # 减少上下边距
        bottom_layout.setSpacing(16)

        # 添加弹性空间让按钮居中
        bottom_layout.addStretch()

        # 重置按钮
        reset_btn = QPushButton("重置设置")
        reset_btn.setObjectName("btn_secondary")
        reset_btn.setFixedSize(120, 48)
        reset_btn.clicked.connect(self._reset_settings)

        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.setObjectName("btn_primary")
        save_btn.setFixedSize(120, 48)
        save_btn.clicked.connect(self._save_settings)

        bottom_layout.addWidget(reset_btn)
        bottom_layout.addWidget(save_btn)
        bottom_layout.addStretch()  # 添加弹性空间让按钮居中

        # 添加到主容器
        self.main_container.layout().addWidget(bottom_container)

    def _switch_to_tab(self, tab_id: str):
        """切换到指定标签页"""
        # 更新按钮状态
        for btn_id, btn in self.tab_buttons.items():
            is_active = btn_id == tab_id
            btn.setProperty("active", is_active)
            btn.style().unpolish(btn)
            btn.style().polish(btn)

        # 切换堆叠窗口
        tab_index = ["basic", "eeg", "stimulation"].index(tab_id)
        self.tab_stack.setCurrentIndex(tab_index)

        # 更新当前标签页
        self.current_tab = tab_id

    def _save_settings(self):
        """保存设置"""
        try:
            # 收集所有设置数据
            settings_data = {
                "basic": {},
                "eeg": {},
                "stimulation": {}
            }

            # 收集基本设置
            for field_id, input_widget in self.basic_inputs.items():
                if isinstance(input_widget, QLineEdit):
                    settings_data["basic"][field_id] = input_widget.text()
                else:  # NoWheelSpinBox
                    settings_data["basic"][field_id] = input_widget.value()

            # 收集脑电设备设置
            for field_id, input_widget in self.eeg_inputs.items():
                if isinstance(input_widget, QComboBox):
                    if field_id == "auto_connect":
                        # 特殊处理自动连接开关
                        settings_data["eeg"][field_id] = input_widget.currentText() == "启用"
                    elif field_id == "gatt_mode":
                        # 特殊处理GATT模式
                        settings_data["eeg"]["skip_gatt"] = input_widget.currentText() == "仅连接(推荐)"
                    else:
                        settings_data["eeg"][field_id] = input_widget.currentText()
                elif isinstance(input_widget, QLineEdit):
                    settings_data["eeg"][field_id] = input_widget.text()
                else:
                    settings_data["eeg"][field_id] = input_widget.value()



            # 收集脑机接口分类设置
            if hasattr(self, 'bci_inputs'):
                settings_data["bci"] = {}
                for field_id, input_widget in self.bci_inputs.items():
                    if isinstance(input_widget, QComboBox):
                        if field_id == "difficulty_level":
                            # 难度等级：从显示文本提取数字
                            settings_data["bci"][field_id] = input_widget.currentIndex() + 1
                        elif field_id == "weight_method":
                            # 权重方法映射
                            method_map = {0: "performance", 1: "equal", 2: "custom"}
                            settings_data["bci"][field_id] = method_map.get(input_widget.currentIndex(), "performance")
                        elif field_id in ["adaptive_threshold", "show_probability_details"]:
                            # 布尔值设置
                            settings_data["bci"][field_id] = input_widget.currentText() == "启用"
                        else:
                            settings_data["bci"][field_id] = input_widget.currentText()
                    else:
                        settings_data["bci"][field_id] = input_widget.value()

            # 收集电刺激设备设置
            for field_id, input_widget in self.stimulation_inputs.items():
                if isinstance(input_widget, QComboBox):
                    settings_data["stimulation"][field_id] = input_widget.currentText()
                else:
                    settings_data["stimulation"][field_id] = input_widget.value()

            # 保存到文件
            self._save_settings_to_file(settings_data)

            # 更新内部数据
            self.settings_data = settings_data

            # 发送信号
            self.settings_saved.emit(settings_data)

            # 保存成功后锁定所有受保护字段
            self._lock_all_protected_fields()

            # 显示成功消息
            show_information(self, "保存成功", "设置已成功保存！")

        except Exception as e:
            show_critical(self, "保存失败", f"保存设置时发生错误：{str(e)}")

    def _reset_settings(self):
        """重置设置"""
        reply = show_question(
            self,
            "重置设置",
            "确定要重置所有设置到默认值吗？此操作不可撤销。"
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 重置为默认设置
                default_settings = self._load_default_settings()

                # 更新基本设置输入框
                for field_id, input_widget in self.basic_inputs.items():
                    default_value = default_settings.get("basic", {}).get(field_id, "")
                    if isinstance(input_widget, QLineEdit):
                        input_widget.setText(str(default_value))
                    else:  # NoWheelSpinBox
                        input_widget.setValue(default_value)

                # 更新脑电设备设置输入框
                for field_id, input_widget in self.eeg_inputs.items():
                    default_value = default_settings.get("eeg", {}).get(field_id)
                    if isinstance(input_widget, QComboBox):
                        if field_id == "auto_connect":
                            # 特殊处理自动连接开关
                            input_widget.setCurrentText("启用" if default_value else "禁用")
                        else:
                            input_widget.setCurrentText(str(default_value))
                    elif isinstance(input_widget, QLineEdit):
                        input_widget.setText(str(default_value))
                    else:
                        input_widget.setValue(default_value)



                # 更新脑机接口分类设置输入框
                if hasattr(self, 'bci_inputs'):
                    for field_id, input_widget in self.bci_inputs.items():
                        default_value = default_settings.get("bci", {}).get(field_id)
                        if isinstance(input_widget, QComboBox):
                            if field_id == "difficulty_level":
                                input_widget.setCurrentIndex(default_value - 1)
                            elif field_id == "weight_method":
                                method_map = {"performance": 0, "equal": 1, "custom": 2}
                                input_widget.setCurrentIndex(method_map.get(default_value, 0))
                            elif field_id in ["adaptive_threshold", "show_probability_details"]:
                                input_widget.setCurrentText("启用" if default_value else "禁用")
                            else:
                                input_widget.setCurrentText(str(default_value))
                        else:
                            input_widget.setValue(default_value)

                # 更新电刺激设备设置输入框
                for field_id, input_widget in self.stimulation_inputs.items():
                    default_value = default_settings.get("stimulation", {}).get(field_id)
                    if isinstance(input_widget, QComboBox):
                        input_widget.setCurrentText(str(default_value))
                    else:
                        input_widget.setValue(default_value)

                # 更新内部数据
                self.settings_data = default_settings

                # 发送信号
                self.settings_reset.emit()

                # 显示成功消息
                show_information(self, "重置成功", "设置已重置为默认值！")

            except Exception as e:
                show_critical(self, "重置失败", f"重置设置时发生错误：{str(e)}")

    def _load_default_settings(self) -> dict:
        """加载默认设置"""
        return {
            "basic": {
                "system_name": "脑机接口康复训练系统",
                "hospital_id": "3",
                "hospital_name": "清华香峰",
                "department_name": "康复科",
                "device_id": "KFY160701",
                # 治疗配置
                "stimulation_duration": 10,  # 刺激时长（秒）
                "treatment_duration": 20,    # 治疗时长（分钟）
                "encouragement_interval": 30, # 鼓励间隔（秒）
                "min_save_duration": 5       # 最小保存时长（分钟）
            },
            "eeg": {
                # 基础连接配置
                "device_name": "BLE5201",
                "auto_connect": True,
                "connect_timeout": 10,
                "scan_timeout": 10,
                # 高级连接配置
                "max_retries": 3,
                "retry_interval": 3,
                "rssi_threshold": -70,
                "connection_priority": "优先已知设备",
                # 数据配置
                "sample_rate": 125.0,
                "channels": 8
            },
            "stimulation": {
                "port": "COM4",  # 从settings.json中的配置
                "max_current": 50,  # 调整为合理的默认值
                "min_current": 1,
                "current_step": 1.0,
                "default_frequency": 20,  # 文档建议的默认频率
                "default_pulse_width": 200,  # 文档建议的默认脉宽
                "default_relax_time": 5.0,  # 新增：默认休息时间
                "default_climb_time": 2.0,  # 重命名：上升时间
                "default_work_time": 10,  # 调整为文档建议值
                "default_fall_time": 2.0,  # 调整为文档建议值
                "waveform": "双相波"  # 文档建议默认使用双相波
            },
            "bci": {
                "difficulty_level": 3,  # 默认中等难度
                "custom_trigger_threshold": 0.60,  # 🔧 自定义触发阈值（与难度3对应）
                "weight_method": "performance",  # 权重计算方法
                "adaptive_threshold": False,  # 自适应阈值
                "show_probability_details": True  # 显示概率详情
            }
        }

    def _get_settings_file_path(self) -> Path:
        """获取设置文件路径"""
        from utils.path_manager import get_config_file_in_dir
        return get_config_file_in_dir("settings.json")

    def _load_settings_from_file(self):
        """从文件加载设置"""
        try:
            settings_file = self._get_settings_file_path()
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    file_settings = json.load(f)

                # 合并默认设置和文件设置
                for category, values in file_settings.items():
                    if category in self.settings_data:
                        self.settings_data[category].update(values)

        except Exception as e:
            print(f"加载设置文件失败: {e}")

    def _save_settings_to_file(self, settings_data: dict):
        """保存设置到文件"""
        try:
            settings_file = self._get_settings_file_path()

            # 确保目录存在
            settings_file.parent.mkdir(parents=True, exist_ok=True)

            # 保存设置
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            raise Exception(f"保存设置文件失败: {e}")

    def get_settings(self) -> dict:
        """获取当前设置"""
        return self.settings_data.copy()

    def get_setting(self, category: str, key: str, default=None):
        """获取特定设置值"""
        return self.settings_data.get(category, {}).get(key, default)

    def _scan_bluetooth_devices(self):
        """扫描蓝牙设备"""
        # 这里将在Phase 3中实现具体的蓝牙扫描功能
        show_information(self, "扫描设备", "蓝牙设备扫描功能将在蓝牙管理服务实现后启用。")

    def _clear_device_history(self):
        """清除设备配对历史"""
        reply = show_question(
            self,
            "清除配对历史",
            "确定要清除所有已配对的设备历史吗？此操作不可撤销。"
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 这里将在Phase 3中实现具体的清除功能
            show_information(self, "清除成功", "设备配对历史已清除。")

    def _toggle_field_lock(self, field_id):
        """切换字段锁定状态"""
        if field_id not in self.locked_fields:
            return

        lock_icon = self.lock_icons.get(field_id)

        if not lock_icon:
            return

        if lock_icon.is_locked():
            # 当前是锁定状态，尝试解锁所有字段
            password = PasswordDialog.get_password_input(self)
            if password == self.admin_password:
                # 密码正确，解锁所有受保护字段
                self._unlock_all_protected_fields()
                show_information(self, "解锁成功", "所有受保护字段已解锁，可以编辑")
            else:
                # 密码错误或取消
                if password is not None:  # 用户输入了密码但错误
                    show_critical(self, "密码错误", "管理员密码错误，无法解锁")
        else:
            # 当前是解锁状态，锁定所有字段
            self._lock_all_protected_fields()
            show_information(self, "锁定成功", "所有受保护字段已锁定")

    def _get_field_label(self, field_id):
        """获取字段的显示标签"""
        field_labels = {
            "hospital_id": "医院编号",
            "hospital_name": "医院名称",
            "department_name": "科室名称",
            "device_id": "设备编号"
        }
        return field_labels.get(field_id, field_id)

    def _unlock_all_protected_fields(self):
        """解锁所有受保护字段"""
        for field_id in self.locked_fields:
            lock_icon = self.lock_icons.get(field_id)
            input_field = self.basic_inputs.get(field_id)

            if lock_icon and input_field:
                lock_icon.set_locked(False)
                input_field.setReadOnly(False)
                input_field.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
                input_field.setProperty("readOnly", "false")
                input_field.style().unpolish(input_field)
                input_field.style().polish(input_field)

    def _lock_all_protected_fields(self):
        """锁定所有受保护字段"""
        for field_id in self.locked_fields:
            lock_icon = self.lock_icons.get(field_id)
            input_field = self.basic_inputs.get(field_id)

            if lock_icon and input_field:
                lock_icon.set_locked(True)
                input_field.setReadOnly(True)
                input_field.setFocusPolicy(Qt.FocusPolicy.NoFocus)
                input_field.setProperty("readOnly", "true")
                input_field.style().unpolish(input_field)
                input_field.style().polish(input_field)

    def update_theme(self):
        """更新主题"""
        # 强制刷新所有组件的样式
        self.style().unpolish(self)
        self.style().polish(self)


class LockIcon(QPushButton):
    """锁图标组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.locked = True  # 默认锁定状态
        self.setFixedSize(24, 24)
        self.setObjectName("lock_icon")
        self.setToolTip("点击解锁编辑")
        self.setStyleSheet("""
            QPushButton#lock_icon {
                border: none;
                background: transparent;
                padding: 2px;
            }
            QPushButton#lock_icon:hover {
                background-color: rgba(0, 0, 0, 0.1);
                border-radius: 4px;
            }
        """)

    def paintEvent(self, event):
        """绘制锁图标"""
        super().paintEvent(event)
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 设置颜色
        if self.locked:
            color = QColor("#64748b")  # 灰色 - 锁定状态
        else:
            color = QColor("#10b981")  # 绿色 - 解锁状态

        painter.setPen(color)
        painter.setBrush(color)

        # 绘制锁图标
        rect = self.rect()
        center_x = rect.width() // 2
        center_y = rect.height() // 2

        if self.locked:
            # 锁定状态 - 绘制闭合的锁
            # 锁体
            lock_body = QPainterPath()
            lock_body.addRoundedRect(center_x - 6, center_y - 2, 12, 8, 2, 2)
            painter.fillPath(lock_body, color)

            # 锁环
            painter.setPen(color)
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawArc(center_x - 4, center_y - 8, 8, 8, 0, 180 * 16)

            # 锁孔
            painter.setBrush(QColor("white"))
            painter.drawEllipse(center_x - 1, center_y - 1, 2, 2)
        else:
            # 解锁状态 - 绘制开放的锁
            # 锁体
            lock_body = QPainterPath()
            lock_body.addRoundedRect(center_x - 6, center_y - 2, 12, 8, 2, 2)
            painter.fillPath(lock_body, color)

            # 开放的锁环
            painter.setPen(color)
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawArc(center_x - 2, center_y - 8, 8, 8, 0, 180 * 16)

            # 锁孔
            painter.setBrush(QColor("white"))
            painter.drawEllipse(center_x - 1, center_y - 1, 2, 2)

    def set_locked(self, locked):
        """设置锁定状态"""
        self.locked = locked
        self.setToolTip("点击解锁编辑" if locked else "点击锁定编辑")
        self.update()

    def is_locked(self):
        """获取锁定状态"""
        return self.locked


class PasswordDialog(QDialog):
    """密码输入对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("输入密码")
        self.setModal(True)
        self.setFixedSize(300, 200)
        self.setObjectName("password_dialog")

        # 设置无边框窗口
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        self._setup_ui()

        # 应用全局样式
        self._apply_global_style()

    def _setup_ui(self):
        """设置界面"""
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("discharge_dialog_container")

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(main_container)

        # 内容布局
        content_layout = QVBoxLayout(main_container)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)

        # 标题
        title_label = QLabel("请输入密码")
        title_label.setObjectName("dialog_title")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(title_label)

        # 密码输入框
        self.password_input = QLineEdit()
        self.password_input.setObjectName("settings_input")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.returnPressed.connect(self.accept)
        content_layout.addWidget(self.password_input)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("btn_secondary")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.setObjectName("btn_primary")
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        content_layout.addLayout(button_layout)

        # 设置焦点
        self.password_input.setFocus()

    def _apply_global_style(self):
        """应用全局样式"""
        try:
            # 方法1：从QApplication获取全局样式
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app and app.styleSheet():
                self.setStyleSheet(app.styleSheet())
                return

            # 方法2：从父窗口获取样式
            if self.parent() and hasattr(self.parent(), 'styleSheet') and self.parent().styleSheet():
                self.setStyleSheet(self.parent().styleSheet())
                return

            # 方法3：查找主窗口的样式
            parent_widget = self.parent()
            level = 0
            while parent_widget and level < 10:
                if hasattr(parent_widget, 'styleSheet') and parent_widget.styleSheet():
                    self.setStyleSheet(parent_widget.styleSheet())
                    return
                parent_widget = parent_widget.parent()
                level += 1

        except Exception as e:
            print(f"应用密码对话框样式失败: {e}")

    def get_password(self):
        """获取输入的密码"""
        return self.password_input.text()

    @staticmethod
    def get_password_input(parent=None):
        """静态方法获取密码输入"""
        dialog = PasswordDialog(parent)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            return dialog.get_password()
        return None
