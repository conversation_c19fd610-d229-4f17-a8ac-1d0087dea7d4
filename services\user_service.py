# -*- coding: utf-8 -*-
"""
用户管理服务
User Management Service

提供用户的增删改查、认证、权限管理等功能
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import hashlib
import secrets
from core.database import db_manager

class UserService:
    """用户管理服务类"""
    
    def __init__(self):
        self.table_name = "operator"
    
    def generate_password_hash(self, password: str) -> str:
        """生成密码哈希"""
        # 简单实现，生产环境应使用bcrypt
        salt = secrets.token_hex(16)
        return hashlib.sha256((password + salt).encode()).hexdigest() + ":" + salt
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            hash_part, salt = password_hash.split(":")
            return hashlib.sha256((password + salt).encode()).hexdigest() == hash_part
        except:
            # 兼容旧的简单哈希
            return hashlib.sha256(password.encode()).hexdigest() == password_hash
    
    def get_all_users(self, include_inactive: bool = True) -> List[Dict[str, Any]]:
        """
        获取所有用户
        
        Args:
            include_inactive: 是否包含停用用户
            
        Returns:
            用户列表
        """
        try:
            where_clause = "" if include_inactive else "WHERE status = 'active'"
            sql = f"""
            SELECT 
                id, username, email, name, role, status,
                created_at, last_login_at, updated_at,
                login_count, total_online_minutes, last_login_ip, last_device_type,
                hospital_id, department, employee_id, phone, notes,
                is_locked, failed_login_attempts
            FROM {self.table_name} 
            {where_clause}
            ORDER BY created_at DESC
            """
            
            rows = db_manager.execute_query(sql)
            users = []
            
            for row in rows:
                user = dict(row)
                # 格式化数据供UI使用
                user['avatar'] = user['name'][:1] if user['name'] else user['username'][:1].upper()
                user['onlineTime'] = self._format_online_time(user.get('total_online_minutes', 0))
                user['deviceType'] = user.get('last_device_type', 'Unknown')
                user['lastIP'] = user.get('last_login_ip', 'N/A')
                user['loginCount'] = user.get('login_count', 0)
                user['lastLogin'] = user.get('last_login_at', 'Never')
                user['created'] = user.get('created_at', '')
                users.append(user)
            
            return users
            
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return []
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取用户"""
        try:
            sql = f"SELECT * FROM {self.table_name} WHERE id = ?"
            row = db_manager.execute_one(sql, (user_id,))
            
            if row:
                user = dict(row)
                user['avatar'] = user['name'][:1] if user['name'] else user['username'][:1].upper()
                user['onlineTime'] = self._format_online_time(user.get('total_online_minutes', 0))
                return user
            return None
            
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """根据用户名获取用户"""
        try:
            sql = f"SELECT * FROM {self.table_name} WHERE username = ?"
            row = db_manager.execute_one(sql, (username,))
            return dict(row) if row else None
            
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新用户
        
        Args:
            user_data: 用户数据字典
            
        Returns:
            操作结果
        """
        try:
            # 检查权限
            try:
                from utils.simple_permission_manager import permission_manager
                if not permission_manager.can_perform_operation('user_create'):
                    return {'success': False, 'error': '没有权限创建用户'}
            except Exception as e:
                print(f"权限检查失败: {e}")
                # 默认允许操作，避免系统崩溃
                pass
            
            # 验证数据
            validation_result = self._validate_user_data(user_data)
            if not validation_result['valid']:
                return {'success': False, 'errors': validation_result['errors']}
            
            # 检查用户名和邮箱唯一性
            if self.get_user_by_username(user_data['username']):
                return {'success': False, 'errors': {'username': '用户名已存在'}}
            
            if user_data.get('email'):
                existing_email = db_manager.execute_one(
                    f"SELECT id FROM {self.table_name} WHERE email = ?",
                    (user_data['email'],)
                )
                if existing_email:
                    return {'success': False, 'errors': {'email': '邮箱已存在'}}
            
            # 准备数据
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            password_hash = self.generate_password_hash(user_data['password'])
            
            # 插入数据
            sql = f"""
            INSERT INTO {self.table_name} (
                username, password_hash, email, name, role, status,
                created_at, updated_at, hospital_id, department, employee_id, phone, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            values = (
                user_data['username'],
                password_hash,
                user_data.get('email'),
                user_data.get('name'),
                user_data.get('role', 'operator'),
                user_data.get('status', 'active'),
                now,
                now,
                user_data.get('hospital_id'),
                user_data.get('department'),
                user_data.get('employee_id'),
                user_data.get('phone'),
                user_data.get('notes')
            )
            
            user_id = db_manager.execute_insert(sql, values)
            
            if user_id:
                return {'success': True, 'user_id': user_id}
            else:
                return {'success': False, 'errors': {'general': '创建用户失败'}}
                
        except Exception as e:
            print(f"创建用户失败: {e}")
            return {'success': False, 'errors': {'general': str(e)}}
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            user_data: 更新的用户数据
            
        Returns:
            操作结果
        """
        try:
            # 验证用户存在
            existing_user = self.get_user_by_id(user_id)
            if not existing_user:
                return {'success': False, 'errors': {'general': '用户不存在'}}
            
            # 验证数据
            validation_result = self._validate_user_data(user_data, is_update=True)
            if not validation_result['valid']:
                return {'success': False, 'errors': validation_result['errors']}
            
            # 检查用户名唯一性（排除自己）
            if 'username' in user_data:
                existing_username = db_manager.execute_one(
                    f"SELECT id FROM {self.table_name} WHERE username = ? AND id != ?",
                    (user_data['username'], user_id)
                )
                if existing_username:
                    return {'success': False, 'errors': {'username': '用户名已存在'}}
            
            # 检查邮箱唯一性（排除自己）
            if user_data.get('email'):
                existing_email = db_manager.execute_one(
                    f"SELECT id FROM {self.table_name} WHERE email = ? AND id != ?",
                    (user_data['email'], user_id)
                )
                if existing_email:
                    return {'success': False, 'errors': {'email': '邮箱已存在'}}
            
            # 准备更新数据
            update_fields = []
            update_values = []
            
            # 可更新的字段
            updatable_fields = [
                'username', 'email', 'name', 'role', 'status',
                'department', 'employee_id', 'phone', 'notes'
            ]
            
            for field in updatable_fields:
                if field in user_data:
                    update_fields.append(f"{field} = ?")
                    update_values.append(user_data[field])
            
            # 处理密码更新
            if 'password' in user_data and user_data['password']:
                update_fields.append("password_hash = ?")
                update_values.append(self.generate_password_hash(user_data['password']))
            
            # 添加更新时间
            update_fields.append("updated_at = ?")
            update_values.append(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # 添加用户ID到参数末尾
            update_values.append(user_id)
            
            # 执行更新
            sql = f"UPDATE {self.table_name} SET {', '.join(update_fields)} WHERE id = ?"
            affected_rows = db_manager.execute_update(sql, tuple(update_values))
            
            if affected_rows > 0:
                return {'success': True}
            else:
                return {'success': False, 'errors': {'general': '更新失败'}}
                
        except Exception as e:
            print(f"更新用户失败: {e}")
            return {'success': False, 'errors': {'general': str(e)}}
    
    def toggle_user_status(self, user_id: int) -> Dict[str, Any]:
        """切换用户状态（启用/停用）"""
        try:
            # 检查权限
            try:
                from utils.simple_permission_manager import permission_manager
                if not permission_manager.can_perform_operation('user_toggle_status'):
                    return {'success': False, 'error': '没有权限修改用户状态'}
            except Exception as e:
                print(f"权限检查失败: {e}")
                # 默认允许操作，避免系统崩溃
                pass
            
            user = self.get_user_by_id(user_id)
            if not user:
                return {'success': False, 'error': '用户不存在'}
            
            new_status = 'inactive' if user['status'] == 'active' else 'active'
            
            sql = f"UPDATE {self.table_name} SET status = ?, updated_at = ? WHERE id = ?"
            affected_rows = db_manager.execute_update(
                sql, 
                (new_status, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), user_id)
            )
            
            if affected_rows > 0:
                return {'success': True, 'new_status': new_status}
            else:
                return {'success': False, 'error': '状态更新失败'}
                
        except Exception as e:
            print(f"切换用户状态失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用户认证"""
        try:
            user = self.get_user_by_username(username)
            if not user:
                return None
            
            if user['status'] != 'active':
                return None
            
            if user.get('is_locked', 0):
                return None
            
            if self.verify_password(password, user['password_hash']):
                # 更新登录信息
                self._update_login_info(user['id'])
                return user
            else:
                # 记录失败登录
                self._record_failed_login(user['id'])
                return None
                
        except Exception as e:
            print(f"用户认证失败: {e}")
            return None
    
    def _validate_user_data(self, data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """验证用户数据"""
        errors = {}
        
        # 用户名验证
        if not is_update or 'username' in data:
            username = data.get('username', '').strip()
            if not username:
                errors['username'] = '用户名不能为空'
            elif len(username) < 3:
                errors['username'] = '用户名至少3个字符'
            elif len(username) > 50:
                errors['username'] = '用户名不能超过50个字符'
        
        # 密码验证（创建时必需，更新时可选）
        if not is_update:
            password = data.get('password', '')
            if not password:
                errors['password'] = '密码不能为空'
            elif len(password) < 6:
                errors['password'] = '密码至少6个字符'
        
        # 邮箱验证（可选）
        if data.get('email'):
            email = data['email'].strip()
            if '@' not in email or '.' not in email:
                errors['email'] = '邮箱格式不正确'
        
        # 角色验证
        if 'role' in data:
            if data['role'] not in ['admin', 'doctor', 'operator']:
                errors['role'] = '角色值无效'
        
        # 状态验证
        if 'status' in data:
            if data['status'] not in ['active', 'inactive']:
                errors['status'] = '状态值无效'
        
        return {'valid': len(errors) == 0, 'errors': errors}
    
    def _format_online_time(self, minutes: int) -> str:
        """格式化在线时长"""
        if minutes < 60:
            return f"{minutes} 分钟"
        elif minutes < 1440:  # 24小时
            hours = minutes // 60
            return f"{hours} 小时"
        else:
            hours = minutes // 60
            return f"{hours:,} 小时"
    
    def _update_login_info(self, user_id: int):
        """更新登录信息"""
        try:
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            sql = f"""
            UPDATE {self.table_name} 
            SET last_login_at = ?, login_count = login_count + 1, failed_login_attempts = 0
            WHERE id = ?
            """
            db_manager.execute_update(sql, (now, user_id))
        except Exception as e:
            print(f"更新登录信息失败: {e}")
    
    def _record_failed_login(self, user_id: int):
        """记录失败登录"""
        try:
            sql = f"""
            UPDATE {self.table_name} 
            SET failed_login_attempts = failed_login_attempts + 1
            WHERE id = ?
            """
            db_manager.execute_update(sql, (user_id,))
        except Exception as e:
            print(f"记录失败登录失败: {e}")

# 全局用户服务实例
user_service = UserService()
