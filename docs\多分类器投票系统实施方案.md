# 多分类器投票系统实施方案
多轮训练问题，保存原始数据
## 📋 项目概述
FBCSP + SVM - 空间滤波方法
TEF + Random Forest - 时频域特征方法（优化后）
Riemannian + MeanField - 黎曼几何复杂分类器
TangentSpace + LR - 黎曼几何简单分类器 ← 新增
### 目标
实现基于多分类器投票的脑机接口分类系统，充分利用FBCSP、TEF、EEGNet三种特征的优势，通过投票机制提高分类精度和系统鲁棒性。

### 系统架构
```
原始EEG数据 [8, 375]
    ↓
┌─────────────────────────────────────┐
│        特征提取阶段                    │
├─────────────────────────────────────┤
│ FBCSP特征    TEF特征    EEGNet特征    │
│   [50维]     [100维]     [176维]     │
│     ↓          ↓          ↓         │
│ SVM分类器  RF分类器   MLP分类器       │
│     ↓          ↓          ↓         │
│  预测1       预测2       预测3        │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│        投票决策阶段                    │
├─────────────────────────────────────┤
│      加权投票 (w1, w2, w3)            │
│           ↓                         │
│       最终预测                       │
└─────────────────────────────────────┘
```

## 🔧 技术实现方案

### 1. 核心类设计

#### 1.1 多分类器投票管理器
```python
class MultiClassifierVotingManager:
    """多分类器投票系统管理器"""
    
    def __init__(self, config: VotingConfig):
        # 特征提取器
        self.fbcsp_extractor = FBCSPExtractor()
        self.tef_extractor = TEFExtractor()
        self.eegnet_extractor = EEGNetExtractor()
        
        # 分类器
        self.fbcsp_classifier = SVM(kernel='linear', probability=True)
        self.tef_classifier = RandomForestClassifier(n_estimators=100, probability=True)
        self.eegnet_classifier = MLPClassifier(hidden_layer_sizes=(128, 64), probability=True)
        
        # 投票权重
        self.voting_weights = config.voting_weights
        self.adaptive_weights = config.adaptive_weights
        
        # 性能统计
        self.performance_stats = {}
        
    def fit(self, X_raw, y):
        """训练所有分类器"""
        
    def predict(self, X_raw):
        """多分类器投票预测"""
        
    def predict_with_details(self, X_raw):
        """详细预测（包含各分类器结果）"""
```

#### 1.2 配置类
```python
@dataclass
class VotingConfig:
    """投票系统配置"""
    # 基础配置
    sampling_rate: int = 125
    n_channels: int = 8
    n_samples: int = 375
    
    # 投票权重
    voting_weights: Dict[str, float] = field(default_factory=lambda: {
        'fbcsp': 0.4,
        'tef': 0.3,
        'eegnet': 0.3
    })
    
    # 自适应权重
    adaptive_weights: bool = True
    weight_optimization_method: str = 'cross_validation'  # 'cross_validation', 'genetic_algorithm'
    
    # 分类器配置
    fbcsp_classifier_config: Dict = field(default_factory=lambda: {
        'kernel': 'linear',
        'C': 1.0,
        'probability': True
    })
    
    tef_classifier_config: Dict = field(default_factory=lambda: {
        'n_estimators': 100,
        'max_depth': 10,
        'probability': True
    })
    
    eegnet_classifier_config: Dict = field(default_factory=lambda: {
        'hidden_layer_sizes': (128, 64, 32),
        'activation': 'relu',
        'solver': 'adam',
        'probability': True
    })
```

### 2. 详细实现代码

#### 2.1 训练方法
```python
def fit(self, X_raw, y):
    """
    训练所有分类器
    
    Args:
        X_raw: 原始EEG数据 [n_trials, 8, 375]
        y: 标签 [n_trials]
    """
    print("开始训练多分类器投票系统...")
    
    # 1. 训练特征提取器
    print("训练特征提取器...")
    self.fbcsp_extractor.fit(X_raw, y)
    self.tef_extractor.fit(X_raw, y)
    self.eegnet_extractor.fit(X_raw, y)
    
    # 2. 提取特征
    print("提取训练特征...")
    fbcsp_features = self.fbcsp_extractor.transform(X_raw)
    tef_features = self.tef_extractor.transform(X_raw)
    eegnet_features = self.eegnet_extractor.transform(X_raw)
    
    print(f"特征维度 - FBCSP: {fbcsp_features.shape[1]}, "
          f"TEF: {tef_features.shape[1]}, "
          f"EEGNet: {eegnet_features.shape[1]}")
    
    # 3. 训练各分类器
    print("训练分类器...")
    self.fbcsp_classifier.fit(fbcsp_features, y)
    self.tef_classifier.fit(tef_features, y)
    self.eegnet_classifier.fit(eegnet_features, y)
    
    # 4. 优化投票权重
    if self.adaptive_weights:
        print("优化投票权重...")
        self._optimize_voting_weights(X_raw, y)
    
    # 5. 评估系统性能
    self._evaluate_system_performance(X_raw, y)
    
    print("多分类器投票系统训练完成")
```

#### 2.2 预测方法
```python
def predict(self, X_raw):
    """
    多分类器投票预测
    
    Args:
        X_raw: 原始EEG数据 [n_trials, 8, 375]
        
    Returns:
        predictions: 预测概率 [n_trials, n_classes]
    """
    # 1. 提取特征
    fbcsp_features = self.fbcsp_extractor.transform(X_raw)
    tef_features = self.tef_extractor.transform(X_raw)
    eegnet_features = self.eegnet_extractor.transform(X_raw)
    
    # 2. 各分类器预测
    fbcsp_pred = self.fbcsp_classifier.predict_proba(fbcsp_features)
    tef_pred = self.tef_classifier.predict_proba(tef_features)
    eegnet_pred = self.eegnet_classifier.predict_proba(eegnet_features)
    
    # 3. 加权投票
    final_pred = (self.voting_weights['fbcsp'] * fbcsp_pred +
                 self.voting_weights['tef'] * tef_pred +
                 self.voting_weights['eegnet'] * eegnet_pred)
    
    return final_pred

def predict_with_details(self, X_raw):
    """
    详细预测（包含各分类器结果）
    
    Returns:
        dict: 包含所有预测信息的字典
    """
    # 提取特征
    fbcsp_features = self.fbcsp_extractor.transform(X_raw)
    tef_features = self.tef_extractor.transform(X_raw)
    eegnet_features = self.eegnet_extractor.transform(X_raw)
    
    # 各分类器预测
    fbcsp_pred = self.fbcsp_classifier.predict_proba(fbcsp_features)
    tef_pred = self.tef_classifier.predict_proba(tef_features)
    eegnet_pred = self.eegnet_classifier.predict_proba(eegnet_features)
    
    # 最终预测
    final_pred = (self.voting_weights['fbcsp'] * fbcsp_pred +
                 self.voting_weights['tef'] * tef_pred +
                 self.voting_weights['eegnet'] * eegnet_pred)
    
    # 返回详细信息
    return {
        'final_prediction': final_pred,
        'individual_predictions': {
            'fbcsp': fbcsp_pred,
            'tef': tef_pred,
            'eegnet': eegnet_pred
        },
        'voting_weights': self.voting_weights.copy(),
        'confidence': np.max(final_pred, axis=1),
        'predicted_class': np.argmax(final_pred, axis=1),
        'feature_dimensions': {
            'fbcsp': fbcsp_features.shape[1],
            'tef': tef_features.shape[1],
            'eegnet': eegnet_features.shape[1]
        }
    }
```

#### 2.3 权重优化方法
```python
def _optimize_voting_weights(self, X_raw, y):
    """优化投票权重"""
    
    if self.weight_optimization_method == 'cross_validation':
        self._optimize_weights_cv(X_raw, y)
    elif self.weight_optimization_method == 'genetic_algorithm':
        self._optimize_weights_ga(X_raw, y)

def _optimize_weights_cv(self, X_raw, y):
    """基于交叉验证优化权重"""
    from sklearn.model_selection import cross_val_score
    
    # 提取特征
    fbcsp_features = self.fbcsp_extractor.transform(X_raw)
    tef_features = self.tef_extractor.transform(X_raw)
    eegnet_features = self.eegnet_extractor.transform(X_raw)
    
    # 评估各分类器性能
    fbcsp_score = cross_val_score(self.fbcsp_classifier, fbcsp_features, y, cv=5).mean()
    tef_score = cross_val_score(self.tef_classifier, tef_features, y, cv=5).mean()
    eegnet_score = cross_val_score(self.eegnet_classifier, eegnet_features, y, cv=5).mean()
    
    # 基于性能调整权重
    total_score = fbcsp_score + tef_score + eegnet_score
    self.voting_weights = {
        'fbcsp': fbcsp_score / total_score,
        'tef': tef_score / total_score,
        'eegnet': eegnet_score / total_score
    }
    
    # 记录性能
    self.performance_stats = {
        'fbcsp_cv_score': fbcsp_score,
        'tef_cv_score': tef_score,
        'eegnet_cv_score': eegnet_score,
        'optimized_weights': self.voting_weights.copy()
    }
    
    print(f"优化后的投票权重: {self.voting_weights}")
    print(f"各分类器CV得分: FBCSP={fbcsp_score:.3f}, TEF={tef_score:.3f}, EEGNet={eegnet_score:.3f}")
```

## 🔄 系统集成方案

### 1. 修改现有特征融合管理器

#### 1.1 创建新的投票管理器
```python
# 在 services/feature_extraction/ 目录下创建
# voting_classifier_manager.py

class VotingClassifierManager:
    """投票分类器管理器"""
    
    def __init__(self, config: VotingConfig = None):
        self.config = config or VotingConfig()
        self.voting_system = MultiClassifierVotingManager(self.config)
        self._is_fitted = False
    
    def fit(self, X, y):
        """训练投票系统"""
        self.voting_system.fit(X, y)
        self._is_fitted = True
    
    def predict(self, X):
        """预测"""
        if not self._is_fitted:
            raise ValueError("投票系统尚未训练")
        return self.voting_system.predict(X)
    
    def predict_with_details(self, X):
        """详细预测"""
        if not self._is_fitted:
            raise ValueError("投票系统尚未训练")
        return self.voting_system.predict_with_details(X)
```

### 2. 集成到治疗页面

#### 2.1 修改治疗页面初始化
```python
# 在 ui/pages/treatment_page.py 中

def _initialize_feature_manager(self):
    """初始化特征管理器"""
    try:
        # 选择分类方法
        classification_method = "voting"  # "fusion" or "voting"
        
        if classification_method == "voting":
            # 使用投票分类器
            from services.feature_extraction.voting_classifier_manager import VotingClassifierManager
            self.classifier_manager = VotingClassifierManager()
        else:
            # 使用独立特征管理器
            from services.feature_extraction import IndividualFeatureManager
            config = FeatureExtractionConfig()
            self.classifier_manager = IndividualFeatureManager(config)
        
        print(f"特征管理器初始化完成: {classification_method}")
        
    except Exception as e:
        print(f"特征管理器初始化失败: {e}")
        self.classifier_manager = None
```

#### 2.2 修改实时分类方法
```python
def _perform_realtime_classification(self, window_data):
    """执行实时分类 - 使用投票系统"""
    try:
        if not self.classifier_manager or not hasattr(self.classifier_manager, '_is_fitted') or not self.classifier_manager._is_fitted:
            return
        
        # 输入数据格式: [8, 375]
        input_data = window_data[np.newaxis, :, :]  # [1, 8, 375]
        
        # 使用投票分类器预测
        detailed_result = self.classifier_manager.predict_with_details(input_data)
        
        # 获取分类结果
        final_prediction = detailed_result['final_prediction']
        individual_predictions = detailed_result['individual_predictions']
        voting_weights = detailed_result['voting_weights']
        confidence = detailed_result['confidence'][0]
        predicted_class = detailed_result['predicted_class'][0]
        
        # 更新显示
        result = 'motor_imagery' if predicted_class == 1 else 'rest'
        
        # 显示详细信息
        print(f"投票分类结果: {result}")
        print(f"总体置信度: {confidence:.3f}")
        print(f"各分类器预测:")
        print(f"  FBCSP: {individual_predictions['fbcsp'][0]}")
        print(f"  TEF: {individual_predictions['tef'][0]}")
        print(f"  EEGNet: {individual_predictions['eegnet'][0]}")
        print(f"投票权重: {voting_weights}")
        
        # 触发电刺激
        if result == 'motor_imagery' and confidence > 0.7:
            self._trigger_stimulation()
            
    except Exception as e:
        print(f"投票分类失败: {e}")
```

## 📊 性能评估方案

### 1. 评估指标
```python
def _evaluate_system_performance(self, X_raw, y):
    """评估系统性能"""
    from sklearn.model_selection import cross_val_score, StratifiedKFold
    from sklearn.metrics import classification_report, confusion_matrix
    
    # 交叉验证评估
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    # 评估各个分类器
    fbcsp_features = self.fbcsp_extractor.transform(X_raw)
    tef_features = self.tef_extractor.transform(X_raw)
    eegnet_features = self.eegnet_extractor.transform(X_raw)
    
    fbcsp_scores = cross_val_score(self.fbcsp_classifier, fbcsp_features, y, cv=cv)
    tef_scores = cross_val_score(self.tef_classifier, tef_features, y, cv=cv)
    eegnet_scores = cross_val_score(self.eegnet_classifier, eegnet_features, y, cv=cv)
    
    # 评估投票系统
    voting_scores = []
    for train_idx, test_idx in cv.split(X_raw, y):
        X_train, X_test = X_raw[train_idx], X_raw[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]
        
        # 临时训练
        temp_system = MultiClassifierVotingManager(self.config)
        temp_system.fit(X_train, y_train)
        
        # 预测
        pred = temp_system.predict(X_test)
        pred_class = np.argmax(pred, axis=1)
        
        # 计算准确率
        accuracy = np.mean(pred_class == y_test)
        voting_scores.append(accuracy)
    
    # 记录结果
    self.performance_stats.update({
        'fbcsp_scores': fbcsp_scores,
        'tef_scores': tef_scores,
        'eegnet_scores': eegnet_scores,
        'voting_scores': voting_scores,
        'mean_scores': {
            'fbcsp': np.mean(fbcsp_scores),
            'tef': np.mean(tef_scores),
            'eegnet': np.mean(eegnet_scores),
            'voting': np.mean(voting_scores)
        }
    })
    
    print("=== 系统性能评估 ===")
    print(f"FBCSP分类器: {np.mean(fbcsp_scores):.3f} ± {np.std(fbcsp_scores):.3f}")
    print(f"TEF分类器: {np.mean(tef_scores):.3f} ± {np.std(tef_scores):.3f}")
    print(f"EEGNet分类器: {np.mean(eegnet_scores):.3f} ± {np.std(eegnet_scores):.3f}")
    print(f"投票系统: {np.mean(voting_scores):.3f} ± {np.std(voting_scores):.3f}")
```

## 🚀 实施步骤

### 第一阶段：基础实现（1-2天）
1. ✅ 创建 `VotingConfig` 配置类
2. ✅ 实现 `MultiClassifierVotingManager` 核心类
3. ✅ 实现基础的训练和预测方法
4. ✅ 创建简单的权重优化方法

### 第二阶段：系统集成（1天）
1. ✅ 创建 `VotingClassifierManager` 包装类
2. ✅ 修改治疗页面的分类器初始化
3. ✅ 修改实时分类方法
4. ✅ 测试基本功能

### 第三阶段：性能优化（2-3天）
1. ✅ 实现详细的性能评估
2. ✅ 优化权重计算方法
3. ✅ 添加遗传算法权重优化
4. ✅ 性能对比测试

### 第四阶段：完善和调优（1-2天）
1. ✅ 添加详细的日志和统计
2. ✅ 优化用户界面显示
3. ✅ 添加配置文件支持
4. ✅ 文档完善

## 📁 文件结构

```
services/feature_extraction/
├── voting_classifier_manager.py      # 投票分类器管理器
├── multi_classifier_voting.py        # 多分类器投票核心实现
├── voting_config.py                  # 投票系统配置
└── __init__.py                       # 导入配置

ui/pages/
└── treatment_page.py                 # 修改实时分类方法

docs/
└── 多分类器投票系统实施方案.md        # 本文档
```

## 🎯 预期效果

### 性能指标
- **分类精度**: 预期提升5-15%
- **系统稳定性**: 显著改善
- **实时性**: 保持32ms要求
- **鲁棒性**: 单分类器失效不影响整体

### 临床价值
- **可靠性**: 多分类器投票提高决策可靠性
- **可解释性**: 可以分析各分类器的贡献
- **适应性**: 可以根据患者特点调整权重
- **扩展性**: 易于添加新的特征和分类器

## 🔍 故障排除指南

### 常见问题及解决方案

#### 1. 特征提取器未拟合
**问题**: `sklearn.exceptions.NotFittedError: This XXX instance is not fitted yet.`
**解决**: 确保在预测前调用了 `fit()` 方法

#### 2. 数据维度不匹配
**问题**: 输入数据形状错误
**解决**: 检查输入数据格式 `[n_trials, 8, 375]`

#### 3. 投票权重异常
**问题**: 权重和不等于1或权重为负
**解决**: 添加权重归一化和验证

```python
def _validate_weights(self):
    """验证投票权重"""
    total = sum(self.voting_weights.values())
    if abs(total - 1.0) > 1e-6:
        # 归一化权重
        for key in self.voting_weights:
            self.voting_weights[key] /= total
```

#### 4. 内存不足
**问题**: 训练时内存溢出
**解决**: 使用批处理训练或减少特征维度

## 📈 性能监控方案

### 1. 实时性能监控
```python
class PerformanceMonitor:
    def __init__(self):
        self.prediction_times = []
        self.accuracy_history = []
        self.confidence_history = []

    def log_prediction(self, prediction_time, confidence, true_label=None, predicted_label=None):
        """记录预测性能"""
        self.prediction_times.append(prediction_time)
        self.confidence_history.append(confidence)

        if true_label is not None and predicted_label is not None:
            accuracy = 1.0 if true_label == predicted_label else 0.0
            self.accuracy_history.append(accuracy)

    def get_stats(self):
        """获取性能统计"""
        return {
            'avg_prediction_time': np.mean(self.prediction_times),
            'avg_confidence': np.mean(self.confidence_history),
            'recent_accuracy': np.mean(self.accuracy_history[-100:]) if self.accuracy_history else 0,
            'total_predictions': len(self.prediction_times)
        }
```

### 2. 分类器贡献度分析
```python
def analyze_classifier_contributions(self, X_test, y_test):
    """分析各分类器的贡献度"""
    results = self.predict_with_details(X_test)

    # 计算各分类器的准确率
    individual_accuracies = {}
    for name, predictions in results['individual_predictions'].items():
        pred_classes = np.argmax(predictions, axis=1)
        accuracy = np.mean(pred_classes == y_test)
        individual_accuracies[name] = accuracy

    # 计算投票系统准确率
    voting_pred_classes = np.argmax(results['final_prediction'], axis=1)
    voting_accuracy = np.mean(voting_pred_classes == y_test)

    return {
        'individual_accuracies': individual_accuracies,
        'voting_accuracy': voting_accuracy,
        'improvement': voting_accuracy - max(individual_accuracies.values()),
        'weights': results['voting_weights']
    }
```

## 🧪 测试验证方案

### 1. 单元测试
```python
# tests/test_voting_classifier.py

import unittest
import numpy as np
from services.feature_extraction.voting_classifier_manager import VotingClassifierManager

class TestVotingClassifier(unittest.TestCase):

    def setUp(self):
        """测试初始化"""
        self.manager = VotingClassifierManager()

        # 创建模拟数据
        self.X_train = np.random.randn(100, 8, 375)
        self.y_train = np.random.randint(0, 2, 100)
        self.X_test = np.random.randn(20, 8, 375)
        self.y_test = np.random.randint(0, 2, 20)

    def test_training(self):
        """测试训练过程"""
        self.manager.fit(self.X_train, self.y_train)
        self.assertTrue(self.manager._is_fitted)

    def test_prediction(self):
        """测试预测过程"""
        self.manager.fit(self.X_train, self.y_train)
        predictions = self.manager.predict(self.X_test)

        self.assertEqual(predictions.shape, (20, 2))
        self.assertTrue(np.allclose(np.sum(predictions, axis=1), 1.0))

    def test_detailed_prediction(self):
        """测试详细预测"""
        self.manager.fit(self.X_train, self.y_train)
        results = self.manager.predict_with_details(self.X_test)

        required_keys = ['final_prediction', 'individual_predictions', 'voting_weights', 'confidence']
        for key in required_keys:
            self.assertIn(key, results)

if __name__ == '__main__':
    unittest.main()
```

### 2. 集成测试
```python
# tests/test_integration.py

def test_end_to_end_classification():
    """端到端分类测试"""
    # 1. 创建模拟EEG数据
    X_raw = np.random.randn(50, 8, 375)
    y = np.random.randint(0, 2, 50)

    # 2. 训练系统
    manager = VotingClassifierManager()
    manager.fit(X_raw, y)

    # 3. 实时预测测试
    single_trial = np.random.randn(1, 8, 375)
    prediction = manager.predict(single_trial)

    # 4. 验证结果
    assert prediction.shape == (1, 2)
    assert np.allclose(np.sum(prediction), 1.0)

    print("✅ 端到端测试通过")

def test_performance_benchmark():
    """性能基准测试"""
    import time

    manager = VotingClassifierManager()
    X_train = np.random.randn(200, 8, 375)
    y_train = np.random.randint(0, 2, 200)

    # 训练时间测试
    start_time = time.time()
    manager.fit(X_train, y_train)
    training_time = time.time() - start_time

    # 预测时间测试
    X_test = np.random.randn(1, 8, 375)
    start_time = time.time()
    prediction = manager.predict(X_test)
    prediction_time = time.time() - start_time

    print(f"训练时间: {training_time:.2f}秒")
    print(f"预测时间: {prediction_time*1000:.2f}毫秒")

    # 验证实时性要求
    assert prediction_time < 0.032, f"预测时间超过32ms要求: {prediction_time*1000:.2f}ms"

    print("✅ 性能基准测试通过")
```

## 📋 配置文件模板

### config/voting_classifier_config.yaml
```yaml
# 多分类器投票系统配置

# 基础配置
basic:
  sampling_rate: 125
  n_channels: 8
  n_samples: 375

# 投票权重配置
voting:
  weights:
    fbcsp: 0.4
    tef: 0.3
    eegnet: 0.3
  adaptive_weights: true
  weight_optimization_method: "cross_validation"  # cross_validation, genetic_algorithm

# FBCSP分类器配置
fbcsp_classifier:
  kernel: "linear"
  C: 1.0
  probability: true
  random_state: 42

# TEF分类器配置
tef_classifier:
  n_estimators: 100
  max_depth: 10
  probability: true
  random_state: 42

# EEGNet分类器配置
eegnet_classifier:
  hidden_layer_sizes: [128, 64, 32]
  activation: "relu"
  solver: "adam"
  probability: true
  random_state: 42
  max_iter: 500

# 性能监控配置
monitoring:
  enable_performance_logging: true
  log_prediction_details: true
  save_performance_history: true
  performance_log_file: "logs/voting_classifier_performance.log"
```

## 🎯 部署检查清单

### 部署前检查
- [ ] 所有依赖包已安装
- [ ] 配置文件已正确设置
- [ ] 单元测试全部通过
- [ ] 集成测试全部通过
- [ ] 性能基准测试通过
- [ ] 内存使用量在可接受范围内

### 部署后验证
- [ ] 系统能正常启动
- [ ] 特征提取器正常工作
- [ ] 分类器训练成功
- [ ] 实时预测响应时间 < 32ms
- [ ] 分类精度达到预期
- [ ] 日志记录正常

### 监控指标
- [ ] 预测时间监控
- [ ] 分类精度监控
- [ ] 系统稳定性监控
- [ ] 内存使用监控
- [ ] 错误率监控

---

## 📞 技术支持

### 联系方式
- **开发团队**: 脑机接口开发组
- **技术文档**: `docs/` 目录
- **问题反馈**: 项目Issue跟踪系统

### 更新日志
- **v1.0**: 初始版本，基础投票功能
- **v1.1**: 添加权重优化
- **v1.2**: 性能监控和详细预测
- **v1.3**: 配置文件支持和测试完善

---

**🚀 准备开始实施时，请按照上述步骤逐步进行，每个阶段完成后进行测试验证。如有问题，请参考故障排除指南或联系技术支持。**
