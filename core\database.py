# -*- coding: utf-8 -*-
"""
数据库连接管理器
Database Connection Manager

管理SQLite数据库连接、事务处理和基础操作
"""

import sqlite3
import os
from typing import Optional, List, Dict, Any, Tuple
from contextlib import contextmanager
import threading
from datetime import datetime
from utils.path_manager import get_database_path

class DatabaseManager:
    """数据库连接管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.db_path = str(get_database_path())
            self.initialized = True
            self._local = threading.local()
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接（线程安全）"""
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
            
            self._local.connection = sqlite3.connect(self.db_path)
            self._local.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
        
        return self._local.connection
    
    def close_connection(self):
        """关闭当前线程的数据库连接"""
        if hasattr(self._local, 'connection') and self._local.connection:
            self._local.connection.close()
            self._local.connection = None
    
    @contextmanager
    def get_cursor(self):
        """获取数据库游标的上下文管理器"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        conn = self.get_connection()
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
    
    def execute_query(self, sql: str, params: Tuple = ()) -> List[sqlite3.Row]:
        """执行查询语句"""
        with self.get_cursor() as cursor:
            cursor.execute(sql, params)
            return cursor.fetchall()
    
    def execute_one(self, sql: str, params: Tuple = ()) -> Optional[sqlite3.Row]:
        """执行查询语句，返回单条记录"""
        with self.get_cursor() as cursor:
            cursor.execute(sql, params)
            return cursor.fetchone()
    
    def execute_update(self, sql: str, params: Tuple = ()) -> int:
        """执行更新语句，返回影响的行数"""
        with self.transaction() as conn:
            cursor = conn.cursor()
            cursor.execute(sql, params)
            return cursor.rowcount
    
    def execute_insert(self, sql: str, params: Tuple = ()) -> int:
        """执行插入语句，返回新记录的ID"""
        with self.transaction() as conn:
            cursor = conn.cursor()
            cursor.execute(sql, params)
            return cursor.lastrowid
    
    def execute_batch(self, sql: str, params_list: List[Tuple]) -> int:
        """批量执行语句"""
        with self.transaction() as conn:
            cursor = conn.cursor()
            cursor.executemany(sql, params_list)
            return cursor.rowcount
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.execute_one(sql, (table_name,))
        return result is not None
    
    def get_table_info(self, table_name: str) -> List[sqlite3.Row]:
        """获取表结构信息"""
        sql = f"PRAGMA table_info({table_name})"
        return self.execute_query(sql)
    
    def get_record_count(self, table_name: str, where_clause: str = "", params: Tuple = ()) -> int:
        """获取表记录数"""
        sql = f"SELECT COUNT(*) as count FROM {table_name}"
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        result = self.execute_one(sql, params)
        return result['count'] if result else 0

# 全局数据库管理器实例
db_manager = DatabaseManager()
