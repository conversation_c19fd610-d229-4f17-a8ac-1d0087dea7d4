# -*- coding: utf-8 -*-
"""
脑机接口康复训练系统 - 主入口
Brain-Computer Interface Rehabilitation Training System

基于HTML设计的PySide6实现
完全按照HTML界面风格和样式实现医疗器械级别的脑机接口康复训练系统
"""


import sys
import os
import warnings
from pathlib import Path

# 修复Qt平台插件问题
def fix_qt_platform():
    """修复Qt平台插件路径问题"""
    try:
        import PySide6
        pyside6_dir = Path(PySide6.__file__).parent
        plugins_dir = pyside6_dir / "plugins"
        if plugins_dir.exists():
            os.environ['QT_PLUGIN_PATH'] = str(plugins_dir)
            # print(f"✅ 设置QT_PLUGIN_PATH: {plugins_dir}")
    except Exception as e:
        print(f"⚠️ Qt插件路径设置失败: {e}")

# 在导入任何Qt模块之前修复Qt环境
fix_qt_platform()

# 抑制libpng的sRGB警告和其他Qt警告
os.environ['QT_LOGGING_RULES'] = 'qt.qpa.xcb.warning=false;*.debug=false'

# 配置警告处理 - 解决numpy 2.0+与PyQtGraph的兼容性问题
# 使用专门的警告管理器统一处理所有警告
try:
    from utils.warning_manager import configure_warnings
    import os

    # 检查是否在开发环境（可通过环境变量控制）
    debug_mode = os.getenv('BCI_DEBUG', '').lower() in ('1', 'true', 'yes', 'on')
    configure_warnings(debug_mode=debug_mode)

    if debug_mode:
        # print("🔧 警告管理器已启用（调试模式）")
        pass
    else:
        # print("🔧 警告管理器已启用（生产模式）")
        pass

except ImportError as e:
    # 如果警告管理器不可用，使用基础配置
    print(f"⚠️ 警告管理器导入失败，使用基础配置: {e}")
    import numpy as np
    np.seterr(over='ignore', invalid='ignore')
    warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*overflow encountered in cast.*")
    warnings.filterwarnings("ignore", category=RuntimeWarning, module="pyqtgraph.*")

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


# 导入Qt模块
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# 导入应用模块
from app.application import BCIApplication


def setup_application():
    """设置应用程序"""
    # 设置高DPI支持（移除弃用的属性）
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )

    # 设置默认字体 - 进一步调整以匹配HTML视觉效果
    font = QFont("Microsoft YaHei", 7)
    font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
    # font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)  # 启用抗锯齿
    QApplication.setFont(font)


def main():
    """主函数"""
    try:
       

        # 设置应用程序
        setup_application()
        
        # 创建应用实例
        app = BCIApplication(sys.argv)
        
        # 启动应用
        return app.run()
        
    except Exception as e:
        # 处理启动错误
        QMessageBox.critical(
            None,
            "启动失败",
            f"应用启动时发生错误：\n{str(e)}\n\n请检查系统环境和依赖项。"
        )
        return 1


if __name__ == "__main__":
    # 设置异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        import traceback
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        print(f"未处理的异常：\n{error_msg}")
        
        # 在GUI环境中显示错误
        try:
            QMessageBox.critical(
                None,
                "系统错误",
                f"发生未处理的异常：\n{exc_type.__name__}: {exc_value}\n\n详细信息请查看控制台输出。"
            )
        except:
            pass
    
    sys.excepthook = handle_exception
    
    # 运行应用
    exit_code = main()
    sys.exit(exit_code)
