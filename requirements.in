# 脑机接口康复训练系统 - Python 3.13.5 依赖配置
# 这是输入文件，pip-tools将基于此生成精确版本的requirements.txt

# 核心科学计算库
numpy>=2.1.0,<2.4.0
scipy>=1.14.0,<1.15.0
pandas>=2.3.0,<2.4.0
scikit-learn>=1.5.0,<1.6.0

# GUI框架 - 使用经过验证的稳定版本组合
PySide6>=6.6.0,<6.9.0

# 可视化库
matplotlib>=3.9.0,<4.0.0
pyqtgraph>=0.13.7

# 脑电信号处理 - 更新为支持Python 3.13的版本
mne>=1.9.0
pyriemann>=0.7,<0.9

# 网络通信
requests>=2.32.0,<3.0.0

# 硬件通信
bleak>=0.22.0,<1.0.0
pyserial>=3.5,<4.0

# 语音合成
pyttsx3>=2.90,<3.0

# 安全加密
cryptography>=43.0.0,<44.0.0

# 数据格式处理
Pillow>=10.4.0,<11.0.0
python-dateutil>=2.9.0,<3.0.0

# 开发和打包工具
pyinstaller>=6.5.0
