# -*- coding: utf-8 -*-
"""
状态指示器组件
Status Indicator Component

完全按照HTML设计实现的状态指示器
包含圆点状态指示器、文字状态显示
"""

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QLabel, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QPainter, QColor, QPen


class StatusIndicator(QWidget):
    """状态指示器组件 - 完全按照HTML设计实现"""
    
    # 状态变化信号
    status_changed = Signal(str)
    
    def __init__(self, 
                 label: str = "",
                 initial_status: str = "disconnected",
                 show_text: bool = True,
                 parent=None):
        super().__init__(parent)
        
        self.label_text = label
        self.current_status = initial_status
        self.show_text = show_text
        
        # 状态配置
        self.status_config = {
            "connected": {
                "color": "#10b981",  # success-color
                "text": "已连接",
                "glow": True
            },
            "disconnected": {
                "color": "#64748b",  # text-tertiary
                "text": "未连接",
                "glow": False
            },
            "connecting": {
                "color": "#f59e0b",  # warning-color
                "text": "连接中",
                "glow": True
            },
            "error": {
                "color": "#ef4444",  # danger-color
                "text": "连接失败",
                "glow": True
            },
            "active": {
                "color": "#06b6d4",  # primary-color
                "text": "运行中",
                "glow": True
            },
            "standby": {
                "color": "#10b981",  # success-color
                "text": "待机",
                "glow": False
            }
        }
        
        # UI组件
        self.dot_widget = None
        self.text_label = None
        
        # 动画
        self.glow_animation = None
        self.glow_timer = None
        
        # 设置组件属性
        self.setObjectName("status_indicator")
        
        # 初始化UI
        self._init_ui()
        self._update_status()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 状态圆点
        self.dot_widget = StatusDot()
        self.dot_widget.setFixedSize(12, 12)
        layout.addWidget(self.dot_widget)
        
        # 状态文字（如果显示）
        if self.show_text:
            self.text_label = QLabel()
            self.text_label.setObjectName("status_text")
            self.text_label.setFont(QFont("Microsoft YaHei", 14))
            layout.addWidget(self.text_label)
        
        # 如果有标签，添加到最后
        if self.label_text:
            label = QLabel(self.label_text)
            label.setObjectName("status_label")
            label.setFont(QFont("Microsoft YaHei", 14))
            layout.addWidget(label)
            layout.addStretch()
    
    def _update_status(self):
        """更新状态显示"""
        config = self.status_config.get(self.current_status, self.status_config["disconnected"])
        
        # 更新圆点颜色
        if self.dot_widget:
            self.dot_widget.set_color(config["color"])
            self.dot_widget.set_glow(config["glow"])
        
        # 更新文字
        if self.text_label:
            self.text_label.setText(config["text"])
        
        # 启动或停止发光动画
        if config["glow"]:
            self._start_glow_animation()
        else:
            self._stop_glow_animation()
    
    def _start_glow_animation(self):
        """启动发光动画"""
        if self.dot_widget:
            self.dot_widget.start_glow_animation()
    
    def _stop_glow_animation(self):
        """停止发光动画"""
        if self.dot_widget:
            self.dot_widget.stop_glow_animation()
    
    def set_status(self, status: str):
        """设置状态"""
        if status != self.current_status and status in self.status_config:
            self.current_status = status
            self._update_status()
            self.status_changed.emit(status)
    
    def get_status(self) -> str:
        """获取当前状态"""
        return self.current_status
    
    def add_custom_status(self, status: str, color: str, text: str, glow: bool = False):
        """添加自定义状态"""
        self.status_config[status] = {
            "color": color,
            "text": text,
            "glow": glow
        }


class StatusDot(QWidget):
    """状态圆点组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        self.color = "#64748b"  # 默认颜色
        self.glow_enabled = False
        self._glow_opacity = 0.0
        
        # 发光动画（暂时禁用）
        self.glow_animation = None
        # self.glow_animation = QPropertyAnimation(self, b"glowOpacity")
        # self.glow_animation.setDuration(1000)
        # self.glow_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        # self.glow_animation.finished.connect(self._on_glow_finished)
        
        # 设置大小策略
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
    
    def set_color(self, color: str):
        """设置颜色"""
        self.color = color
        self.update()
    
    def set_glow(self, enabled: bool):
        """设置发光效果"""
        self.glow_enabled = enabled
        if not enabled:
            self.stop_glow_animation()
    
    def start_glow_animation(self):
        """启动发光动画"""
        if self.glow_enabled and self.glow_animation:
            self.glow_animation.setStartValue(0.0)
            self.glow_animation.setEndValue(1.0)
            self.glow_animation.start()
    
    def stop_glow_animation(self):
        """停止发光动画"""
        if self.glow_animation:
            self.glow_animation.stop()
        self._glow_opacity = 0.0
        self.update()
    
    def _on_glow_finished(self):
        """发光动画完成"""
        if self.glow_enabled:
            # 反向播放
            if self._glow_opacity >= 1.0:
                self.glow_animation.setStartValue(1.0)
                self.glow_animation.setEndValue(0.0)
            else:
                self.glow_animation.setStartValue(0.0)
                self.glow_animation.setEndValue(1.0)
            self.glow_animation.start()
    
    def get_glow_opacity(self):
        """获取发光透明度"""
        return self._glow_opacity

    def set_glow_opacity(self, opacity):
        """设置发光透明度"""
        self._glow_opacity = opacity
        self.update()
    
    # 属性定义（用于动画）
    glowOpacity = property(get_glow_opacity, set_glow_opacity)
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 获取绘制区域
        rect = self.rect()
        center_x = rect.width() // 2
        center_y = rect.height() // 2
        radius = min(rect.width(), rect.height()) // 2 - 1
        
        # 绘制发光效果
        if self.glow_enabled and self._glow_opacity > 0:
            glow_color = QColor(self.color)
            glow_color.setAlphaF(self._glow_opacity * 0.6)
            
            painter.setPen(QPen(glow_color, 3))
            painter.setBrush(glow_color)
            painter.drawEllipse(center_x - radius - 1, center_y - radius - 1, 
                              (radius + 1) * 2, (radius + 1) * 2)
        
        # 绘制主圆点
        color = QColor(self.color)
        painter.setPen(QPen(color, 1))
        painter.setBrush(color)
        painter.drawEllipse(center_x - radius, center_y - radius, radius * 2, radius * 2)


class ConnectionStatus(StatusIndicator):
    """连接状态指示器 - 专用于设备连接状态"""
    
    def __init__(self, device_name: str = "设备", parent=None):
        super().__init__(label="", initial_status="disconnected", show_text=True, parent=parent)
        
        self.device_name = device_name
        
        # 更新状态文字
        self.status_config.update({
            "connected": {
                "color": "#10b981",
                "text": "已连接",
                "glow": True
            },
            "disconnected": {
                "color": "#64748b",
                "text": "未连接",
                "glow": False
            },
            "connecting": {
                "color": "#f59e0b",
                "text": "连接中...",
                "glow": True
            }
        })
        
        self._update_status()
    
    def connect_device(self):
        """连接设备"""
        self.set_status("connecting")
        
        # 模拟连接过程
        QTimer.singleShot(2000, lambda: self.set_status("connected"))
    
    def disconnect_device(self):
        """断开设备"""
        self.set_status("disconnected")
