# -*- coding: utf-8 -*-
"""
统一定时器管理器
Unified Timer Manager

统一管理治疗过程中的所有定时器，避免定时器冲突和资源竞争
解决性能问题的核心组件

作者: AI Assistant
创建时间: 2025-01-18
"""

from PySide6.QtCore import QObject, QTimer, Signal
from typing import Optional, Callable
import logging


class UnifiedTimerManager(QObject):
    """统一定时器管理器
    
    使用单一主定时器（100ms间隔）来协调所有定时任务，
    避免多个定时器同时运行造成的资源竞争和性能问题。
    """
    
    # 定时器事件信号
    classification_tick = Signal()  # 每500ms触发（分类）
    monitoring_tick = Signal()      # 每1000ms触发（监测）
    countdown_tick = Signal()       # 每1000ms触发（倒计时）
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 主定时器：100ms间隔作为基准时钟
        self.master_timer = QTimer(self)
        self.master_timer.timeout.connect(self._on_master_tick)
        
        # 计数器
        self.tick_count = 0
        self.is_running = False
        
        # 回调函数注册（可选，也可以使用信号）
        self.classification_callback: Optional[Callable] = None
        self.monitoring_callback: Optional[Callable] = None
        self.countdown_callback: Optional[Callable] = None
        
        # 性能统计
        self.stats = {
            'total_ticks': 0,
            'classification_calls': 0,
            'monitoring_calls': 0,
            'countdown_calls': 0
        }
        
        self.logger.info("统一定时器管理器初始化完成")
    
    def start(self):
        """启动统一定时器管理器"""
        if not self.is_running:
            self.master_timer.start(100)  # 100ms间隔
            self.is_running = True
            self.tick_count = 0
            self.logger.info("统一定时器管理器已启动（100ms基准时钟）")
    
    def stop(self):
        """停止统一定时器管理器"""
        if self.is_running:
            self.master_timer.stop()
            self.is_running = False
            self.logger.info("统一定时器管理器已停止")
            self._log_performance_stats()
    
    def _on_master_tick(self):
        """主定时器回调 - 100ms间隔"""
        self.tick_count += 1
        self.stats['total_ticks'] += 1
        
        # 每500ms执行分类（5个tick = 500ms）
        if self.tick_count % 5 == 0:
            self.classification_tick.emit()
            if self.classification_callback:
                self.classification_callback()
            self.stats['classification_calls'] += 1
        
        # 每1000ms执行监测和倒计时（10个tick = 1000ms）
        if self.tick_count % 10 == 0:
            self.monitoring_tick.emit()
            self.countdown_tick.emit()
            
            if self.monitoring_callback:
                self.monitoring_callback()
            if self.countdown_callback:
                self.countdown_callback()
                
            self.stats['monitoring_calls'] += 1
            self.stats['countdown_calls'] += 1
        
        # 防止计数器溢出（每100秒重置）
        if self.tick_count >= 1000:
            self.tick_count = 0
    
    def register_classification_callback(self, callback: Callable):
        """注册分类回调函数（500ms间隔）"""
        self.classification_callback = callback
        self.logger.debug("分类回调函数已注册")
    
    def register_monitoring_callback(self, callback: Callable):
        """注册监测回调函数（1000ms间隔）"""
        self.monitoring_callback = callback
        self.logger.debug("监测回调函数已注册")
    
    def register_countdown_callback(self, callback: Callable):
        """注册倒计时回调函数（1000ms间隔）"""
        self.countdown_callback = callback
        self.logger.debug("倒计时回调函数已注册")
    
    def unregister_all_callbacks(self):
        """注销所有回调函数"""
        self.classification_callback = None
        self.monitoring_callback = None
        self.countdown_callback = None
        self.logger.debug("所有回调函数已注销")
    
    def get_performance_stats(self) -> dict:
        """获取性能统计信息"""
        if self.stats['total_ticks'] > 0:
            uptime_seconds = self.stats['total_ticks'] * 0.1  # 100ms per tick
            return {
                **self.stats,
                'uptime_seconds': uptime_seconds,
                'classification_frequency': self.stats['classification_calls'] / uptime_seconds if uptime_seconds > 0 else 0,
                'monitoring_frequency': self.stats['monitoring_calls'] / uptime_seconds if uptime_seconds > 0 else 0
            }
        return self.stats
    
    def _log_performance_stats(self):
        """记录性能统计信息"""
        stats = self.get_performance_stats()
        self.logger.info(f"定时器性能统计: {stats}")
    
    def is_timer_running(self) -> bool:
        """检查定时器是否正在运行"""
        return self.is_running and self.master_timer.isActive()