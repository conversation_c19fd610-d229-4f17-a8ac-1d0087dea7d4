# -*- coding: utf-8 -*-
"""
报告分析服务
Report Analysis Service

提供报告分析页面的数据聚合和统计功能
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from core.database import db_manager
from services.treatment_service import treatment_service
from services.patient_service import patient_service


class ReportService:
    """报告分析服务类"""
    
    def __init__(self):
        self.patient_table = "bingren"
        self.treatment_table = "zhiliao"
        self.doctor_table = "doctor"
        # 注意：医院信息现在从配置文件获取，不再使用数据库表
    
    def get_overview_statistics(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        获取概览统计数据
        
        Args:
            filters: 筛选条件字典
            
        Returns:
            概览统计数据
        """
        try:
            # 解析筛选条件
            parsed_filters = self._parse_filters(filters)

            # 获取基础统计
            treatment_stats = treatment_service.get_treatment_statistics(
                filters=parsed_filters
            )
            
            # 获取活跃患者数（最近30天有治疗记录的患者）
            active_patients = self._get_active_patients_count(30)
            
            # 获取设备使用率（假设每天最多8小时使用）
            device_usage_rate = self._calculate_device_usage_rate(
                parsed_filters.get('start_date'),
                parsed_filters.get('end_date')
            )
            
            return {
                'total_patients': treatment_stats['basic_stats']['total_patients'],
                'total_treatments': treatment_stats['basic_stats']['total_treatments'],
                'avg_success_rate': treatment_stats['basic_stats']['avg_score'],
                'active_patients': active_patients,
                'total_duration_hours': round(treatment_stats['basic_stats']['total_duration'] / 60, 1),
                'device_usage_rate': device_usage_rate,
                'date_range': {
                    'start': treatment_stats['basic_stats']['first_date'],
                    'end': treatment_stats['basic_stats']['last_date']
                }
            }
        except Exception as e:
            print(f"获取概览统计失败: {e}")
            return self._get_empty_overview_stats()
    
    def get_trend_analysis_data(self, filters: Dict[str, Any] = None, 
                              period: str = 'daily') -> Dict[str, Any]:
        """
        获取趋势分析数据
        
        Args:
            filters: 筛选条件
            period: 统计周期 ('daily', 'weekly', 'monthly')
            
        Returns:
            趋势分析数据
        """
        try:
            parsed_filters = self._parse_filters(filters)

            # 获取基础趋势数据
            treatment_stats = treatment_service.get_treatment_statistics(
                filters=parsed_filters
            )
            
            daily_trends = treatment_stats['daily_trends']
            
            # 根据周期聚合数据
            if period == 'weekly':
                aggregated_trends = self._aggregate_by_week(daily_trends)
            elif period == 'monthly':
                aggregated_trends = self._aggregate_by_month(daily_trends)
            else:
                aggregated_trends = daily_trends
            
            return {
                'trends': aggregated_trends,
                'period': period,
                'summary': {
                    'total_points': len(aggregated_trends),
                    'avg_treatments_per_period': sum(t['treatment_count'] for t in aggregated_trends) / max(len(aggregated_trends), 1),
                    'avg_score': sum(t['avg_score'] for t in aggregated_trends) / max(len(aggregated_trends), 1)
                }
            }
        except Exception as e:
            print(f"获取趋势分析数据失败: {e}")
            return {'trends': [], 'period': period, 'summary': {}}
    
    def get_distribution_analysis_data(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        获取分布分析数据
        
        Args:
            filters: 筛选条件
            
        Returns:
            分布分析数据
        """
        try:
            parsed_filters = self._parse_filters(filters)

            # 获取治疗效果分布
            treatment_stats = treatment_service.get_treatment_statistics(
                filters=parsed_filters
            )
            
            effect_distribution = treatment_stats['effect_distribution']
            
            # 获取患者年龄分布
            age_distribution = self._get_age_distribution(parsed_filters)

            # 获取性别分布
            gender_distribution = self._get_gender_distribution(parsed_filters)
            
            return {
                'effect_distribution': effect_distribution,
                'age_distribution': age_distribution,
                'gender_distribution': gender_distribution
            }
        except Exception as e:
            print(f"获取分布分析数据失败: {e}")
            return {
                'effect_distribution': [],
                'age_distribution': [],
                'gender_distribution': []
            }
    
    def get_comparison_analysis_data(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        获取对比分析数据
        
        Args:
            filters: 筛选条件
            
        Returns:
            对比分析数据
        """
        try:
            parsed_filters = self._parse_filters(filters)
            start_date = parsed_filters.get('start_date')
            end_date = parsed_filters.get('end_date')

            # 获取设备使用对比（如果有多台设备）
            device_performance = self._get_device_performance_stats(start_date, end_date)

            return {
                'device_performance': device_performance
            }
        except Exception as e:
            print(f"获取对比分析数据失败: {e}")
            return {
                'device_performance': []
            }
    
    def get_patient_report_data(self, patient_id: str, 
                              start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        获取单个患者的详细报告数据
        
        Args:
            patient_id: 患者ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            患者报告数据
        """
        try:
            # 获取患者基本信息
            patient_info = patient_service.get_patient_by_id(patient_id)
            if not patient_info:
                return {}
            
            # 获取患者治疗统计
            patient_stats = treatment_service.get_patient_statistics(patient_id)
            
            # 获取患者治疗记录
            treatments = treatment_service.get_treatments_by_patient(patient_id, "全部记录")
            
            # 按日期筛选治疗记录
            if start_date or end_date:
                filtered_treatments = []
                for treatment in treatments:
                    treatment_date = treatment.get('date', '')
                    if start_date and treatment_date < start_date:
                        continue
                    if end_date and treatment_date > end_date:
                        continue
                    filtered_treatments.append(treatment)
                treatments = filtered_treatments
            
            # 计算康复进度
            recovery_progress = self._calculate_recovery_progress(treatments)
            
            # 生成治疗建议
            treatment_recommendations = self._generate_treatment_recommendations(patient_info, treatments)
            
            return {
                'patient_info': patient_info,
                'statistics': patient_stats,
                'treatments': treatments,
                'recovery_progress': recovery_progress,
                'recommendations': treatment_recommendations,
                'report_generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            print(f"获取患者报告数据失败: {e}")
            return {}
    
    def _parse_filters(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """解析筛选条件"""
        if not filters:
            return {}

        parsed_filters = {
            'start_date': filters.get('start_date'),
            'end_date': filters.get('end_date'),
            'patient_search': filters.get('patient_search', '').strip(),
            'age_range': filters.get('age_range', '全部年龄'),
            'gender': filters.get('gender', '全部'),
            'effects': filters.get('effects', ['优', '良', '中', '差'])
        }

        return parsed_filters
    
    def _get_active_patients_count(self, days: int = 30) -> int:
        """获取活跃患者数量"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            sql = f"""
                SELECT COUNT(DISTINCT bianh) as count 
                FROM {self.treatment_table} 
                WHERE rq >= ?
            """
            result = db_manager.execute_one(sql, (cutoff_date,))
            return result['count'] if result else 0
        except Exception as e:
            print(f"获取活跃患者数量失败: {e}")
            return 0
    
    def _calculate_device_usage_rate(self, start_date: str = None, end_date: str = None) -> float:
        """计算设备使用率"""
        try:
            # 简化计算：假设每天最多使用8小时，计算实际使用时长占比
            where_conditions = []
            params = []
            
            if start_date:
                where_conditions.append("rq >= ?")
                params.append(start_date)
            
            if end_date:
                where_conditions.append("rq <= ?")
                params.append(end_date)
            
            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            sql = f"""
                SELECT 
                    COUNT(DISTINCT rq) as treatment_days,
                    SUM(shijian) as total_minutes
                FROM {self.treatment_table}
                {where_clause}
            """
            
            result = db_manager.execute_one(sql, tuple(params))
            if result and result['treatment_days'] > 0:
                total_minutes = result['total_minutes'] or 0
                treatment_days = result['treatment_days']
                max_minutes_per_day = 8 * 60  # 8小时
                usage_rate = (total_minutes / (treatment_days * max_minutes_per_day)) * 100
                return min(round(usage_rate, 1), 100.0)  # 最大100%
            
            return 0.0
        except Exception as e:
            print(f"计算设备使用率失败: {e}")
            return 0.0
    
    def _get_empty_overview_stats(self) -> Dict[str, Any]:
        """获取空的概览统计数据"""
        return {
            'total_patients': 0,
            'total_treatments': 0,
            'avg_success_rate': 0.0,
            'active_patients': 0,
            'total_duration_hours': 0.0,
            'device_usage_rate': 0.0,
            'date_range': {'start': '', 'end': ''}
        }


    def _aggregate_by_week(self, daily_trends: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按周聚合数据"""
        weekly_data = {}
        for trend in daily_trends:
            date_obj = datetime.strptime(trend['date'], '%Y-%m-%d')
            week_start = date_obj - timedelta(days=date_obj.weekday())
            week_key = week_start.strftime('%Y-%m-%d')

            if week_key not in weekly_data:
                weekly_data[week_key] = {
                    'date': week_key,
                    'treatment_count': 0,
                    'avg_score': 0,
                    'patient_count': 0,
                    'score_sum': 0,
                    'score_count': 0
                }

            weekly_data[week_key]['treatment_count'] += trend['treatment_count']
            weekly_data[week_key]['patient_count'] += trend['patient_count']
            weekly_data[week_key]['score_sum'] += trend['avg_score'] * trend['treatment_count']
            weekly_data[week_key]['score_count'] += trend['treatment_count']

        # 计算平均分数
        for week_data in weekly_data.values():
            if week_data['score_count'] > 0:
                week_data['avg_score'] = round(week_data['score_sum'] / week_data['score_count'], 1)
            del week_data['score_sum']
            del week_data['score_count']

        return sorted(weekly_data.values(), key=lambda x: x['date'])

    def _aggregate_by_month(self, daily_trends: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按月聚合数据"""
        monthly_data = {}
        for trend in daily_trends:
            date_obj = datetime.strptime(trend['date'], '%Y-%m-%d')
            month_key = date_obj.strftime('%Y-%m')

            if month_key not in monthly_data:
                monthly_data[month_key] = {
                    'date': month_key,
                    'treatment_count': 0,
                    'avg_score': 0,
                    'patient_count': 0,
                    'score_sum': 0,
                    'score_count': 0
                }

            monthly_data[month_key]['treatment_count'] += trend['treatment_count']
            monthly_data[month_key]['patient_count'] += trend['patient_count']
            monthly_data[month_key]['score_sum'] += trend['avg_score'] * trend['treatment_count']
            monthly_data[month_key]['score_count'] += trend['treatment_count']

        # 计算平均分数
        for month_data in monthly_data.values():
            if month_data['score_count'] > 0:
                month_data['avg_score'] = round(month_data['score_sum'] / month_data['score_count'], 1)
            del month_data['score_sum']
            del month_data['score_count']

        return sorted(monthly_data.values(), key=lambda x: x['date'])

    def _get_age_distribution(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取年龄分布"""
        try:
            where_conditions = []
            params = []

            if filters:
                patient_search = filters.get('patient_search', '')
                gender = filters.get('gender', '全部')

                # 患者搜索筛选
                if patient_search:
                    where_conditions.append("(name LIKE ? OR bianhao LIKE ?)")
                    params.extend([f"%{patient_search}%", f"%{patient_search}%"])

                # 性别筛选
                if gender != '全部':
                    where_conditions.append("xingbie = ?")
                    params.append(gender)

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            sql = f"""
                SELECT
                    CASE
                        WHEN age < 18 THEN '未成年'
                        WHEN age BETWEEN 18 AND 30 THEN '18-30岁'
                        WHEN age BETWEEN 31 AND 50 THEN '31-50岁'
                        WHEN age BETWEEN 51 AND 70 THEN '51-70岁'
                        WHEN age > 70 THEN '70岁以上'
                        ELSE '未知'
                    END as age_group,
                    COUNT(*) as count
                FROM {self.patient_table}
                {where_clause}
                GROUP BY age_group
                ORDER BY
                    CASE age_group
                        WHEN '未成年' THEN 1
                        WHEN '18-30岁' THEN 2
                        WHEN '31-50岁' THEN 3
                        WHEN '51-70岁' THEN 4
                        WHEN '70岁以上' THEN 5
                        ELSE 6
                    END
            """

            rows = db_manager.execute_query(sql, tuple(params))
            return [{'age_group': row['age_group'], 'count': row['count']} for row in rows]
        except Exception as e:
            print(f"获取年龄分布失败: {e}")
            return []

    def _get_gender_distribution(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取性别分布"""
        try:
            where_conditions = []
            params = []

            if filters:
                patient_search = filters.get('patient_search', '')
                age_range = filters.get('age_range', '全部年龄')

                # 患者搜索筛选
                if patient_search:
                    where_conditions.append("(name LIKE ? OR bianhao LIKE ?)")
                    params.extend([f"%{patient_search}%", f"%{patient_search}%"])

                # 年龄筛选
                if age_range != '全部年龄':
                    if age_range == '未成年 (<18)':
                        where_conditions.append("age < 18")
                    elif age_range == '青年 (18-30)':
                        where_conditions.append("age BETWEEN 18 AND 30")
                    elif age_range == '中年 (31-50)':
                        where_conditions.append("age BETWEEN 31 AND 50")
                    elif age_range == '中老年 (51-70)':
                        where_conditions.append("age BETWEEN 51 AND 70")
                    elif age_range == '老年 (>70)':
                        where_conditions.append("age > 70")

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            sql = f"""
                SELECT
                    xingbie as gender,
                    COUNT(*) as count
                FROM {self.patient_table}
                {where_clause}
                GROUP BY xingbie
                ORDER BY count DESC
            """

            rows = db_manager.execute_query(sql, tuple(params))
            return [{'gender': row['gender'] or '未知', 'count': row['count']} for row in rows]
        except Exception as e:
            print(f"获取性别分布失败: {e}")
            return []





    def _get_device_performance_stats(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取设备使用统计"""
        try:
            where_conditions = []
            params = []

            if start_date:
                where_conditions.append("rq >= ?")
                params.append(start_date)

            if end_date:
                where_conditions.append("rq <= ?")
                params.append(end_date)

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            sql = f"""
                SELECT
                    shebeih as device_id,
                    COUNT(*) as treatment_count,
                    COUNT(DISTINCT bianh) as patient_count,
                    AVG(defen) as avg_score,
                    SUM(shijian) as total_duration
                FROM {self.treatment_table}
                {where_clause}
                GROUP BY shebeih
                HAVING shebeih IS NOT NULL AND shebeih != ''
                ORDER BY treatment_count DESC
            """

            rows = db_manager.execute_query(sql, tuple(params))

            return [
                {
                    'device_id': row['device_id'],
                    'treatment_count': row['treatment_count'],
                    'patient_count': row['patient_count'],
                    'avg_score': round(row['avg_score'] or 0, 1),
                    'total_duration': row['total_duration'] or 0,
                    'avg_duration_per_treatment': round((row['total_duration'] or 0) / max(row['treatment_count'], 1), 1)
                } for row in rows
            ]
        except Exception as e:
            print(f"获取设备使用统计失败: {e}")
            return []

    def _calculate_recovery_progress(self, treatments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算康复进度"""
        try:
            if not treatments:
                return {'progress_trend': [], 'overall_improvement': 0.0, 'latest_score': 0.0}

            # 按日期排序
            sorted_treatments = sorted(treatments, key=lambda x: x.get('date', ''))

            # 计算进度趋势（每5次治疗的平均分数）
            progress_trend = []
            batch_size = 5
            for i in range(0, len(sorted_treatments), batch_size):
                batch = sorted_treatments[i:i+batch_size]
                avg_score = sum(t.get('score', 0) for t in batch) / len(batch)
                progress_trend.append({
                    'period': f"第{i//batch_size + 1}阶段",
                    'avg_score': round(avg_score, 1),
                    'treatment_count': len(batch)
                })

            # 计算总体改善程度
            if len(sorted_treatments) >= 2:
                first_score = sorted_treatments[0].get('score', 0)
                latest_score = sorted_treatments[-1].get('score', 0)
                overall_improvement = latest_score - first_score
            else:
                overall_improvement = 0.0
                latest_score = sorted_treatments[0].get('score', 0) if sorted_treatments else 0.0

            return {
                'progress_trend': progress_trend,
                'overall_improvement': round(overall_improvement, 1),
                'latest_score': latest_score
            }
        except Exception as e:
            print(f"计算康复进度失败: {e}")
            return {'progress_trend': [], 'overall_improvement': 0.0, 'latest_score': 0.0}

    def _generate_treatment_recommendations(self, patient_info: Dict[str, Any],
                                          treatments: List[Dict[str, Any]]) -> List[str]:
        """生成治疗建议"""
        try:
            recommendations = []

            if not treatments:
                recommendations.append("建议开始规律的脑机接口康复训练")
                return recommendations

            # 分析最近的治疗效果
            recent_treatments = sorted(treatments, key=lambda x: x.get('date', ''))[-5:]
            recent_avg_score = sum(t.get('score', 0) for t in recent_treatments) / len(recent_treatments)

            # 根据分数给出建议
            if recent_avg_score >= 80:
                recommendations.append("治疗效果优秀，建议继续保持当前训练强度")
                recommendations.append("可以考虑适当增加训练难度以进一步提升效果")
            elif recent_avg_score >= 60:
                recommendations.append("治疗效果良好，建议保持规律训练")
                recommendations.append("注意训练过程中的专注度，避免外界干扰")
            elif recent_avg_score >= 40:
                recommendations.append("治疗效果一般，建议调整训练参数")
                recommendations.append("可以考虑增加训练频次或延长单次训练时间")
            else:
                recommendations.append("治疗效果需要改善，建议重新评估训练方案")
                recommendations.append("建议与医生讨论是否需要调整治疗策略")

            # 根据治疗频次给出建议
            if len(treatments) < 10:
                recommendations.append("建议增加治疗频次，保持连续性训练")

            # 根据患者年龄给出建议
            age = patient_info.get('age', 0)
            if age > 65:
                recommendations.append("考虑到患者年龄，建议适当延长休息间隔")
            elif age < 30:
                recommendations.append("年轻患者恢复能力强，可以适当增加训练强度")

            return recommendations
        except Exception as e:
            print(f"生成治疗建议失败: {e}")
            return ["请咨询专业医生获取个性化治疗建议"]

    def get_patient_list_for_filter(self, search_term: str = "") -> List[Dict[str, Any]]:
        """获取用于筛选的患者列表"""
        try:
            where_conditions = []
            params = []

            if search_term:
                where_conditions.append("(name LIKE ? OR bianhao LIKE ?)")
                params.extend([f"%{search_term}%", f"%{search_term}%"])

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            sql = f"""
                SELECT bianhao, name, age, xingbie
                FROM {self.patient_table}
                {where_clause}
                ORDER BY name
                LIMIT 50
            """

            rows = db_manager.execute_query(sql, tuple(params))

            return [
                {
                    'id': row['bianhao'],
                    'name': row['name'],
                    'age': row['age'],
                    'gender': row['xingbie']
                } for row in rows
            ]
        except Exception as e:
            print(f"获取患者列表失败: {e}")
            return []

# 全局报告服务实例
report_service = ReportService()
