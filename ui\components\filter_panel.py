# -*- coding: utf-8 -*-
"""
筛选面板组件
Filter Panel Component

用于报告分析页面的数据筛选功能
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QLineEdit, QComboBox, QDateEdit, QPushButton, QCheckBox,
    QScrollArea, QButtonGroup
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont
from datetime import datetime, timedelta
from typing import Dict, Any, List


class FilterPanel(QFrame):
    """筛选面板组件"""
    
    # 筛选条件改变信号
    filters_changed = Signal(dict)  # 传递筛选条件字典
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 筛选条件数据
        self.current_filters = {}
        
        # 设置面板属性
        self.setObjectName("filter_panel")
        self.setFrameStyle(QFrame.Shape.NoFrame)
        self.setFixedWidth(280)
        
        # 初始化UI
        self._init_ui()
        self._setup_default_filters()

        # 设置UI控件的默认值
        self._sync_ui_with_filters()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题
        # title_label = QLabel("筛选条件")
        # title_label.setObjectName("filter_panel_title")
        # title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        # main_layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setObjectName("filter_scroll_area")
        
        # 创建滚动内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(10)
        
        # 添加各个筛选组件
        self._create_time_filter(scroll_layout)
        self._create_patient_filter(scroll_layout)
        self._create_effect_filter(scroll_layout)
        self._create_action_buttons(scroll_layout)
        
        # 添加弹性空间
        scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
    
    def _create_time_filter(self, layout: QVBoxLayout):
        """创建时间筛选组件"""
        # 时间筛选组
        time_group = self._create_filter_group("时间范围")
        time_layout = QVBoxLayout()
        time_layout.setSpacing(8)
        
        # 快速时间选择
        self.time_preset = QComboBox()
        self.time_preset.setObjectName("filter_combo")
        self.time_preset.addItems([
            "全部时间", "今日", "本周", "本月", "本季度", "本年", "自定义"
        ])
        self.time_preset.currentTextChanged.connect(self._on_time_preset_changed)
        time_layout.addWidget(self.time_preset)
        
        # 自定义日期范围
        date_range_widget = QWidget()
        date_range_layout = QVBoxLayout(date_range_widget)
        date_range_layout.setContentsMargins(0, 0, 0, 0)
        date_range_layout.setSpacing(4)
        
        # 开始日期
        start_date_label = QLabel("开始日期:")
        start_date_label.setFont(QFont("Microsoft YaHei", 10))
        date_range_layout.addWidget(start_date_label)
        
        self.start_date = QDateEdit()
        self.start_date.setObjectName("filter_date")
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.dateChanged.connect(self._on_date_changed)
        date_range_layout.addWidget(self.start_date)
        
        # 结束日期
        end_date_label = QLabel("结束日期:")
        end_date_label.setFont(QFont("Microsoft YaHei", 10))
        date_range_layout.addWidget(end_date_label)
        
        self.end_date = QDateEdit()
        self.end_date.setObjectName("filter_date")
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        self.end_date.dateChanged.connect(self._on_date_changed)
        date_range_layout.addWidget(self.end_date)
        
        # 默认隐藏自定义日期范围
        date_range_widget.setVisible(False)
        self.date_range_widget = date_range_widget
        
        time_layout.addWidget(date_range_widget)
        time_group.layout().addLayout(time_layout)
        layout.addWidget(time_group)
    
    def _create_patient_filter(self, layout: QVBoxLayout):
        """创建患者筛选组件"""
        patient_group = self._create_filter_group("患者筛选")
        patient_layout = QVBoxLayout()
        patient_layout.setSpacing(8)
        
        # 患者搜索
        search_label = QLabel("患者姓名/ID:")
        search_label.setFont(QFont("Microsoft YaHei", 10))
        patient_layout.addWidget(search_label)
        
        self.patient_search = QLineEdit()
        self.patient_search.setObjectName("filter_input")
        self.patient_search.setPlaceholderText("输入患者姓名或ID")
        self.patient_search.textChanged.connect(self._on_patient_search_changed)
        patient_layout.addWidget(self.patient_search)
        
        # 年龄范围
        age_label = QLabel("年龄范围:")
        age_label.setFont(QFont("Microsoft YaHei", 10))
        patient_layout.addWidget(age_label)
        
        self.age_range = QComboBox()
        self.age_range.setObjectName("filter_combo")
        self.age_range.addItems([
            "全部年龄", "未成年 (<18)", "青年 (18-30)", 
            "中年 (31-50)", "中老年 (51-70)", "老年 (>70)"
        ])
        self.age_range.currentTextChanged.connect(self._on_age_range_changed)
        patient_layout.addWidget(self.age_range)
        
        # 性别筛选
        gender_label = QLabel("性别:")
        gender_label.setFont(QFont("Microsoft YaHei", 10))
        patient_layout.addWidget(gender_label)
        
        self.gender_filter = QComboBox()
        self.gender_filter.setObjectName("filter_combo")
        self.gender_filter.addItems(["全部", "男", "女"])
        self.gender_filter.currentTextChanged.connect(self._on_gender_changed)
        patient_layout.addWidget(self.gender_filter)
        
        patient_group.layout().addLayout(patient_layout)
        layout.addWidget(patient_group)
    
    def _create_effect_filter(self, layout: QVBoxLayout):
        """创建治疗效果筛选组件"""
        effect_group = self._create_filter_group("治疗效果")
        effect_layout = QVBoxLayout()
        effect_layout.setSpacing(8)
        
        # 治疗效果复选框
        self.effect_checkboxes = {}
        effects = ["优", "良", "中", "差"]
        
        for effect in effects:
            checkbox = QCheckBox(effect)
            checkbox.setObjectName("filter_checkbox")
            checkbox.setFont(QFont("Microsoft YaHei", 10))
            checkbox.setChecked(True)  # 默认全选
            checkbox.stateChanged.connect(self._on_effect_filter_changed)
            effect_layout.addWidget(checkbox)
            self.effect_checkboxes[effect] = checkbox
        
        effect_group.layout().addLayout(effect_layout)
        layout.addWidget(effect_group)
    

    
    def _create_action_buttons(self, layout: QVBoxLayout):
        """创建操作按钮"""
        button_widget = QWidget()
        button_layout = QVBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)
        
        # 应用筛选按钮
        self.apply_btn = QPushButton("应用筛选")
        self.apply_btn.setObjectName("filter_apply_btn")
        self.apply_btn.setFont(QFont("Microsoft YaHei", 12))
        self.apply_btn.clicked.connect(self._apply_filters)
        button_layout.addWidget(self.apply_btn)
        
        # 重置筛选按钮
        self.reset_btn = QPushButton("重置筛选")
        self.reset_btn.setObjectName("filter_reset_btn")
        self.reset_btn.setFont(QFont("Microsoft YaHei", 12))
        self.reset_btn.clicked.connect(self._reset_filters)
        button_layout.addWidget(self.reset_btn)
        
        layout.addWidget(button_widget)
    
    def _create_filter_group(self, title: str) -> QFrame:
        """创建筛选组"""
        group = QFrame()
        group.setObjectName("filter_group")
        
        group_layout = QVBoxLayout(group)
        group_layout.setContentsMargins(12, 12, 12, 12)
        group_layout.setSpacing(8)
        
        # 组标题
        # title_label = QLabel(title)
        # title_label.setObjectName("filter_group_title")
        # title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        # group_layout.addWidget(title_label)
        
        return group
    
    def _setup_default_filters(self):
        """设置默认筛选条件"""
        # 计算真正的本月日期范围（本月1号到当前日期）
        now = datetime.now()
        start_of_month = now.replace(day=1)

        self.current_filters = {
            'time_preset': '本月',
            'start_date': start_of_month.strftime('%Y-%m-%d'),
            'end_date': now.strftime('%Y-%m-%d'),
            'patient_search': '',
            'age_range': '全部年龄',
            'gender': '全部',
            'effects': ['优', '良', '中', '差']
        }

    def _sync_ui_with_filters(self):
        """同步UI控件与筛选条件"""
        # 设置时间预设
        self.time_preset.setCurrentText(self.current_filters.get('time_preset', '本月'))

        # 设置年龄范围
        self.age_range.setCurrentText(self.current_filters.get('age_range', '全部年龄'))

        # 设置性别
        self.gender_filter.setCurrentText(self.current_filters.get('gender', '全部'))

        # 设置治疗效果复选框
        selected_effects = self.current_filters.get('effects', [])
        for effect, checkbox in self.effect_checkboxes.items():
            checkbox.setChecked(effect in selected_effects)

    def _on_time_preset_changed(self, preset: str):
        """时间预设改变"""
        self.current_filters['time_preset'] = preset
        
        if preset == "自定义":
            self.date_range_widget.setVisible(True)
        else:
            self.date_range_widget.setVisible(False)
            
            # 根据预设计算日期范围
            end_date = datetime.now()
            if preset == "今日":
                start_date = end_date
            elif preset == "本周":
                start_date = end_date - timedelta(days=end_date.weekday())
            elif preset == "本月":
                start_date = end_date.replace(day=1)
            elif preset == "本季度":
                quarter_start_month = ((end_date.month - 1) // 3) * 3 + 1
                start_date = end_date.replace(month=quarter_start_month, day=1)
            elif preset == "本年":
                start_date = end_date.replace(month=1, day=1)
            else:  # 全部时间
                start_date = None
                end_date = None
            
            if start_date:
                self.current_filters['start_date'] = start_date.strftime('%Y-%m-%d')
                self.current_filters['end_date'] = end_date.strftime('%Y-%m-%d')
            else:
                self.current_filters['start_date'] = None
                self.current_filters['end_date'] = None
    
    def _on_date_changed(self):
        """自定义日期改变"""
        self.current_filters['start_date'] = self.start_date.date().toString('yyyy-MM-dd')
        self.current_filters['end_date'] = self.end_date.date().toString('yyyy-MM-dd')
    
    def _on_patient_search_changed(self, text: str):
        """患者搜索改变"""
        self.current_filters['patient_search'] = text.strip()
    
    def _on_age_range_changed(self, age_range: str):
        """年龄范围改变"""
        self.current_filters['age_range'] = age_range
    
    def _on_gender_changed(self, gender: str):
        """性别筛选改变"""
        self.current_filters['gender'] = gender
    
    def _on_effect_filter_changed(self):
        """治疗效果筛选改变"""
        selected_effects = []
        for effect, checkbox in self.effect_checkboxes.items():
            if checkbox.isChecked():
                selected_effects.append(effect)
        self.current_filters['effects'] = selected_effects
    
    def _apply_filters(self):
        """应用筛选条件"""
        self.filters_changed.emit(self.current_filters.copy())
    
    def _reset_filters(self):
        """重置筛选条件"""
        # 重置UI控件
        self.time_preset.setCurrentText("本月")
        self.patient_search.clear()
        self.age_range.setCurrentText("全部年龄")
        self.gender_filter.setCurrentText("全部")

        # 重置效果复选框
        for checkbox in self.effect_checkboxes.values():
            checkbox.setChecked(True)

        # 重置筛选条件
        self._setup_default_filters()

        # 发送重置后的筛选条件
        self.filters_changed.emit(self.current_filters.copy())
    
    def get_current_filters(self) -> Dict[str, Any]:
        """获取当前筛选条件"""
        return self.current_filters.copy()
