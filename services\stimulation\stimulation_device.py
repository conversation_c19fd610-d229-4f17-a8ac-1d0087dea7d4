# -*- coding: utf-8 -*-
"""
电刺激设备控制器
Electrical Stimulation Device Controller

基于C2设备DLL的电刺激设备控制实现
"""

import ctypes
import threading
import time
import logging
import json
from pathlib import Path
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass
from enum import Enum

from PySide6.QtCore import QObject, Signal, QTimer


@dataclass
class StimulationParameters:
    """刺激参数数据类"""
    channel_num: int = 1          # 通道号 (1=A通道, 2=B通道)
    frequency: float = 20.0       # 频率 (Hz, 范围: 2-160)
    pulse_width: float = 200.0    # 脉宽 (μs, 范围: 10-500)
    relax_time: float = 5.0       # 休息时间 (s, 范围: 0-16)
    climb_time: float = 2.0       # 上升时间 (s, 范围: 0-5)
    work_time: float = 10.0       # 工作时间 (s, 范围: 0-30)
    fall_time: float = 2.0        # 下降时间 (s, 范围: 0-5)
    wave_type: int = 0            # 波形类型 (0=双相波, 1=单相波)


class StimulationDeviceStatus(Enum):
    """电刺激设备状态枚举"""
    DISCONNECTED = "未连接"
    CONNECTING = "连接中"
    CONNECTED = "已连接"
    STIMULATING = "刺激中"
    ERROR = "错误"


class StimulationDevice(QObject):
    """电刺激设备控制器"""
    
    # Qt信号
    connection_status_changed = Signal(str)  # 连接状态变化
    device_connected = Signal()              # 设备连接成功
    device_disconnected = Signal()           # 设备断开连接
    connection_failed = Signal(str)          # 连接失败
    stimulation_started = Signal(int)        # 刺激开始 (通道号)
    stimulation_stopped = Signal(int)        # 刺激停止 (通道号)

    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化电刺激设备控制器"""
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config = self._load_config(config)
        
        # 设备状态
        self.dll: Optional[ctypes.CDLL] = None
        self.status = StimulationDeviceStatus.DISCONNECTED
        self.callback_function = None
        self.callback_func_type = None
        
        # 通道状态
        self.channel_a_status = 0
        self.channel_b_status = 0
        
        # 当前参数
        self.current_parameters = StimulationParameters()



        self.logger.info("电刺激设备控制器初始化完成")
    
    def _load_config(self, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """加载配置"""
        if config:
            return config
        
        # 从settings.json加载配置
        try:
            from utils.path_manager import get_config_file_in_dir
            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                stimulation_config = settings.get("stimulation", {})
                
                # 构建完整配置
                from utils.path_manager import get_lib_file
                full_config = {
                    'dll_path': get_lib_file("RecoveryDLL.dll"),  # 使用路径管理器
                    'port_num': self._parse_port_number(stimulation_config.get('port', 'COM4')),
                    'max_current': stimulation_config.get('max_current', 50),
                    'min_current': stimulation_config.get('min_current', 1),
                    'current_step': stimulation_config.get('current_step', 1.0),
                    'default_frequency': stimulation_config.get('default_frequency', 20),
                    'default_pulse_width': stimulation_config.get('default_pulse_width', 200),
                    'default_relax_time': stimulation_config.get('default_relax_time', 5.0),
                    'default_climb_time': stimulation_config.get('default_climb_time', 2.0),
                    'default_work_time': stimulation_config.get('default_work_time', 10),
                    'default_fall_time': stimulation_config.get('default_fall_time', 2.0),
                    'default_wave_type': 0 if stimulation_config.get('waveform', '双相波') == '双相波' else 1,
                    'connection_timeout': 5
                }
                
                self.logger.info(f"配置加载成功: 端口={stimulation_config.get('port')}, "
                               f"最大电流={full_config['max_current']}mA")
                return full_config
        
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认配置: {e}")
        
        # 默认配置
        from utils.path_manager import get_lib_file
        return {
            'dll_path': get_lib_file("RecoveryDLL.dll"),
            'port_num': 4,  # COM4
            'max_current': 50,
            'min_current': 1,
            'current_step': 1.0,
            'default_frequency': 20,
            'default_pulse_width': 200,
            'default_relax_time': 5.0,
            'default_climb_time': 2.0,
            'default_work_time': 10,
            'default_fall_time': 2.0,
            'default_wave_type': 0,
            'connection_timeout': 5
        }
    
    def _parse_port_number(self, port_str: str) -> int:
        """解析端口号字符串，如COM4 -> 4"""
        try:
            if port_str.upper().startswith('COM'):
                return int(port_str[3:])
            return int(port_str)
        except (ValueError, IndexError):
            self.logger.warning(f"无法解析端口号: {port_str}，使用默认值4")
            return 4
    
    def load_dll(self) -> bool:
        """加载DLL库"""
        try:
            dll_path = self.config['dll_path']
            if not dll_path.exists():
                self.logger.error(f"DLL文件不存在: {dll_path}")
                return False
            
            # 加载DLL
            self.dll = ctypes.CDLL(str(dll_path))
            
            # 定义函数原型
            self._define_function_prototypes()
            
            self.logger.info(f"成功加载DLL: {dll_path}")
            return True
        
        except Exception as e:
            self.logger.error(f"加载DLL失败: {e}")
            return False
    
    def _define_function_prototypes(self):
        """定义DLL函数原型"""
        try:
            # 定义回调函数类型
            self.callback_func_type = ctypes.WINFUNCTYPE(
                ctypes.c_int,                    # 返回值类型
                ctypes.c_void_p,                 # HANDLE pHandle
                ctypes.POINTER(ctypes.c_short),  # short* lpBuffer (修正为short指针)
                ctypes.c_int                     # int nSize
            )
            
            # 设备连接函数
            self.dll.OpenRecPort.argtypes = [
                ctypes.c_int,           # portNum
                ctypes.c_int,           # iReadSize
                self.callback_func_type, # funDataProc callback
                ctypes.c_void_p         # HANDLE pHandle
            ]
            self.dll.OpenRecPort.restype = ctypes.c_int
            
            self.dll.CloseRecPort.argtypes = []
            self.dll.CloseRecPort.restype = ctypes.c_int
            
            self.dll.IsRecOpen.argtypes = []
            self.dll.IsRecOpen.restype = ctypes.c_bool
            
            # 设备控制函数
            self.dll.SwitchDeviceState.argtypes = [ctypes.c_int]
            self.dll.SwitchDeviceState.restype = ctypes.c_int
            
            self.dll.SwitchChannelState.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.SwitchChannelState.restype = ctypes.c_int
            
            # 参数设置函数
            self.dll.StimPara.argtypes = [
                ctypes.c_int,    # ChanNum
                ctypes.c_double, # ActFreq
                ctypes.c_double, # PulseWidth
                ctypes.c_double, # RelaxTime
                ctypes.c_double, # ClimbTime
                ctypes.c_double, # WorkTime
                ctypes.c_double, # FallTime
                ctypes.c_int     # WaveType
            ]
            self.dll.StimPara.restype = ctypes.c_int
            
            # 电流控制函数
            self.dll.CurrentSet.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.CurrentSet.restype = ctypes.c_int
            
            self.dll.RegulateCurrent.argtypes = [ctypes.c_int, ctypes.c_int, ctypes.c_bool]
            self.dll.RegulateCurrent.restype = ctypes.c_int
            
            self.logger.info("DLL函数原型定义完成")
        
        except Exception as e:
            self.logger.error(f"定义DLL函数原型失败: {e}")
            raise

    def _create_data_callback(self):
        """创建数据回调函数"""
        def data_callback(pHandle, lpBuffer, nSize):
            """数据回调函数 - 处理设备返回的状态数据"""
            try:
                if nSize == 0:
                    return nSize

                if nSize == 6 and lpBuffer:
                    # 6个short值的数据包格式：
                    # [设备标识, 设备状态, A通道状态, A进度, B通道状态, B进度]
                    short_array = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_short * 6)).contents

                    # 更新通道状态
                    new_a_status = short_array[2]  # A通道状态
                    new_b_status = short_array[4]  # B通道状态

                    # 检测状态变化
                    if new_a_status != self.channel_a_status:
                        self.logger.debug(f"A通道状态变化: {self.channel_a_status} -> {new_a_status}")
                        self.channel_a_status = new_a_status

                    if new_b_status != self.channel_b_status:
                        self.logger.debug(f"B通道状态变化: {self.channel_b_status} -> {new_b_status}")
                        self.channel_b_status = new_b_status

                return nSize

            except Exception as e:
                self.logger.error(f"回调函数处理数据时发生错误: {e}")
                return nSize

        return self.callback_func_type(data_callback)

    def connect_device(self, port_num: Optional[int] = None) -> bool:
        """连接电刺激设备"""
        try:
            if port_num is None:
                port_num = self.config['port_num']

            self.status = StimulationDeviceStatus.CONNECTING
            self.connection_status_changed.emit("连接中...")
            self.logger.info(f"正在连接电刺激设备，端口: COM{port_num}")

            # 加载DLL
            if not self.dll and not self.load_dll():
                self.status = StimulationDeviceStatus.ERROR
                self.connection_failed.emit("DLL加载失败")
                return False

            # 创建回调函数
            self.callback_function = self._create_data_callback()

            # 连接设备
            result = self.dll.OpenRecPort(port_num, 6, self.callback_function, None)

            if result == 1:  # 连接成功
                self.status = StimulationDeviceStatus.CONNECTED
                self.connection_status_changed.emit("已连接")
                self.device_connected.emit()
                self.logger.info("电刺激设备连接成功")

                # 切换设备到循环刺激状态
                switch_result = self.dll.SwitchDeviceState(1)
                if switch_result == 0:
                    self.logger.info("设备状态切换成功")
                else:
                    self.logger.warning(f"设备状态切换失败，错误码: {switch_result}")

                # 自动下载系统配置的默认参数到设备
                self._download_default_parameters()

                return True

            else:
                self.status = StimulationDeviceStatus.ERROR
                error_msg = f"连接失败，错误码: {result}"
                self.connection_failed.emit(error_msg)
                self.logger.error(error_msg)
                return False

        except Exception as e:
            self.logger.error(f"连接电刺激设备时发生异常: {e}")
            self.status = StimulationDeviceStatus.ERROR
            self.connection_failed.emit(f"连接异常: {str(e)}")
            return False

    def disconnect_device(self) -> bool:
        """断开电刺激设备连接"""
        try:
            self.logger.info("正在断开电刺激设备连接")

            # 停止所有刺激
            if self.is_connected():
                self.stop_all_stimulation()

            # 关闭设备端口
            if self.dll:
                result = self.dll.CloseRecPort()
                if result == 1:  # 1表示成功
                    self.status = StimulationDeviceStatus.DISCONNECTED
                    self.connection_status_changed.emit("未连接")
                    self.device_disconnected.emit()
                    self.logger.info("电刺激设备断开成功")

                    # 清理回调函数引用
                    self.callback_function = None
                    return True
                else:
                    self.logger.error(f"断开电刺激设备失败，错误码: {result}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"断开电刺激设备时发生错误: {e}")
            return False

    def is_connected(self) -> bool:
        """检查设备是否已连接"""
        try:
            if self.dll:
                return self.dll.IsRecOpen()
            return False
        except Exception as e:
            self.logger.error(f"检查设备连接状态失败: {e}")
            return False

    def set_stimulation_parameters(self, params: StimulationParameters) -> bool:
        """设置刺激参数"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法设置参数")
                return False

            # 调用DLL函数设置参数
            result = self.dll.StimPara(
                params.channel_num,
                params.frequency,
                params.pulse_width,
                params.relax_time,
                params.climb_time,
                params.work_time,
                params.fall_time,
                params.wave_type
            )

            if result == 0:
                self.current_parameters = params
                self.logger.info(f"刺激参数设置成功: 通道{params.channel_num}, 频率{params.frequency}Hz")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"设置刺激参数失败，错误码: {result} ({error_msg})")
                return False

        except Exception as e:
            self.logger.error(f"设置刺激参数时发生错误: {e}")
            return False

    def _download_default_parameters(self):
        """下载系统配置的默认参数到设备"""
        try:
            self.logger.info("开始下载系统配置参数到电刺激设备...")

            # 显示当前配置参数
            self.logger.info(f"系统配置参数:")
            self.logger.info(f"  - 波形类型: {'双相波' if self.config['default_wave_type'] == 0 else '单相波'}")
            self.logger.info(f"  - 频率: {self.config['default_frequency']}Hz")
            self.logger.info(f"  - 脉宽: {self.config['default_pulse_width']}μs")
            self.logger.info(f"  - 休息时间: {self.config['default_relax_time']}s")
            self.logger.info(f"  - 上升时间: {self.config['default_climb_time']}s")
            self.logger.info(f"  - 工作时间: {self.config['default_work_time']}s")
            self.logger.info(f"  - 下降时间: {self.config['default_fall_time']}s")

            # 为A通道和B通道分别设置参数
            success_count = 0

            for channel_num in [1, 2]:  # 1=A通道, 2=B通道
                channel_name = "A" if channel_num == 1 else "B"

                # 创建参数对象
                params = StimulationParameters(
                    channel_num=channel_num,
                    frequency=self.config['default_frequency'],
                    pulse_width=self.config['default_pulse_width'],
                    relax_time=self.config['default_relax_time'],
                    climb_time=self.config['default_climb_time'],
                    work_time=self.config['default_work_time'],
                    fall_time=self.config['default_fall_time'],
                    wave_type=self.config['default_wave_type']
                )

                # 下载参数到设备
                if self.set_stimulation_parameters(params):
                    success_count += 1
                    self.logger.info(f"{channel_name}通道参数下载成功")
                else:
                    self.logger.error(f"{channel_name}通道参数下载失败")

            if success_count == 2:
                self.logger.info("所有通道参数下载完成！设备已使用系统配置参数")
            else:
                self.logger.warning(f"参数下载部分成功：{success_count}/2个通道")

        except Exception as e:
            self.logger.error(f"下载默认参数失败: {e}")

    def set_current(self, channel_num: int, current_ma: float) -> bool:
        """设置电流强度（单位：mA）"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法设置电流")
                return False

            # 检查电流范围
            max_current = self.config.get('max_current', 50.0)
            min_current = self.config.get('min_current', 1.0)

            if not (min_current <= current_ma <= max_current):
                self.logger.error(f"电流值超出安全范围: {current_ma}mA, 允许范围: {min_current}-{max_current}mA")
                return False

            self.logger.debug(f"开始设置通道{channel_num}电流: {current_ma}mA")

            # 步骤1：确保设备处于循环刺激状态
            device_result = self.dll.SwitchDeviceState(1)  # 1: 循环刺激
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")

            # 移除time.sleep，使用更快的响应
            # time.sleep(0.1)

            # 步骤2：切换通道到电流调节状态
            switch_result = self.dll.SwitchChannelState(channel_num, 2)  # 2: 电流调节状态
            if switch_result != 0:
                error_msg = self._get_error_message(switch_result)
                self.logger.error(f"切换通道{channel_num}到电流调节状态失败: {error_msg}")
                return False

            # 移除time.sleep，使用更快的响应
            # time.sleep(0.1)

            # 步骤3：设置电流值
            current_value = int(current_ma)  # 直接使用mA值
            result = self.dll.CurrentSet(channel_num, current_value)

            if result == 0:
                self.logger.debug(f"通道 {channel_num} 电流设置成功: {current_ma}mA")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"设置电流失败，错误码: {result} - {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"设置电流时发生错误: {e}")
            return False

    def start_stimulation(self, channel_num: int) -> bool:
        """开始刺激"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法启动刺激")
                return False

            self.logger.info(f"启动通道{channel_num}刺激")

            # 确保设备处于循环刺激状态
            device_result = self.dll.SwitchDeviceState(1)
            if device_result != 0:
                error_msg = self._get_error_message(device_result)
                self.logger.error(f"切换设备到循环刺激状态失败: {error_msg}")
                return False

            # 启动通道
            result = self.dll.SwitchChannelState(channel_num, 3)  # 3: 正常工作
            if result == 0:
                self.status = StimulationDeviceStatus.STIMULATING
                self.stimulation_started.emit(channel_num)
                self.logger.info(f"通道{channel_num}刺激启动成功")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"通道{channel_num}刺激启动失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"启动刺激时发生异常: {e}")
            return False

    def stop_stimulation(self, channel_num: int) -> bool:
        """停止刺激"""
        try:
            if not self.is_connected():
                self.logger.warning("设备未连接，无法停止刺激")
                return False

            self.logger.info(f"停止通道{channel_num}刺激")

            # 停止通道
            result = self.dll.SwitchChannelState(channel_num, 0)  # 0: 停止
            if result == 0:
                self.stimulation_stopped.emit(channel_num)
                self.logger.info(f"通道{channel_num}刺激停止成功")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"停止通道{channel_num}刺激失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"停止刺激时发生异常: {e}")
            return False

    def stop_all_stimulation(self) -> bool:
        """停止所有通道刺激"""
        try:
            success = True

            # 停止A通道
            if not self.stop_stimulation(1):
                success = False

            # 停止B通道
            if not self.stop_stimulation(2):
                success = False

            if success:
                self.status = StimulationDeviceStatus.CONNECTED
                self.logger.info("所有通道刺激已停止")
            else:
                self.logger.warning("部分通道停止失败")

            return success

        except Exception as e:
            self.logger.error(f"停止所有刺激时发生异常: {e}")
            return False



    def get_default_parameters(self) -> StimulationParameters:
        """获取默认刺激参数"""
        return StimulationParameters(
            channel_num=1,
            frequency=self.config['default_frequency'],
            pulse_width=self.config['default_pulse_width'],
            relax_time=self.config['default_relax_time'],
            climb_time=self.config['default_climb_time'],
            work_time=self.config['default_work_time'],
            fall_time=self.config['default_fall_time'],
            wave_type=self.config['default_wave_type']
        )

    def get_status(self) -> StimulationDeviceStatus:
        """获取设备状态"""
        return self.status

    def get_channel_status(self, channel_num: int) -> int:
        """获取通道状态"""
        if channel_num == 1:
            return self.channel_a_status
        elif channel_num == 2:
            return self.channel_b_status
        else:
            return 0

    def _get_error_message(self, error_code: int) -> str:
        """根据错误码获取错误信息"""
        error_messages = {
            0: "成功",
            1: "命令错误",
            2: "命令参数错误",
            3: "校验错误",
            4: "没有读取到硬件的命令应答",
            5: "将命令写入串口时失败"
        }
        return error_messages.get(error_code, f"未知错误码: {error_code}")

    def validate_parameters(self, params: StimulationParameters) -> bool:
        """验证刺激参数"""
        # 频率验证
        if not (2 <= params.frequency <= 160):
            self.logger.error(f"频率超出范围: {params.frequency}Hz (允许范围: 2-160Hz)")
            return False

        # 脉宽验证
        if not (10 <= params.pulse_width <= 500):
            self.logger.error(f"脉宽超出范围: {params.pulse_width}μs (允许范围: 10-500μs)")
            return False

        # 时间参数验证
        if not (0 <= params.relax_time <= 16):
            self.logger.error(f"休息时间超出范围: {params.relax_time}s (允许范围: 0-16s)")
            return False

        if not (0 <= params.climb_time <= 5):
            self.logger.error(f"上升时间超出范围: {params.climb_time}s (允许范围: 0-5s)")
            return False

        if not (0 <= params.work_time <= 30):
            self.logger.error(f"工作时间超出范围: {params.work_time}s (允许范围: 0-30s)")
            return False

        if not (0 <= params.fall_time <= 5):
            self.logger.error(f"下降时间超出范围: {params.fall_time}s (允许范围: 0-5s)")
            return False

        # 通道验证
        if params.channel_num not in [1, 2]:
            self.logger.error(f"无效的通道号: {params.channel_num} (允许值: 1, 2)")
            return False

        # 波形类型验证
        if params.wave_type not in [0, 1]:
            self.logger.error(f"无效的波形类型: {params.wave_type} (允许值: 0=双相波, 1=单相波)")
            return False

        return True

    def fast_dual_channel_start(self, channel_a_current: float = 0, channel_b_current: float = 0) -> bool:
        """快速双通道启动，最小化AB通道启动间隔"""
        try:
            import time

            if not self.is_connected():
                self.logger.error("设备未连接，无法启动双通道刺激")
                return False

            start_time = time.time()
            self.logger.info(f"开始快速双通道启动: A={channel_a_current}mA, B={channel_b_current}mA")

            # 1. 确保设备处于循环刺激状态（只调用一次）
            device_result = self.dll.SwitchDeviceState(1)
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")

            # 2. 批量设置电流（如果需要）
            current_set_time = time.time()
            if channel_a_current > 0:
                self.set_current(1, channel_a_current)
            if channel_b_current > 0:
                self.set_current(2, channel_b_current)
            current_set_elapsed = (time.time() - current_set_time) * 1000

            # 3. 超快速连续启动通道（无延迟批量执行）
            channel_start_time = time.time()
            success_count = 0

            # 批量准备启动命令
            commands_to_execute = []
            if channel_a_current > 0:
                commands_to_execute.append(('A', 1))
            if channel_b_current > 0:
                commands_to_execute.append(('B', 2))

            # 连续执行启动命令，最小化时间间隔
            results = []
            for channel_name, channel_num in commands_to_execute:
                result = self.dll.SwitchChannelState(channel_num, 3)
                elapsed = (time.time() - channel_start_time) * 1000
                results.append((channel_name, channel_num, result, elapsed))

            # 处理结果
            for channel_name, channel_num, result, elapsed in results:
                if result == 0:
                    success_count += 1
                    self.logger.info(f"{channel_name}通道启动成功 (+{elapsed:.1f}ms)")
                    self.stimulation_started.emit(channel_num)
                else:
                    self.logger.error(f"{channel_name}通道启动失败，错误码: {result}")

            # 4. 记录总时间
            total_elapsed = (time.time() - start_time) * 1000
            channel_elapsed = (time.time() - channel_start_time) * 1000

            self.logger.info(f"快速双通道启动完成: 电流设置耗时{current_set_elapsed:.1f}ms, "
                           f"通道启动耗时{channel_elapsed:.1f}ms, 总耗时{total_elapsed:.1f}ms")

            if success_count > 0:
                self.status = StimulationDeviceStatus.STIMULATING
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"快速双通道启动失败: {e}")
            return False
