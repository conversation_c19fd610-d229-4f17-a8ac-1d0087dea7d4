# -*- coding: utf-8 -*-
"""
Static analyzer for project cleanup:
- Build module import graph
- Detect unused imports per file
- Detect potentially unused top-level functions/classes across modules
- Identify modules not reachable from main entry points

Notes:
- Conservative: marks as "suspect" rather than definitive for cross-file defs
- Python 3.8+ AST used; compatible with 3.13
"""
from __future__ import annotations
import os
import sys
import ast
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional

PROJECT_ROOT = Path(__file__).resolve().parents[1]
SRC_DIRS = [PROJECT_ROOT]
EXCLUDE_DIR_NAMES = {"__pycache__", ".git", ".venv", "venv", "env", "data", "icons", "libs", "package_output"}
EXCLUDE_FILE_NAMES = {"__init__.py"}
ENTRY_FILES = [
    PROJECT_ROOT / "main.py",
    PROJECT_ROOT / "build_package.py",
    PROJECT_ROOT / "build_package_backup.py",
]

Report = Dict[str, object]

class ModuleInfo:
    def __init__(self, path: Path, module_name: str):
        self.path = path
        self.module_name = module_name
        self.imports: List[Tuple[str, Optional[str], Optional[str], int]] = []  # (type, module, name, lineno); type in {"import","from"}
        self.import_aliases: Dict[str, str] = {}  # alias -> module
        self.from_imports: List[Tuple[str, str, Optional[str], int]] = []  # (module, name, asname, lineno)
        self.defined_funcs: Dict[str, int] = {}  # name -> lineno
        self.defined_classes: Dict[str, int] = {}  # name -> lineno
        self.names_used: Set[str] = set()
        self.attr_used: List[Tuple[str, str]] = []  # (base_name, attr)
        self.parse_error: Optional[str] = None

class UsageVisitor(ast.NodeVisitor):
    def __init__(self, mi: ModuleInfo):
        self.mi = mi

    def visit_Import(self, node: ast.Import):
        for alias in node.names:
            name = alias.name
            asname = alias.asname or alias.name.split(".")[-1]
            self.mi.imports.append(("import", name, None, node.lineno))
            self.mi.import_aliases[asname] = name
        self.generic_visit(node)

    def visit_ImportFrom(self, node: ast.ImportFrom):
        mod = node.module or ""
        for alias in node.names:
            self.mi.imports.append(("from", mod, alias.name, node.lineno))
            self.mi.from_imports.append((mod, alias.name, alias.asname, node.lineno))
        self.generic_visit(node)

    def visit_FunctionDef(self, node: ast.FunctionDef):
        if isinstance(getattr(node, 'parent', None), ast.Module):
            self.mi.defined_funcs[node.name] = node.lineno
        self.generic_visit(node)

    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef):
        if isinstance(getattr(node, 'parent', None), ast.Module):
            self.mi.defined_funcs[node.name] = node.lineno
        self.generic_visit(node)

    def visit_ClassDef(self, node: ast.ClassDef):
        if isinstance(getattr(node, 'parent', None), ast.Module):
            self.mi.defined_classes[node.name] = node.lineno
        self.generic_visit(node)

    def visit_Name(self, node: ast.Name):
        self.mi.names_used.add(node.id)

    def visit_Attribute(self, node: ast.Attribute):
        base = node.value
        if isinstance(base, ast.Name):
            self.mi.attr_used.append((base.id, node.attr))
        self.generic_visit(node)


def iter_python_files() -> List[Path]:
    files: List[Path] = []
    for base in SRC_DIRS:
        for root, dirs, filenames in os.walk(base):
            # prune excluded dirs
            dirs[:] = [d for d in dirs if d not in EXCLUDE_DIR_NAMES]
            for fn in filenames:
                if not fn.endswith('.py'):
                    continue
                if fn in EXCLUDE_FILE_NAMES:
                    # keep __init__.py for import graph, but analysis of unused imports often irrelevant
                    pass
                files.append(Path(root) / fn)
    return files


def module_name_from_path(p: Path) -> str:
    rel = p.relative_to(PROJECT_ROOT)
    parts = list(rel.parts)
    if parts[-1].endswith('.py'):
        parts[-1] = parts[-1][:-3]
    return ".".join(parts)


def parse_file(path: Path) -> ModuleInfo:
    modname = module_name_from_path(path)
    mi = ModuleInfo(path, modname)
    try:
        src = path.read_text(encoding='utf-8')
        tree = ast.parse(src, filename=str(path))
        # set parents
        for node in ast.walk(tree):
            for child in ast.iter_child_nodes(node):
                child.parent = node
        UsageVisitor(mi).visit(tree)
    except Exception as e:
        mi.parse_error = str(e)
    return mi


def build_modules() -> Dict[str, ModuleInfo]:
    mods: Dict[str, ModuleInfo] = {}
    for f in iter_python_files():
        mi = parse_file(f)
        mods[mi.module_name] = mi
    return mods


def build_import_graph(mods: Dict[str, ModuleInfo]) -> Dict[str, Set[str]]:
    internal_modules = set(mods.keys())
    graph: Dict[str, Set[str]] = {m: set() for m in mods}
    for m, mi in mods.items():
        for typ, mod, name, _ in mi.imports:
            if typ == "import":
                # top-level module
                top = (mod or '').split('.')[0]
                # if importing internal module fully
                if mod in internal_modules:
                    graph[m].add(mod)
                # also consider top-level package imports like 'services.x'
                # try progressive prefixes
                parts = (mod or '').split('.')
                while parts:
                    pref = '.'.join(parts)
                    if pref in internal_modules:
                        graph[m].add(pref)
                        break
                    parts.pop()
            else:  # from import
                # e.g., from services.plan_a_manager import PlanAManager
                if mod in internal_modules:
                    graph[m].add(mod)
                else:
                    # try prefixes
                    parts = (mod or '').split('.')
                    while parts:
                        pref = '.'.join(parts)
                        if pref in internal_modules:
                            graph[m].add(pref)
                            break
                        parts.pop()
    return graph


def reachable_from(entries: List[Path], graph: Dict[str, Set[str]]) -> Set[str]:
    entry_mods: List[str] = []
    for p in entries:
        if p.exists():
            entry_mods.append(module_name_from_path(p))
    seen: Set[str] = set()
    stack: List[str] = list(entry_mods)
    while stack:
        m = stack.pop()
        if m in seen:
            continue
        seen.add(m)
        for n in graph.get(m, ()):  # neighbors
            if n not in seen:
                stack.append(n)
    return seen


def detect_unused_imports(mods: Dict[str, ModuleInfo]) -> Dict[str, List[Dict[str, object]]]:
    result: Dict[str, List[Dict[str, object]]] = {}
    for m, mi in mods.items():
        if mi.parse_error:
            continue
        unused = []
        used_names = mi.names_used
        for typ, mod, name, lineno in mi.imports:
            if typ == "import":
                # alias name used?
                # find alias key by reverse lookup
                alias_used = False
                for alias, target in mi.import_aliases.items():
                    if target == mod and alias in used_names:
                        alias_used = True
                        break
                if not alias_used:
                    unused.append({"type": typ, "module": mod, "line": lineno})
            else:
                # from import: consider alias or original name
                # Determine reference name
                ref_names = []
                for mod2, nm, asname, l in mi.from_imports:
                    if mod2 == mod and nm == name:
                        ref_names.append(asname or nm)
                is_used = any((ref in used_names) for ref in ref_names)
                if not is_used:
                    unused.append({"type": typ, "module": mod, "name": name, "line": lineno})
        if unused:
            result[m] = unused
    return result


def detect_potentially_unused_defs(mods: Dict[str, ModuleInfo]) -> Dict[str, Dict[str, List[Tuple[str, int]]]]:
    # Build reverse usage maps
    # from-import references
    referenced_defs: Dict[str, Set[str]] = {m: set() for m in mods}
    # attribute references via module alias: module.attr
    attr_refs: Dict[str, Set[str]] = {m: set() for m in mods}

    # map alias -> module for each module to inspect attr usage
    for m, mi in mods.items():
        # from-imports (track if actually used)
        for mod, name, asname, _ in mi.from_imports:
            ref = (asname or name)
            if ref in mi.names_used and mod in mods:
                referenced_defs.setdefault(mod, set()).add(name)
        # attribute usage
        for base, attr in mi.attr_used:
            target_mod = mi.import_aliases.get(base)
            if target_mod in mods:
                attr_refs.setdefault(target_mod, set()).add(attr)

    suspects: Dict[str, Dict[str, List[Tuple[str, int]]]] = {}
    for m, mi in mods.items():
        if mi.parse_error:
            continue
        # Functions
        unused_funcs: List[Tuple[str, int]] = []
        for fn, ln in mi.defined_funcs.items():
            used_locally = fn in mi.names_used
            used_by_from = fn in referenced_defs.get(m, set())
            used_by_attr = fn in attr_refs.get(m, set())
            if not (used_locally or used_by_from or used_by_attr):
                unused_funcs.append((fn, ln))
        # Classes
        unused_classes: List[Tuple[str, int]] = []
        for cn, ln in mi.defined_classes.items():
            used_locally = cn in mi.names_used
            used_by_from = cn in referenced_defs.get(m, set())
            used_by_attr = cn in attr_refs.get(m, set())
            if not (used_locally or used_by_from or used_by_attr):
                unused_classes.append((cn, ln))
        if unused_funcs or unused_classes:
            suspects[m] = {
                "unused_functions": unused_funcs,
                "unused_classes": unused_classes,
            }
    return suspects


def keyword_scan(mods: Dict[str, ModuleInfo], keywords: List[str]) -> Dict[str, List[Tuple[int, str]]]:
    hits: Dict[str, List[Tuple[int, str]]] = {}
    for m, mi in mods.items():
        try:
            text = mi.path.read_text(encoding='utf-8', errors='ignore')
        except Exception:
            continue
        lines = text.splitlines()
        for i, line in enumerate(lines, 1):
            low = line.lower()
            for kw in keywords:
                if kw in low:
                    hits.setdefault(m, []).append((i, line.strip()[:160]))
    return hits


def main():
    mods = build_modules()
    graph = build_import_graph(mods)
    reachable = reachable_from(ENTRY_FILES, graph)

    unused_imports = detect_unused_imports(mods)
    suspects = detect_potentially_unused_defs(mods)

    # modules not reachable
    unreachable_modules = sorted([m for m in mods.keys() if m not in reachable])

    # keyword scan for legacy voting/feature extraction/classifier
    keywords = [
        'voting', 'ensemble', 'bagging', 'boosting',
        'feature', 'extract', 'csp', 'ica', 'wavelet',
        'spectral', 'spectrum', 'psd', 'bandpower',
        'svm', 'knn', 'randomforest', 'xgboost',
        'lda', 'logistic', 'bayes', 'naive', 'tree',
        'multiclass', 'one-vs', 'ovo', 'ovr'
    ]
    keyword_hits = keyword_scan(mods, keywords)

    report: Report = {
        "project_root": str(PROJECT_ROOT),
        "total_modules": len(mods),
        "entry_points": [module_name_from_path(p) for p in ENTRY_FILES if p.exists()],
        "reachable_module_count": len(reachable),
        "unreachable_modules": unreachable_modules,
        "unused_imports": unused_imports,
        "suspect_unused_defs": suspects,
        "keyword_hits": keyword_hits,
    }

    out_dir = PROJECT_ROOT / "tools" / "cleanup_reports"
    out_dir.mkdir(parents=True, exist_ok=True)
    (out_dir / "cleanup_report.json").write_text(json.dumps(report, indent=2, ensure_ascii=False), encoding='utf-8')

    # also write a brief markdown summary
    lines: List[str] = []
    lines.append(f"# Cleanup Static Analysis Report")
    lines.append("")
    lines.append(f"- Total modules scanned: {len(mods)}")
    lines.append(f"- Entry points: {', '.join(report['entry_points'])}")
    lines.append(f"- Reachable modules: {len(reachable)}")
    lines.append(f"- Unreachable modules: {len(unreachable_modules)}")
    lines.append("")
    if unreachable_modules:
        lines.append("## Unreachable Modules (from main/build scripts)")
        for m in unreachable_modules[:200]:
            lines.append(f"- {m}")
        if len(unreachable_modules) > 200:
            lines.append(f"- ... ({len(unreachable_modules)-200} more)")
    if unused_imports:
        lines.append("")
        lines.append("## Files with Unused Imports")
        for m, lst in unused_imports.items():
            lines.append(f"- {m}: {len(lst)} unused imports")
    if suspects:
        lines.append("")
        lines.append("## Modules with Potentially Unused Top-level Definitions")
        for m, detail in suspects.items():
            nf = len(detail.get("unused_functions", []))
            nc = len(detail.get("unused_classes", []))
            lines.append(f"- {m}: {nf} functions, {nc} classes")
    if keyword_hits:
        lines.append("")
        lines.append("## Keyword Hits (legacy algorithm/classifier related)")
        for m, lst in keyword_hits.items():
            lines.append(f"- {m}: {len(lst)} hits")
    (out_dir / "cleanup_report.md").write_text("\n".join(lines), encoding='utf-8')

    print("Analysis complete. Reports at tools/cleanup_reports/")

if __name__ == "__main__":
    main()

