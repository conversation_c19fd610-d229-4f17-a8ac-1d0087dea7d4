# -*- coding: utf-8 -*-
"""
基础页面类
Base Page Class

所有页面的基类，提供通用功能
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont


class BasePage(QWidget):
    """基础页面类"""
    
    # 页面信号
    page_loaded = Signal()
    page_unloaded = Signal()
    access_denied = Signal(str)  # 访问被拒绝信号
    
    def __init__(self, page_id: str, title: str):
        super().__init__()
        
        self.page_id = page_id
        self.title = title
        self.is_loaded = False
        
        # 设置对象名称
        self.setObjectName(f"{page_id}_page")
        
        # 初始化UI
        self._init_base_ui()
    
    def check_page_access(self) -> bool:
        """检查页面访问权限"""
        try:
            from utils.simple_permission_manager import permission_manager
            return permission_manager.can_access_page(self.page_id)
        except Exception as e:
            print(f"权限检查失败: {e}")
            return True  # 默认允许访问，避免系统崩溃
    
    def handle_access_denied(self):
        """处理访问被拒绝的情况"""
        self.access_denied.emit(self.page_id)
        
        # 显示访问被拒绝的消息
        self._show_access_denied_message()
    
    def _show_access_denied_message(self):
        """显示访问被拒绝的消息"""
        # 清空现有内容
        while self.main_layout.count():
            child = self.main_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # 显示访问被拒绝消息
        access_denied_label = QLabel("⚠️ 访问被拒绝\n\n您没有权限访问此页面")
        access_denied_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        access_denied_label.setFont(QFont("Microsoft YaHei", 18))
        access_denied_label.setObjectName("access_denied_label")
        
        self.main_layout.addWidget(access_denied_label)
    
    def _init_base_ui(self):
        """初始化基础UI"""
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 10, 10, 10)  # 按照HTML中的padding
        self.main_layout.setSpacing(10)
        
        # 子类可以重写此方法来添加具体内容
        self._init_content()
    
    def _init_content(self):
        """初始化页面内容 - 子类重写"""
        # 默认显示占位内容
        placeholder = QLabel(f"{self.title}\n页面正在开发中...")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setFont(QFont("Microsoft YaHei", 24))
        # 颜色将通过主题系统设置
        
        self.main_layout.addWidget(placeholder)
    
    def load_page(self):
        """加载页面"""
        if not self.is_loaded:
            self._load_data()
            self.is_loaded = True
            self.page_loaded.emit()
    
    def unload_page(self):
        """卸载页面"""
        if self.is_loaded:
            self._unload_data()
            self.is_loaded = False
            self.page_unloaded.emit()
    
    def _load_data(self):
        """加载数据 - 子类重写"""
        pass
    
    def _unload_data(self):
        """卸载数据 - 子类重写"""
        pass
    
    def cleanup(self):
        """清理资源"""
        self.unload_page()
    
    def get_page_id(self) -> str:
        """获取页面ID"""
        return self.page_id
    
    def get_title(self) -> str:
        """获取页面标题"""
        return self.title

    def update_theme(self):
        """更新主题 - 子类可以重写此方法"""
        pass
