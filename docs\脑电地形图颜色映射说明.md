# 脑电地形图颜色映射说明

## 概述

脑机接口康复训练系统中的脑电地形图使用专业的颜色映射来可视化脑电活动的空间分布。本文档详细说明了颜色映射的配置、数值范围和医学意义。

## 颜色映射配置

### 基本配置
- **颜色映射名称**: `'RdBu_r'` (Red-Blue Reversed)
- **类型**: 线性分段颜色映射 (LinearSegmentedColormap)
- **颜色数量**: 256个颜色级别
- **颜色范围**: 红色 → 白色 → 蓝色

### 颜色映射原理
`RdBu_r` 是一个发散型颜色映射，专门设计用于显示双极性数据（正负值）：

```python
# 在 _create_topography_pixmap_internal 方法中设置
cmap='RdBu_r'  # 使用经典红蓝配色
```

## 数值映射规则

### 颜色-数值对应关系

| 数值范围 | 颜色 | 医学意义 | RGBA值示例 |
|---------|------|----------|-----------|
| 最大正值 | 深红色 | 高度激活 | (0.40, 0.00, 0.12, 1.0) |
| 中等正值 | 浅红色 | 中度激活 | (0.89, 0.50, 0.40, 1.0) |
| 零值 | 白色 | 基线状态 | (0.97, 0.97, 0.96, 1.0) |
| 中等负值 | 浅蓝色 | 中度抑制 | (0.42, 0.68, 0.82, 1.0) |
| 最大负值 | 深蓝色 | 高度抑制 | (0.02, 0.19, 0.38, 1.0) |

### 数值归一化过程

1. **自动范围检测**: 系统自动检测输入数据的最大值和最小值
2. **归一化映射**: 将数值范围映射到 [0, 1] 区间
3. **颜色查找**: 根据归一化值在颜色映射中查找对应颜色

```python
# 数值映射示例
原始值 -50 → 归一化 0.00 → 深蓝色
原始值 -25 → 归一化 0.25 → 浅蓝色  
原始值   0 → 归一化 0.50 → 白色
原始值  25 → 归一化 0.75 → 浅红色
原始值  50 → 归一化 1.00 → 深红色
```

## 医学意义解释

### 脑电活动的颜色表示

#### 红色区域（正值）
- **含义**: 脑电活动增强、神经元激活
- **临床意义**: 
  - 运动想象时的运动皮层激活
  - 认知任务时的相关脑区激活
  - 注意力集中时的前额叶激活

#### 蓝色区域（负值）
- **含义**: 脑电活动减弱、神经元抑制
- **临床意义**:
  - 放松状态下的脑区抑制
  - 注意力分散时的活动减弱
  - 非相关脑区的功能抑制

#### 白色区域（零值）
- **含义**: 基线状态、中性活动
- **临床意义**:
  - 静息状态的脑电活动
  - 平衡状态的神经活动
  - 参考基线水平

### 康复训练中的应用

#### 运动想象训练
- **目标**: 运动皮层区域显示红色激活
- **评估**: 通过颜色强度评估想象质量
- **反馈**: 红色区域越强越集中，训练效果越好

#### 注意力训练
- **目标**: 前额叶区域显示红色激活
- **评估**: 通过颜色分布评估注意力集中度
- **反馈**: 蓝色区域减少表示注意力提升

## 技术参数

### 地形图生成参数

```python
# 实际地形图参数
contours = 8        # 等高线数量
res = 128          # 分辨率
cmap = 'RdBu_r'    # 颜色映射

# 头模轮廓参数
contours = 0       # 不显示等高线
res = 64           # 降低分辨率提高性能
cmap = 'RdBu_r'    # 保持相同颜色映射
```

### 数据处理流程

1. **数据输入**: 8通道脑电数据 (Pz, P3, P4, C3, Cz, C4, F3, F4)
2. **数据验证**: 检查数据完整性和有效性
3. **数值映射**: 自动检测数据范围并归一化
4. **颜色映射**: 应用RdBu_r颜色映射
5. **地形图生成**: 使用MNE库生成地形图
6. **显示输出**: 转换为QPixmap并显示

## 数值范围规定

### 当前配置
- **范围设定**: 自动检测，无固定范围
- **归一化**: 基于数据最大值和最小值
- **优点**: 自适应不同数据范围
- **缺点**: 不同时间点的颜色可能不具可比性

### 可选配置方案

#### 1. 固定范围配置
```python
# 设置固定的数值范围
vmin = -100  # 最小值
vmax = 100   # 最大值

# 在plot_topomap中使用
mne.viz.plot_topomap(
    data, self.info,
    vmin=vmin, vmax=vmax,
    # ... 其他参数
)
```

#### 2. 标准化配置
```python
# 基于标准差的范围设置
std_multiplier = 2
vmin = -std_multiplier * np.std(data)
vmax = std_multiplier * np.std(data)
```

#### 3. 百分位数配置
```python
# 基于百分位数的范围设置
vmin = np.percentile(data, 5)   # 5%分位数
vmax = np.percentile(data, 95)  # 95%分位数
```

## 自定义配置

### 修改颜色映射
如需更改颜色映射，可在 `_create_topography_pixmap_internal` 方法中修改：

```python
# 其他可选的颜色映射
cmap='viridis'     # 绿-蓝-紫色映射
cmap='plasma'      # 紫-红-黄色映射
cmap='coolwarm'    # 蓝-红色映射
cmap='seismic'     # 蓝-白-红色映射
```

### 修改数值范围
如需设置固定数值范围，可在 `plot_topomap` 调用中添加：

```python
mne.viz.plot_topomap(
    data, self.info,
    vmin=-50,      # 设置最小值
    vmax=50,       # 设置最大值
    # ... 其他参数
)
```

### 修改颜色级别
如需调整颜色的精细程度，可修改等高线数量：

```python
contours = 12      # 增加等高线数量，提高颜色精度
contours = 4       # 减少等高线数量，简化显示
```

## 性能考虑

### 优化策略
1. **分辨率调整**: 头模轮廓使用较低分辨率(64)提高性能
2. **等高线控制**: 头模轮廓不显示等高线减少计算
3. **颜色映射缓存**: 重用颜色映射对象避免重复创建

### 内存管理
- 及时清理matplotlib图形对象
- 使用透明背景减少内存占用
- 优化图像转换过程

## 故障排除

### 常见问题

1. **颜色显示异常**
   - 检查数据是否包含NaN或Inf值
   - 确认数据范围是否合理
   - 验证颜色映射是否正确加载

2. **性能问题**
   - 降低分辨率参数
   - 减少等高线数量
   - 优化数据更新频率

3. **显示不一致**
   - 检查数据归一化过程
   - 确认颜色映射配置
   - 验证数值范围设置

### 调试方法

```python
# 打印数据信息
print(f"数据范围: {data.min()} - {data.max()}")
print(f"数据类型: {data.dtype}")
print(f"数据形状: {data.shape}")

# 检查颜色映射
import matplotlib.cm as cm
cmap = cm.get_cmap('RdBu_r')
print(f"颜色映射: {cmap.name}")
```

## 总结

脑电地形图使用 `RdBu_r` 颜色映射提供了直观的脑电活动可视化：
- **红色**表示激活状态，适合显示运动想象等正向活动
- **蓝色**表示抑制状态，适合显示放松等负向活动  
- **白色**表示基线状态，提供中性参考
- **自动范围**确保不同数据都能得到合适的颜色显示
- **高分辨率**保证地形图的精确性和美观性

这种配置既符合神经科学的标准实践，又能为康复训练提供清晰的视觉反馈。 