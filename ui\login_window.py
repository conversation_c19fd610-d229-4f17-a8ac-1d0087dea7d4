# -*- coding: utf-8 -*-
"""
登录窗口
Login Window

轻量级登录窗口，快速启动，支持医疗器械认证要求
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QFrame, QWidget,
    QGraphicsDropShadowEffect, QApplication, QComboBox
)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve, QPoint
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QKeyEvent, QIcon, QMouseEvent, QPainterPath, QBrush
from services.auth_service import auth_service
from app.config import AppConfig
from ui.themes.theme_manager import ThemeManager
from pathlib import Path
from utils.path_manager import get_icon_path


class LoginWindow(QDialog):
    """登录窗口 - 轻量级快速启动"""
    
    # 登录信号
    login_success = Signal(str)  # 传递用户名
    login_failed = Signal(str)   # 传递错误信息
    
    def __init__(self):
        super().__init__()

        # 检查许可证状态
        self._check_license_status()

        # 加载配置和主题
        self.config = AppConfig()
        self.theme_manager = ThemeManager()
        self.current_theme = self.config.get('theme', 'tech')

        # 窗口属性
        self.setWindowTitle("NeuroLink Pro - 系统登录")
        self.setFixedSize(420, 380)  # 调整窗口尺寸：420x380 (再增加10px高度)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 设置窗口图标（用于任务栏显示）
        icon_path = get_icon_path("ht.png")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        else:
            print(f"⚠️ 登录窗口图标文件不存在：{icon_path}")

        # 居中显示
        self._center_window()

        # 先加载用户列表
        self._load_users()

        # 初始化UI
        self._init_ui()
        self._setup_style()
        self._setup_animations()

        # 设置焦点到密码输入框
        self.password_input.setFocus()
        
        # 拖拽相关变量
        self.drag_position = QPoint()
    
    def _center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)

    def _load_users(self):
        """加载活跃用户列表"""
        try:
            from core.database import db_manager

            # 查询活跃用户
            sql = """
            SELECT username, name
            FROM operator
            WHERE status = 'active'
            ORDER BY username
            """
            users = db_manager.execute_query(sql)

            # 存储用户列表
            self.users = []
            for user in users:
                display_name = f"{user['username']}"
                if user['name']:
                    display_name += f" ({user['name']})"
                self.users.append({
                    'username': user['username'],
                    'display': display_name
                })

        except Exception as e:
            print(f"加载用户列表失败: {e}")
            # 使用默认用户列表
            self.users = [
                {'username': 'admin', 'display': 'admin'},
                {'username': 'operator1', 'display': 'operator1'},
                {'username': '111', 'display': '111'}
            ]
    
    def _init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 主容器
        self.main_container = QFrame()
        self.main_container.setObjectName("login_container")
        main_layout.addWidget(self.main_container)
        
        # 容器布局 - 精确计算高度分配
        # 总高度350px，减去边距后约280px可用空间
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(35, 25, 35, 25)  # 上下边距25px
        container_layout.setSpacing(15)  # 组件间距15px
        
        # 标题区域
        self._create_header(container_layout)
        
        # 输入区域
        self._create_input_area(container_layout)
        
        # 按钮区域
        self._create_button_area(container_layout)
        
        # 状态区域
        self._create_status_area(container_layout)
    
    def _create_header(self, layout):
        """创建标题区域"""
        # Logo和标题 - 总高度约80px
        header_layout = QVBoxLayout()
        header_layout.setSpacing(5)  # 减少间距
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Logo（使用ht.png图片）- 高度约35px
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 加载ht.png图片
        logo_path = get_icon_path("ht.png")
        if logo_path.exists():
            pixmap = QPixmap(str(logo_path))
            # 缩放图片到合适尺寸，保持宽高比
            scaled_pixmap = pixmap.scaled(32, 32, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            logo_label.setPixmap(scaled_pixmap)
        else:
            # 如果图片不存在，使用文字作为备用
            logo_label.setText("🧠")
            logo_label.setFont(QFont("Microsoft YaHei", 28))
            print(f"⚠️ 图标文件不存在：{logo_path}")

        logo_label.setFixedHeight(35)  # 固定高度
        header_layout.addWidget(logo_label)

        # 系统名称 - 高度约25px
        title_label = QLabel("脑机接口康复训练系统")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setObjectName("login_title")
        title_label.setFixedHeight(25)
        header_layout.addWidget(title_label)

        # 副标题 - 高度约20px
        subtitle_label = QLabel("BCI-based Rehabilitation Training")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setFont(QFont("Microsoft YaHei", 9))
        subtitle_label.setObjectName("login_subtitle")
        subtitle_label.setFixedHeight(20)
        header_layout.addWidget(subtitle_label)
        
        layout.addLayout(header_layout)
    
    def _create_input_area(self, layout):
        """创建输入区域"""
        # 输入区域 - 总高度约80px (2个输入框 + 间距)
        input_layout = QVBoxLayout()
        input_layout.setSpacing(10)  # 输入框间距

        # 用户名选择 - 高度45px
        self.username_input = QComboBox()
        self.username_input.setObjectName("login_input")
        self.username_input.setFont(QFont("Microsoft YaHei", 13))
        self.username_input.setFixedHeight(45)
        self.username_input.setEditable(False)  # 不允许编辑，只能选择

        # 填充用户列表
        if hasattr(self, 'users') and self.users:
            for user in self.users:
                self.username_input.addItem(user['display'], user['username'])

            # 设置默认选择第一个用户
            self.username_input.setCurrentIndex(0)
        else:
            # 如果没有用户列表，添加默认项
            self.username_input.addItem("请选择用户", "")

        input_layout.addWidget(self.username_input)

        # 密码输入 - 高度45px (再增加5px)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setObjectName("login_input")
        self.password_input.setFont(QFont("Microsoft YaHei", 13))  # 再增大字体
        self.password_input.setFixedHeight(45)  # 再增加高度
        self.password_input.returnPressed.connect(self._on_login)
        input_layout.addWidget(self.password_input)
        
        layout.addLayout(input_layout)
    
    def _create_button_area(self, layout):
        """创建按钮区域"""
        # 按钮区域 - 高度约40px
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)  # 按钮间距

        # 登录按钮 - 高度40px
        self.login_btn = QPushButton("登录")
        self.login_btn.setObjectName("login_button")
        self.login_btn.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))  # 适中字体
        self.login_btn.setFixedHeight(40)  # 固定高度
        self.login_btn.clicked.connect(self._on_login)
        self.login_btn.setDefault(True)
        button_layout.addWidget(self.login_btn)

        # 取消按钮 - 高度40px
        self.cancel_btn = QPushButton("退出")
        self.cancel_btn.setObjectName("cancel_button")
        self.cancel_btn.setFont(QFont("Microsoft YaHei", 11))  # 适中字体
        self.cancel_btn.setFixedHeight(40)  # 固定高度
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _create_status_area(self, layout):
        """创建状态区域"""
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setFont(QFont("Microsoft YaHei", 9))
        self.status_label.setObjectName("login_status")
        self.status_label.hide()
        layout.addWidget(self.status_label)
        
        # 加载提示
        self.loading_label = QLabel("系统正在后台加载...")
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setFont(QFont("Microsoft YaHei", 9))
        self.loading_label.setObjectName("loading_status")
        layout.addWidget(self.loading_label)
    
    def _setup_style(self):
        """设置样式 - 根据当前主题动态生成"""
        theme_config = self.theme_manager.get_theme_config(self.current_theme)

        # 根据主题生成样式
        stylesheet = self._generate_login_stylesheet(theme_config)
        self.setStyleSheet(stylesheet)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(10)
        shadow.setColor(QColor(0, 0, 0, 30))
        self.main_container.setGraphicsEffect(shadow)

    def _generate_login_stylesheet(self, theme_config):
        """根据主题配置生成登录窗口样式表"""
        return f"""
            QDialog {{
                background: transparent;
            }}

            QFrame#login_container {{
                background-color: {theme_config['bg_glass']};
                border: 1px solid {theme_config['neutral_200']};
                border-radius: 16px;
            }}

            QLabel#login_title {{
                color: {theme_config['neutral_800']};
            }}

            QLabel#login_subtitle {{
                color: {theme_config['neutral_500']};
            }}

            QLineEdit#login_input {{
                padding: 10px 16px;
                border: 2px solid {theme_config['neutral_200']};
                border-radius: 8px;
                background-color: {theme_config['bg_secondary']};
                color: {theme_config['neutral_800']};
                font-size: 16px;
                height: 45px;
                line-height: 1.2;
            }}

            QLineEdit#login_input:focus {{
                border-color: {theme_config['primary_color']};
            }}

            QComboBox#login_input {{
                padding: 10px 16px;
                border: 2px solid {theme_config['neutral_200']};
                border-radius: 8px;
                background-color: {theme_config['bg_secondary']};
                color: {theme_config['neutral_800']};
                font-size: 16px;
                height: 45px;
            }}

            QComboBox#login_input:focus {{
                border-color: {theme_config['primary_color']};
            }}

            QComboBox#login_input::drop-down {{
                border: none;
                width: 30px;
            }}

            QComboBox#login_input::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {theme_config['neutral_500']};
                margin-right: 10px;
            }}

            QComboBox#login_input QAbstractItemView {{
                border: 2px solid {theme_config['neutral_200']};
                border-radius: 8px;
                background-color: {theme_config['bg_secondary']};
                color: {theme_config['neutral_800']};
                selection-background-color: {theme_config['primary_color']};
                selection-color: white;
                padding: 5px;
            }}

            QPushButton#login_button {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {theme_config['primary_color']}, stop:1 {theme_config['primary_dark']});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                height: 40px;
                font-size: 15px;
            }}

            QPushButton#login_button:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {theme_config['primary_light']}, stop:1 {theme_config['primary_color']});
            }}

            QPushButton#login_button:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {theme_config['primary_dark']}, stop:1 {theme_config['primary_color']});
            }}

            QPushButton#cancel_button {{
                background-color: {theme_config['neutral_100']};
                color: {theme_config['neutral_500']};
                border: 1px solid {theme_config['neutral_200']};
                border-radius: 8px;
                padding: 12px 24px;
                height: 40px;
                font-size: 15px;
            }}

            QPushButton#cancel_button:hover {{
                background-color: {theme_config['neutral_200']};
                color: {theme_config['neutral_600']};
            }}

            QLabel#login_status {{
                color: {theme_config['danger_color']};
            }}

            QLabel#loading_status {{
                color: {theme_config['primary_color']};
            }}
        """
    
    def _setup_animations(self):
        """设置动画效果"""
        # 窗口淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 启动淡入动画
        self.setWindowOpacity(0)
        self.fade_animation.setStartValue(0)
        self.fade_animation.setEndValue(1)
        self.fade_animation.start()
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 开始拖拽"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 执行拖拽"""
        if event.buttons() == Qt.MouseButton.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件 - 结束拖拽"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = QPoint()
            event.accept()
    
    def _on_login(self):
        """登录按钮点击"""
        # 从下拉框获取选中的用户名
        username = self.username_input.currentData()  # 获取用户名数据
        password = self.password_input.text().strip()

        if not username or not password:
            self._show_error("请选择用户名并输入密码")
            return

        # 禁用输入
        self._set_inputs_enabled(False)
        self.status_label.hide()

        # 验证登录
        self._validate_login(username, password)
    
    def _validate_login(self, username: str, password: str):
        """验证登录信息"""
        # 使用数据库认证服务进行验证
        success, message = auth_service.authenticate(username, password)

        if success:
            self.login_success.emit(username)
        else:
            self._show_error(message)
            self._set_inputs_enabled(True)
    
    def _show_error(self, message: str):
        """显示错误信息"""
        self.status_label.setText(message)
        self.status_label.show()
        
        # 错误消息自动隐藏
        QTimer.singleShot(3000, self.status_label.hide)
    
    def _set_inputs_enabled(self, enabled: bool):
        """设置输入控件启用状态"""
        self.username_input.setEnabled(enabled)
        self.password_input.setEnabled(enabled)
        self.login_btn.setEnabled(enabled)
        self.cancel_btn.setEnabled(enabled)
    
    def keyPressEvent(self, event: QKeyEvent):
        """键盘事件处理"""
        if event.key() == Qt.Key.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
    
    def _check_license_status(self):
        """检查许可证状态"""
        try:
            from services.license_manager import license_system
            
            if not license_system.is_activated():
                # 显示激活对话框
                self._show_activation_dialog()
        except Exception as e:
            print(f"许可证检查失败: {e}")
            # 许可证检查失败时仍允许继续，但可以记录日志
    
    def _show_activation_dialog(self):
        """显示激活对话框"""
        try:
            from ui.dialogs.activation_dialog import ActivationDialog
            
            # 不设置父窗口，避免关闭时影响登录窗口渲染
            activation_dialog = ActivationDialog(None)
            result = activation_dialog.exec()
            
            if result != QDialog.DialogCode.Accepted:
                # 用户取消激活，退出应用
                print("用户取消激活，退出应用")
                # 直接退出，避免显示登录窗口
                import sys
                sys.exit(0)
            else:
                print("软件激活成功")
                # 激活成功后，重新设置登录窗口属性以确保渲染正常
                self._refresh_window_properties()
        except Exception as e:
            print(f"显示激活对话框失败: {e}")
            # 激活对话框失败时，允许继续使用（开发模式）
            # 实际部署时可以选择退出应用
    def set_loading_status(self, message: str):
        """设置加载状态信息"""
        self.loading_label.setText(message)
    
    def _refresh_window_properties(self):
        """刷新窗口属性 - 在激活对话框关闭后确保渲染正常"""
        try:
            # 重新设置窗口属性，确保透明背景正常工作
            self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
            self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
            
            # 显示窗口
            self.show()
            
            # 强制重绘
            self.update()
            
            print("登录窗口属性已刷新")
        except Exception as e:
            print(f"刷新窗口属性失败: {e}")
    
    def paintEvent(self, event):
        """自定义绘制事件 - 备用方案，用于在透明背景有问题时使用"""
        # 如果透明背景工作正常，这个方法不会被调用
        # 只有在移除透明背景属性时才会启用
        super().paintEvent(event)
        
        # 可选：绘制圆角背景
        # painter = QPainter(self)
        # painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        # 
        # # 创建圆角路径
        # path = QPainterPath()
        # path.addRoundedRect(self.rect(), 16, 16)
        # 
        # # 绘制背景
        # painter.fillPath(path, QBrush(QColor(255, 255, 255, 250)))
        # 
        # painter.end()
