# -*- coding: utf-8 -*-
"""
侧边栏组件
Sidebar Component

完全按照HTML设计实现的侧边栏
包含Logo、导航菜单、用户信息等
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QFrame, QScrollArea, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PySide6.QtGui import QFont, QPixmap, QPainter, QLinearGradient, QColor, QBrush
from PySide6.QtSvgWidgets import QSvgWidget
from typing import Dict, List


class NavigationItem(QPushButton):
    """导航项按钮 - 完全按照HTML设计"""
    
    item_clicked = Signal(str)
    
    def __init__(self, item_id: str, icon_svg: str, text: str, badge: str = None):
        super().__init__()
        
        self.item_id = item_id
        self.icon_svg = icon_svg
        self.text = text
        self.badge = badge
        self.is_active = False
        self.is_collapsed = False
        
        self._init_ui()
        self._setup_style()
        
    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("nav_item")
        # 设置最小高度确保有足够空间进行垂直居中
        self.setMinimumHeight(56)
        self.setCursor(Qt.CursorShape.PointingHandCursor)

        # 创建布局 - 恢复原始padding，专注于垂直居中
        layout = QHBoxLayout(self)
        layout.setContentsMargins(24, 16, 24, 16)  # 恢复HTML中的padding
        layout.setSpacing(16)
        # 设置布局的垂直对齐方式
        layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)
        
        # 图标区域（使用传入的图标）
        self.icon_widget = QLabel()
        self.icon_widget.setFixedSize(28, 28)  # 调大图标尺寸以匹配HTML比例
        self.icon_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 设置图标字体大小
        icon_font = QFont("Microsoft YaHei", 16)
        icon_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.icon_widget.setFont(icon_font)
        # 使用传入的图标参数
        self.icon_widget.setText(self.icon_svg)
        layout.addWidget(self.icon_widget)
        
        # 文字区域
        self.text_widget = QLabel(self.text)
        self.text_widget.setObjectName("nav_text")
        # 调整字体大小以匹配HTML视觉效果
        font = QFont("Microsoft YaHei", 12, QFont.Weight.Medium)
        font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.text_widget.setFont(font)
        # 保持文字左对齐，只在垂直方向居中
        self.text_widget.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(self.text_widget, 1)

        # 徽章区域
        if self.badge:
            self.badge_widget = QLabel(self.badge)
            self.badge_widget.setObjectName("nav_badge")
            # 调整尺寸以适应3位数显示，宽度自适应，最小宽度24px
            self.badge_widget.setMinimumSize(24, 20)
            self.badge_widget.setMaximumHeight(20)
            self.badge_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            # 调整徽章字体大小以匹配HTML视觉效果
            badge_font = QFont("Microsoft YaHei", 8, QFont.Weight.DemiBold)
            badge_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
            self.badge_widget.setFont(badge_font)
            layout.addWidget(self.badge_widget)
        else:
            self.badge_widget = None
        
        # 连接点击事件
        self.clicked.connect(lambda: self.item_clicked.emit(self.item_id))
    
    def _setup_style(self):
        """设置样式 - 按照HTML的CSS"""
        # 基础样式会在主题系统中定义
        self.setProperty("active", False)
        self.setProperty("collapsed", False)
    
    def set_active(self, active: bool):
        """设置激活状态"""
        self.is_active = active
        self.setProperty("active", active)

        # 刷新样式
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()
    
    def set_collapsed(self, collapsed: bool):
        """设置折叠状态"""
        self.is_collapsed = collapsed
        
        # 设置collapsed属性用于样式控制
        self.setProperty("collapsed", collapsed)
        
        # 隐藏/显示文字和徽章
        self.text_widget.setVisible(not collapsed)
        if self.badge_widget:
            self.badge_widget.setVisible(not collapsed)
        
        # 动态调整边距以确保图标在收起状态下居中显示
        if collapsed:
            # 收起状态：减小左右边距，确保图标在80px宽度内居中
            self.layout().setContentsMargins(26, 16, 8, 16)
        else:
            # 展开状态：恢复原始边距
            self.layout().setContentsMargins(24, 16, 24, 16)
        
        # 刷新样式以应用collapsed属性
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()


class SectionTitle(QLabel):
    """区段标题 - 按照HTML设计"""

    def __init__(self, title: str):
        super().__init__(title)
        self.setObjectName("nav_title")
        # 调整区段标题字体大小以匹配HTML视觉效果
        title_font = QFont("Microsoft YaHei", 9, QFont.Weight.Bold)
        title_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.setFont(title_font)
        self.setContentsMargins(24, 0, 24, 16)
        
        # 样式由全局主题系统控制，不设置本地样式


class UserProfile(QFrame):
    """用户信息卡片 - 按照HTML设计"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("user_profile")
        self.setFixedHeight(50)
        
        self._init_ui()
        self._setup_style()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(13, 6, 13, 6)
        layout.setSpacing(6)
        
        # 用户头像
        self.avatar = QLabel("Dr")
        self.avatar.setObjectName("user_avatar")
        self.avatar.setFixedSize(36, 36)
        self.avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 调整用户头像字体大小以匹配HTML视觉效果
        avatar_font = QFont("Microsoft YaHei", 13, QFont.Weight.Bold)
        avatar_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.avatar.setFont(avatar_font)
        layout.addWidget(self.avatar)
        
        # 用户信息
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(2)
        
        self.user_name = QLabel("未登录")
        self.user_name.setObjectName("user_name")
        # 调整用户名字体大小以匹配HTML视觉效果
        name_font = QFont("Microsoft YaHei", 12, QFont.Weight.DemiBold)
        name_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.user_name.setFont(name_font)
        info_layout.addWidget(self.user_name)

        self.user_role = QLabel("请登录")
        self.user_role.setObjectName("user_role")
        # 调整用户角色字体大小以匹配HTML视觉效果
        role_font = QFont("Microsoft YaHei", 10)
        role_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.user_role.setFont(role_font)
        info_layout.addWidget(self.user_role)
        
        layout.addWidget(info_widget, 1)
        
        # 在线状态指示器
        self.status_dot = QLabel()
        self.status_dot.setObjectName("user_status")
        self.status_dot.setFixedSize(8, 8)
        layout.addWidget(self.status_dot)
    
    def _setup_style(self):
        """设置样式"""
        # 样式会在主题系统中定义
        pass
    
    def set_collapsed(self, collapsed: bool):
        """设置折叠状态"""
        # 在收起状态下直接隐藏整个用户信息卡片
        self.setVisible(not collapsed)

    def update_user_info(self, user_info: dict):
        """更新用户信息显示"""
        if user_info:
            # 更新用户名显示
            name = user_info.get('name') or user_info.get('username', '未知用户')
            self.user_name.setText(name)

            # 更新角色显示
            role = user_info.get('role', '')
            role_text = self._get_role_text(role)
            self.user_role.setText(role_text)

            # 更新头像显示（取用户名的第一个字符）
            avatar_text = name[0] if name else "U"
            self.avatar.setText(avatar_text)
        else:
            # 清空用户信息
            self.user_name.setText("未登录")
            self.user_role.setText("请登录")
            self.avatar.setText("U")

    def _get_role_text(self, role: str) -> str:
        """获取角色显示文本"""
        role_map = {
            'admin': '系统管理员',
            'doctor': '医生',
            'operator': '操作员'
        }
        return role_map.get(role, role or '未知角色')


class Sidebar(QFrame):
    """侧边栏组件 - 完全按照HTML设计实现"""
    
    # 信号
    navigation_changed = Signal(str)
    sidebar_toggled = Signal(bool)
    
    def __init__(self):
        super().__init__()
        
        self.is_collapsed = False
        self.current_page = "patients"
        
        # 导航项
        self.nav_items = {}
        
        # 动画
        self.collapse_animation = None
        
        self._init_ui()
        self._setup_animations()
        self._create_navigation()
        self._setup_style()
    
    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("sidebar")
        self.setFixedWidth(220)  # 按照HTML中的宽度
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 头部区域
        self.header = self._create_header()
        layout.addWidget(self.header)
        
        # 导航区域（可滚动）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)
        scroll_area.setObjectName("nav_scroll")
        
        self.nav_widget = QWidget()
        self.nav_layout = QVBoxLayout(self.nav_widget)
        self.nav_layout.setContentsMargins(0, 24, 0, 0)
        self.nav_layout.setSpacing(0)
        
        scroll_area.setWidget(self.nav_widget)
        layout.addWidget(scroll_area, 1)
        
        # 底部用户信息
        self.footer = self._create_footer()
        layout.addWidget(self.footer)
    
    def _create_header(self) -> QWidget:
        """创建头部区域 - 按照HTML设计"""
        header = QWidget()
        header.setObjectName("sidebar_header")
        header.setFixedHeight(100)  # 按照HTML中的高度
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(15, 26, 15, 26)
        layout.setSpacing(16)
        
        # Logo
        self.logo = QLabel("BCI")
        self.logo.setObjectName("logo")
        self.logo.setFixedSize(46, 46)
        self.logo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 调整Logo字体大小以匹配HTML视觉效果
        logo_font = QFont("Microsoft YaHei", 18, QFont.Weight.Bold)
        logo_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.logo.setFont(logo_font)
        layout.addWidget(self.logo)

        # 标题区域
        self.title_area = QWidget()
        title_layout = QVBoxLayout(self.title_area)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        self.logo_text = QLabel("脑机接口康复系统")
        self.logo_text.setObjectName("logo_text")
        # 调整Logo标题字体大小以匹配HTML视觉效果
        logo_text_font = QFont("Microsoft YaHei", 12, QFont.Weight.Bold)
        logo_text_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.logo_text.setFont(logo_text_font)
        title_layout.addWidget(self.logo_text)

        self.logo_subtitle = QLabel("BCI-based Rehabilitation Training")
        self.logo_subtitle.setObjectName("logo_subtitle")
        # 调整Logo副标题字体大小以匹配HTML视觉效果
        subtitle_font = QFont("Microsoft YaHei", 6)
        subtitle_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.logo_subtitle.setFont(subtitle_font)
        title_layout.addWidget(self.logo_subtitle)
        
        layout.addWidget(self.title_area, 1)
        
        return header
    
    def _create_footer(self) -> QWidget:
        """创建底部区域"""
        footer = QWidget()
        footer.setObjectName("sidebar_footer")
        footer.setFixedHeight(80)
        
        layout = QVBoxLayout(footer)
        layout.setContentsMargins(15, 6, 15, 0)
        
        # 用户信息卡片
        self.user_profile = UserProfile()
        layout.addWidget(self.user_profile)
        
        return footer
    
    def _setup_animations(self):
        """设置动画 - 按照HTML中的transition效果"""
        self.collapse_animation = QPropertyAnimation(self, b"minimumWidth")
        self.collapse_animation.setDuration(400)  # 按照HTML中的0.4s
        self.collapse_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.max_width_animation = QPropertyAnimation(self, b"maximumWidth")
        self.max_width_animation.setDuration(400)
        self.max_width_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def _create_navigation(self):
        """创建导航菜单 - 按照HTML结构，根据权限静态创建"""
        # 获取当前用户允许的页面
        try:
            from utils.simple_permission_manager import permission_manager
            allowed_pages = permission_manager.get_allowed_pages()
        except Exception as e:
            print(f"获取权限信息失败: {e}")
            # 默认允许访问基础页面
            allowed_pages = ['patients', 'treatment']
        
        # 核心功能区
        self._add_section("核心功能")
        if "patients" in allowed_pages:
            self._add_nav_item("patients", "●", "患者管理", "24")
        if "treatment" in allowed_pages:
            self._add_nav_item("treatment", "■", "治疗系统")
        self._add_section_spacing()  # 添加区段间距

        # 分析报告区
        if "reports" in allowed_pages:
            self._add_section("分析报告")
            self._add_nav_item("reports", "♦", "报告分析")
            self._add_section_spacing()  # 添加区段间距

        # 系统管理区
        has_admin_pages = any(page in allowed_pages for page in ['users', 'settings'])
        if has_admin_pages:
            self._add_section("系统管理")
            if "users" in allowed_pages:
                self._add_nav_item("users", "★", "用户管理")
            if "settings" in allowed_pages:
                self._add_nav_item("settings", "◆", "系统设置")
        
        # 退出系统始终可用
        if has_admin_pages:
            self._add_nav_item("exit", "✕", "退出系统")
        else:
            self._add_section("系统管理")
            self._add_nav_item("exit", "✕", "退出系统")

        # 添加弹性空间
        self.nav_layout.addStretch()

        # 设置默认激活项
        if "patients" in allowed_pages:
            self.set_active_item("patients")
        elif allowed_pages:
            self.set_active_item(allowed_pages[0])
        else:
            self.set_active_item("patients")  # 默认
    
    def _add_section(self, title: str):
        """添加区段标题"""
        section_title = SectionTitle(title)
        self.nav_layout.addWidget(section_title)

    def _add_section_spacing(self):
        """添加区段间距 - 按照HTML中的40px margin-bottom"""
        spacer = QWidget()
        spacer.setFixedHeight(24)  # 40px - 16px(标题底部) = 24px额外间距
        self.nav_layout.addWidget(spacer)

    def _add_nav_item(self, item_id: str, icon: str, text: str, badge: str = None):
        """添加导航项"""
        nav_item = NavigationItem(item_id, icon, text, badge)
        nav_item.item_clicked.connect(self._on_nav_clicked)
        
        self.nav_items[item_id] = nav_item
        self.nav_layout.addWidget(nav_item)
    
    def _setup_style(self):
        """设置样式"""
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(shadow)
    
    def _on_nav_clicked(self, item_id: str):
        """导航项点击处理"""
        self.set_active_item(item_id)
        self.navigation_changed.emit(item_id)
    
    def set_active_item(self, item_id: str):
        """设置激活项"""
        # 取消所有激活状态
        for nav_item in self.nav_items.values():
            nav_item.set_active(False)

        # 设置新的激活状态
        if item_id in self.nav_items:
            self.nav_items[item_id].set_active(True)
            self.current_page = item_id

    def update_patients_badge(self, count: int):
        """更新患者管理菜单项的徽章数量"""
        if "patients" in self.nav_items:
            patients_item = self.nav_items["patients"]
            if hasattr(patients_item, 'badge_widget') and patients_item.badge_widget:
                patients_item.badge_widget.setText(str(count))
    
    def toggle_collapse(self):
        """切换折叠状态"""
        self.set_collapsed(not self.is_collapsed)
    
    def set_collapsed(self, collapsed: bool):
        """设置折叠状态"""
        if collapsed == self.is_collapsed:
            return
        
        self.is_collapsed = collapsed
        
        # 执行动画
        self._animate_collapse(collapsed)
        
        # 发送信号
        self.sidebar_toggled.emit(collapsed)
    
    def _animate_collapse(self, collapsed: bool):
        """执行折叠动画"""
        start_width = 220 if not collapsed else 80
        end_width = 80 if collapsed else 220
        
        # 设置动画
        self.collapse_animation.setStartValue(start_width)
        self.collapse_animation.setEndValue(end_width)
        
        self.max_width_animation.setStartValue(start_width)
        self.max_width_animation.setEndValue(end_width)
        
        # 动画完成后更新组件可见性
        self.collapse_animation.finished.connect(
            lambda: self._update_collapsed_state(collapsed)
        )
        
        # 开始动画
        self.collapse_animation.start()
        self.max_width_animation.start()
    
    def _update_collapsed_state(self, collapsed: bool):
        """更新折叠状态"""
        # 更新标题区域可见性
        self.title_area.setVisible(not collapsed)
        
        # 更新头部Logo位置
        if collapsed:
            # 收起状态：Logo向左移动，减小左边距
            self.header.layout().setContentsMargins(12, 32, 12, 32)
        else:
            # 展开状态：恢复原始边距
            self.header.layout().setContentsMargins(15, 26, 15, 26)
        
        # 更新导航项
        for nav_item in self.nav_items.values():
            nav_item.set_collapsed(collapsed)
        
        # 更新用户信息
        self.user_profile.set_collapsed(collapsed)
        
        # 更新区段标题可见性
        for i in range(self.nav_layout.count()):
            widget = self.nav_layout.itemAt(i).widget()
            if widget and isinstance(widget, SectionTitle):
                widget.setVisible(not collapsed)
        
        # 断开信号连接
        try:
            self.collapse_animation.finished.disconnect()
        except:
            pass

    def update_user_info(self, user_info: dict):
        """更新用户信息显示"""
        if hasattr(self, 'user_profile'):
            self.user_profile.update_user_info(user_info)
