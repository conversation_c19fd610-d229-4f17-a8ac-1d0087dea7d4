# 脑机接口康复训练系统 UI布局设计规范文档

## 目录
1. [主窗口框架](#主窗口框架)
2. [侧边栏组件](#侧边栏组件)
3. [顶部栏组件](#顶部栏组件)
4. [页面内容区域](#页面内容区域)
5. [卡片组件](#卡片组件)
6. [表单组件](#表单组件)
7. [按钮组件](#按钮组件)
8. [字体规范](#字体规范)
9. [修改指南](#修改指南)

---

## 主窗口框架

### 窗口尺寸
- **最小尺寸**: 1200px × 800px
- **默认尺寸**: 1400px × 900px
- **控制代码**: `ui/main_window.py` 第125-127行
```python
self.setMinimumSize(1200, 800)
self.resize(1400, 900)
```

### 主布局结构
- **布局类型**: QHBoxLayout（水平布局）
- **边距**: 0px（所有方向）
- **间距**: 0px
- **控制代码**: `ui/main_window.py` 第80-82行
```python
main_layout.setContentsMargins(0, 0, 0, 0)
main_layout.setSpacing(0)
```

### 主要区域划分
1. **侧边栏**: 固定宽度280px（展开）/ 80px（收起）
2. **主内容区**: 自适应宽度（拉伸填充）

---

## 侧边栏组件

### 整体尺寸
- **展开宽度**: 280px
- **收起宽度**: 80px
- **高度**: 100%（填充整个窗口高度）
- **控制代码**: `ui/components/sidebar.py` 第277行
```python
self.setFixedWidth(280)
```

### 侧边栏主布局
- **布局类型**: QVBoxLayout（垂直布局）
- **边距**: 0px（所有方向）
- **间距**: 0px
- **控制代码**: `ui/components/sidebar.py` 第280-282行

### 头部区域（Logo区域）
- **高度**: 120px
- **内边距**: 24px（左右）, 32px（上下）
- **组件间距**: 16px
- **控制代码**: `ui/components/sidebar.py` 第312-316行
```python
header.setFixedHeight(120)
layout.setContentsMargins(24, 32, 24, 32)
layout.setSpacing(16)
```

#### Logo组件
- **尺寸**: 56px × 56px
- **字体**: Microsoft YaHei, 20px, Bold
- **内容**: "BCI"
- **控制代码**: `ui/components/sidebar.py` 第321-326行

#### 标题文字
- **主标题字体**: Microsoft YaHei, 14px, Bold
- **副标题字体**: Microsoft YaHei, 7px, Normal
- **内容**: "脑机接口康复系统" / "BCI-based Rehabilitation Training"

### 导航区域
- **顶部边距**: 24px
- **区段间距**: 24px
- **控制代码**: `ui/components/sidebar.py` 第298行, 第440行

#### 导航项（NavigationItem）
- **最小高度**: 56px
- **内边距**: 24px（左右）, 16px（上下）
- **组件间距**: 16px
- **外边距**: 0px 16px 8px 16px
- **控制代码**: `ui/components/sidebar.py` 第42行, 第47-48行
```python
self.setMinimumHeight(56)
layout.setContentsMargins(24, 16, 24, 16)
layout.setSpacing(16)
```

##### 导航项图标
- **尺寸**: 28px × 28px
- **字体**: Microsoft YaHei, 16px
- **内容**: 特殊符号（●, ■, ♦, ★, ◆, ✕）

##### 导航项文字
- **字体**: Microsoft YaHei, 12px, Medium
- **内容**: "患者管理", "治疗系统", "报告分析", "用户管理", "系统设置", "退出系统"

##### 导航项徽章
- **最小尺寸**: 24px × 20px
- **最大高度**: 20px
- **字体**: Microsoft YaHei, 8px, DemiBold
- **内边距**: 2px 6px（CSS控制）
- **内容**: 数字（如"24"）

#### 收起状态调整
- **导航项外边距**: 0px 6px 8px 6px
- **头部内边距**: 12px（左右）, 32px（上下）
- **控制代码**: `ui/components/sidebar.py` 第253行, 第530行

### 底部用户信息区域
- **高度**: 120px
- **内边距**: 24px（所有方向）
- **控制代码**: `ui/components/sidebar.py` 第359-362行

#### 用户信息卡片
- **高度**: 72px
- **内边距**: 16px（所有方向）
- **组件间距**: 16px
- **控制代码**: `ui/components/sidebar.py` 第157行, 第165-166行

##### 用户头像
- **尺寸**: 44px × 44px
- **字体**: Microsoft YaHei, 15px, Bold
- **内容**: 用户名首字母（如"Dr"）

##### 用户信息文字
- **用户名字体**: Microsoft YaHei, 12px, DemiBold
- **角色字体**: Microsoft YaHei, 10px, Normal
- **文字间距**: 2px

##### 状态指示器
- **尺寸**: 8px × 8px
- **形状**: 圆形

---

## 顶部栏组件

### 整体尺寸
- **高度**: 80px
- **宽度**: 100%（填充剩余宽度）
- **内边距**: 32px（左右）, 0px（上下）
- **区域间距**: 24px
- **控制代码**: `ui/components/topbar.py` 第250行, 第254-255行
```python
self.setFixedHeight(80)
layout.setContentsMargins(32, 0, 32, 0)
layout.setSpacing(24)
```

### 左侧区域
- **组件间距**: 24px
- **控制代码**: `ui/components/topbar.py` 第274行

#### 菜单切换按钮
- **尺寸**: 48px × 48px
- **字体**: Microsoft YaHei, 16px
- **内容**: "☰"
- **控制代码**: `ui/components/topbar.py` 第24行, 第29行

#### 页面标题区域
- **布局**: 垂直布局
- **内边距**: 0px（当前设置，可调整）
- **组件间距**: 4px
- **对齐方式**: 垂直居中
- **控制代码**: `ui/components/topbar.py` 第285-287行
```python
title_layout.setContentsMargins(0, 0, 0, 0)  # 可修改为(0, 12, 0, 0)增加上边距
title_layout.setSpacing(4)
title_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)
```

##### 主标题
- **字体**: Microsoft YaHei, 22px, Bold
- **内容**: "患者管理", "治疗系统", "报告分析", "用户管理", "系统设置"

##### 副标题
- **字体**: Microsoft YaHei, 12px, Normal
- **内容**: "Patient Management", "Treatment System", "Report Analysis", "User Management", "System Settings"

### 中央区域（系统状态）
- **对齐方式**: 居中
- **组件间距**: 24px
- **控制代码**: `ui/components/topbar.py` 第315行

#### 状态项
- **内边距**: 8px（左）, 8px（上下）, 16px（右）
- **组件间距**: 8px
- **控制代码**: `ui/components/topbar.py` 第46-47行

##### 状态点
- **尺寸**: 8px × 8px
- **形状**: 圆形

##### 状态文字
- **字体**: Microsoft YaHei, 11px, DemiBold
- **内容**: "设备断开", "信号断开"

### 右侧区域（主题切换）
- **组件间距**: 20px
- **控制代码**: `ui/components/topbar.py` 第331行

#### 主题切换器
- **内边距**: 8px（左）, 8px（上下）, 16px（右）
- **组件间距**: 12px
- **控制代码**: `ui/components/topbar.py` 第153-154行

##### 滑动开关
- **尺寸**: 56px × 28px
- **滑块尺寸**: 24px × 24px
- **控制代码**: `ui/components/topbar.py` 第88行, 第94行

##### 主题标签
- **字体**: Microsoft YaHei, 11px, DemiBold
- **内容**: "医疗", "科技"

---

## 页面内容区域

### 整体布局
- **布局类型**: QVBoxLayout
- **边距**: 0px（所有方向）
- **间距**: 0px
- **控制代码**: `ui/main_window.py` 第110-112行

### 页面堆栈
- **背景**: 透明
- **边框**: 无
- **控制代码**: `ui/main_window.py` 第115-117行

---

## 卡片组件

### 患者卡片（PatientCard）
- **边框半径**: 16px
- **外边距**: 0px 0px 16px 0px
- **边框**: 2px solid transparent（默认）/ accent_color（选中）
- **CSS控制**: `ui/themes/theme_manager.py` 第838-852行
```css
QFrame#patient_card {
    background-color: {theme_config['bg_tertiary']};
    border: 2px solid transparent;
    border-radius: 16px;
    margin: 0px 0px 16px 0px;
}
```

#### 患者头像
- **尺寸**: 根据卡片大小自适应
- **边框半径**: 14px
- **字体**: Microsoft YaHei, 字重700

#### 患者信息文字
- **姓名字体**: Microsoft YaHei, 字重600
- **信息字体**: Microsoft YaHei, 普通字重

#### 状态徽章
- **内边距**: 4px 8px
- **边框半径**: 6px
- **字体**: 字重600

### 用户卡片（UserCard）
- **边框半径**: 16px
- **外边距**: 0px 0px 12px 0px
- **CSS控制**: `ui/themes/theme_manager.py` 第1056-1071行

### 详情面板卡片
- **边框半径**: 20px
- **边框**: 1px solid border_color
- **CSS控制**: `ui/themes/theme_manager.py` 第895-905行

#### 详情头部
- **边框半径**: 20px 20px 0px 0px
- **底部边框**: 1px solid border_color

#### 详情头像
- **边框半径**: 40px（圆形）

### 治疗记录卡片
- **边框半径**: 12px
- **边框**: 1px solid border_color
- **CSS控制**: `ui/themes/theme_manager.py` 第959-963行

### 历史统计卡片
- **边框半径**: 12px
- **边框**: 1px solid border_color
- **CSS控制**: `ui/themes/theme_manager.py` 第990-994行

---

## 表单组件

### 输入框（QLineEdit）
- **内边距**: 16px 20px
- **边框**: 1px solid border_color
- **边框半径**: 12px
- **字体大小**: 14px
- **CSS控制**: `ui/themes/theme_manager.py` 第444-456行

### 搜索框
- **内边距**: 16px 48px 16px 20px（右侧留空间给搜索图标）
- **CSS控制**: `ui/themes/theme_manager.py` 第1005-1017行

### 文本编辑框（QTextEdit）
- **内边距**: 16px 20px
- **边框**: 1px solid border_color
- **边框半径**: 12px
- **字体大小**: 14px

### 下拉框（QComboBox）
- **内边距**: 16px 20px
- **边框**: 1px solid border_color
- **边框半径**: 12px
- **字体大小**: 14px
- **下拉箭头宽度**: 20px

### 日期选择器（QDateEdit）
- **内边距**: 8px 12px
- **边框**: 1px solid border_color
- **边框半径**: 8px
- **字体**: Microsoft YaHei, 12px
- **下拉按钮宽度**: 20px
- **下拉按钮边距**: 2px

### 单选按钮（QRadioButton）
- **指示器尺寸**: 20px × 20px
- **边框半径**: 10px（圆形）
- **边框**: 3px solid border_color
- **文字间距**: 10px
- **内边距**: 6px 0px
- **字体**: Microsoft YaHei, 字重500

### 复选框（QCheckBox）
- **指示器尺寸**: 20px × 20px
- **边框半径**: 4px
- **边框**: 3px solid border_color
- **文字间距**: 10px
- **内边距**: 6px 0px
- **字体**: Microsoft YaHei, 字重500

---

## 按钮组件

### 普通按钮
- **内边距**: 12px 24px
- **边框**: 1px solid border_color
- **边框半径**: 12px
- **字体**: 字重500
- **CSS控制**: `ui/themes/theme_manager.py` 第640-658行

### 主要按钮（btn_primary）
- **内边距**: 10px 16px
- **边框**: 无
- **边框半径**: 8px
- **最小高度**: 28px
- **最小宽度**: 120px
- **字体**: 13px, Bold
- **CSS控制**: `ui/themes/theme_manager.py` 第661-681行

### 次要按钮（btn_secondary）
- **内边距**: 8px 12px
- **边框**: 1px solid border_color
- **边框半径**: 8px
- **最小高度**: 28px
- **最小宽度**: 60px
- **字体**: 12px, Normal

### 小型按钮（加减号按钮）
- **尺寸**: 28px × 28px（固定）
- **内边距**: 0px
- **边框**: 2px solid border_color
- **边框半径**: 6px
- **字体**: Microsoft YaHei, 16px, Bold
- **CSS控制**: `ui/themes/theme_manager.py` 第1231-1262行

### 分页按钮
- **内边距**: 8px 16px
- **边框**: 1px solid border_color
- **边框半径**: 8px
- **最小高度**: 20px
- **字体**: Microsoft YaHei, 13px, 字重500

---

## 字体规范

### 字体族
- **主字体**: Microsoft YaHei
- **备用字体**: Source Han Sans SC, Noto Sans CJK SC, PingFang SC, sans-serif

### 字体大小层级
- **超大标题**: 22px（页面主标题）
- **大标题**: 20px（Logo文字）
- **中标题**: 14px-16px（区段标题、图标）
- **正文**: 12px-13px（导航文字、按钮文字）
- **小字**: 10px-11px（副标题、状态文字）
- **微字**: 7px-8px（徽章、Logo副标题）

### 字重规范
- **Bold/700**: Logo、主标题、重要信息
- **DemiBold/600**: 用户名、卡片标题
- **Medium/500**: 导航文字、按钮文字
- **Normal/400**: 普通文字、描述信息

---

## 修改指南

### 1. 修改组件尺寸
在对应的Python文件中找到组件初始化代码：
```python
# 修改固定尺寸
widget.setFixedSize(width, height)
widget.setFixedWidth(width)
widget.setFixedHeight(height)

# 修改最小/最大尺寸
widget.setMinimumSize(width, height)
widget.setMaximumSize(width, height)
```

### 2. 修改布局间距
```python
# 修改内边距（左、上、右、下）
layout.setContentsMargins(left, top, right, bottom)

# 修改组件间距
layout.setSpacing(spacing)
```

### 3. 修改CSS样式
在 `ui/themes/theme_manager.py` 中找到对应的CSS规则：
```css
/* 修改内边距 */
QWidget#component_name {
    padding: 10px 20px;  /* 上下10px，左右20px */
}

/* 修改外边距 */
QWidget#component_name {
    margin: 10px 20px;   /* 上下10px，左右20px */
}

/* 修改尺寸 */
QWidget#component_name {
    min-width: 100px;
    min-height: 50px;
}
```

### 4. 修改字体
```python
# 在Python代码中
font = QFont("Microsoft YaHei", size, weight)
widget.setFont(font)
```

### 5. 常用修改位置
- **侧边栏宽度**: `ui/components/sidebar.py` 第277行
- **顶部栏高度**: `ui/components/topbar.py` 第250行
- **页面标题上边距**: `ui/components/topbar.py` 第285行
- **导航项高度**: `ui/components/sidebar.py` 第42行
- **卡片样式**: `ui/themes/theme_manager.py` CSS部分

### 6. 调试技巧
1. 使用Qt Designer预览效果
2. 在代码中添加临时背景色调试布局
3. 使用`print()`输出组件尺寸信息
4. 逐步调整参数，观察效果变化

---

## 滚动条样式

### 垂直滚动条
- **宽度**: 8px
- **边框半径**: 4px
- **边距**: 0px
- **CSS控制**: `ui/themes/theme_manager.py` 第1441-1465行
```css
QScrollBar:vertical {
    background-color: {theme_config['bg_tertiary']};
    width: 8px;
    border-radius: 4px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: {theme_config['border_color']};
    border-radius: 4px;
    min-height: 20px;
}
```

---

## 页面特定组件

### 患者管理页面

#### 搜索过滤栏
- **内边距**: 0px 26px 16px 26px
- **CSS控制**: `ui/themes/theme_manager.py` 第1503-1506行

#### 分页框架
- **内边距**: 20px 32px
- **顶部边框**: 1px solid border_color
- **CSS控制**: `ui/themes/theme_manager.py` 第1508-1512行

#### 标签页导航框架
- **内边距**: 0px 32px
- **底部边框**: 1px solid border_color
- **CSS控制**: `ui/themes/theme_manager.py` 第1514-1518行

#### 操作按钮框架
- **内边距**: 24px 32px
- **顶部边框**: 1px solid border_color
- **CSS控制**: `ui/themes/theme_manager.py` 第1520-1524行

### 系统设置页面

#### 设置标签页按钮
- **内边距**: 20px 16px
- **外边距**: 0px 4px
- **底部边框**: 3px solid transparent（默认）/ primary_color（激活）
- **字体**: 15px, 字重500
- **CSS控制**: `ui/themes/theme_manager.py` 第1574-1599行

#### 设置输入框
- **内边距**: 12px 16px
- **边框**: 1px solid border_color
- **边框半径**: 8px
- **CSS控制**: `ui/themes/theme_manager.py` 第1644-1656行

#### 设置下拉框
- **内边距**: 12px 16px
- **下拉箭头宽度**: 20px
- **CSS控制**: `ui/themes/theme_manager.py` 第1658-1681行

#### 设置数值框
- **内边距**: 12px 16px
- **边框**: 1px solid border_color
- **边框半径**: 8px
- **CSS控制**: `ui/themes/theme_manager.py` 第1692-1704行

### 治疗页面组件

#### 过滤器组件
- **内边距**: 6px 12px
- **边框**: 1px solid border_color
- **边框半径**: 6px
- **字体**: 11px
- **最小宽度**: 80px（部分组件）
- **CSS控制**: `ui/themes/theme_manager.py` 第1754-1925行

#### 过滤器复选框
- **指示器尺寸**: 16px × 16px
- **边框**: 2px solid border_color
- **边框半径**: 3px
- **文字间距**: 8px

---

## 间距系统规范

### 标准间距值
- **微间距**: 2px-4px（文字行间距、小元素间距）
- **小间距**: 8px（状态点、小组件间距）
- **标准间距**: 12px-16px（常用组件间距）
- **中等间距**: 20px-24px（区域间距、大组件间距）
- **大间距**: 32px（页面边距、主要区域间距）
- **超大间距**: 40px+（页面分区间距）

### 内边距规范
- **紧凑内边距**: 4px-8px（徽章、小按钮）
- **标准内边距**: 12px-16px（输入框、普通按钮）
- **宽松内边距**: 20px-32px（卡片、容器）

### 外边距规范
- **组件外边距**: 8px-16px（列表项、卡片间距）
- **区域外边距**: 24px-32px（页面区域间距）

---

## 颜色和主题变量

### 主题配置位置
- **文件**: `ui/themes/theme_manager.py`
- **医疗主题**: `_get_medical_theme()` 方法（第24-74行）
- **科技主题**: `_get_tech_theme()` 方法（第76-126行）

### 主要颜色变量
```python
# 主色调
"primary_color": "#06b6d4"      # 主要颜色
"primary_light": "#0891b2"      # 主要颜色（浅）
"primary_dark": "#0e7490"       # 主要颜色（深）
"accent_color": "#00d4ff"       # 强调色

# 状态颜色
"success_color": "#00ff88"      # 成功色
"warning_color": "#ffaa00"      # 警告色
"danger_color": "#ff3366"       # 危险色

# 背景色
"bg_primary": "#0f172a"         # 主背景
"bg_secondary": "#1e293b"       # 次背景
"bg_tertiary": "#334155"        # 第三背景
"bg_glass": "rgba(30, 41, 59, 0.95)"  # 玻璃效果

# 文字颜色
"text_primary": "#f1f5f9"       # 主要文字
"text_secondary": "#cbd5e1"     # 次要文字
"text_tertiary": "#94a3b8"      # 第三文字

# 边框颜色
"border_color": "#475569"       # 边框色
```

---

## 动画和过渡效果

### 侧边栏折叠动画
- **动画时长**: 400ms
- **缓动曲线**: OutCubic
- **控制代码**: `ui/components/sidebar.py` 第372-378行
```python
self.collapse_animation.setDuration(400)
self.collapse_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
```

### 主题切换动画
- **滑块动画时长**: 300ms
- **缓动曲线**: OutCubic
- **控制代码**: `ui/components/topbar.py` 第99-100行

---

## 响应式设计考虑

### 最小尺寸限制
- **窗口最小宽度**: 1200px
- **窗口最小高度**: 800px
- **侧边栏最小宽度**: 80px（收起状态）
- **顶部栏固定高度**: 80px

### 自适应组件
- **主内容区**: 自动填充剩余宽度
- **页面内容**: 自动填充剩余高度
- **卡片列表**: 垂直滚动适应内容
- **文字**: 根据容器宽度自动换行

---

## 无障碍设计

### 对比度要求
- **主要文字**: 与背景对比度 ≥ 4.5:1
- **次要文字**: 与背景对比度 ≥ 3:1
- **交互元素**: 与背景对比度 ≥ 3:1

### 焦点指示
- **键盘焦点**: 明显的边框或背景变化
- **鼠标悬停**: 颜色或透明度变化
- **激活状态**: 明显的视觉反馈

### 字体大小
- **最小字体**: 7px（仅用于辅助信息）
- **正文字体**: 12px-14px
- **标题字体**: 16px+

---

## 开发调试技巧

### 1. 布局调试
```python
# 添加临时背景色
widget.setStyleSheet("background-color: red;")

# 添加边框
widget.setStyleSheet("border: 2px solid blue;")

# 打印组件尺寸
print(f"Widget size: {widget.size()}")
print(f"Widget geometry: {widget.geometry()}")
```

### 2. 样式调试
```python
# 强制刷新样式
widget.style().unpolish(widget)
widget.style().polish(widget)
widget.update()

# 检查对象名称
print(f"Object name: {widget.objectName()}")
```

### 3. 布局信息
```python
# 获取布局信息
layout = widget.layout()
print(f"Layout margins: {layout.contentsMargins()}")
print(f"Layout spacing: {layout.spacing()}")
```

### 4. 常见问题解决
1. **组件不显示**: 检查父容器尺寸和布局设置
2. **样式不生效**: 确认objectName设置正确
3. **间距异常**: 检查margin和padding设置
4. **字体显示问题**: 确认字体名称和大小设置

---

## 版本更新记录

### V2.0.0 (当前版本)
- 完整的双主题系统（医疗/科技）
- 响应式侧边栏设计
- 统一的组件样式规范
- 完善的动画过渡效果

### 未来规划
- 支持更多主题色彩
- 增加深色/浅色模式切换
- 优化移动端适配
- 增强无障碍支持

---

*本文档基于脑机接口康复训练系统V2.0.0，最后更新时间：2024年*
*文档维护：UI设计团队*
*如有疑问或建议，请联系开发团队*
