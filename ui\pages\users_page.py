# -*- coding: utf-8 -*-
"""
用户管理页面
Users Management Page

完全按照HTML设计实现的用户管理页面
包含用户列表、详情展示、权限管理等功能
"""

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton,
    QLineEdit, QComboBox, QFrame, QScrollArea, QStackedWidget,
    QGridLayout, QSpacerItem, QSizePolicy, QDialog, QDateEdit,
    QTextEdit, QFormLayout
)
from PySide6.QtCore import Qt, Signal, QTimer, QDate
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor
from PySide6.QtSvgWidgets import QSvgWidget
from typing import Dict, List, Optional
import math

from ui.pages.base_page import BasePage
from services.user_service import user_service
from utils.user_helpers import (
    validate_user_data, format_user_for_ui, get_role_display_name,
    get_status_display_name, get_user_permissions
)


class UserCard(QFrame):
    """用户卡片组件 - 按照HTML设计实现"""

    user_selected = Signal(int)  # 用户ID

    def __init__(self, user_data: Dict):
        super().__init__()
        self.user_data = user_data
        self.is_selected = False

        self._init_ui()
        self._setup_style()

    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("user_card")
        self.setFixedHeight(97)
        self.setCursor(Qt.CursorShape.PointingHandCursor)

        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(16)

        # 用户头像 - 与患者卡片保持一致
        avatar_label = QLabel(self.user_data['avatar'])
        avatar_label.setObjectName("user_avatar")
        avatar_label.setFixedSize(48, 48)  # 与患者卡片一致
        avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar_label.setFont(QFont("Microsoft YaHei", 15, QFont.Weight.Bold))  # 与患者卡片一致
        # 样式通过全局样式表设置
        layout.addWidget(avatar_label)

        # 用户信息区域 - 按照HTML设计调整布局
        info_layout = QVBoxLayout()
        info_layout.setSpacing(4)

        # 用户名 - 与患者卡片保持一致
        name_label = QLabel(self.user_data['username'])
        name_label.setObjectName("user_name")
        name_label.setFont(QFont("Microsoft YaHei", 13, QFont.Weight.DemiBold))  # 与患者卡片一致
        info_layout.addWidget(name_label)

        # 用户信息行 - 与患者卡片保持一致，移除邮箱
        user_id = self.user_data.get('id', 'N/A')
        role_text = self._get_role_text(self.user_data['role'])
        status_text = "启用" if self.user_data['status'] == 'active' else "停用"
        info_text = f"ID: {user_id} | {role_text} | {status_text}"
        info_label = QLabel(info_text)
        info_label.setObjectName("user_info")
        info_label.setFont(QFont("Microsoft YaHei", 11))  # 与患者卡片一致
        # 样式通过全局样式表设置
        info_layout.addWidget(info_label)

        # 移除状态和角色标签区域，信息已合并到用户信息行中

        layout.addLayout(info_layout)

        # 右侧最后登录时间区域 - 按照HTML设计
        login_area = QVBoxLayout()
        login_area.setSpacing(2)

        login_title = QLabel("最后登录")
        login_title.setObjectName("user_login_title")
        login_title.setFont(QFont("Microsoft YaHei", 11))  # 与患者卡片一致
        login_title.setAlignment(Qt.AlignmentFlag.AlignRight)
        login_area.addWidget(login_title)

        login_time = QLabel(self.user_data['lastLogin'])
        login_time.setObjectName("user_login_time")
        login_time.setFont(QFont("Microsoft YaHei", 11))  # 与患者卡片一致
        login_time.setAlignment(Qt.AlignmentFlag.AlignRight)
        login_area.addWidget(login_time)

        layout.addLayout(login_area)

        # 状态指示器
        status_label = QLabel()
        status_label.setObjectName("user_status_dot")
        status_label.setFixedSize(8, 8)
        # 根据状态设置属性，样式通过全局样式表设置
        if self.user_data['status'] == 'active':
            status_label.setProperty("status", "active")
        else:
            status_label.setProperty("status", "inactive")
        layout.addWidget(status_label)

    def _get_role_text(self, role: str) -> str:
        """获取角色显示文本"""
        role_map = {
            'admin': '管理员',
            'doctor': '医生',
            'operator': '操作员'
        }
        return role_map.get(role, role)

    def _setup_style(self):
        """设置样式 - 使用全局样式表"""
        # 样式通过全局样式表设置，不使用硬编码样式
        pass

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.user_selected.emit(self.user_data['id'])
        super().mousePressEvent(event)

    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.is_selected = selected
        # 使用属性设置选中状态，样式通过全局样式表控制
        self.setProperty("selected", selected)
        # 强制刷新样式
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()


class UsersPage(BasePage):
    """用户管理页面 - 完全按照HTML设计实现"""

    def __init__(self):
        # 初始化数据
        self.users_data = []
        self.filtered_users = []
        self.selected_user = None
        self.current_page = 1
        self.users_per_page = 7

        # 模糊遮罩层
        self.blur_overlay = None

        # UI组件
        self.user_cards = []
        self.users_scroll = None
        self.users_container = None
        self.users_layout = None
        self.detail_panel = None
        self.search_input = None
        self.role_filter = None
        self.prev_btn = None
        self.next_btn = None
        self.page_info = None
        self.tab_stack = None
        self.tab_buttons = {}
        self.detail_avatar = None
        self.detail_username = None
        self.detail_email = None
        self.detail_role = None
        self.detail_status = None
        self.detail_info_labels = {}
        self.detail_stats_labels = {}
        self.system_permissions_container = None
        self.data_permissions_container = None
        self.toggle_status_btn = None

        super().__init__("users", "用户管理")
        
        # 检查页面访问权限
        if not self.check_page_access():
            self.handle_access_denied()
            return

    def _init_content(self):
        """初始化页面内容 - 按照HTML设计实现双栏布局"""
        # 清除基类的占位内容
        for i in reversed(range(self.main_layout.count())):
            child = self.main_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 创建主容器
        main_container = QFrame()
        main_container.setObjectName("users_main_container")
        self.main_layout.addWidget(main_container)

        # 主布局 - 双栏布局 (2:1)
        main_layout = QHBoxLayout(main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # 左侧用户列表区域 - 使用与患者管理页面相同的比例
        self._create_users_list_area(main_layout)

        # 右侧用户详情区域 - 使用与患者管理页面相同的比例
        self._create_user_detail_area(main_layout)

        # 初始化数据
        self._load_users()

    def _create_users_list_area(self, parent_layout):
        """创建用户列表区域"""
        # 用户列表卡片 - 使用与患者管理页面相同的样式
        list_card = QFrame()
        list_card.setObjectName("patients_list_card")
        # 样式通过全局样式表设置
        parent_layout.addWidget(list_card, 2)  # 2:1 比例，与患者管理页面相同

        # 卡片布局
        card_layout = QVBoxLayout(list_card)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(0)

        # 卡片头部
        self._create_list_header(card_layout)

        # 搜索和筛选栏
        self._create_search_filter_bar(card_layout)

        # 用户列表滚动区域
        self._create_users_scroll_area(card_layout)

        # 分页控制
        self._create_pagination_controls(card_layout)

    def _create_list_header(self, parent_layout):
        """创建列表头部"""
        header_frame = QFrame()
        header_frame.setObjectName("list_header")
        header_frame.setFixedHeight(70)  # 增加高度以容纳按钮
        # 样式通过全局样式表设置
        parent_layout.addWidget(header_frame)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(32, 6, 32,6)
        header_layout.setSpacing(16)

        # 标题和用户数量
        title_layout = QHBoxLayout()
        title_layout.setSpacing(8)

        # 图标 - 与患者管理页面保持一致
        icon_label = QLabel("👥")
        # 调整图标字体大小以匹配患者管理页面
        icon_font = QFont("Microsoft YaHei", 18)
        icon_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        icon_label.setFont(icon_font)
        title_layout.addWidget(icon_label)

        # 标题 - 与患者管理页面保持一致
        title_label = QLabel("用户列表")
        # 调整列表标题字体大小以匹配患者管理页面
        list_title_font = QFont("Microsoft YaHei", 15, QFont.Weight.Bold)
        list_title_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        title_label.setFont(list_title_font)
        title_layout.addWidget(title_label)

        # 用户数量徽章 - 与患者管理页面保持一致
        self.count_badge = QLabel("0")  # 初始显示0，数据加载后更新
        self.count_badge.setObjectName("count_badge")
        self.count_badge.setFixedSize(32, 20)
        self.count_badge.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 调整徽章字体大小以匹配患者管理页面
        badge_font = QFont("Microsoft YaHei", 10, QFont.Weight.Bold)
        badge_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.count_badge.setFont(badge_font)
        # 样式通过全局样式表设置
        title_layout.addWidget(self.count_badge)

        title_layout.addStretch()
        header_layout.addLayout(title_layout)

        # 操作按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(12)

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setObjectName("btn_secondary")
        refresh_btn.clicked.connect(self._refresh_users)
        buttons_layout.addWidget(refresh_btn)

        # 新增用户按钮 - 仅管理员可见
        try:
            from utils.simple_permission_manager import permission_manager
            if permission_manager.can_perform_operation('user_create'):
                add_btn = QPushButton("➕ 新增用户")
                add_btn.setObjectName("btn_secondary")  # 改为与患者管理页面一致的样式
                add_btn.clicked.connect(self._show_add_user_modal)
                buttons_layout.addWidget(add_btn)
        except Exception as e:
            print(f"权限检查失败: {e}")
            # 默认显示按钮，避免系统崩溃
            add_btn = QPushButton("➕ 新增用户")
            add_btn.setObjectName("btn_secondary")
            add_btn.clicked.connect(self._show_add_user_modal)
            buttons_layout.addWidget(add_btn)

        header_layout.addLayout(buttons_layout)

    def _create_search_filter_bar(self, parent_layout):
        """创建搜索和筛选栏"""
        search_frame = QFrame()
        search_frame.setObjectName("search_filter_bar")
        search_frame.setFixedHeight(65)  # 增加高度以容纳搜索框和下拉框
        # 样式通过全局样式表设置
        parent_layout.addWidget(search_frame)

        search_layout = QHBoxLayout(search_frame)
        # 让搜索框边框与用户卡片边框对齐：用户卡片边距24px + 卡片边框2px = 26px
        search_layout.setContentsMargins(6, 0, 26, 0)  # 搜索框边框与用户卡片边框对齐
        search_layout.setSpacing(6)
        search_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)  # 垂直居中对齐

        # 搜索框 - 与患者管理页面保持一致
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索用户名、角色或邮箱...")
        self.search_input.setObjectName("user_search")
        self.search_input.setFont(QFont("Microsoft YaHei", 14))  # 与患者管理页面一致
        self.search_input.textChanged.connect(self._filter_users)
        search_layout.addWidget(self.search_input, 1)

        # 角色筛选下拉框 - 与患者管理页面保持一致
        self.role_filter = QComboBox()
        self.role_filter.addItems(["全部角色", "管理员", "医生", "操作员"])
        self.role_filter.setObjectName("role_filter")
        self.role_filter.setFont(QFont("Microsoft YaHei", 14))  # 与患者管理页面一致
        # 样式通过全局样式表设置
        self.role_filter.currentTextChanged.connect(self._filter_users)
        search_layout.addWidget(self.role_filter)

    def _create_users_scroll_area(self, parent_layout):
        """创建用户列表滚动区域"""
        # 滚动区域
        self.users_scroll = QScrollArea()
        self.users_scroll.setObjectName("users_scroll")
        self.users_scroll.setWidgetResizable(True)
        self.users_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.users_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        # 样式通过全局样式表设置
        parent_layout.addWidget(self.users_scroll, 1)

        # 用户容器
        self.users_container = QWidget()
        self.users_container.setObjectName("users_container")
        self.users_scroll.setWidget(self.users_container)

        # 用户布局 - 调整上边距以与搜索栏保持一致的间距
        self.users_layout = QVBoxLayout(self.users_container)
        self.users_layout.setContentsMargins(24, 0, 24, 6)  # 上边距改为0，由搜索栏底部间距控制
        self.users_layout.setSpacing(3)  # 与患者管理页面一致
        self.users_layout.addStretch()

    def _create_pagination_controls(self, parent_layout):
        """创建分页控制 - 与患者管理页面保持一致"""
        pagination_area = QFrame()
        pagination_area.setFixedHeight(80)  # 与患者管理页面保持一致
        # 样式通过全局样式表设置
        parent_layout.addWidget(pagination_area)

        layout = QHBoxLayout(pagination_area)
        layout.setContentsMargins(24, 6, 32, 6)  # 与患者管理页面一致
        layout.setSpacing(6)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 与患者管理页面一致

        # 上一页按钮 - 与患者管理页面一致
        self.prev_btn = QPushButton("◀ 上一页")
        self.prev_btn.setFont(QFont("Microsoft YaHei", 10))  # 与患者管理页面一致
        self.prev_btn.setObjectName("pagination_btn")
        self.prev_btn.clicked.connect(self._prev_page)
        layout.addWidget(self.prev_btn)

        # 页面信息 - 与患者管理页面一致
        self.page_info = QLabel("第 1 页，共 1 页")
        self.page_info.setFont(QFont("Microsoft YaHei", 12))  # 与患者管理页面一致
        self.page_info.setObjectName("page_info")
        # 样式通过全局样式表设置
        layout.addWidget(self.page_info)

        # 下一页按钮 - 与患者管理页面一致
        self.next_btn = QPushButton("下一页 ▶")
        self.next_btn.setFont(QFont("Microsoft YaHei", 10))  # 与患者管理页面一致
        self.next_btn.setObjectName("pagination_btn")
        self.next_btn.clicked.connect(self._next_page)
        layout.addWidget(self.next_btn)

    def _create_user_detail_area(self, parent_layout):
        """创建用户详情区域"""
        # 详情卡片 - 使用与患者管理页面相同的样式
        detail_card = QFrame()
        detail_card.setObjectName("patient_detail_card")
        # 样式通过全局样式表设置
        parent_layout.addWidget(detail_card, 1)  # 2:1 比例，与患者管理页面相同

        # 卡片布局
        card_layout = QVBoxLayout(detail_card)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(0)

        # 详情头部
        self._create_detail_header(card_layout)

        # 标签页导航
        self._create_tab_navigation(card_layout)

        # 标签页内容
        self._create_tab_content(card_layout)

        # 操作按钮
        self._create_action_buttons(card_layout)

    def _create_detail_header(self, parent_layout):
        """创建详情头部 - 与患者管理页面保持一致"""
        header_frame = QFrame()
        header_frame.setObjectName("detail_header")
        header_frame.setFixedHeight(70)  # 增加高度以容纳按钮
        # 样式通过全局样式表设置
        parent_layout.addWidget(header_frame)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(32, 6, 32, 6)  # 与患者管理页面保持一致
        header_layout.setSpacing(12)

        # 标题 - 与患者管理页面保持一致
        title_label = QLabel("👤 用户详情")
        # 调整详情标题字体大小以匹配患者管理页面
        detail_title_font = QFont("Microsoft YaHei", 16, QFont.Weight.Bold)
        detail_title_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        title_label.setFont(detail_title_font)
        header_layout.addWidget(title_label, 1)

        # 操作按钮 - 与患者管理页面保持一致
        edit_btn = QPushButton("✏️ 编辑")
        edit_btn.setObjectName("btn_secondary")  # 使用全局样式
        edit_btn.clicked.connect(self._edit_user)
        # 不设置字体，让全局样式控制
        header_layout.addWidget(edit_btn)

        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setObjectName("btn_secondary")  # 使用全局样式
        refresh_btn.clicked.connect(self._refresh_user_details)
        # 不设置字体，让全局样式控制
        header_layout.addWidget(refresh_btn)

    def _refresh_user_details(self):
        """刷新用户详情"""
        if self.selected_user:
            self._update_user_details()
            print(f"用户详情已刷新: {self.selected_user['username']}")
        else:
            print("请先选择一个用户")

    def _create_tab_navigation(self, parent_layout):
        """创建标签页导航"""
        tab_nav_frame = QFrame()
        tab_nav_frame.setObjectName("tab_nav_frame")
        # 样式通过全局样式表设置
        parent_layout.addWidget(tab_nav_frame)

        tab_nav_layout = QHBoxLayout(tab_nav_frame)
        tab_nav_layout.setContentsMargins(32, 0, 32, 0)
        tab_nav_layout.setSpacing(0)

        # 标签页按钮
        self.tab_buttons = {}
        tabs = [
            ("basic", "基本信息"),
            ("permissions", "权限管理"),
            ("activity", "活动记录")
        ]

        for tab_id, tab_name in tabs:
            btn = QPushButton(tab_name)
            btn.setObjectName("user_tab_btn")
            btn.setProperty("tab_id", tab_id)
            # 设置字体 - 与患者管理页面保持一致
            tab_font = QFont("Microsoft YaHei", 11)
            tab_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
            btn.setFont(tab_font)
            btn.clicked.connect(lambda checked=False, tid=tab_id: self._switch_tab(tid))
            # 样式通过全局样式表设置
            self.tab_buttons[tab_id] = btn
            tab_nav_layout.addWidget(btn)

        tab_nav_layout.addStretch()

    def _create_tab_content(self, parent_layout):
        """创建标签页内容"""
        # 标签页内容堆叠
        self.tab_stack = QStackedWidget()
        self.tab_stack.setObjectName("user_tab_stack")
        parent_layout.addWidget(self.tab_stack, 1)

        # 基本信息标签页
        self._create_basic_info_tab()

        # 权限管理标签页
        self._create_permissions_tab()

        # 活动记录标签页
        self._create_activity_tab()

        # 设置默认激活标签
        self._switch_tab("basic")

    def _create_basic_info_tab(self):
        """创建基本信息标签页"""
        basic_widget = QWidget()
        basic_layout = QVBoxLayout(basic_widget)
        basic_layout.setContentsMargins(32, 24, 32, 24)
        basic_layout.setSpacing(20)

        # 基本信息卡片
        info_card = QFrame()
        info_card.setObjectName("info_card")
        # 样式通过全局样式表设置
        basic_layout.addWidget(info_card)

        info_layout = QVBoxLayout(info_card)
        info_layout.setContentsMargins(16, 16, 16, 16)
        info_layout.setSpacing(12)

        # 信息项
        info_items = [
            ("用户ID", "detail_user_id"),
            ("创建时间", "detail_user_created"),
            ("最后登录", "detail_user_last_login")
        ]

        self.detail_info_labels = {}
        for label_text, label_id in info_items:
            item_layout = QHBoxLayout()
            item_layout.setSpacing(16)

            # 标签 - 与患者详情保持一致
            label = QLabel(label_text)
            label.setObjectName("field_label")
            label.setFont(QFont("Microsoft YaHei", 11))  # 与患者详情一致
            label.setFixedWidth(80)
            # 样式通过全局样式表设置
            item_layout.addWidget(label)

            # 值 - 与患者详情保持一致
            value_label = QLabel("")
            value_label.setObjectName("field_value")
            value_label.setFont(QFont("Microsoft YaHei", 13, QFont.Weight.DemiBold))  # 与患者详情一致
            # 样式通过全局样式表设置
            self.detail_info_labels[label_id] = value_label
            item_layout.addWidget(value_label)
            item_layout.addStretch()

            info_layout.addLayout(item_layout)

        basic_layout.addStretch()
        self.tab_stack.addWidget(basic_widget)

    def _create_permissions_tab(self):
        """创建权限管理标签页"""
        permissions_widget = QWidget()
        permissions_layout = QVBoxLayout(permissions_widget)
        permissions_layout.setContentsMargins(32, 24, 32, 24)
        permissions_layout.setSpacing(20)

        # 系统权限卡片
        system_card = QFrame()
        system_card.setObjectName("permission_card")
        # 样式通过全局样式表设置
        permissions_layout.addWidget(system_card)

        system_layout = QVBoxLayout(system_card)
        system_layout.setContentsMargins(16, 16, 16, 16)
        system_layout.setSpacing(12)

        # 系统权限标题
        system_title = QLabel("系统权限")
        system_title.setObjectName("permission_title")
        system_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        # 样式通过全局样式表设置
        system_layout.addWidget(system_title)

        # 系统权限标签容器
        self.system_permissions_container = QFrame()
        system_permissions_layout = QHBoxLayout(self.system_permissions_container)
        system_permissions_layout.setContentsMargins(0, 0, 0, 0)
        system_permissions_layout.setSpacing(8)
        system_permissions_layout.addStretch()
        system_layout.addWidget(self.system_permissions_container)

        # 数据权限卡片
        data_card = QFrame()
        data_card.setObjectName("permission_card")
        # 样式通过全局样式表设置
        permissions_layout.addWidget(data_card)

        data_layout = QVBoxLayout(data_card)
        data_layout.setContentsMargins(16, 16, 16, 16)
        data_layout.setSpacing(12)

        # 数据权限标题
        data_title = QLabel("数据权限")
        data_title.setObjectName("permission_title")
        data_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        # 样式通过全局样式表设置
        data_layout.addWidget(data_title)

        # 数据权限标签容器
        self.data_permissions_container = QFrame()
        data_permissions_layout = QHBoxLayout(self.data_permissions_container)
        data_permissions_layout.setContentsMargins(0, 0, 0, 0)
        data_permissions_layout.setSpacing(8)
        data_permissions_layout.addStretch()
        data_layout.addWidget(self.data_permissions_container)

        permissions_layout.addStretch()
        self.tab_stack.addWidget(permissions_widget)

    def _create_activity_tab(self):
        """创建活动记录标签页"""
        activity_widget = QWidget()
        activity_layout = QVBoxLayout(activity_widget)
        activity_layout.setContentsMargins(32, 24, 32, 24)
        activity_layout.setSpacing(20)

        # 活动统计卡片
        stats_card = QFrame()
        stats_card.setObjectName("stats_card")
        # 样式通过全局样式表设置
        activity_layout.addWidget(stats_card)

        stats_layout = QVBoxLayout(stats_card)
        stats_layout.setContentsMargins(16, 16, 16, 16)
        stats_layout.setSpacing(12)

        # 统计标题
        stats_title = QLabel("登录统计")
        stats_title.setObjectName("stats_title")
        stats_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        stats_layout.addWidget(stats_title)

        # 统计项
        stats_items = [
            ("登录次数", "detail_login_count"),
            ("在线时长", "detail_online_time"),
            ("最后IP", "detail_last_ip"),
            ("设备类型", "detail_device_type")
        ]

        self.detail_stats_labels = {}
        for label_text, label_id in stats_items:
            item_layout = QHBoxLayout()
            item_layout.setSpacing(16)

            # 标签 - 与患者详情保持一致
            label = QLabel(label_text)
            label.setObjectName("field_label")
            label.setFont(QFont("Microsoft YaHei", 11))  # 与患者详情一致
            label.setFixedWidth(80)
            # 样式通过全局样式表设置
            item_layout.addWidget(label)

            # 值 - 与患者详情保持一致
            value_label = QLabel("")
            value_label.setObjectName("field_value")
            value_label.setFont(QFont("Microsoft YaHei", 13, QFont.Weight.DemiBold))  # 与患者详情一致
            # 样式通过全局样式表设置
            self.detail_stats_labels[label_id] = value_label
            item_layout.addWidget(value_label)
            item_layout.addStretch()

            stats_layout.addLayout(item_layout)



        activity_layout.addStretch()
        self.tab_stack.addWidget(activity_widget)

    def _create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        buttons_frame = QFrame()
        buttons_frame.setObjectName("action_buttons_frame")
        # 样式通过全局样式表设置
        parent_layout.addWidget(buttons_frame)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(32, 24, 32, 24)
        buttons_layout.setSpacing(12)

        # 停用/启用用户按钮 - 仅管理员可见
        try:
            from utils.simple_permission_manager import permission_manager
            if permission_manager.can_perform_operation('user_toggle_status'):
                self.toggle_status_btn = QPushButton("停用用户")
                self.toggle_status_btn.setObjectName("btn_warning")
                # 样式通过全局样式表设置
                self.toggle_status_btn.clicked.connect(self._toggle_user_status)
                buttons_layout.addWidget(self.toggle_status_btn, 1)
            else:
                self.toggle_status_btn = None
        except Exception as e:
            print(f"权限检查失败: {e}")
            # 默认显示按钮，避免系统崩溃
            self.toggle_status_btn = QPushButton("停用用户")
            self.toggle_status_btn.setObjectName("btn_warning")
            self.toggle_status_btn.clicked.connect(self._toggle_user_status)
            buttons_layout.addWidget(self.toggle_status_btn, 1)

        # 编辑用户按钮 - 仅管理员可见
        try:
            from utils.simple_permission_manager import permission_manager
            if permission_manager.can_perform_operation('user_edit'):
                edit_btn = QPushButton("✏️ 编辑用户")
                edit_btn.setObjectName("btn_secondary")
                edit_btn.clicked.connect(self._edit_user)
                buttons_layout.addWidget(edit_btn)
        except Exception as e:
            print(f"权限检查失败: {e}")
            # 默认显示按钮，避免系统崩溃
            edit_btn = QPushButton("✏️ 编辑用户")
            edit_btn.setObjectName("btn_secondary")
            edit_btn.clicked.connect(self._edit_user)
            buttons_layout.addWidget(edit_btn)

    def _load_users(self):
        """加载用户数据"""
        try:
            # 从数据库获取用户数据
            self.users_data = user_service.get_all_users(include_inactive=True)
        except Exception as e:
            print(f"❌ 加载用户数据失败: {e}")
            # 如果数据库加载失败，使用空列表
            self.users_data = []

        # 更新用户数量徽章
        if hasattr(self, 'count_badge'):
            self.count_badge.setText(str(len(self.users_data)))

        # 应用筛选
        self._filter_users()

    def _filter_users(self):
        """筛选用户"""
        search_text = self.search_input.text().lower() if self.search_input else ""
        role_filter = self.role_filter.currentText() if self.role_filter else "全部角色"

        # 筛选逻辑
        self.filtered_users = []
        for user in self.users_data:
            # 搜索筛选
            if search_text:
                if not (search_text in user['username'].lower() or
                       search_text in user['email'].lower() or
                       search_text in self._get_role_text(user['role']).lower()):
                    continue

            # 角色筛选
            if role_filter != "全部角色":
                role_map = {"管理员": "admin", "医生": "doctor", "操作员": "operator"}
                if user['role'] != role_map.get(role_filter):
                    continue

            self.filtered_users.append(user)

        # 重置到第一页
        self.current_page = 1
        self._update_user_list()
        self._update_pagination()

    def _update_user_list(self):
        """更新用户列表显示"""
        # 清除现有卡片
        for card in self.user_cards:
            card.setParent(None)
        self.user_cards.clear()

        # 计算当前页的用户
        start_idx = (self.current_page - 1) * self.users_per_page
        end_idx = start_idx + self.users_per_page
        current_users = self.filtered_users[start_idx:end_idx]

        # 创建用户卡片
        for user in current_users:
            card = UserCard(user)
            card.user_selected.connect(self._on_user_selected)
            self.user_cards.append(card)
            # 插入到stretch之前
            self.users_layout.insertWidget(self.users_layout.count() - 1, card)

        # 如果有选中的用户，保持选中状态
        if self.selected_user:
            for card in self.user_cards:
                if card.user_data['id'] == self.selected_user['id']:
                    card.set_selected(True)
                    break

    def _update_pagination(self):
        """更新分页信息 - 与患者管理页面保持一致"""
        total_users = len(self.filtered_users)
        total_pages = math.ceil(total_users / self.users_per_page) if total_users > 0 else 1

        # 更新页面信息 - 与患者管理页面格式一致
        if self.page_info:
            self.page_info.setText(f"第 {self.current_page} 页，共 {total_pages} 页")

        # 更新按钮状态
        if self.prev_btn:
            self.prev_btn.setEnabled(self.current_page > 1)
        if self.next_btn:
            self.next_btn.setEnabled(self.current_page < total_pages)

    def _prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self._update_user_list()
            self._update_pagination()

    def _next_page(self):
        """下一页"""
        total_pages = math.ceil(len(self.filtered_users) / self.users_per_page)
        if self.current_page < total_pages:
            self.current_page += 1
            self._update_user_list()
            self._update_pagination()

    def _on_user_selected(self, user_id: int):
        """用户选中事件"""
        # 取消所有卡片的选中状态
        for card in self.user_cards:
            card.set_selected(False)

        # 设置选中的卡片
        for card in self.user_cards:
            if card.user_data['id'] == user_id:
                card.set_selected(True)
                self.selected_user = card.user_data
                break

        # 更新详情面板
        self._update_user_details()

    def _update_user_details(self):
        """更新用户详情"""
        if not self.selected_user:
            return

        user = self.selected_user

        # 更新按钮状态（如果按钮存在）
        if hasattr(self, 'toggle_status_btn') and self.toggle_status_btn:
            if user['status'] == 'active':
                self.toggle_status_btn.setText("停用用户")
            else:
                self.toggle_status_btn.setText("启用用户")

        # 更新基本信息
        self.detail_info_labels['detail_user_id'].setText(str(user['id']))

        # 处理created字段 - 兼容不同的数据来源
        created_date = user.get('created', user.get('created_at', ''))
        self.detail_info_labels['detail_user_created'].setText(created_date)

        # 处理lastLogin字段 - 兼容不同的数据来源
        last_login = user.get('lastLogin', user.get('last_login_at', 'Never'))
        self.detail_info_labels['detail_user_last_login'].setText(last_login)

        # 更新活动记录 - 兼容不同的数据来源
        login_count = user.get('loginCount', user.get('login_count', 0))
        self.detail_stats_labels['detail_login_count'].setText(str(login_count))

        online_time = user.get('onlineTime', self._format_online_time(user.get('total_online_minutes', 0)))
        self.detail_stats_labels['detail_online_time'].setText(online_time)

        last_ip = user.get('lastIP', user.get('last_login_ip', 'N/A'))
        self.detail_stats_labels['detail_last_ip'].setText(last_ip)

        device_type = user.get('deviceType', user.get('last_device_type', 'Unknown'))
        self.detail_stats_labels['detail_device_type'].setText(device_type)

        # 更新权限标签
        self._update_permissions_display(user['role'])

    def _update_permissions_display(self, role: str):
        """更新权限显示"""
        # 清除现有权限标签
        for i in reversed(range(self.system_permissions_container.layout().count())):
            child = self.system_permissions_container.layout().itemAt(i).widget()
            if child and child.objectName() == "permission_tag":
                child.setParent(None)

        for i in reversed(range(self.data_permissions_container.layout().count())):
            child = self.data_permissions_container.layout().itemAt(i).widget()
            if child and child.objectName() == "permission_tag":
                child.setParent(None)

        # 使用工具函数获取权限
        permissions = get_user_permissions(role)
        system_permissions = permissions.get('system', [])
        data_permissions = permissions.get('data', [])

        # 添加系统权限标签
        for perm in system_permissions:
            tag = QLabel(perm)
            tag.setObjectName("permission_tag")
            tag.setProperty("permission_type", "system")
            # 样式通过全局样式表设置
            self.system_permissions_container.layout().insertWidget(
                self.system_permissions_container.layout().count() - 1, tag)

        # 添加数据权限标签
        for perm in data_permissions:
            tag = QLabel(perm)
            tag.setObjectName("permission_tag")
            tag.setProperty("permission_type", "data")
            # 样式通过全局样式表设置
            self.data_permissions_container.layout().insertWidget(
                self.data_permissions_container.layout().count() - 1, tag)

    def _switch_tab(self, tab_id: str):
        """切换标签页"""
        # 更新按钮状态 - 使用属性控制，样式通过全局样式表设置
        for tid, btn in self.tab_buttons.items():
            if tid == tab_id:
                btn.setProperty("active", True)
            else:
                btn.setProperty("active", False)
            # 强制刷新样式
            btn.style().unpolish(btn)
            btn.style().polish(btn)
            btn.update()

        # 切换内容
        tab_index = {"basic": 0, "permissions": 1, "activity": 2}
        if tab_id in tab_index:
            self.tab_stack.setCurrentIndex(tab_index[tab_id])

    # 操作功能方法
    def _refresh_users(self):
        """刷新用户列表"""
        self._load_users()
        print("用户列表已刷新")

    def _show_add_user_modal(self):
        """显示新增用户对话框"""
        # 应用模糊效果
        self._apply_blur_effect()

        dialog = UserEditDialog(self)
        try:
            if dialog.exec() == QDialog.DialogCode.Accepted:
                user_data = dialog.get_user_data()
                self._create_user(user_data)
        finally:
            # 无论对话框如何关闭，都要移除模糊效果
            self._remove_blur_effect()

    def _edit_user(self):
        """编辑用户"""
        if not self.selected_user:
            print("请先选择一个用户")
            return

        # 应用模糊效果
        self._apply_blur_effect()

        dialog = UserEditDialog(self, self.selected_user)
        try:
            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_data = dialog.get_user_data()
                self._update_user(self.selected_user['id'], updated_data)
        finally:
            # 无论对话框如何关闭，都要移除模糊效果
            self._remove_blur_effect()



    def _toggle_user_status(self):
        """切换用户状态"""
        if not self.selected_user:
            print("请先选择一个用户")
            return

        try:
            # 调用用户服务切换状态
            result = user_service.toggle_user_status(self.selected_user['id'])

            if result['success']:
                # 更新本地数据
                new_status = result['new_status']
                self.selected_user['status'] = new_status

                # 更新原始数据
                for user in self.users_data:
                    if user['id'] == self.selected_user['id']:
                        user['status'] = new_status
                        break

                # 刷新显示
                self._update_user_details()
                self._update_user_list()

                action = "启用" if new_status == 'active' else "停用"
                print(f"✅ 用户 {self.selected_user['username']} 已{action}")
            else:
                print(f"❌ 状态切换失败: {result.get('error', '未知错误')}")

        except Exception as e:
            print(f"❌ 切换用户状态失败: {e}")



    def _apply_blur_effect(self):
        """应用模糊效果到背景内容 - 使用半透明遮罩层"""
        # 获取主窗口的main_content区域
        main_window = self._get_main_window()
        if main_window and hasattr(main_window, 'main_content'):
            # 创建半透明遮罩层
            self.blur_overlay = QWidget(main_window.main_content)
            self.blur_overlay.setObjectName("blur_overlay")

            # 设置遮罩层样式
            self.blur_overlay.setStyleSheet("""
                QWidget#blur_overlay {
                    background-color: rgba(0, 0, 0, 0.5);
                    border-radius: 0px;
                }
            """)

            # 设置遮罩层大小和位置
            self.blur_overlay.setGeometry(main_window.main_content.rect())
            self.blur_overlay.show()
            self.blur_overlay.raise_()

    def _remove_blur_effect(self):
        """移除模糊效果"""
        # 移除半透明遮罩层
        if hasattr(self, 'blur_overlay') and self.blur_overlay:
            self.blur_overlay.hide()
            self.blur_overlay.deleteLater()
            self.blur_overlay = None

    def _get_main_window(self):
        """获取主窗口引用"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'main_content'):
                return parent
            parent = parent.parent()
        return None

    def _create_user(self, user_data):
        """创建新用户"""
        try:
            result = user_service.create_user(user_data)
            if result['success']:
                from ui.components.themed_message_box import show_information
                show_information(self, "成功", "用户创建成功！")
                self._load_users()
            else:
                from ui.components.themed_message_box import show_warning
                error_msg = "\n".join([f"{msg}" for field, msg in result['errors'].items()])
                show_warning(self, "创建失败", f"用户创建失败：\n{error_msg}")
        except Exception as e:
            from ui.components.themed_message_box import show_critical
            show_critical(self, "错误", f"创建用户时发生错误：{str(e)}")

    def _update_user(self, user_id, user_data):
        """更新用户信息"""
        try:
            result = user_service.update_user(user_id, user_data)
            if result['success']:
                from ui.components.themed_message_box import show_information
                show_information(self, "成功", "用户信息更新成功！")
                self._load_users()
                # 更新选中的用户数据
                updated_user = user_service.get_user_by_id(user_id)
                if updated_user:
                    self.selected_user = updated_user
                    self._update_user_details()
            else:
                from ui.components.themed_message_box import show_warning
                error_msg = "\n".join([f"{msg}" for field, msg in result['errors'].items()])
                show_warning(self, "更新失败", f"用户信息更新失败：\n{error_msg}")
        except Exception as e:
            from ui.components.themed_message_box import show_critical
            show_critical(self, "错误", f"更新用户信息时发生错误：{str(e)}")

    def _format_online_time(self, minutes: int) -> str:
        """格式化在线时长"""
        if minutes < 60:
            return f"{minutes} 分钟"
        elif minutes < 1440:  # 24小时
            hours = minutes // 60
            return f"{hours} 小时"
        else:
            hours = minutes // 60
            return f"{hours:,} 小时"

    def update_theme(self):
        """更新主题"""
        # 主题更新将通过全局样式表自动应用
        pass


class UserEditDialog(QDialog):
    """用户编辑对话框 - 按照患者管理页面的样式实现"""

    def __init__(self, parent=None, user_data=None):
        super().__init__(parent)
        self.user_data = user_data
        self.is_edit_mode = user_data is not None

        # 验证状态
        self.field_errors = {}
        self.error_labels = {}

        self.setWindowTitle("编辑用户" if self.is_edit_mode else "新增用户")
        self.setModal(True)
        self.setFixedSize(700, 800)
        self.setObjectName("user_edit_dialog")

        # 设置无边框窗口 - 与患者管理页面保持一致
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 应用全局样式
        if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
            self.setStyleSheet(parent.styleSheet())

        self._init_ui()
        self._setup_validation()

        if self.is_edit_mode:
            self._load_user_data()

    def _init_ui(self):
        """初始化UI"""
        # 主容器 - 使用与患者对话框相同的样式
        main_container = QFrame()
        main_container.setObjectName("discharge_dialog_container")

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)

        # 容器布局 - 与患者对话框保持一致
        container_layout = QVBoxLayout(main_container)
        container_layout.setContentsMargins(32, 32, 32, 32)
        container_layout.setSpacing(24)

        # 标题
        self._create_title_area(container_layout)

        # 表单区域
        self._create_form_area(container_layout)

        # 按钮区域
        self._create_button_area(container_layout)

    def _create_title_area(self, parent_layout):
        """创建标题区域"""
        title_layout = QHBoxLayout()

        # 标题 - 与患者对话框保持一致
        title = QLabel("编辑用户" if self.is_edit_mode else "新增用户")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))  # 与患者对话框保持一致
        title.setObjectName("dialog_title")
        title_layout.addWidget(title)

        title_layout.addStretch()

        # 关闭按钮 - 与患者对话框保持一致
        close_btn = QPushButton("✕")
        close_btn.setObjectName("dialog_close_btn")  # 使用与患者对话框相同的样式
        close_btn.setFixedSize(36, 36)  # 与患者对话框保持一致
        close_btn.clicked.connect(self.reject)
        title_layout.addWidget(close_btn)

        parent_layout.addLayout(title_layout)

    def _create_form_area(self, parent_layout):
        """创建表单区域"""
        # 滚动区域
        scroll = QScrollArea()
        scroll.setObjectName("form_scroll")
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 表单容器
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setSpacing(20)

        # 基本信息组
        self._create_basic_info_group(form_layout)

        # 角色权限组
        self._create_role_group(form_layout)

        # 联系信息组
        self._create_contact_group(form_layout)

        # 其他信息组
        self._create_other_group(form_layout)

        form_layout.addStretch()

        scroll.setWidget(form_widget)
        parent_layout.addWidget(scroll)

    def _create_basic_info_group(self, parent_layout):
        """创建基本信息组"""
        group_frame = QFrame()
        group_frame.setObjectName("form_group")

        group_layout = QVBoxLayout(group_frame)
        group_layout.setContentsMargins(20, 16, 20, 16)
        group_layout.setSpacing(16)

        # 组标题
        group_title = QLabel("基本信息")
        group_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))  # 与患者对话框一致
        group_title.setObjectName("form_group_title")
        group_layout.addWidget(group_title)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

        # 用户名
        self.username_edit = QLineEdit()
        self.username_edit.setObjectName("form_input")
        self.username_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.username_edit.setPlaceholderText("请输入用户名")
        if self.is_edit_mode:
            self.username_edit.setEnabled(False)  # 编辑模式下不允许修改用户名
        self._add_form_row(form_layout, "用户名 *", self.username_edit, "username")

        # 密码
        self.password_edit = QLineEdit()
        self.password_edit.setObjectName("form_input")
        self.password_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        if self.is_edit_mode:
            self.password_edit.setPlaceholderText("留空表示不修改密码")
        else:
            self.password_edit.setPlaceholderText("请输入密码")
        self._add_form_row(form_layout, "密码" + (" *" if not self.is_edit_mode else ""), self.password_edit, "password")

        # 姓名
        self.name_edit = QLineEdit()
        self.name_edit.setObjectName("form_input")
        self.name_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.name_edit.setPlaceholderText("请输入真实姓名")
        self._add_form_row(form_layout, "姓名", self.name_edit, "name")

        # 邮箱
        self.email_edit = QLineEdit()
        self.email_edit.setObjectName("form_input")
        self.email_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.email_edit.setPlaceholderText("请输入邮箱地址")
        self._add_form_row(form_layout, "邮箱", self.email_edit, "email")

        group_layout.addLayout(form_layout)
        parent_layout.addWidget(group_frame)

    def _create_role_group(self, parent_layout):
        """创建角色权限组"""
        group_frame = QFrame()
        group_frame.setObjectName("form_group")

        group_layout = QVBoxLayout(group_frame)
        group_layout.setContentsMargins(20, 16, 20, 16)
        group_layout.setSpacing(16)

        # 组标题
        group_title = QLabel("角色权限")
        group_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))  # 与患者对话框一致
        group_title.setObjectName("form_group_title")
        group_layout.addWidget(group_title)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

        # 角色
        self.role_combo = QComboBox()
        self.role_combo.setObjectName("form_combo")
        self.role_combo.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.role_combo.addItems(["operator", "doctor", "admin"])
        self.role_combo.setItemText(0, "操作员")
        self.role_combo.setItemText(1, "医生")
        self.role_combo.setItemText(2, "管理员")
        self._add_form_row(form_layout, "角色 *", self.role_combo, "role")

        # 状态
        self.status_combo = QComboBox()
        self.status_combo.setObjectName("form_combo")
        self.status_combo.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.status_combo.addItems(["active", "inactive"])
        self.status_combo.setItemText(0, "启用")
        self.status_combo.setItemText(1, "停用")
        self._add_form_row(form_layout, "状态 *", self.status_combo, "status")

        group_layout.addLayout(form_layout)
        parent_layout.addWidget(group_frame)

    def _create_contact_group(self, parent_layout):
        """创建联系信息组"""
        group_frame = QFrame()
        group_frame.setObjectName("form_group")

        group_layout = QVBoxLayout(group_frame)
        group_layout.setContentsMargins(20, 16, 20, 16)
        group_layout.setSpacing(16)

        # 组标题
        group_title = QLabel("联系信息")
        group_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))  # 与患者对话框一致
        group_title.setObjectName("form_group_title")
        group_layout.addWidget(group_title)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

        # 电话
        self.phone_edit = QLineEdit()
        self.phone_edit.setObjectName("form_input")
        self.phone_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.phone_edit.setPlaceholderText("请输入联系电话")
        self._add_form_row(form_layout, "电话", self.phone_edit, "phone")

        # 科室
        self.department_edit = QLineEdit()
        self.department_edit.setObjectName("form_input")
        self.department_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.department_edit.setPlaceholderText("请输入所属科室")
        self._add_form_row(form_layout, "科室", self.department_edit, "department")

        group_layout.addLayout(form_layout)
        parent_layout.addWidget(group_frame)

    def _create_other_group(self, parent_layout):
        """创建其他信息组"""
        group_frame = QFrame()
        group_frame.setObjectName("form_group")

        group_layout = QVBoxLayout(group_frame)
        group_layout.setContentsMargins(20, 16, 20, 16)
        group_layout.setSpacing(16)

        # 组标题
        group_title = QLabel("其他信息")
        group_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))  # 与患者对话框一致
        group_title.setObjectName("form_group_title")
        group_layout.addWidget(group_title)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

        # 工号
        self.employee_id_edit = QLineEdit()
        self.employee_id_edit.setObjectName("form_input")
        self.employee_id_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.employee_id_edit.setPlaceholderText("请输入工号")
        self._add_form_row(form_layout, "工号", self.employee_id_edit, "employee_id")

        # 备注
        self.notes_edit = QTextEdit()
        self.notes_edit.setObjectName("form_textarea")
        self.notes_edit.setFont(QFont("Microsoft YaHei", 10))  # 与患者对话框一致
        self.notes_edit.setPlaceholderText("请输入备注信息")
        self.notes_edit.setMaximumHeight(80)
        self._add_form_row(form_layout, "备注", self.notes_edit, "notes")

        group_layout.addLayout(form_layout)
        parent_layout.addWidget(group_frame)

    def _add_form_row(self, form_layout, label_text, widget, field_name):
        """添加表单行"""
        # 标签 - 与患者对话框保持一致
        label = QLabel(label_text)
        label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Medium))  # 与患者对话框一致
        label.setObjectName("form_label")

        # 容器（包含输入框和错误提示）
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(4)

        # 输入框
        container_layout.addWidget(widget)

        # 错误提示标签
        error_label = QLabel()
        error_label.setObjectName("form_error")
        error_label.hide()
        container_layout.addWidget(error_label)

        # 保存错误标签引用
        self.error_labels[field_name] = error_label

        form_layout.addRow(label, container)

    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(12)

        button_layout.addStretch()

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("btn_secondary")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        # 保存按钮
        self.save_btn = QPushButton("保存" if self.is_edit_mode else "添加用户")
        self.save_btn.setObjectName("btn_primary")
        self.save_btn.clicked.connect(self._validate_and_accept)
        button_layout.addWidget(self.save_btn)

        parent_layout.addLayout(button_layout)

    def paintEvent(self, event):
        """绘制圆角背景 - 让全局样式处理背景"""
        # 移除自定义绘制，让全局样式表处理背景
        super().paintEvent(event)

    def _load_user_data(self):
        """加载用户数据到表单"""
        if not self.user_data:
            return

        # 基本信息
        self.username_edit.setText(self.user_data.get('username', ''))
        self.name_edit.setText(self.user_data.get('name', ''))
        self.email_edit.setText(self.user_data.get('email', ''))

        # 角色权限
        role = self.user_data.get('role', 'operator')
        role_index = {'operator': 0, 'doctor': 1, 'admin': 2}.get(role, 0)
        self.role_combo.setCurrentIndex(role_index)

        status = self.user_data.get('status', 'active')
        status_index = {'active': 0, 'inactive': 1}.get(status, 0)
        self.status_combo.setCurrentIndex(status_index)

        # 联系信息
        self.phone_edit.setText(self.user_data.get('phone', ''))
        self.department_edit.setText(self.user_data.get('department', ''))

        # 其他信息
        self.employee_id_edit.setText(self.user_data.get('employee_id', ''))
        self.notes_edit.setText(self.user_data.get('notes', ''))

    def _setup_validation(self):
        """设置实时验证"""
        # 连接输入变化事件
        self.username_edit.textChanged.connect(lambda text: self._validate_field('username'))
        self.password_edit.textChanged.connect(lambda text: self._validate_field('password'))
        self.email_edit.textChanged.connect(lambda text: self._validate_field('email'))
        self.phone_edit.textChanged.connect(lambda text: self._validate_field('phone'))

        # 连接下拉框变化事件
        self.role_combo.currentIndexChanged.connect(lambda index: self._validate_field('role'))
        self.status_combo.currentIndexChanged.connect(lambda index: self._validate_field('status'))

    def _validate_field(self, field_name):
        """验证单个字段"""
        error_message = None

        if field_name == 'username':
            value = self.username_edit.text().strip()
            if not value:
                error_message = "用户名不能为空"
            elif len(value) < 3:
                error_message = "用户名至少3个字符"

        elif field_name == 'password' and not self.is_edit_mode:
            value = self.password_edit.text()
            if not value:
                error_message = "密码不能为空"
            elif len(value) < 6:
                error_message = "密码至少6个字符"

        elif field_name == 'email':
            value = self.email_edit.text().strip()
            if value and '@' not in value:
                error_message = "邮箱格式不正确"

        elif field_name == 'phone':
            value = self.phone_edit.text().strip()
            if value and not value.replace('-', '').replace('+', '').isdigit():
                error_message = "电话号码格式不正确"

        # 更新错误状态
        if error_message:
            self.field_errors[field_name] = error_message
        elif field_name in self.field_errors:
            del self.field_errors[field_name]

        # 更新错误显示
        self._update_error_display(field_name, error_message)

        # 更新保存按钮状态
        self._update_save_button()

        return error_message is None

    def _update_error_display(self, field_name, error_message):
        """更新错误显示"""
        if field_name in self.error_labels:
            error_label = self.error_labels[field_name]
            if error_message:
                error_label.setText(error_message)
                error_label.show()
            else:
                error_label.hide()

    def _update_save_button(self):
        """更新保存按钮状态"""
        has_errors = bool(self.field_errors)
        self.save_btn.setEnabled(not has_errors)

        if has_errors:
            self.save_btn.setToolTip("请修正错误后再保存")
        else:
            self.save_btn.setToolTip("")

    def _validate_and_accept(self):
        """验证所有字段并接受对话框"""
        # 验证所有字段
        self._validate_field('username')
        if not self.is_edit_mode:
            self._validate_field('password')
        self._validate_field('email')
        self._validate_field('phone')
        self._validate_field('role')
        self._validate_field('status')

        # 如果有错误，不接受对话框
        if self.field_errors:
            return

        # 无错误，接受对话框
        self.accept()

    def get_user_data(self):
        """获取用户数据 - 只返回可更新的字段"""
        # 获取角色和状态的实际值
        role_index = self.role_combo.currentIndex()
        role_values = ['operator', 'doctor', 'admin']
        role = role_values[role_index]

        status_index = self.status_combo.currentIndex()
        status_values = ['active', 'inactive']
        status = status_values[status_index]

        # 构建用户数据 - 只包含数据库中实际存在且可更新的字段
        user_data = {}

        # 基本字段（编辑模式下用户名不可修改）
        if not self.is_edit_mode:
            user_data['username'] = self.username_edit.text().strip()

        user_data['role'] = role
        user_data['status'] = status

        # 密码（仅在新增模式或编辑模式下有输入时添加）
        password = self.password_edit.text()
        if password:
            user_data['password'] = password

        # 可选字段 - 只添加非空值
        optional_fields = {
            'name': self.name_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'department': self.department_edit.text().strip(),
            'employee_id': self.employee_id_edit.text().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }

        # 添加非空的可选字段
        for field, value in optional_fields.items():
            if value:
                user_data[field] = value

        return user_data
