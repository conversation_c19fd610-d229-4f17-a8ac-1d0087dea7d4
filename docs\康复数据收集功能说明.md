# 康复数据收集功能说明

## 概述

本系统已集成基于硬件指纹的康复评估数据收集功能，能够在训练过程中自动收集、加密并保存原始EEG数据，用于后续的离线康复评估分析。

## 功能特点

### 🔐 硬件指纹加密保护
- 基于设备硬件指纹（CPU ProcessorID + 磁盘序列号）生成唯一加密密钥
- 使用Fernet对称加密算法保护数据安全
- 每个设备生成的数据只能在原设备或拥有硬件信息的设备上解密

### 🧠 智能数据收集
- 在训练过程中自动收集运动想象和平静状态的原始EEG数据
- 精确的状态标签同步（基于语音提示时机）
- 2秒数据窗口，8通道，125Hz采样率
- 自动时间戳记录和试次编号管理

### 📁 结构化数据存储
- 按患者ID和会话时间组织数据目录
- 分别保存运动想象和平静状态数据
- 生成对应的硬件信息文件用于解密验证

## 数据文件结构

```
data/rehabilitation_raw/
├── {患者ID}/
│   └── {日期}_session_{时间}/
│       ├── motor_imagery_raw.nkd      # 加密的运动想象数据
│       ├── motor_imagery_raw.hwinfo   # 运动想象数据硬件信息
│       ├── rest_raw.nkd              # 加密的平静状态数据
│       ├── rest_raw.hwinfo           # 平静状态数据硬件信息
│       ├── session_info.nkd          # 加密的会话信息
│       └── session_info.hwinfo       # 会话信息硬件信息
```

## 数据内容说明

### 运动想象数据 (motor_imagery_raw.nkd)
- **data**: numpy数组 [n_trials, 8, 250] - 运动想象试次数据
- **timestamps**: numpy数组 [n_trials] - 每个试次的时间戳
- **trial_numbers**: numpy数组 [n_trials] - 试次编号
- **label**: 字符串 "motor_imagery" - 数据标签

### 平静状态数据 (rest_raw.nkd)
- **data**: numpy数组 [n_trials, 8, 250] - 平静状态试次数据
- **timestamps**: numpy数组 [n_trials] - 每个试次的时间戳
- **trial_numbers**: numpy数组 [n_trials] - 试次编号
- **label**: 字符串 "rest" - 数据标签

### 会话信息 (session_info.nkd)
- **patient_id**: 患者ID
- **hospital_id**: 医院标识
- **device_hardware_id**: 设备硬件指纹
- **session_date**: 会话日期
- **session_time**: 会话时间
- **motor_trials**: 运动想象试次数
- **rest_trials**: 平静状态试次数
- **sampling_rate**: 采样率 (125Hz)
- **channels**: 通道列表 ["PZ", "P3", "P4", "C3", "CZ", "C4", "F3", "F4"]
- **data_shape**: 数据形状 [8, 250]

## 使用方法

### 自动启用（推荐）
康复数据收集功能已在训练过程中自动启用，无需额外配置。

### 手动控制（高级用户）
```python
# 在训练会话管理器中启用康复数据收集
session_manager.enable_rehabilitation_data_collection("医院ID")

# 禁用康复数据收集
session_manager.disable_rehabilitation_data_collection()
```

## 数据安全性

### 加密机制
- 每台设备基于硬件指纹生成唯一的加密密钥
- 使用SHA256哈希算法处理硬件信息
- 采用Fernet对称加密，符合工业安全标准

### 硬件信息文件
- 包含设备硬件ID、患者信息、时间戳等元数据
- 用于数据来源验证和解密密钥生成
- 明文存储，便于数据管理和追溯

## 数据传输和分析

### 数据收集流程
1. 在不同医院的设备上进行训练
2. 系统自动收集并加密保存康复数据
3. 手动复制数据文件到主分析设备
4. 在主设备上进行统一的离线分析

### 解密要求
- 需要原设备的硬件指纹信息
- 或者在另一个系统中实现相同的解密算法
- 硬件信息文件提供解密所需的关键信息

## 注意事项

1. **数据完整性**: 确保.nkd和.hwinfo文件成对保存
2. **硬件依赖**: 数据加密基于设备硬件，更换硬件可能影响解密
3. **存储空间**: 原始EEG数据文件较大，注意存储空间管理
4. **隐私保护**: 加密数据包含患者信息，请妥善保管

## 技术支持

如需解密算法实现或数据分析支持，请联系系统开发团队。
