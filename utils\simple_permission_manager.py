# -*- coding: utf-8 -*-
"""
简化权限管理器
Simple Permission Manager

为医疗器械认证优化的轻量级权限管理系统
只包含核心权限验证功能，确保启动速度和运行效率
"""

from typing import Dict, List, Optional
from services.auth_service import auth_service


class SimplePermissionManager:
    """简化权限管理器"""
    
    def __init__(self):
        """初始化权限管理器"""
        self._user_permissions = {}
        self._role_permissions = {
            'admin': {
                'pages': ['patients', 'treatment', 'reports', 'users', 'settings'],
                'operations': ['user_create', 'user_edit', 'user_delete', 'user_toggle_status']
            },
            'doctor': {
                'pages': ['patients', 'treatment', 'reports'],
                'operations': []
            },
            'operator': {
                'pages': ['patients', 'treatment'],
                'operations': []
            }
        }
    
    def get_current_user_role(self) -> Optional[str]:
        """获取当前用户角色"""
        current_user = auth_service.get_current_user()
        return current_user.get('role') if current_user else None
    
    def can_access_page(self, page_id: str) -> bool:
        """检查是否可以访问指定页面"""
        role = self.get_current_user_role()
        if not role:
            return False
        
        allowed_pages = self._role_permissions.get(role, {}).get('pages', [])
        return page_id in allowed_pages
    
    def can_perform_operation(self, operation: str) -> bool:
        """检查是否可以执行指定操作"""
        role = self.get_current_user_role()
        if not role:
            return False
        
        allowed_operations = self._role_permissions.get(role, {}).get('operations', [])
        return operation in allowed_operations
    
    def is_admin(self) -> bool:
        """检查当前用户是否为管理员"""
        return self.get_current_user_role() == 'admin'
    
    def is_doctor(self) -> bool:
        """检查当前用户是否为医生"""
        return self.get_current_user_role() == 'doctor'
    
    def is_operator(self) -> bool:
        """检查当前用户是否为操作员"""
        return self.get_current_user_role() == 'operator'
    
    def get_allowed_pages(self) -> List[str]:
        """获取当前用户允许访问的页面列表"""
        role = self.get_current_user_role()
        if not role:
            return []
        
        return self._role_permissions.get(role, {}).get('pages', [])
    
    def get_allowed_operations(self) -> List[str]:
        """获取当前用户允许执行的操作列表"""
        role = self.get_current_user_role()
        if not role:
            return []
        
        return self._role_permissions.get(role, {}).get('operations', [])
    
    def get_user_info_summary(self) -> Dict:
        """获取用户权限信息摘要"""
        current_user = auth_service.get_current_user()
        if not current_user:
            return {'authenticated': False}
        
        role = current_user.get('role')
        return {
            'authenticated': True,
            'username': current_user.get('username'),
            'name': current_user.get('name'),
            'role': role,
            'is_admin': role == 'admin',
            'is_doctor': role == 'doctor',
            'is_operator': role == 'operator',
            'allowed_pages': self.get_allowed_pages(),
            'allowed_operations': self.get_allowed_operations()
        }


# 创建全局权限管理器实例
permission_manager = SimplePermissionManager() 