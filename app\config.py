# -*- coding: utf-8 -*-
"""
应用配置管理
Application Configuration Management

管理应用程序的配置信息，包括主题、窗口状态、用户偏好等
"""

import json
import os
from pathlib import Path
from typing import Any, Dict
from utils.path_manager import get_config_file_path


class AppConfig:
    """应用配置管理器"""
    
    def __init__(self):
        self.config_file = get_config_file_path()
        self._config_data = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
            except Exception as e:
                print(f"配置文件加载失败: {e}")
                self._config_data = {}
        else:
            # 创建默认配置
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self._config_data = {
            # 主题设置
            "theme": "tech",  # medical 或 tech
            
            # 窗口设置
            "window": {
                "width": 1400,
                "height": 900,
                "x": 100,
                "y": 100,
                "maximized": True  # 默认最大化启动
            },
            
            # 侧边栏设置
            "sidebar": {
                "collapsed": False,
                "width": 280
            },
            
            # 用户界面设置
            "ui": {
                "animation_enabled": True,
                "auto_save": True,
                "language": "zh_CN"
            },
            
            # 系统设置
            "system": {
                "auto_backup": True,
                "log_level": "INFO"
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        data = self._config_data
        
        try:
            for k in keys:
                data = data[k]
            return data
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        data = self._config_data
        
        # 创建嵌套字典路径
        for k in keys[:-1]:
            if k not in data:
                data[k] = {}
            data = data[k]
        
        # 设置值
        data[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def reset(self):
        """重置为默认配置"""
        self._create_default_config()
        self.save()
    
    def get_all(self) -> Dict:
        """获取所有配置"""
        return self._config_data.copy()
