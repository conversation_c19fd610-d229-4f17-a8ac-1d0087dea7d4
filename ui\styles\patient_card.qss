/* 患者卡片样式 - 匹配HTML设计 */

/* 患者卡片主容器 */
QFrame#patient_card {
    background-color: #f1f5f9;
    border: 2px solid transparent;
    border-radius: 16px;
    margin: 8px;
}

/* 悬停效果 */
QFrame#patient_card:hover {
    background-color: #ffffff;
    border: 2px solid transparent;
}

/* 选中状态 */
QFrame#patient_card[selected="true"] {
    background-color: #ffffff;
    border: 2px solid #00d4ff;
}

/* 患者头像 */
QLabel#patient_avatar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #06b6d4, stop:1 #0891b2);
    border-radius: 14px;
    color: white;
    font-weight: bold;
}

/* 患者姓名 */
QLabel#patient_name {
    color: #1e293b;
    font-weight: bold;
}

/* 患者信息 */
QLabel#patient_info {
    color: #64748b;
}

/* 状态标签 */
QLabel#patient_status_label {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: bold;
}

/* 治疗次数标签 */
QLabel#patient_treatment_label {
    background-color: rgba(6, 182, 212, 0.1);
    color: #06b6d4;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: bold;
}

/* 时间值 */
QLabel#patient_time_value {
    color: #475569;
}

/* 悬停按钮容器 */
QFrame#patient_hover_actions {
    background-color: transparent;
    border: none;
}

/* 编辑按钮 */
QPushButton#patient_edit_btn {
    background-color: #f59e0b;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-weight: bold;
}

QPushButton#patient_edit_btn:hover {
    background-color: #d97706;
}

/* 删除按钮 */
QPushButton#patient_delete_btn {
    background-color: #ef4444;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-weight: bold;
}

QPushButton#patient_delete_btn:hover {
    background-color: #dc2626;
}

/* 患者列表卡片容器 */
QFrame#patients_list_card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
}

/* 患者详情卡片容器 */
QFrame#patient_detail_card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
}

/* 滚动区域 */
QScrollArea#patients_scroll {
    border: none;
    background-color: transparent;
}

QScrollArea#patients_scroll QScrollBar:vertical {
    background-color: #f1f5f9;
    width: 8px;
    border-radius: 4px;
}

QScrollArea#patients_scroll QScrollBar::handle:vertical {
    background-color: #e2e8f0;
    border-radius: 4px;
    min-height: 20px;
}

QScrollArea#patients_scroll QScrollBar::handle:vertical:hover {
    background-color: #64748b;
}

/* 分页按钮 */
QPushButton#pagination_btn {
    background-color: #f1f5f9;
    color: #1e293b;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
}

QPushButton#pagination_btn:hover {
    background-color: #ffffff;
}

QPushButton#pagination_btn:disabled {
    background-color: #f1f5f9;
    color: #94a3b8;
}
