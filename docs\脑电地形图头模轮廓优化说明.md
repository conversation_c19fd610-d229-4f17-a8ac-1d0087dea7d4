# 脑电地形图头模轮廓优化说明

## 优化目标

将脑电地形图的头模轮廓显示提前到登录后即可显示，而不需要等到开始治疗并有数据时才显示。

## 优化前的问题

- 脑电地形图在初始状态只显示空白
- 只有在开始治疗并接收到脑电数据后才显示头模轮廓
- 用户体验不佳，界面显得空旷

## 优化后的效果

- 登录后立即显示头模轮廓，提供更好的视觉反馈
- 无论是否有数据，都始终显示头模轮廓
- 有数据时显示实际地形图，无数据时显示空白头模轮廓
- 错误状态和清空显示时也保持头模轮廓显示

## 技术实现

### 修改的文件

1. **ui/components/mne_topography_widget.py**
   - 修改 `_show_waiting_state()` 方法，调用头模轮廓显示
   - 新增 `_show_head_outline()` 方法，生成头模轮廓
   - 新增 `_show_simple_head_outline()` 方法，备用简单轮廓
   - 新增 `_create_head_outline_pixmap()` 方法，使用MNE生成头模轮廓
   - 修改 `_show_error_state()` 方法，显示头模轮廓而不是空白
   - 修改 `_create_simple_topography_display()` 方法，显示头模轮廓
   - 修改 `clear_display()` 方法，避免清空显示

2. **ui/pages/treatment_page.py**
   - 修改 `_update_monitoring_display()` 方法，设备未连接时显示头模轮廓而不是清空

### 核心实现逻辑

```python
def _show_head_outline(self):
    """显示头模轮廓（无数据状态）"""
    try:
        # 检查MNE信息是否可用
        if self.info is None:
            self._show_simple_head_outline()
            return

        # 生成头模轮廓图
        pixmap = self._create_head_outline_pixmap()
        
        if pixmap is not None:
            self.topography_label.setPixmap(pixmap)
        else:
            # 如果MNE地形图失败，使用简单显示
            self._show_simple_head_outline()
            
    except Exception as e:
        self.logger.error(f"显示头模轮廓失败: {e}")
        self._show_simple_head_outline()
```

### 头模轮廓生成

```python
def _create_head_outline_pixmap(self):
    """创建头模轮廓QPixmap"""
    try:
        # 使用零数据创建头模轮廓
        zero_data = np.zeros(8)
        
        # 使用MNE绘制头模轮廓（无数据，只显示轮廓）
        im, cn = mne.viz.plot_topomap(
            zero_data, self.info,
            axes=ax,
            show=False,
            contours=0,  # 不显示等高线
            cmap='RdBu_r',
            sensors=True,  # 显示电极位置
            names=None,
            sphere=None,
            res=64,  # 降低分辨率以提高性能
            extrapolate='auto',
            outlines='head'
        )
        
        # 设置头模轮廓颜色和样式
        # 转换为QPixmap并返回
        
    except Exception as e:
        self.logger.error(f"创建头模轮廓失败: {e}")
        return None
```

### 备用方案

当MNE库不可用或出现错误时，使用CSS样式显示简单的圆形头模轮廓：

```python
def _show_simple_head_outline(self):
    """显示简单的头模轮廓（备用方案）"""
    self.topography_label.clear()
    self.topography_label.setStyleSheet("""
        QLabel {
            border: 2px solid #666666;
            border-radius: 75px;
            background-color: transparent;
            min-width: 150px;
            min-height: 150px;
        }
    """)
```

## 优化特点

### 1. 性能优化
- 使用零数据生成头模轮廓，减少计算开销
- 降低分辨率（res=64）提高生成速度
- 不显示等高线（contours=0）减少渲染时间

### 2. 兼容性保证
- 保留原有的数据更新逻辑
- 提供备用的简单轮廓显示方案
- 异常处理确保系统稳定性

### 3. 用户体验
- 登录后立即显示头模轮廓
- 始终保持一致的视觉效果
- 无论何种状态都不显示空白界面

### 4. 代码复用
- 避免重复的头模创建代码
- 统一的头模轮廓显示逻辑
- 集中的错误处理机制

## 测试验证

创建了完整的测试脚本验证以下功能：
1. 初始状态显示头模轮廓
2. 数据更新时显示实际地形图
3. 清空显示时重新显示头模轮廓
4. 错误状态时显示头模轮廓

所有测试均通过，确保优化功能正常工作。

## 注意事项

1. **MNE版本兼容性**: 移除了不兼容的参数（如`show_names`）
2. **主题适配**: 头模轮廓颜色会根据当前主题自动调整
3. **性能影响**: 优化后的头模轮廓生成对系统性能影响微乎其微
4. **向后兼容**: 保持了原有的所有功能和接口

## 尺寸一致性优化

### 问题
头模轮廓和实际地形图的尺寸不一致，屏幕变化时适应性不佳。

### 解决方案
1. **统一尺寸计算逻辑**: 创建`_create_topography_pixmap_internal`方法，统一处理地形图和头模轮廓的尺寸计算
2. **响应式设计**: 添加`resizeEvent`方法，处理组件尺寸变化时的重新渲染
3. **备用方案优化**: 改进`_show_simple_head_outline`方法，使其也能适应屏幕尺寸变化

### 技术实现

#### 统一的尺寸计算
```python
def _create_topography_pixmap_internal(self, data, is_outline_only=False):
    """创建地形图QPixmap的内部方法（统一处理地形图和头模轮廓）"""
    # 动态计算图形尺寸 - 统一的尺寸计算逻辑
    widget_size = self.topography_label.size()
    fig_size = min(widget_size.width(), widget_size.height()) / 100.0
    fig_size = max(2.0, min(fig_size, 6.0))
    
    # 根据是否只显示轮廓来设置参数
    if is_outline_only:
        contours = 0  # 头模轮廓不显示等高线
        res = 64      # 降低分辨率提高性能
    else:
        contours = 8  # 实际地形图显示等高线
        res = 128     # 提高分辨率
```

#### 响应式尺寸变化
```python
def resizeEvent(self, event):
    """处理组件尺寸变化"""
    super().resizeEvent(event)
    
    # 尺寸变化时重新生成显示内容
    if self.latest_data is not None:
        # 有数据时重新生成地形图
        self.update_data(self.latest_data)
    else:
        # 无数据时重新生成头模轮廓
        self._show_head_outline()
```

#### 自适应备用方案
```python
def _show_simple_head_outline(self):
    """显示简单的头模轮廓（备用方案）"""
    # 获取当前组件尺寸
    widget_size = self.topography_label.size()
    size = min(widget_size.width(), widget_size.height()) - 20
    size = max(150, size)  # 最小尺寸
    radius = size // 2
    
    # 检测主题并设置轮廓颜色
    outline_color = self._get_outline_color()
    
    # 动态设置样式
    self.topography_label.setStyleSheet(f"""
        QLabel {{
            border: 2px solid {outline_color};
            border-radius: {radius}px;
            background-color: transparent;
            min-width: {size}px;
            min-height: {size}px;
            max-width: {size}px;
            max-height: {size}px;
        }}
    """)
```

## 更新记录

- **v1.0**: 初始优化，实现登录后显示头模轮廓
- **v1.1**: 修复MNE版本兼容性问题
- **v1.2**: 完善备用方案和错误处理
- **v1.3**: 统一尺寸计算逻辑，实现头模轮廓和实际地形图尺寸一致性
- **v1.4**: 添加响应式设计，支持屏幕尺寸变化时的自适应重新渲染 