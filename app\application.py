# -*- coding: utf-8 -*-
"""
应用主类
Application Main Class

管理应用程序的生命周期和核心功能
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QObject, Signal

from app.config import AppConfig
from app.background_loader import LoadingManager
from ui.login_window import LoginWindow
from services.auth_service import auth_service


class BCIApplication(QApplication):
    """脑机接口康复训练系统应用主类"""
    
    # 应用信号
    application_started = Signal()
    application_closing = Signal()
    
    def __init__(self, argv):
        super().__init__(argv)

        # 应用配置
        self.config = AppConfig()

        # 主窗口和登录窗口
        self.main_window = None
        self.login_window = None

        # 后台加载管理器
        self.loading_manager = LoadingManager(self.config)

        # 设置应用信息
        self.setApplicationName("脑机接口康复训练系统")


        # 连接信号
        self.aboutToQuit.connect(self._on_about_to_quit)
        self._setup_loading_signals()
    
    def run(self):
        """运行应用程序 - 快速启动登录窗口"""
        try:
            # 总是显示登录窗口
            print("启动登录窗口")
            return self._start_login_flow()

        except Exception as e:
            print(f"应用运行错误: {e}")
            QMessageBox.critical(
                None,
                "启动失败",
                f"应用启动时发生错误：\n{str(e)}"
            )
            return 1
    
    def _setup_loading_signals(self):
        """设置加载管理器信号"""
        self.loading_manager.main_window_ready.connect(self._on_main_window_ready)
        self.loading_manager.loading_progress.connect(self._on_loading_progress)
        self.loading_manager.loading_error.connect(self._on_loading_error)

    def _start_login_flow(self):
        """启动登录流程"""
        # 立即创建并显示登录窗口
        self.login_window = LoginWindow()
        self.login_window.login_success.connect(self._on_login_success)
        self.login_window.login_failed.connect(self._on_login_failed)

        # 显示登录窗口
        self.login_window.show()

        # 立即开始后台加载
        self.loading_manager.start_background_loading()

        # 启动事件循环
        return self.exec()



    def _apply_theme(self):
        """应用主题样式"""
        if not self.main_window:
            return

        # 如果主窗口已经有准备好的样式表，直接使用
        if hasattr(self.main_window, '_prepared_stylesheet'):
            self.setStyleSheet(self.main_window._prepared_stylesheet)
            return

        # 否则重新加载主题
        theme_name = self.config.get('theme', 'tech')
        try:
            from ui.themes.theme_manager import ThemeManager
            theme_manager = ThemeManager()
            stylesheet = theme_manager.get_stylesheet(theme_name)
            self.setStyleSheet(stylesheet)
        except Exception as e:
            print(f"主题加载失败: {e}")
    
    def _on_login_success(self, username: str):
        """登录成功处理"""
        # 验证用户身份
        success, message = auth_service.authenticate(username, self.login_window.password_input.text())

        if success:
            print(f"用户 {username} 登录成功")

            # 显示加载状态
            self.login_window.set_loading_status("正在启动系统...")

            # 开始创建主窗口
            self._create_main_window()
        else:
            self.login_window._show_error(message)
            self.login_window._set_inputs_enabled(True)

    def _on_login_failed(self, error_message: str):
        """登录失败处理"""
        print(f"登录失败: {error_message}")

    def _create_main_window(self):
        """创建主窗口（在用户登录后）"""
        try:
            # 检查模块是否已预加载
            if hasattr(self.loading_manager, 'loading_result') and \
               self.loading_manager.loading_result.get('modules_preloaded', False):
                self.login_window.set_loading_status("正在启动主界面...")
                # print("使用预加载的模块快速创建主窗口")
            else:
                self.login_window.set_loading_status("正在加载界面组件...")
                # print("模块未预加载，正常加载流程")

            # 在主线程中创建主窗口（模块已在后台预加载）
            from ui.main_window import MainWindow

            # 获取配置和样式
            if hasattr(self.loading_manager, 'loading_result'):
                config = self.loading_manager.loading_result['config']
                stylesheet = self.loading_manager.loading_result['stylesheet']
            else:
                config = self.config
                from ui.themes.theme_manager import ThemeManager
                theme_manager = ThemeManager()
                theme_name = config.get('theme', 'tech')
                stylesheet = theme_manager.get_stylesheet(theme_name)

            # 创建主窗口（由于模块已预加载，这个过程会很快）
            self.main_window = MainWindow(config)
            self.main_window._prepared_stylesheet = stylesheet

            # 显示主窗口
            self._show_main_window()

        except Exception as e:
            print(f"创建主窗口失败: {e}")
            self.login_window.set_loading_status(f"启动失败: {str(e)}")
            self.login_window._set_inputs_enabled(True)



    def _on_main_window_ready(self, loading_result):
        """主窗口准备就绪（后台加载完成）"""
        # 后台加载完成，但主窗口创建在用户登录后进行
        print("后台准备工作完成")

    def _on_loading_progress(self, message: str, progress: int):
        """加载进度更新"""
        if self.login_window:
            self.login_window.set_loading_status(f"{message} ({progress}%)")

    def _on_loading_error(self, error_message: str):
        """加载错误处理"""
        print(f"后台加载失败: {error_message}")
        if self.login_window:
            self.login_window.set_loading_status(f"加载失败: {error_message}")

    def _show_main_window(self):
        """显示主窗口"""
        if not self.main_window:
            return

        # 应用主题
        self._apply_theme()

        # 确保所有页面组件都应用了正确的主题（包括实时曲线等组件）
        if hasattr(self.main_window, 'update_theme'):
            self.main_window.update_theme()

        # 更新用户信息显示
        self._update_user_info()

        # 隐藏登录窗口
        if self.login_window:
            self.login_window.hide()

        # 显示主窗口
        self.main_window.showMaximized()

        # 发送启动信号
        self.application_started.emit()

        print("主窗口已显示")

    def _update_user_info(self):
        """更新用户信息显示"""
        try:
            from services.auth_service import auth_service
            current_user = auth_service.get_current_user()
            if current_user and self.main_window:
                self.main_window.update_user_info(current_user)
        except Exception as e:
            print(f"更新用户信息失败: {e}")

    def _send_udp_exit_command(self):
        """发送UDP退出指令到VR系统"""
        try:
            from core.udp_communicator import get_udp_communicator
            udp_comm = get_udp_communicator()
            
            # 确保UDP通信器已启动
            if not udp_comm.is_connected:
                udp_comm.start_listening()
            
            # 发送退出指令
            success = udp_comm.send_exit_command()
            if success:
                print("应用退出时UDP退出指令发送成功")
            else:
                print("应用退出时UDP退出指令发送失败")
            
            # 停止UDP监听
            udp_comm.stop_listening()
                
        except Exception as e:
            print(f"应用退出时发送UDP退出指令发生错误: {e}")

    def _on_about_to_quit(self):
        """应用即将退出"""
        self.application_closing.emit()

        # 发送UDP退出指令到VR系统
        self._send_udp_exit_command()

        # 停止后台加载
        if self.loading_manager:
            self.loading_manager.stop_loading()

        # 清理患者服务的后台线程
        try:
            from services.patient_service import patient_service
            patient_service.cleanup()
        except Exception as e:
            print(f"清理患者服务失败: {e}")

        # 保存配置
        if self.config:
            self.config.save()

        # 注意：不在这里调用main_window.cleanup()
        # 因为主窗口的closeEvent已经调用了cleanup()
        # 避免重复清理导致的问题
    
    def get_config(self):
        """获取应用配置"""
        return self.config
    
    def get_main_window(self):
        """获取主窗口"""
        return self.main_window
