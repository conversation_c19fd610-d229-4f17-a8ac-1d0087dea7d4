# -*- coding: utf-8 -*-
"""
治疗系统页面
Treatment System Page

完全按照HTML设计实现的治疗系统页面
包含脑电控制、实时监测、参数调节等功能
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QPushButton, QComboBox, QCheckBox, QProgressBar, QRadioButton,
    QButtonGroup, QSpacerItem, QSizePolicy, QFrame
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QPainter, QColor, QLinearGradient, QPen
import numpy as np
import time
import os
import glob
from collections import deque

from ui.pages.base_page import BasePage
from ui.components.modern_card import ModernCard
from ui.components.parameter_adjuster import ParameterAdjuster, CompactParameterAdjuster, DifficultyLevelAdjuster
from ui.components.status_indicator import StatusIndicator, ConnectionStatus
from ui.components.themed_message_box import show_warning
from services.eeg_processing import EEGDataProcessor
# 旧的导入已移动到_init_bluetooth_manager方法中
# from services.bluetooth.simple_ble_manager import SimpleBLEManager, ConnectionState



class TreatmentPage(BasePage):
    """治疗系统页面 - 完全按照HTML设计实现"""

    # 治疗相关信号
    treatment_started = Signal()
    treatment_stopped = Signal()
    device_connected = Signal(str)  # 设备类型
    device_disconnected = Signal(str)  # 设备类型
    parameter_changed = Signal(str, float)  # 参数名, 新值

    def __init__(self):
        # 先初始化属性，再调用基类
        # 治疗状态
        self.treatment_active = False
        self.eeg_connected = False
        self.stim_connected = False

        # 稳定蓝牙管理器
        self.bluetooth_manager = None
        self.pre_training_active = False

        # 多轮训练状态（默认开启）
        self.multi_round_training_active = True
        self.current_training_round = 0
        self.total_training_rounds = 1

        # 特征提取相关
        self.feature_manager = None  # 方案A不再使用特征管理器，仅保留占位避免大改
        self.session_manager = None

        # 治疗参数
        self.current_accuracy = 87.3
        self.success_triggers = 0
        self.total_triggers = 0
        # 从设置中读取治疗时长
        treatment_duration_minutes = self._get_treatment_duration_from_settings()
        self.treatment_countdown = treatment_duration_minutes * 60  # 转换为秒

        # 电刺激状态管理
        self.stimulation_active = False  # 当前是否正在进行电刺激

        # 治疗流程状态管理
        self.imagination_request_count = 0  # 要求想象次数（yaoqiucs）
        self.successful_trigger_count = 0   # 实际触发次数（shijics）
        self.encouragement_timer = None     # 鼓励间隔计时器
        self.voice_playing = False          # 语音播放状态
        # 从设置中读取鼓励间隔
        self.encouragement_interval = self._get_encouragement_interval_from_settings()
        self.treatment_paused = False       # 治疗暂停状态
        self.paused_countdown = 0           # 暂停时的倒计时值

        # 🔧 难度等级到触发阈值的映射（与WeightedVotingClassifierSystem完全一致）
        self.difficulty_threshold_mapping = {
            1: 0.55,  # 简单 - 低阈值，容易触发
            2: 0.60,  # 较简单 - 中低阈值，容易触发
            3: 0.70,  # 中等 - 中等阈值，适中触发（提高稳定性）
            4: 0.75,  # 较困难 - 中高阈值，需要集中注意力
            5: 0.80   # 困难 - 高阈值，需要强烈运动想象
        }

        # 🔧 加权投票系统参数（与系统默认值一致）
        self.bci_params = {
            "difficulty_level": 3,  # 1-5级难度
            "trigger_threshold": 0.70,  # 触发阈值（与难度3对应）
        }

        # 电刺激参数
        self.stim_params = {
            "channel_a": 0,  # mA
            "channel_b": 0   # mA
        }

        # UI组件引用
        self.patient_info_bar = None
        self.eeg_control_card = None
        self.stim_control_card = None
        self.monitoring_card = None
        self.training_control_card = None
        self.bci_params_card = None

        # 新增的监测组件引用
        self.realtime_curves_widget = None
        self.realtime_metrics_widget = None

        # 定时器
        self.countdown_timer = None
        self.monitoring_timer = None

        # 🔧 统一定时器管理器（解决多定时器竞争问题）
        self.unified_timer_manager = None

        # 当前患者信息
        self.current_patient = None

        # 脑电数据处理器
        self.eeg_data_processor = None

        # 数据包计数器
        self.eeg_packet_counter = 0

        # 实时集成管理器
        self.realtime_integration_manager = None

        # 触发空白期与平滑控制（治疗开始前若干秒不触发，且结束时重置平滑器）
        self.trigger_blank_enabled = True
        self.trigger_blank_seconds = 15.0
        self.trigger_blank_until = 0.0
        self._blanking_reset_done = False
        self._prob_progressbar_original_style = ""

        # 调用基类初始化
        super().__init__("treatment", "治疗系统")

    def refresh_settings_parameters(self):
        """刷新从设置文件读取的参数"""
        try:
            # 刷新治疗时长
            treatment_duration_minutes = self._get_treatment_duration_from_settings()
            self.treatment_countdown = treatment_duration_minutes * 60
            self._update_countdown_display()

            # 刷新鼓励间隔
            self.encouragement_interval = self._get_encouragement_interval_from_settings()

            print(f"✅ 设置参数已刷新 - 治疗时长: {treatment_duration_minutes}分钟, 鼓励间隔: {self.encouragement_interval}秒")

        except Exception as e:
            print(f"❌ 刷新设置参数失败: {e}")

    def _init_content(self):
        """初始化页面内容 - 完全按照HTML设计实现"""
        # 清空基类的占位内容
        while self.main_layout.count():
            child = self.main_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 创建患者信息栏
        self._create_patient_info_bar()

        # 创建主要内容区域（三栏布局）
        self._create_main_content_area()

        # 初始化定时器
        self._init_timers()

        # 初始化蓝牙管理器
        self._init_bluetooth_manager()

        # 初始化脑电数据处理器
        self._init_eeg_data_processor()

        # 初始化实时集成管理器
        self._initialize_realtime_integration_manager()

        # 更新显示
        self._update_all_displays()

        # 初始化轮次显示
        self._update_round_display()

    def _create_patient_info_bar(self):
        """创建患者信息栏"""
        self.patient_info_bar = ModernCard()
        self.patient_info_bar.setObjectName("placeholder_card")
        self.patient_info_bar.setMaximumHeight(90)  # 限制高度，使其成为长条形

        # 创建信息栏内容
        info_layout = QHBoxLayout()
        info_layout.setContentsMargins(20, 0, 20, 0)
        info_layout.setSpacing(16)
        info_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)

        # 左侧：患者基本信息
        left_layout = QHBoxLayout()
        left_layout.setSpacing(16)
        left_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)

        # 患者头像
        self.patient_avatar = QLabel("?")
        self.patient_avatar.setObjectName("patient_avatar_placeholder")
        self.patient_avatar.setFixedSize(36, 36)
        self.patient_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.patient_avatar.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        left_layout.addWidget(self.patient_avatar)

        # 患者信息
        patient_info_layout = QVBoxLayout()
        patient_info_layout.setSpacing(2)
        patient_info_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)

        self.patient_name = QLabel("请选择患者")
        self.patient_name.setObjectName("patient_name_placeholder")
        self.patient_name.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        patient_info_layout.addWidget(self.patient_name)

        self.patient_id = QLabel("患者ID: 未选择")
        self.patient_id.setObjectName("patient_info")
        self.patient_id.setFont(QFont("Microsoft YaHei", 10))
        patient_info_layout.addWidget(self.patient_id)

        left_layout.addLayout(patient_info_layout)

        # 系统状态标签
        self.system_status = QLabel("系统待机")
        self.system_status.setObjectName("patient_status_badge")
        self.system_status.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        left_layout.addWidget(self.system_status)

        info_layout.addLayout(left_layout)
        info_layout.addStretch()

        # 右侧：治疗状态信息
        right_layout = QHBoxLayout()
        right_layout.setSpacing(12)
        right_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)

        # 成功触发
        success_layout = QVBoxLayout()
        success_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        success_label = QLabel("成功触发")
        success_label.setObjectName("field_label")
        success_label.setFont(QFont("Microsoft YaHei", 9))
        success_layout.addWidget(success_label)

        self.success_triggers_label = QLabel("0 / 0 次")
        self.success_triggers_label.setObjectName("field_value")
        self.success_triggers_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.success_triggers_label.setMinimumHeight(20)  # 添加这行，设置最小高度
        success_layout.addWidget(self.success_triggers_label)
        right_layout.addLayout(success_layout)

        # 治疗倒计时
        countdown_layout = QVBoxLayout()
        countdown_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        countdown_label = QLabel("治疗倒计时")
        countdown_label.setObjectName("field_label")
        countdown_label.setFont(QFont("Microsoft YaHei", 9))
        countdown_layout.addWidget(countdown_label)

        # 从设置中读取治疗时长并格式化显示
        treatment_duration_minutes = self._get_treatment_duration_from_settings()
        initial_countdown = f"{treatment_duration_minutes}:00"
        self.countdown_label = QLabel(initial_countdown)
        self.countdown_label.setObjectName("stat_value")
        self.countdown_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        self.countdown_label.setMinimumHeight(20)  # 添加这行，设置最小高度
        countdown_layout.addWidget(self.countdown_label)
        right_layout.addLayout(countdown_layout)

        info_layout.addLayout(right_layout)

        # 添加到卡片
        info_widget = QWidget()
        info_widget.setLayout(info_layout)
        self.patient_info_bar.add_content(info_widget)

        # 添加到主布局
        self.main_layout.addWidget(self.patient_info_bar)

    def _create_main_content_area(self):
        """创建主要内容区域 - 三栏布局（28% + 44% + 28%）"""
        # 创建网格布局容器
        content_widget = QWidget()
        content_layout = QGridLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(10)

        # 设置列比例：28% + 44% + 28%
        content_layout.setColumnStretch(0, 28)
        content_layout.setColumnStretch(1, 44)
        content_layout.setColumnStretch(2, 28)

        # 左侧控制区
        left_column = self._create_left_control_area()
        content_layout.addWidget(left_column, 0, 0)

        # 中央监测区
        center_column = self._create_center_monitoring_area()
        content_layout.addWidget(center_column, 0, 1)

        # 右侧参数区
        right_column = self._create_right_parameter_area()
        content_layout.addWidget(right_column, 0, 2)

        # 添加到主布局
        self.main_layout.addWidget(content_widget, 1)

    def _create_left_control_area(self) -> QWidget:
        """创建左侧控制区域"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)

        # 脑电控制卡片
        self.eeg_control_card = self._create_eeg_control_card()
        left_layout.addWidget(self.eeg_control_card)

        # 电刺激控制卡片
        self.stim_control_card = self._create_stim_control_card()
        left_layout.addWidget(self.stim_control_card)

        # 添加弹性空间
        left_layout.addStretch()

        return left_widget

    def _create_eeg_control_card(self) -> ModernCard:
        """创建脑电控制卡片"""
        card = ModernCard("")

        # 连接状态
        status_layout = QHBoxLayout()
        status_layout.setSpacing(0)

        # 暂时使用简单的状态指示器
        self.eeg_status = QLabel("未连接")
        self.eeg_status.setObjectName("status_text")
        self.eeg_status.setFont(QFont("Microsoft YaHei", 10))
        status_layout.addWidget(self.eeg_status)
        status_layout.addStretch()

        # 连接按钮
        self.eeg_connect_btn = QPushButton("连接脑电")
        self.eeg_connect_btn.setObjectName("btn_secondary")
        self.eeg_connect_btn.setMaximumWidth(120)  # 设置最大宽度与删除模型按钮一致
        self.eeg_connect_btn.clicked.connect(self._toggle_eeg_connection)
        status_layout.addWidget(self.eeg_connect_btn)

        status_widget = QWidget()
        status_widget.setLayout(status_layout)
        card.add_content(status_widget)

        # 模型操作按钮
        model_btn_layout = QHBoxLayout()
        model_btn_layout.setSpacing(8)

        self.load_model_btn = QPushButton("打开VR")
        self.load_model_btn.setObjectName("btn_primary")
        self.load_model_btn.clicked.connect(self._load_model)
        model_btn_layout.addWidget(self.load_model_btn)

        self.remove_model_btn = QPushButton("删除模型")
        self.remove_model_btn.setObjectName("btn_secondary")
        self.remove_model_btn.clicked.connect(self._remove_model)
        model_btn_layout.addWidget(self.remove_model_btn)

        model_btn_widget = QWidget()
        model_btn_widget.setLayout(model_btn_layout)
        card.add_content(model_btn_widget)

        return card

    def _create_stim_control_card(self) -> ModernCard:
        """创建电刺激控制卡片"""
        card = ModernCard("")

        # 连接状态
        status_layout = QHBoxLayout()
        status_layout.setSpacing(16)

        # 暂时使用简单的状态指示器
        self.stim_status = QLabel("未连接")
        self.stim_status.setObjectName("status_text")
        self.stim_status.setFont(QFont("Microsoft YaHei", 10))
        status_layout.addWidget(self.stim_status)
        status_layout.addStretch()

        # 连接按钮
        self.stim_connect_btn = QPushButton("连接电刺激")
        self.stim_connect_btn.setObjectName("btn_secondary")
        self.stim_connect_btn.setMaximumWidth(120)  # 设置最大宽度与删除模型按钮一致
        self.stim_connect_btn.clicked.connect(self._toggle_stim_connection)
        status_layout.addWidget(self.stim_connect_btn)

        status_widget = QWidget()
        status_widget.setLayout(status_layout)
        card.add_content(status_widget)

        # 通道控制
        # A通道
        channel_a_layout = QHBoxLayout()
        channel_a_layout.setSpacing(8)

        self.channel_a_checkbox = QCheckBox("A通道")
        self.channel_a_checkbox.setChecked(True)
        self.channel_a_checkbox.setFont(QFont("Microsoft YaHei", 12))
        channel_a_layout.addWidget(self.channel_a_checkbox)
        channel_a_layout.addStretch()

        # A通道电流调节 - 从配置中读取最大电流值
        max_current = self._get_max_current_from_config()
        self.channel_a_adjuster = ParameterAdjuster(
            label="",
            initial_value=0,
            min_value=0,
            max_value=max_current,
            step=1,
            precision=0,
            unit="mA"
        )
        # 断开默认的按钮连接，使用自定义的连接
        self.channel_a_adjuster.increase_btn.clicked.disconnect()
        self.channel_a_adjuster.decrease_btn.clicked.disconnect()

        # 连接按钮点击事件，而不是值变化事件，这样可以在值变化前检查设备状态
        self.channel_a_adjuster.increase_btn.clicked.connect(
            lambda: self._on_current_increase_clicked("A")
        )
        self.channel_a_adjuster.decrease_btn.clicked.connect(
            lambda: self._on_current_decrease_clicked("A")
        )
        channel_a_layout.addWidget(self.channel_a_adjuster)

        channel_a_widget = QWidget()
        channel_a_widget.setLayout(channel_a_layout)
        card.add_content(channel_a_widget)

        # B通道
        channel_b_layout = QHBoxLayout()
        channel_b_layout.setSpacing(8)

        self.channel_b_checkbox = QCheckBox("B通道")
        self.channel_b_checkbox.setFont(QFont("Microsoft YaHei", 12))
        channel_b_layout.addWidget(self.channel_b_checkbox)
        channel_b_layout.addStretch()

        # B通道电流调节 - 从配置中读取最大电流值
        self.channel_b_adjuster = ParameterAdjuster(
            label="",
            initial_value=0,
            min_value=0,
            max_value=max_current,
            step=1,
            precision=0,
            unit="mA"
        )
        # 断开默认的按钮连接，使用自定义的连接
        self.channel_b_adjuster.increase_btn.clicked.disconnect()
        self.channel_b_adjuster.decrease_btn.clicked.disconnect()

        # 连接按钮点击事件，而不是值变化事件，这样可以在值变化前检查设备状态
        self.channel_b_adjuster.increase_btn.clicked.connect(
            lambda: self._on_current_increase_clicked("B")
        )
        self.channel_b_adjuster.decrease_btn.clicked.connect(
            lambda: self._on_current_decrease_clicked("B")
        )
        channel_b_layout.addWidget(self.channel_b_adjuster)

        channel_b_widget = QWidget()
        channel_b_widget.setLayout(channel_b_layout)
        card.add_content(channel_b_widget)

        # 通道组（不再需要QButtonGroup，因为支持多选）

        # 手动控制按钮
        manual_control_layout = QHBoxLayout()
        manual_control_layout.setSpacing(8)

        self.manual_start_btn = QPushButton("手动开启")
        self.manual_start_btn.setObjectName("btn_primary")
        self.manual_start_btn.clicked.connect(self._manual_start_stimulation)
        manual_control_layout.addWidget(self.manual_start_btn)

        self.manual_stop_btn = QPushButton("手动停止")
        self.manual_stop_btn.setObjectName("btn_secondary")
        self.manual_stop_btn.clicked.connect(self._manual_stop_stimulation)
        manual_control_layout.addWidget(self.manual_stop_btn)

        manual_control_widget = QWidget()
        manual_control_widget.setLayout(manual_control_layout)
        card.add_content(manual_control_widget)

        return card

    def _create_center_monitoring_area(self) -> QWidget:
        """创建中央监测区域"""
        self.monitoring_card = ModernCard("")

        # 主监测区域
        main_monitoring_widget = QWidget()
        main_layout = QVBoxLayout(main_monitoring_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)  # 🔧 调整内边距，与左右卡片保持一致
        main_layout.setSpacing(10)

        # 实时曲线显示（100%高度）- 性能优化后只保留实时曲线
        self._create_realtime_curves_widget()
        main_layout.addWidget(self.realtime_curves_widget, 1)  # 占用全部空间

        # 地形图和指标面板已完全移除（性能优化）
        # 实时曲线现在占用整个监测卡片的空间

        self.monitoring_card.add_content(main_monitoring_widget)

        return self.monitoring_card

    def _create_realtime_curves_widget(self):
        """创建高性能实时曲线组件（基于PyQtGraph）"""
        try:
            # 🔧 性能优化：使用PyQtGraph替换matplotlib，提升10-100倍性能
            from ui.components.pyqtgraph_curves_widget import PyQtGraphCurvesWidget

            self.realtime_curves_widget = PyQtGraphCurvesWidget()
            self.realtime_curves_widget.setObjectName("realtime_curves_widget")

        except Exception as e:
            print(f"❌ 创建PyQtGraph实时曲线组件失败: {e}")
            # 创建错误显示组件
            backup_label = QLabel("PyQtGraph实时曲线组件加载失败\n请确保已安装pyqtgraph>=0.13.0")
            backup_label.setMinimumSize(200, 300)
            backup_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            backup_label.setStyleSheet("""
                QLabel {
                    border: 1px solid #ef4444;
                    border-radius: 8px;
                    background-color: #fef2f2;
                    color: #dc2626;
                    font-size: 12px;
                }
            """)
            self.realtime_curves_widget = backup_label

    # 实时指标面板组件已完全移除（性能优化）
    # def _create_realtime_metrics_widget(self):
    #     """创建实时指标面板组件"""
    #     pass

    def update_theme(self):
        """更新主题 - 重写父类方法以更新新增组件"""
        # 调用父类的主题更新
        super().update_theme()

        # 🔧 使用全局样式管理主题：通过QSS自动应用主题，组件只需触发样式重新应用
        if hasattr(self, 'realtime_curves_widget') and hasattr(self.realtime_curves_widget, 'update_theme'):
            self.realtime_curves_widget.update_theme()

    # 地形图组件已完全移除（性能优化）
    # def _create_topography_widget(self):
    #     """创建脑电地形图组件"""
    #     pass

        return self.monitoring_card

    def _create_right_parameter_area(self) -> QWidget:
        """创建右侧参数区域"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)

        # 预训练与治疗控制卡片
        self.training_control_card = self._create_training_control_card()
        right_layout.addWidget(self.training_control_card)

        # BCI分类参数卡片
        self.bci_params_card = self._create_bci_params_card()
        right_layout.addWidget(self.bci_params_card)

        # 添加弹性空间
        right_layout.addStretch()

        return right_widget

    def _create_training_control_card(self) -> ModernCard:
        """创建预训练与治疗控制卡片"""
        card = ModernCard("")

        # 预训练控制按钮
        pretraining_layout = QHBoxLayout()
        # pretraining_layout.setContentsMargins(0, 0, 0, 0)  # 无内边距
        pretraining_layout.setSpacing(8)

        self.pretraining_btn = QPushButton("开始训练")
        self.pretraining_btn.setObjectName("btn_start")
        self.pretraining_btn.clicked.connect(self._toggle_pretraining)
        pretraining_layout.addWidget(self.pretraining_btn, 2)  # 增加stretch factor

        self.stop_pretraining_btn = QPushButton("停止")
        self.stop_pretraining_btn.setObjectName("btn_stop")
        self.stop_pretraining_btn.clicked.connect(self._stop_pretraining)
        self.stop_pretraining_btn.setEnabled(False)  # 初始状态为不可用
        pretraining_layout.addWidget(self.stop_pretraining_btn, 1)  # 添加stretch factor

        pretraining_widget = QWidget()
        pretraining_widget.setLayout(pretraining_layout)
        card.add_content(pretraining_widget)

        # 多轮训练控制区域
        self._create_multi_round_training_section(card)



        return card

    def _create_multi_round_training_section(self, card: ModernCard):
        """创建多轮训练控制区域"""
        # 多轮训练参数
        multi_round_layout = QHBoxLayout()
        multi_round_layout.setSpacing(20)

        # 当前轮次
        round_layout = QVBoxLayout()
        round_label = QLabel("当前轮次")
        round_label.setObjectName("param_label_small")
        round_label.setFont(QFont("Microsoft YaHei", 11))
        round_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 文本居中
        round_layout.addWidget(round_label)

        self.current_round_label = QLabel("--")
        self.current_round_label.setObjectName("current_round_value")
        self.current_round_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.current_round_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 文本居中
        round_layout.addWidget(self.current_round_label)

        round_widget = QWidget()
        round_widget.setLayout(round_layout)
        multi_round_layout.addWidget(round_widget, 1)

        # 当前状态
        status_layout = QVBoxLayout()
        status_label = QLabel("当前状态")
        status_label.setObjectName("param_label_small")
        status_label.setFont(QFont("Microsoft YaHei", 11))
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 文本居中
        status_layout.addWidget(status_label)

        self.training_status_label = QLabel("--")
        self.training_status_label.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        self.training_status_label.setObjectName("training_status_value")
        self.training_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 文本居中
        status_layout.addWidget(self.training_status_label)

        status_widget = QWidget()
        status_widget.setLayout(status_layout)
        multi_round_layout.addWidget(status_widget, 1)

        multi_round_widget = QWidget()
        multi_round_widget.setLayout(multi_round_layout)
        card.add_content(multi_round_widget)



    def _create_bci_params_card(self) -> ModernCard:
        """创建BCI分类参数调节卡片"""
        card = ModernCard("")

        # 添加折叠按钮到标题栏
        toggle_btn = QPushButton("▼")
        toggle_btn.setObjectName("bci_toggle_btn")
        toggle_btn.setFixedSize(16, 16)
        toggle_btn.setFont(QFont("Microsoft YaHei", 12))
        toggle_btn.clicked.connect(self._toggle_bci_params)
        card.add_action(toggle_btn)

        # 参数调节区域
        self.bci_params_widget = QWidget()
        params_layout = QVBoxLayout(self.bci_params_widget)
        params_layout.setContentsMargins(0, 0, 0, 0)  # 无内边距
        params_layout.setSpacing(16)

        # 治疗控制区域（移动到这里）
        treatment_layout = QHBoxLayout()
        # treatment_layout.setContentsMargins(0, 0, 0, 0)  # 无内边距
        treatment_layout.setSpacing(8)

        self.treatment_main_btn = QPushButton("开始治疗")
        self.treatment_main_btn.setObjectName("btn_start")
        self.treatment_main_btn.clicked.connect(self._handle_treatment_button_click)
        treatment_layout.addWidget(self.treatment_main_btn, 2)  # 增加stretch factor

        self.stop_treatment_btn = QPushButton("停止")
        self.stop_treatment_btn.setObjectName("btn_stop")
        self.stop_treatment_btn.clicked.connect(self._stop_treatment)
        self.stop_treatment_btn.setEnabled(False)  # 初始状态为不可用
        treatment_layout.addWidget(self.stop_treatment_btn, 1)  # 添加stretch factor

        treatment_widget = QWidget()
        treatment_widget.setLayout(treatment_layout)
        params_layout.addWidget(treatment_widget)

        # 概率显示区域
        probability_layout = QVBoxLayout()
        probability_layout.setSpacing(8)

        # 标题和概率值
        probability_info_layout = QHBoxLayout()
        probability_label = QLabel("概率")
        probability_label.setFont(QFont("Microsoft YaHei", 11))
        probability_info_layout.addWidget(probability_label)
        probability_info_layout.addStretch()

        self.motor_imagery_probability_label = QLabel("0%")
        self.motor_imagery_probability_label.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        probability_info_layout.addWidget(self.motor_imagery_probability_label)

        probability_layout.addLayout(probability_info_layout)

        # 运动想象概率进度条
        self.motor_imagery_progress_bar = QProgressBar()
        self.motor_imagery_progress_bar.setObjectName("motor_imagery_progress_bar")
        self.motor_imagery_progress_bar.setFixedHeight(8)
        self.motor_imagery_progress_bar.setRange(0, 100)
        self.motor_imagery_progress_bar.setValue(0)
        self.motor_imagery_progress_bar.setTextVisible(False)  # 隐藏进度条自带的百分比显示
        probability_layout.addWidget(self.motor_imagery_progress_bar)

        probability_widget = QWidget()
        probability_widget.setLayout(probability_layout)
        params_layout.addWidget(probability_widget)

        # 第一行：难度等级、触发阈值（改为上下结构）
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(16)

        # 难度等级调节器（垂直布局）
        self.difficulty_adjuster = DifficultyLevelAdjuster(
            param_name="difficulty_level",
            label="难度等级",
            initial_value=self.bci_params["difficulty_level"],
            layout_direction="vertical"
        )
        self.difficulty_adjuster.value_changed.connect(self._on_difficulty_changed_new)
        row1_layout.addWidget(self.difficulty_adjuster)

        # 触发阈值调节器（垂直布局）
        self.trigger_threshold_adjuster = CompactParameterAdjuster(
            "trigger_threshold", "触发阈值", 0.75, 0.50, 1.00, 0.01, 2, "vertical"
        )
        self.trigger_threshold_adjuster.value_changed.connect(self._on_bci_param_changed)
        row1_layout.addWidget(self.trigger_threshold_adjuster)

        row1_widget = QWidget()
        row1_widget.setLayout(row1_layout)
        params_layout.addWidget(row1_widget)

        card.add_content(self.bci_params_widget)

        return card

    def _init_timers(self):
        """初始化定时器"""
        # 倒计时定时器
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self._update_countdown)

        # 监测数据更新定时器
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self._update_monitoring_data)

    def _update_all_displays(self):
        """更新所有显示"""
        self._update_patient_info()
        self._update_treatment_status()
        self._update_countdown_display()
        self._update_monitoring_display()

    def _update_patient_info(self):
        """更新患者信息显示"""
        # 这里可以从患者管理模块获取当前患者信息
        # 暂时使用模拟数据
        pass

    def set_patient_info(self, patient_data: dict):
        """设置患者信息并更新显示"""
        self.current_patient = patient_data

        # 更新患者头像
        name = patient_data.get('name', '患者')
        self.patient_avatar.setText(name[0] if name else 'P')
        self.patient_avatar.setObjectName("patient_avatar")  # 切换到正常样式

        # 更新患者姓名
        self.patient_name.setText(name)
        self.patient_name.setObjectName("patient_name")  # 切换到正常样式

        # 更新患者ID
        patient_id = patient_data.get('bianhao', 'N/A')
        self.patient_id.setText(f"患者ID: {patient_id}")

        # 重新应用样式
        self._refresh_patient_info_styles()

    def clear_patient_info(self):
        """清除患者信息，恢复到未选择状态"""
        self.current_patient = None

        # 恢复默认显示
        self.patient_avatar.setText("?")
        self.patient_avatar.setObjectName("patient_avatar_placeholder")

        self.patient_name.setText("请选择患者")
        self.patient_name.setObjectName("patient_name_placeholder")

        self.patient_id.setText("患者ID: 未选择")

        # 重新应用样式
        self._refresh_patient_info_styles()

    def _refresh_patient_info_styles(self):
        """刷新患者信息区域的样式"""
        try:
            # 重新应用样式表以更新外观
            self.patient_avatar.style().unpolish(self.patient_avatar)
            self.patient_avatar.style().polish(self.patient_avatar)

            self.patient_name.style().unpolish(self.patient_name)
            self.patient_name.style().polish(self.patient_name)

            self.patient_id.style().unpolish(self.patient_id)
            self.patient_id.style().polish(self.patient_id)
        except Exception as e:
            pass  # 忽略样式刷新错误

    def _validate_patient_info(self) -> bool:
        """验证患者信息是否完整

        Returns:
            bool: 患者信息是否有效
        """
        # 检查患者对象是否存在
        if not self.current_patient:
            return False

        # 检查患者姓名是否有效
        patient_name = self.current_patient.get('name', '').strip()
        if not patient_name or patient_name in ['张三', '患者', '请选择患者', '']:
            return False

        # 检查患者ID是否有效
        patient_id = self.current_patient.get('bianhao', '').strip()
        if not patient_id or patient_id in ['N/A', '**********', '']:
            return False

        return True

    def _validate_treatment_conditions(self) -> dict:
        """验证开始治疗的前置条件"""
        try:
            # 1. 检查患者信息
            if not self._validate_patient_info():
                return {
                    'valid': False,
                    'message': "请先选择患者信息。\n\n请在患者管理页面选择患者后再开始治疗。"
                }

            # 2. 检查脑电设备连接
            if not self.eeg_connected:
                return {
                    'valid': False,
                    'message': "脑电设备未连接。\n\n请先连接脑电设备后再开始治疗。"
                }

            # 3. 检查电刺激设备连接
            if not self.stim_connected:
                return {
                    'valid': False,
                    'message': "电刺激设备未连接。\n\n请先连接电刺激设备后再开始治疗。"
                }

            # 4. 检查模型加载
            if not self._load_patient_model():
                return {
                    'valid': False,
                    'message': "未找到患者训练模型。\n\n请先完成预训练后再开始治疗。"
                }

            # 5. 检查电刺激通道选择和电流设置
            channel_validation = self._validate_stimulation_channels()
            if not channel_validation['valid']:
                return channel_validation

            # 6. 检查是否已经在治疗中
            if self.treatment_active:
                return {
                    'valid': False,
                    'message': "治疗已在进行中。\n\n请先停止当前治疗再开始新的治疗。"
                }

            # 所有条件都满足
            return {'valid': True, 'message': ''}

        except Exception as e:
            print(f"验证治疗条件失败: {e}")
            return {
                'valid': False,
                'message': f"验证治疗条件时发生错误：{e}\n\n请检查系统状态后重试。"
            }

    def _validate_stimulation_channels(self) -> dict:
        """验证电刺激通道选择和电流设置"""
        try:
            # 检查是否至少选中了一个通道
            channel_a_selected = self.channel_a_checkbox.isChecked()
            channel_b_selected = self.channel_b_checkbox.isChecked()

            if not channel_a_selected and not channel_b_selected:
                return {
                    'valid': False,
                    'message': "未选择电刺激通道。\n\n请至少勾选A通道或B通道中的一个。"
                }

            # 检查选中通道的电流值
            invalid_channels = []

            if channel_a_selected:
                current_a = self.stim_params.get("channel_a", 0)
                if current_a <= 0:
                    invalid_channels.append("A通道")

            if channel_b_selected:
                current_b = self.stim_params.get("channel_b", 0)
                if current_b <= 0:
                    invalid_channels.append("B通道")

            if invalid_channels:
                channels_text = "、".join(invalid_channels)
                return {
                    'valid': False,
                    'message': f"选中的{channels_text}电流值为0。\n\n请设置大于0的电流值后再开始治疗。"
                }

            # 验证通过
            return {'valid': True, 'message': ''}

        except Exception as e:
            print(f"验证电刺激通道失败: {e}")
            return {
                'valid': False,
                'message': f"验证电刺激通道时发生错误：{e}\n\n请检查电刺激设置后重试。"
            }

    def _update_treatment_status(self):
        """更新治疗状态显示"""
        # 使用正确的计数器更新显示
        self.success_triggers_label.setText(f"{self.successful_trigger_count} / {self.imagination_request_count} 次")

    def _update_countdown_display(self):
        """更新倒计时显示"""
        minutes = self.treatment_countdown // 60
        seconds = self.treatment_countdown % 60
        self.countdown_label.setText(f"{minutes:02d}:{seconds:02d}")

    def _update_countdown(self):
        """倒计时更新"""
        if self.treatment_countdown > 0:
            self.treatment_countdown -= 1
            self._update_countdown_display()

            # 进度更新已移除，现在显示运动想象概率
        else:
            # 治疗时间结束 - 自动完成
            self._complete_treatment()

    def _update_probability_display(self, motor_imagery_prob: float):
        """更新运动想象概率显示"""
        try:
            if hasattr(self, 'motor_imagery_probability_label'):
                self.motor_imagery_probability_label.setText(f"{motor_imagery_prob:.0f}%")
            if hasattr(self, 'motor_imagery_progress_bar'):
                self.motor_imagery_progress_bar.setValue(int(motor_imagery_prob))
        except Exception as e:
            print(f"更新概率显示失败: {e}")

    def _update_training_status(self, status: str):
        """更新训练状态显示"""
        try:
            if hasattr(self, 'training_status_label'):
                self.training_status_label.setText(status)
        except Exception as e:
            print(f"更新训练状态显示失败: {e}")

    def _update_monitoring_display(self):
        """更新监测显示"""
        if self.eeg_connected:
            # 连接状态下的处理
            pass
        else:
            # 地形图组件已移除，无需处理
            pass

    def _update_monitoring_data(self):
        """监测数据更新定时器回调"""
        self._update_monitoring_display()

    # 事件处理方法


    def _toggle_stim_connection(self):
        """切换电刺激设备连接"""
        if not self.stimulation_device:
            self.stim_status.setText("设备管理器未初始化")
            return

        if not self.stim_connected:
            # 开始连接
            self.stim_status.setText("连接中...")
            self.stim_connect_btn.setText("连接中...")
            self.stim_connect_btn.setEnabled(False)

            # 启动连接
            success = self.stimulation_device.connect_device()
            if not success:
                # 连接失败，恢复UI状态
                self.stim_status.setText("连接失败")
                self.stim_connect_btn.setText("连接电刺激")
                self.stim_connect_btn.setEnabled(True)
        else:
            # 断开连接
            self._disconnect_stim()



    def _disconnect_stim(self):
        """断开电刺激设备"""
        if self.stimulation_device:
            self.stimulation_device.disconnect_device()
        else:
            # 直接更新UI状态
            self._update_stim_disconnected_state()

        # 重置A、B通道电流值为0
        self._reset_channel_currents()

        # 重置刺激状态显示
        self.channel_a_stimulating = False
        self.channel_b_stimulating = False
        self._update_channel_display("A")
        self._update_channel_display("B")

    # 电刺激设备信号处理函数

    def _on_stim_device_connected(self):
        """电刺激设备连接成功"""
        self.stim_connected = True
        self.stim_status.setText("已连接")
        self.stim_connect_btn.setText("断开电刺激")
        self.stim_connect_btn.setObjectName("btn_primary")
        self.stim_connect_btn.setEnabled(True)

        # 应用样式
        self.stim_connect_btn.style().unpolish(self.stim_connect_btn)
        self.stim_connect_btn.style().polish(self.stim_connect_btn)



        # 更新顶部栏设备状态
        self._update_topbar_device_status()

        self.device_connected.emit("stim")
        print("电刺激设备连接成功")

    def _on_stim_device_disconnected(self):
        """电刺激设备断开连接"""
        self._update_stim_disconnected_state()
        print("电刺激设备已断开")

    def _on_stim_connection_failed(self, error_msg: str):
        """电刺激设备连接失败"""
        self.stim_status.setText(f"连接失败: {error_msg}")
        self.stim_connect_btn.setText("连接电刺激")
        self.stim_connect_btn.setObjectName("btn_secondary")
        self.stim_connect_btn.setEnabled(True)

        # 应用样式
        self.stim_connect_btn.style().unpolish(self.stim_connect_btn)
        self.stim_connect_btn.style().polish(self.stim_connect_btn)

        print(f"电刺激设备连接失败: {error_msg}")

    def _on_stim_status_changed(self, status: str):
        """电刺激设备状态变化"""
        self.stim_status.setText(status)

    def _update_stim_disconnected_state(self):
        """更新电刺激设备断开状态的UI"""
        self.stim_connected = False
        self.stim_status.setText("未连接")
        self.stim_connect_btn.setText("连接电刺激")
        self.stim_connect_btn.setObjectName("btn_secondary")
        self.stim_connect_btn.setEnabled(True)

        # 应用样式
        self.stim_connect_btn.style().unpolish(self.stim_connect_btn)
        self.stim_connect_btn.style().polish(self.stim_connect_btn)



        # 更新顶部栏设备状态
        self._update_topbar_device_status()

        self.device_disconnected.emit("stim")

    def _update_topbar_device_status(self):
        """更新顶部栏的设备状态显示 - 仅根据电刺激设备状态"""
        try:
            # 获取主窗口的顶部栏
            main_window = self.window()
            if hasattr(main_window, 'topbar') and main_window.topbar:
                topbar = main_window.topbar

                # 只检查电刺激设备连接状态
                if self.stim_connected:
                    # 电刺激设备已连接
                    topbar.device_status.status_text.setText("设备在线")
                    topbar.device_status.status_type = "online"
                    topbar.device_status.setProperty("status_type", "online")
                else:
                    # 电刺激设备未连接
                    topbar.device_status.status_text.setText("设备断开")
                    topbar.device_status.status_type = "offline"
                    topbar.device_status.setProperty("status_type", "offline")

                # 刷新样式
                topbar.device_status.style().unpolish(topbar.device_status)
                topbar.device_status.style().polish(topbar.device_status)

                # 也刷新文字标签的样式
                topbar.device_status.status_text.style().unpolish(topbar.device_status.status_text)
                topbar.device_status.status_text.style().polish(topbar.device_status.status_text)

                print(f"顶部栏设备状态已更新: {topbar.device_status.status_text.text()}")

        except Exception as e:
            print(f"更新顶部栏设备状态失败: {e}")

    def _update_topbar_signal_status(self):
        """更新顶部栏的信号状态显示 - 根据脑电设备状态"""
        try:
            # 获取主窗口的顶部栏
            main_window = self.window()
            if hasattr(main_window, 'topbar') and main_window.topbar:
                topbar = main_window.topbar

                # 检查脑电设备连接状态
                if self.eeg_connected:
                    # 脑电设备已连接
                    topbar.signal_status.status_text.setText("信号正常")
                    topbar.signal_status.status_type = "online"
                    topbar.signal_status.setProperty("status_type", "online")
                else:
                    # 脑电设备未连接
                    topbar.signal_status.status_text.setText("信号断开")
                    topbar.signal_status.status_type = "offline"
                    topbar.signal_status.setProperty("status_type", "offline")

                # 刷新样式
                topbar.signal_status.style().unpolish(topbar.signal_status)
                topbar.signal_status.style().polish(topbar.signal_status)

                # 也刷新文字标签的样式
                topbar.signal_status.status_text.style().unpolish(topbar.signal_status.status_text)
                topbar.signal_status.status_text.style().polish(topbar.signal_status.status_text)

                print(f"顶部栏信号状态已更新: {topbar.signal_status.status_text.text()}")

        except Exception as e:
            print(f"更新顶部栏信号状态失败: {e}")

    def _load_model(self):
        """打开VR训练软件"""
        try:
            import subprocess
            import os

            vr_exe_path = "D:/HT/VR/脑康复训练VR软件.exe"

            # 检查文件是否存在
            if not os.path.exists(vr_exe_path):
                from ui.components.themed_message_box import show_warning
                show_warning(self, "文件不存在", f"VR软件未找到：\n{vr_exe_path}\n\n请检查文件路径是否正确")
                return

            # 启动VR软件
            print(f"正在启动VR软件: {vr_exe_path}")
            subprocess.Popen([vr_exe_path], shell=True)



        except Exception as e:
            print(f"❌ 启动VR软件失败: {e}")
            from ui.components.themed_message_box import show_critical
            show_critical(self, "启动失败", f"启动VR软件时发生错误：\n{str(e)}")

    def _remove_model(self):
        """移除当前患者的所有训练数据"""
        try:
            # 检查患者信息是否已选择
            if not self._validate_patient_info():
                from ui.components.themed_message_box import show_warning
                show_warning(self, "患者信息缺失", "请先在患者管理页面选择患者后再进行操作")
                return

            # 获取患者ID
            patient_id = self.current_patient.get('bianhao') or self.current_patient.get('id', '')
            patient_name = self.current_patient.get('name', '未知患者')

            if not patient_id:
                from ui.components.themed_message_box import show_warning
                show_warning(self, "患者ID无效", "无法获取有效的患者ID")
                return

            # 弹出确认对话框
            from ui.components.themed_message_box import show_question
            from PySide6.QtWidgets import QMessageBox

            confirm_text = f"确定要删除患者 {patient_name} (ID: {patient_id}) 的所有训练数据吗？\n\n" \
                          f"此操作将删除以下数据：\n" \
                          f"• 所有训练模型文件\n" \
                          f"• 所有特征提取文件\n" \
                          f"• 所有原始训练数据\n" \
                          f"• 训练历史记录\n\n" \
                          f"此操作不可撤销！"

            result = show_question(
                self,
                "确认删除患者数据",
                confirm_text,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if result != QMessageBox.StandardButton.Yes:
                return

            # 执行删除操作
            self._delete_patient_data(patient_id, patient_name)

        except Exception as e:
            print(f"❌ 移除患者数据失败: {e}")
            from ui.components.themed_message_box import show_critical
            show_critical(self, "删除失败", f"删除患者数据时发生错误：\n{str(e)}")

    def _delete_patient_data(self, patient_id: str, patient_name: str):
        """删除指定患者的所有数据文件"""
        import os
        import glob

        deleted_files = []
        failed_files = []

        try:
            # 定义数据目录
            from utils.path_manager import get_data_file
            data_dirs = {
                "训练模型": str(get_data_file("models")),
                "训练历史": str(get_data_file("training_history"))
            }

            # 遍历每个数据目录
            for data_type, data_dir in data_dirs.items():
                if not os.path.exists(data_dir):
                    continue

                # 查找该患者的所有文件（支持各种命名模式）
                patterns = [
                    f"{patient_id}_*.pkl",      # 特征文件和模型文件
                    f"{patient_id}_*.npz",      # 原始数据文件
                    f"{patient_id}_*.json",     # 历史记录文件
                    f"{patient_id}_*.txt",      # 报告文件
                    f"*{patient_id}*.pkl",      # 包含患者ID的文件
                    f"*{patient_id}*.npz",      # 包含患者ID的文件
                    f"*{patient_id}*.json",     # 包含患者ID的文件
                    f"*{patient_id}*.txt"       # 包含患者ID的文件
                ]

                for pattern in patterns:
                    file_pattern = os.path.join(data_dir, pattern)
                    matching_files = glob.glob(file_pattern)

                    for file_path in matching_files:
                        try:
                            if os.path.exists(file_path):
                                os.remove(file_path)
                                deleted_files.append(file_path)
                                print(f"✅ 已删除 {data_type}: {os.path.basename(file_path)}")
                        except Exception as e:
                            failed_files.append((file_path, str(e)))
                            print(f"❌ 删除失败 {data_type}: {os.path.basename(file_path)} - {e}")

            # 显示删除结果
            self._show_deletion_result(patient_name, patient_id, deleted_files, failed_files)

        except Exception as e:
            print(f"❌ 删除患者数据过程中发生错误: {e}")
            from ui.components.themed_message_box import show_critical
            show_critical(self, "删除过程错误", f"删除过程中发生错误：\n{str(e)}")

    def _show_deletion_result(self, patient_name: str, patient_id: str, deleted_files: list, failed_files: list):
        """显示删除结果"""
        from ui.components.themed_message_box import show_information, show_warning

        if deleted_files:
            # 按文件类型分组显示
            file_types = {}
            for file_path in deleted_files:
                if 'models' in file_path:
                    file_types.setdefault('训练模型', []).append(os.path.basename(file_path))
                elif 'features' in file_path or 'raw_training_data' in file_path:
                    # 这些目录已不再使用，仅兼容显示
                    continue
                elif 'training_history' in file_path:
                    file_types.setdefault('训练历史', []).append(os.path.basename(file_path))
                else:
                    file_types.setdefault('其他文件', []).append(os.path.basename(file_path))

            result_text = f"成功删除患者 {patient_name} (ID: {patient_id}) 的数据：\n\n"
            for file_type, files in file_types.items():
                result_text += f"• {file_type}: {len(files)} 个文件\n"

            result_text += f"\n总计删除 {len(deleted_files)} 个文件"

            if failed_files:
                result_text += f"\n\n⚠ 有 {len(failed_files)} 个文件删除失败"

            show_information(self, "删除完成", result_text)

        else:
            # 没有找到文件
            show_information(self, "删除完成", f"未找到患者 {patient_name} (ID: {patient_id}) 的训练数据文件")

        # 如果有删除失败的文件，显示详细信息
        if failed_files:
            failed_text = "以下文件删除失败：\n\n"
            for file_path, error in failed_files[:10]:  # 最多显示10个失败文件
                failed_text += f"• {os.path.basename(file_path)}: {error}\n"

            if len(failed_files) > 10:
                failed_text += f"\n... 还有 {len(failed_files) - 10} 个文件删除失败"

            show_warning(self, "部分文件删除失败", failed_text)

    def _on_current_increase_clicked(self, channel: str):
        """电流增加按钮点击事件 - 在值变化前检查设备连接状态"""
        # 首先检查设备连接状态
        if not self.stim_connected or not self.stimulation_device:
            show_warning(self, "设备未连接", "请先连接电刺激设备")
            return  # 阻止操作，UI值不会变化

        # 设备已连接，执行正常的增加操作
        adjuster = self.channel_a_adjuster if channel == "A" else self.channel_b_adjuster
        current_value = adjuster.get_value()
        new_value = min(adjuster.max_value, current_value + adjuster.step)

        # 手动更新值并触发后续处理
        adjuster.set_value(new_value)
        self._update_stim_current(channel, new_value)

    def _on_current_decrease_clicked(self, channel: str):
        """电流减少按钮点击事件 - 减少操作总是允许的（安全操作）"""
        # 减少电流是安全操作，即使设备未连接也允许
        adjuster = self.channel_a_adjuster if channel == "A" else self.channel_b_adjuster
        current_value = adjuster.get_value()
        new_value = max(adjuster.min_value, current_value - adjuster.step)

        # 手动更新值并触发后续处理
        adjuster.set_value(new_value)
        self._update_stim_current(channel, new_value)

    def _update_stim_current(self, channel: str, value: float):
        """更新电刺激电流并启动预刺激 - 设备连接检查已在按钮点击时完成"""
        # 更新电流值显示
        self.stim_params[f"channel_{channel.lower()}"] = value
        print(f"更新{channel}通道电流: {value}mA")
        self.parameter_changed.emit(f"stim_{channel.lower()}", value)

        # 如果设备未连接，不执行预刺激
        if not self.stim_connected or not self.stimulation_device:
            return

        # 检查通道是否选中
        channel_checkbox = self.channel_a_checkbox if channel == "A" else self.channel_b_checkbox
        if not channel_checkbox.isChecked():
            return

        # 实现参考文件中的简化预刺激逻辑
        if value <= 0:
            # 电流为0时停止预刺激
            self._stop_channel_pre_stimulation(channel)
            return

        # 直接启动预刺激，不使用复杂的防抖动逻辑
        self._start_simple_pre_stimulation(channel, int(value))

    def _get_max_current_from_config(self) -> int:
        """从配置文件中读取最大电流值"""
        try:
            import json
            from pathlib import Path

            from utils.path_manager import get_config_file_in_dir
            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                max_current = settings.get("stimulation", {}).get("max_current", 30)
                return max_current

        except Exception as e:
            print(f"读取配置失败，使用默认值30mA: {e}")

        return 30  # 默认值

    def _reset_all_channels_to_zero(self):
        """将AB通道电流置零并发送指令到电刺激设备"""
        try:
            # print("🔄 开始将AB通道电流置零...")

            # 重置A通道
            if hasattr(self, 'channel_a_adjuster'):
                self.channel_a_adjuster.set_value(0)
                self.stim_params["channel_a"] = 0
                # print("A通道电流已置零")

                # 发送A通道电流为0的指令
                if self.stim_connected and self.stimulation_device:
                    self._stop_channel_pre_stimulation("A")
                    # 发送电流设置指令
                    self._send_current_setting_command("A", 0)

            # 重置B通道
            if hasattr(self, 'channel_b_adjuster'):
                self.channel_b_adjuster.set_value(0)
                self.stim_params["channel_b"] = 0
                # print("B通道电流已置零")

                # 发送B通道电流为0的指令
                if self.stim_connected and self.stimulation_device:
                    self._stop_channel_pre_stimulation("B")
                    # 发送电流设置指令
                    self._send_current_setting_command("B", 0)

            print("✅ AB通道电流置零完成")

        except Exception as e:
            print(f"❌ 重置通道电流失败: {e}")

    def _send_current_setting_command(self, channel: str, current_value: int):
        """发送电流设置指令到电刺激设备"""
        try:
            if not self.stim_connected or not self.stimulation_device:
                return

            # 这里使用与电流调节相同的指令格式
            # 具体指令格式需要根据电刺激设备的协议来实现
            # print(f"📤 发送{channel}通道电流设置指令: {current_value}mA")

            # 调用现有的预刺激停止方法来确保通道停止
            self._stop_channel_pre_stimulation(channel)

        except Exception as e:
            print(f"❌ 发送{channel}通道电流设置指令失败: {e}")

    def _start_simple_pre_stimulation(self, channel: str, current_value: int):
        """快速响应的预刺激启动 - 智能处理连续调节"""
        try:
            channel_num = 1 if channel == 'A' else 2

            # 检查是否已经在预刺激中
            is_already_pre_stimulating = (channel == 'A' and self.channel_a_pre_stimulating) or \
                                       (channel == 'B' and self.channel_b_pre_stimulating)

            if is_already_pre_stimulating:
                # 已经在预刺激中，只更新电流值并重置定时器
                self._update_pre_stimulation_current(channel, current_value)
            else:
                # 首次启动预刺激
                self._start_new_pre_stimulation(channel, current_value)

        except Exception as e:
            print(f"{channel}通道预刺激启动失败: {e}")
            # 重置状态
            if channel == 'A':
                self.channel_a_pre_stimulating = False
            else:
                self.channel_b_pre_stimulating = False

    def _start_new_pre_stimulation(self, channel: str, current_value: int):
        """启动新的预刺激"""
        try:
            # 设置预刺激状态
            if channel == 'A':
                self.channel_a_pre_stimulating = True
            else:
                self.channel_b_pre_stimulating = True

            # 立即更新UI状态（用户立即看到反馈）
            print(f"{channel}通道电流调节为{current_value}mA，启动3秒预刺激")

            # 启动UI定时器（从当前时刻开始计时）
            self._start_pre_stimulation_timer(channel)

            # 启动设备预刺激
            channel_num = 1 if channel == 'A' else 2

            # 直接在主线程中执行，参考文件中也是这样做的
            try:
                # 设置电流并启动刺激
                if self.stimulation_device.set_current(channel_num, float(current_value)):
                    if self.stimulation_device.start_stimulation(channel_num):
                        print(f"{channel}通道预刺激设备启动成功")
                    else:
                        print(f"{channel}通道预刺激设备启动失败")
                else:
                    print(f"{channel}通道电流设置失败")

            except Exception as e:
                print(f"{channel}通道预刺激异常: {str(e)}")

        except Exception as e:
            print(f"{channel}通道新预刺激启动失败: {e}")

    def _update_pre_stimulation_current(self, channel: str, current_value: int):
        """更新预刺激电流值并重置定时器"""
        try:
            # 重置UI定时器（重要：从当前时刻重新计时3秒）
            if channel == 'A':
                if hasattr(self, 'channel_a_pre_timer') and self.channel_a_pre_timer and self.channel_a_pre_timer.isActive():
                    self.channel_a_pre_timer.stop()
            else:
                if hasattr(self, 'channel_b_pre_timer') and self.channel_b_pre_timer and self.channel_b_pre_timer.isActive():
                    self.channel_b_pre_timer.stop()

            # 启动新的UI定时器
            self._start_pre_stimulation_timer(channel)

            print(f"{channel}通道电流调节为{current_value}mA，重置3秒预刺激定时器")

            # 更新设备电流（不重新启动预刺激，避免设备定时器冲突）
            channel_num = 1 if channel == 'A' else 2

            # 直接在主线程中执行
            try:
                # 只设置电流，不重新启动预刺激
                success = self.stimulation_device.set_current(channel_num, current_value)
                if success:
                    print(f"{channel}通道电流更新成功: {current_value}mA")
                else:
                    print(f"{channel}通道电流更新失败")

            except Exception as e:
                print(f"{channel}通道电流更新异常: {str(e)}")

        except Exception as e:
            print(f"{channel}通道电流更新失败: {e}")

    def _start_pre_stimulation_timer(self, channel: str):
        """启动预刺激定时器 - 每次调节都重新开始计时3秒"""
        try:
            if channel == 'A':
                self.channel_a_pre_timer = QTimer()
                self.channel_a_pre_timer.setSingleShot(True)
                self.channel_a_pre_timer.timeout.connect(lambda: self._on_pre_stimulation_finished('A'))
                self.channel_a_pre_timer.start(3000)  # 3秒定时器
            else:
                self.channel_b_pre_timer = QTimer()
                self.channel_b_pre_timer.setSingleShot(True)
                self.channel_b_pre_timer.timeout.connect(lambda: self._on_pre_stimulation_finished('B'))
                self.channel_b_pre_timer.start(3000)  # 3秒定时器

        except Exception as e:
            print(f"{channel}通道预刺激定时器启动失败: {e}")

    def _stop_channel_pre_stimulation(self, channel: str):
        """停止指定通道的预刺激"""
        try:
            channel_num = 1 if channel == 'A' else 2

            # 停止预刺激定时器
            if channel == 'A':
                if hasattr(self, 'channel_a_pre_timer') and self.channel_a_pre_timer and self.channel_a_pre_timer.isActive():
                    self.channel_a_pre_timer.stop()
                self.channel_a_pre_stimulating = False
            else:
                if hasattr(self, 'channel_b_pre_timer') and self.channel_b_pre_timer and self.channel_b_pre_timer.isActive():
                    self.channel_b_pre_timer.stop()
                self.channel_b_pre_stimulating = False

            # 停止设备刺激
            if self.stimulation_device:
                self.stimulation_device.stop_stimulation(channel_num)

            # print(f"{channel}通道预刺激已停止")

        except Exception as e:
            print(f"停止{channel}通道预刺激失败: {e}")

    def _on_pre_stimulation_finished(self, channel: str):
        """预刺激结束回调 - 停止设备刺激并更新状态"""
        try:
            # 停止设备刺激（重要：确保设备实际停止输出）
            channel_num = 1 if channel == 'A' else 2
            if self.stimulation_device and self.stim_connected:
                self.stimulation_device.stop_stimulation(channel_num)
                print(f"{channel}通道预刺激设备已停止")

            # 重置预刺激状态
            if channel == 'A':
                self.channel_a_pre_stimulating = False
            else:
                self.channel_b_pre_stimulating = False

            print(f"{channel}通道3秒预刺激结束")

        except Exception as e:
            print(f"{channel}通道预刺激结束处理失败: {e}")
            # 异常情况下也要重置状态
            if channel == 'A':
                self.channel_a_pre_stimulating = False
            else:
                self.channel_b_pre_stimulating = False

    def cleanup(self):
        """页面清理 - 系统退出时调用"""
        print("治疗页面清理开始...")

        # 1. 停止所有治疗活动
        if self.treatment_active:
            self._stop_treatment()

        # 2. 停止预刺激定时器
        if hasattr(self, 'channel_a_pre_timer') and self.channel_a_pre_timer and self.channel_a_pre_timer.isActive():
            self.channel_a_pre_timer.stop()
        if hasattr(self, 'channel_b_pre_timer') and self.channel_b_pre_timer and self.channel_b_pre_timer.isActive():
            self.channel_b_pre_timer.stop()

        # 停止设备状态监控定时器
        if hasattr(self, 'device_status_timer') and self.device_status_timer and self.device_status_timer.isActive():
            self.device_status_timer.stop()

        # 3. 断开电刺激设备连接
        if self.stimulation_device and self.stim_connected:
            print("正在关闭电刺激设备...")
            try:
                # 停止所有刺激
                self.stimulation_device.stop_all_stimulation()
                # 断开设备连接
                self.stimulation_device.disconnect_device()
                print("电刺激设备已安全关闭")
            except Exception as e:
                print(f"关闭电刺激设备时发生错误: {e}")

        # 4. 断开脑电设备连接
        if self.bluetooth_manager and self.eeg_connected:
            print("正在断开脑电设备...")
            try:
                self.bluetooth_manager.disconnect_device()
                print("脑电设备已断开")
            except Exception as e:
                print(f"断开脑电设备时发生错误: {e}")

        # 5. 停止所有定时器
        if hasattr(self, 'countdown_timer') and self.countdown_timer:
            self.countdown_timer.stop()
        if hasattr(self, 'monitoring_timer') and self.monitoring_timer:
            self.monitoring_timer.stop()

        # 6. 清理蓝牙管理器
        if hasattr(self, 'bluetooth_manager') and self.bluetooth_manager:
            try:
                self.bluetooth_manager.cleanup()
                self.bluetooth_manager = None
                print("蓝牙管理器已清理")
            except Exception as e:
                print(f"清理蓝牙管理器时发生错误: {e}")

        # 7. 清理PyQtGraph组件 - 修复ViewBox警告
        if hasattr(self, 'realtime_curves_widget') and self.realtime_curves_widget:
            print("正在清理PyQtGraph组件...")
            try:
                self.realtime_curves_widget.cleanup()
                print("PyQtGraph组件已清理")
            except Exception as e:
                print(f"清理PyQtGraph组件时发生错误: {e}")

        print("治疗页面清理完成")



    def _manual_start_stimulation(self):
        """手动开启电刺激"""
        # 检查设备连接状态
        if not self.stim_connected or not self.stimulation_device:
            show_warning(self, "设备未连接", "请先连接电刺激设备")
            return

        # 获取当前选中的通道
        selected_channels = []
        if self.channel_a_checkbox.isChecked():
            selected_channels.append("A")
        if self.channel_b_checkbox.isChecked():
            selected_channels.append("B")

        # 检查是否至少有一个通道被选中
        if not selected_channels:
            show_warning(self, "未选中通道", "请至少选中一个通道（A或B）")
            return

        # 检查选中通道的电流是否为0
        zero_current_channels = []
        for channel in selected_channels:
            current_value = self.stim_params[f"channel_{channel.lower()}"]
            if current_value <= 0:
                zero_current_channels.append(channel)

        if zero_current_channels:
            channels_text = "、".join(zero_current_channels)
            show_warning(self, "电流为零", f"{channels_text}通道电流为0，请先调节电流值")
            return

        # 使用快速双通道启动方法，减少AB通道启动时间差
        if len(selected_channels) == 2:
            # 双通道同时启动
            channel_a_current = self.stim_params["channel_a"] if "A" in selected_channels else 0
            channel_b_current = self.stim_params["channel_b"] if "B" in selected_channels else 0

            try:
                # 调用快速双通道启动方法
                success = self.stimulation_device.fast_dual_channel_start(channel_a_current, channel_b_current)

                if success:
                    success_channels = []
                    if "A" in selected_channels:
                        success_channels.append(f"A({channel_a_current}mA)")
                        print(f"手动开启电刺激成功 - A通道: {channel_a_current}mA")
                    if "B" in selected_channels:
                        success_channels.append(f"B({channel_b_current}mA)")
                        print(f"手动开启电刺激成功 - B通道: {channel_b_current}mA")

                    print(f"电刺激已启动: {', '.join(success_channels)}")
                else:
                    show_warning(self, "启动失败", "双通道启动失败，请检查设备状态")

            except Exception as e:
                print(f"快速双通道启动异常: {e}")
                show_warning(self, "启动失败", f"双通道启动异常: {e}")
        else:
            # 单通道启动
            success_channels = []
            failed_channels = []

            for channel in selected_channels:
                channel_num = 1 if channel == "A" else 2
                current_value = self.stim_params[f"channel_{channel.lower()}"]

                try:
                    # 设置电流
                    if self.stimulation_device.set_current(channel_num, current_value):
                        # 启动刺激
                        if self.stimulation_device.start_stimulation(channel_num):
                            success_channels.append(f"{channel}({current_value}mA)")
                            print(f"手动开启电刺激成功 - {channel}通道: {current_value}mA")
                        else:
                            failed_channels.append(channel)
                            print(f"手动开启电刺激失败 - {channel}通道启动失败")
                    else:
                        failed_channels.append(channel)
                        print(f"手动开启电刺激失败 - {channel}通道电流设置失败")
                except Exception as e:
                    failed_channels.append(channel)
                    print(f"手动开启电刺激异常 - {channel}通道: {e}")

            # 显示结果
            if success_channels:
                print(f"电刺激已启动: {', '.join(success_channels)}")
            if failed_channels:
                channels_text = "、".join(failed_channels)
                show_warning(self, "启动失败", f"{channels_text}通道启动失败，请检查设备状态")

    def _manual_stop_stimulation(self):
        """手动停止电刺激"""
        # 检查设备连接状态
        if not self.stim_connected or not self.stimulation_device:
            show_warning(self, "设备未连接", "电刺激设备未连接")
            return

        # 获取当前选中的通道
        selected_channels = []
        if self.channel_a_checkbox.isChecked():
            selected_channels.append("A")
        if self.channel_b_checkbox.isChecked():
            selected_channels.append("B")

        # 如果没有选中通道，停止所有通道
        if not selected_channels:
            selected_channels = ["A", "B"]
            print("未选中通道，停止所有通道的电刺激")

        # 停止选中通道的电刺激
        success_channels = []
        failed_channels = []

        for channel in selected_channels:
            channel_num = 1 if channel == "A" else 2

            try:
                if self.stimulation_device.stop_stimulation(channel_num):
                    success_channels.append(channel)
                    print(f"手动停止电刺激成功 - {channel}通道")
                else:
                    failed_channels.append(channel)
                    print(f"手动停止电刺激失败 - {channel}通道")
            except Exception as e:
                failed_channels.append(channel)
                print(f"手动停止电刺激异常 - {channel}通道: {e}")

        # 显示结果
        if success_channels:
            channels_text = "、".join(success_channels)
            print(f"电刺激已停止: {channels_text}通道")
        if failed_channels:
            channels_text = "、".join(failed_channels)
            show_warning(self, "停止失败", f"{channels_text}通道停止失败，请检查设备状态")

        # 重置A、B通道电流值为0
        self._reset_channel_currents()

        # 重置刺激状态显示
        self.channel_a_stimulating = False
        self.channel_b_stimulating = False
        self._update_channel_display("A")
        self._update_channel_display("B")

    def _reset_channel_currents(self):
        """重置A、B通道电流值为0"""
        try:
            # 重置A通道电流
            self.channel_a_adjuster.set_value(0)
            self.stim_params["channel_a"] = 0

            # 重置B通道电流
            self.channel_b_adjuster.set_value(0)
            self.stim_params["channel_b"] = 0

            print("A、B通道电流已重置为0mA")

        except Exception as e:
            print(f"重置通道电流时发生错误: {e}")

    def _check_device_status(self):
        """检查设备状态并更新通道显示 - 基于设备反馈信号"""
        try:
            if not self.stimulation_device or not self.stim_connected:
                # 设备未连接时重置状态
                if self.channel_a_stimulating or self.channel_b_stimulating:
                    self.channel_a_stimulating = False
                    self.channel_b_stimulating = False
                    self._update_channel_display("A")
                    self._update_channel_display("B")
                return

            # 获取设备反馈的通道状态
            channel_a_status = self.stimulation_device.get_channel_status(1)  # A通道
            channel_b_status = self.stimulation_device.get_channel_status(2)  # B通道

            # 判断A通道刺激状态（状态1表示正常刺激，其他状态都是未刺激）
            a_stimulating = (channel_a_status == 1)
            if a_stimulating != self.channel_a_stimulating:
                import time
                current_time = time.time()
                self.channel_a_stimulating = a_stimulating
                self._update_channel_display("A")

            # 判断B通道刺激状态（状态1表示正常刺激，其他状态都是未刺激）
            b_stimulating = (channel_b_status == 1)
            if b_stimulating != self.channel_b_stimulating:
                import time
                current_time = time.time()
                self.channel_b_stimulating = b_stimulating
                self._update_channel_display("B")

        except Exception as e:
            print(f"检查设备状态失败: {e}")



    def _update_channel_display(self, channel: str):
        """更新通道显示状态"""
        try:
            if channel == "A":
                if self.channel_a_stimulating:
                    self.channel_a_checkbox.setText("A通道 ⚡")
                    # 直接设置样式，避免全局CSS冲突
                    self.channel_a_checkbox.setStyleSheet("""
                        QCheckBox {
                            color: #10b981;
                            font-weight: bold;
                        }
                    """)
                else:
                    self.channel_a_checkbox.setText("A通道")
                    # 恢复默认样式
                    self.channel_a_checkbox.setStyleSheet("")

            elif channel == "B":
                if self.channel_b_stimulating:
                    self.channel_b_checkbox.setText("B通道 ⚡")
                    # 直接设置样式，避免全局CSS冲突
                    self.channel_b_checkbox.setStyleSheet("""
                        QCheckBox {
                            color: #10b981;
                            font-weight: bold;
                        }
                    """)
                else:
                    self.channel_b_checkbox.setText("B通道")
                    # 恢复默认样式
                    self.channel_b_checkbox.setStyleSheet("")

        except Exception as e:
            print(f"更新{channel}通道显示失败: {e}")

    def _toggle_pretraining(self):
        """切换预训练状态"""
        if not self.pre_training_active:
            self._start_pretraining()
        else:
            self._stop_pretraining()

    def _start_pretraining(self):
        """开始预训练"""
        try:
            # 检查患者信息是否已选择
            if not self._validate_patient_info():
                from ui.components.themed_message_box import show_warning
                show_warning(self, "患者信息缺失", "请先在患者管理页面选择患者后再开始预训练")
                return

            # 检查设备连接状态
            if not self.eeg_connected:
                from ui.components.themed_message_box import show_warning
                show_warning(self, "设备未连接", "请先连接脑电设备后再开始预训练")
                return

            self.pre_training_active = True
            self.pretraining_btn.setText("训练中...")
            self.pretraining_btn.setEnabled(False)

            # 禁用治疗开始按钮（训练和治疗不能同时进行）
            self.treatment_main_btn.setEnabled(False)

            # 启用停止按钮（变为红色）
            self.stop_pretraining_btn.setEnabled(True)

            # 更新训练状态显示
            self._update_training_status("准备")

            print("开始预训练")

            # 初始化特征提取管理器
            self._initialize_feature_extraction()

            # 启动多轮训练流程（统一使用多轮训练逻辑）
            self._start_multi_round_training()

        except Exception as e:
            print(f"启动预训练失败: {e}")
            self._reset_pretraining_state()

    def _initialize_feature_extraction(self):
        """初始化特征提取管理器和实时集成管理器"""
        try:
            # 1. 初始化实时集成管理器（环形缓冲区处理）
            from services.eeg_preprocessing.realtime_integration_manager import RealtimeIntegrationManager
            from services.eeg_preprocessing.preprocessing_config import PreprocessingConfig

            # 创建预处理配置
            preprocessing_config = PreprocessingConfig()

            # 创建实时集成管理器
            self.realtime_integration_manager = RealtimeIntegrationManager(preprocessing_config)

            # 方案A：不再使用独立特征管理器
            config = None

            # 3. 连接实时集成管理器和特征提取管理器
            self.realtime_integration_manager.set_window_ready_callback(
                self._on_realtime_window_ready
            )

            # 4. 启动实时集成管理器
            self.realtime_integration_manager.start()

            # 5. 存储最新的窗口数据用于训练数据收集
            self.latest_window_data = None

            # print(f"实时集成管理器和特征提取管理器初始化完成")
            # print(f"启用特征算法: {config.get_enabled_extractors()}")

            # 方案A：不再进行旧特征/配置的验证校验

        except Exception as e:
            print(f"特征提取系统初始化失败: {e}")
            raise

    def _start_training_session(self):
        """启动训练会话"""
        try:
            from services.training_session_manager import TrainingSessionManager, TrainingConfig


            # 创建训练配置
            config = TrainingConfig(
                trials_per_class=10,  # 每类10个试次，总共20个
                trial_duration=2.0,  # 修改为2秒数据收集
                rest_duration=2.0,
                preparation_time=0.0,  # 移除无意义的准备时间
                inter_trial_interval=2.5,  # 调整为2.5秒间隔
                voice_delay=1.0  # 语音提示后延迟1.0秒
            )

            # 创建训练会话管理器（如果不存在或重新创建）
            if self.session_manager:
                # 断开之前的信号连接
                self._disconnect_session_signals()

            self.session_manager = TrainingSessionManager(config)

            # 设置特征管理器
            if self.feature_manager:
                self.session_manager.set_feature_manager(self.feature_manager)

            # 设置患者ID和多轮训练状态
            if self.current_patient:
                patient_id = self.current_patient.get('bianhao') or self.current_patient.get('id', '')
                if patient_id:
                    self.session_manager.set_patient_id(patient_id)

                    # 如果是多轮训练，启用多轮训练模式
                    if self.multi_round_training_active:
                        self.session_manager.enable_multi_round_training(True)

            # 设置数据收集回调（使用实时窗口数据）
            self.session_manager.set_data_callback(self._get_current_window_data)

            # 启用康复数据收集功能
            hospital_id = "default_hospital"  # 可以从配置文件或用户设置中获取
            self.session_manager.enable_rehabilitation_data_collection(hospital_id)

            # 连接信号
            self._connect_session_signals()

            # 启动训练会话
            success = self.session_manager.start_session()
            if not success:
                print("启动训练会话失败")
                self._reset_pretraining_state()

        except Exception as e:
            print(f"启动训练会话失败: {e}")
            self._reset_pretraining_state()

    def _connect_session_signals(self):
        """连接训练会话信号"""
        if self.session_manager:
            self.session_manager.state_changed.connect(self._on_training_state_changed)
            self.session_manager.progress_updated.connect(self._on_training_progress_updated)
            self.session_manager.trial_started.connect(self._on_trial_started)
            self.session_manager.trial_completed.connect(self._on_trial_completed)
            self.session_manager.session_completed.connect(self._on_session_completed)
            self.session_manager.error_occurred.connect(self._on_training_error)
            self.session_manager.voice_prompt_requested.connect(self._on_voice_prompt_requested)

    def _disconnect_session_signals(self):
        """断开训练会话信号"""
        if self.session_manager:
            try:
                self.session_manager.state_changed.disconnect()
                self.session_manager.progress_updated.disconnect()
                self.session_manager.trial_started.disconnect()
                self.session_manager.trial_completed.disconnect()
                self.session_manager.session_completed.disconnect()
                self.session_manager.error_occurred.disconnect()
                self.session_manager.voice_prompt_requested.disconnect()
            except Exception as e:
                print(f"断开信号连接失败: {e}")

    def _on_training_state_changed(self, state: str):
        """训练状态变化处理"""
        print(f"训练状态: {state}")

        # 将训练状态映射到用户友好的显示文本
        state_mapping = {
            'idle': '准备',
            'preparing': '准备',
            'preparation': '准备',
            'motor_imagery': '想象状态',
            'rest': '平静状态',
            'trial_end': '休息',
            'baseline': '平静状态',
            'task': '想象状态',
            'feedback': '休息',
            'inter_trial': '休息'
        }

        display_state = state_mapping.get(state, '平静状态')
        self._update_training_status(display_state)

    def _on_training_progress_updated(self, current: int, total: int):
        """训练进度更新处理"""
        progress = int((current / total) * 100) if total > 0 else 0
        print(f"训练进度: {current}/{total} ({progress}%)")

    def _on_trial_started(self, trial_number: int, trial_type: str):
        """试次开始处理"""
        print(f"试次 {trial_number} 开始: {trial_type}")

    def _on_trial_completed(self, trial_number: int, trial_type: str, quality_info: dict):
        """试次完成处理"""
        quality = quality_info.get('quality_score', 0.0)
        print(f"试次 {trial_number} 完成: {trial_type}, 质量: {quality:.3f}")

    def _on_session_completed(self, session_stats: dict):
        """会话完成处理"""
        try:
            if self.multi_round_training_active:
                print(f"第{self.current_training_round}轮训练会话完成")
            else:
                print("训练会话完成")

            print(f"统计信息: {session_stats}")

            # 调用训练完成处理
            self._on_training_completed()

        except Exception as e:
            print(f"会话完成处理失败: {e}")
            self._reset_pretraining_state()

    def _on_training_error(self, error_message: str):
        """训练错误处理"""
        print(f"训练错误: {error_message}")

        # 立即停止训练会话，避免继续执行
        try:
            if hasattr(self, 'session_manager') and self.session_manager:
                print("🛑 检测到训练错误，立即停止训练会话...")
                self.session_manager.stop_session()
                print("✓ 训练会话已停止")
        except Exception as e:
            print(f"⚠️ 停止训练会话失败: {e}")

        # 弹出主题化错误对话框通知用户
        from ui.components.themed_message_box import show_warning
        show_warning(
            self,
            "训练错误",
            f"错误详情：{error_message}\n\n训练已自动停止，请检查设备连接后重新开始训练。"
        )

        self._reset_pretraining_state()

    def _on_voice_prompt_requested(self, prompt_key: str):
        """语音提示请求处理（简洁版本）"""
        try:
            from core.simple_voice import speak_prompt
            speak_prompt(prompt_key, async_mode=True)
        except Exception as e:
            print(f"语音提示失败: {e}")

    def _on_training_completed(self):
        """训练完成处理"""
        try:
            from core.simple_voice import speak_prompt

            # 实际的特征提取和模型训练已在TrainingSessionManager中完成
            # 包含高性能Stacking集成学习系统（性能93.2%±5.6%）
            print("训练数据处理完成，高性能Stacking分类器已就绪")

            speak_prompt('save_success')

            # 更新轮次信息
            if self.multi_round_training_active:
                # 多轮训练模式：轮次信息已在session_manager中管理
                self._update_round_display()
                print(f"第{self.current_training_round}轮训练完成，模型已保存")
            else:
                # 单轮训练模式：设置为第1轮
                self.current_training_round = 1
                self.total_training_rounds = 1
                self._update_round_display()
                print("预训练完成，模型已保存")

            self._reset_pretraining_state()

        except Exception as e:
            print(f"训练完成处理失败: {e}")
            from core.simple_voice import speak_prompt
            speak_prompt('save_failed')
            self._reset_pretraining_state()

    def _update_round_display(self):
        """更新轮次显示"""
        try:
            if hasattr(self, 'current_round_label'):
                # 如果训练未激活或轮次为0，显示"--"
                if not self.pre_training_active or self.current_training_round == 0:
                    self.current_round_label.setText("--")
                else:
                    # 训练进行中，显示实际轮次数字
                    self.current_round_label.setText(str(self.current_training_round))
        except Exception as e:
            print(f"更新轮次显示失败: {e}")



    def _reset_pretraining_state(self):
        """重置预训练状态"""
        self.pre_training_active = False
        # 注意：不重置multi_round_training_active，保持默认开启状态

        # 重置轮次计数
        self.current_training_round = 0

        # 断开信号连接
        self._disconnect_session_signals()

        # 更新按钮状态
        self.pretraining_btn.setText("开始训练")
        self.pretraining_btn.setEnabled(True)

        # 恢复治疗开始按钮（训练结束后可以开始治疗）
        self.treatment_main_btn.setEnabled(True)

        # 禁用停止按钮（恢复灰色）
        self.stop_pretraining_btn.setEnabled(False)

        # 重置训练状态显示
        self._update_training_status("--")

        # 重置轮次显示
        self._update_round_display()



    def _stop_pretraining(self):
        """停止预训练 - 改进版本，避免卡死"""
        try:
            print("开始停止预训练...")
            self.pre_training_active = False

            # 第一步：立即停止训练会话管理器（最重要）
            if hasattr(self, 'session_manager') and self.session_manager:
                try:
                    self.session_manager.stop_session()
                    print("✓ 训练会话管理器已停止")
                except Exception as e:
                    print(f"⚠️ 停止训练会话管理器失败: {e}")

            # 第二步：停止实时集成管理器并清理回调
            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                try:
                    # 清除窗口就绪回调（重要：避免治疗时的双重触发）
                    self.realtime_integration_manager.set_window_ready_callback(None)
                    # 停止管理器
                    self.realtime_integration_manager.stop()
                    print("✓ 实时集成管理器已停止，回调已清理")
                except Exception as e:
                    print(f"⚠️ 停止实时集成管理器失败: {e}")

            # 第三步：停止所有可能的定时器（重要：避免状态冲突）
            try:
                if hasattr(self, 'classification_timer') and self.classification_timer and self.classification_timer.isActive():
                    self.classification_timer.stop()
                    print("✓ 分类定时器已停止")

                if hasattr(self, 'countdown_timer') and self.countdown_timer and self.countdown_timer.isActive():
                    self.countdown_timer.stop()
                    print("✓ 倒计时定时器已停止")
            except Exception as e:
                print(f"⚠️ 停止定时器失败: {e}")

            # 第四步：安全处理语音引擎（非阻塞方式）
            self._safe_stop_voice_engine()

            # 第四步：重置UI状态
            self._reset_pretraining_state()
            print("✓ 预训练已安全停止")

        except Exception as e:
            print(f"❌ 停止预训练失败: {e}")
            # 强制重置状态，确保UI不会卡住
            self._reset_pretraining_state()

    def _safe_stop_voice_engine(self):
        """安全停止语音引擎（简洁版本）"""
        try:
            from core.simple_voice import stop_voice, speak_simple

            # 停止当前语音
            stop_voice()
            print("✓ 语音播放已停止")

            # 播放停止提示
            speak_simple("训练停止", async_mode=True)
            print("✓ 停止语音提示已发送")

        except Exception as e:
            print(f"⚠️ 停止语音失败: {e}")





    def _has_training_history(self) -> bool:
        """检查是否有训练历史"""
        try:
            if not hasattr(self, 'current_patient') or not self.current_patient:
                return False

            patient_id = self.current_patient.get('id', '') or self.current_patient.get('bianhao', '')
            if not patient_id:
                return False

            # 首先检查训练历史记录
            try:
                from services.multi_round_training_manager import MultiRoundTrainingManager
                manager = MultiRoundTrainingManager(patient_id)
                if manager.has_training_history():
                    return True
            except Exception as e:
                print(f"检查训练历史记录失败: {e}")

            # 备选方案：检查是否存在历史训练文件
            import os
            from utils.path_manager import get_data_file
            features_dir = str(get_data_file("features"))

            if not os.path.exists(features_dir):
                return False

            # 查找任何以患者ID开头的特征文件
            for filename in os.listdir(features_dir):
                if filename.startswith(f"{patient_id}_") and filename.endswith('.pkl'):
                    return True

            return False

        except Exception as e:
            print(f"检查训练历史失败: {e}")
            return False

    def _start_multi_round_training(self):
        """启动多轮训练"""
        try:
            # 检查是否是首次训练还是继续训练
            has_history = self._has_training_history()

            if has_history:
                # print("启动多轮训练（继续训练）...")
                pass
            else:
                # print("启动多轮训练（首次训练）...")
                pass

            # 初始化多轮训练管理器
            self._initialize_multi_round_training()

            # 启动新一轮训练
            self._start_new_training_round()

        except Exception as e:
            print(f"启动多轮训练失败: {e}")
            self._reset_pretraining_state()

    def _initialize_multi_round_training(self):
        """初始化多轮训练管理器"""
        try:
            if not hasattr(self, 'current_patient') or not self.current_patient:
                print("患者信息未设置")
                return

            patient_id = self.current_patient.get('id', '') or self.current_patient.get('bianhao', '')
            if not patient_id:
                print("患者ID无效")
                return

            # 设置多轮训练状态
            self.multi_round_training_active = True

            # 如果session_manager存在，设置患者ID并启用多轮训练
            if self.session_manager:
                self.session_manager.set_patient_id(patient_id)
                self.session_manager.enable_multi_round_training(True)

            # print(f"多轮训练管理器初始化成功，患者ID: {patient_id}")

        except Exception as e:
            print(f"初始化多轮训练管理器失败: {e}")

    def _start_new_training_round(self):
        """开始新一轮训练"""
        try:
            # 确保多轮训练状态已设置
            self.multi_round_training_active = True

            # 先启动训练会话（这会创建session_manager）
            self._start_training_session()

            # 如果session_manager创建成功，开始新轮次
            if self.session_manager:
                try:
                    round_number = self.session_manager.start_new_training_round()
                    self.current_training_round = round_number
                    self.total_training_rounds = max(round_number, self.total_training_rounds)

                    # 更新UI状态
                    self.pretraining_btn.setText(f"第{round_number}轮训练中...")

                    # 更新轮次显示
                    self._update_round_display()

                    print(f"第{round_number}轮训练已开始")

                except Exception as e:
                    print(f"启动新轮次失败: {e}")
                    # 如果轮次启动失败，至少设置为下一轮
                    self.current_training_round = self._get_next_round_number()
                    self.pretraining_btn.setText(f"第{self.current_training_round}轮训练中...")
                    self._update_round_display()
            else:
                # 如果session_manager创建失败，设置基本状态
                self.current_training_round = self._get_next_round_number()
                self.pretraining_btn.setText(f"第{self.current_training_round}轮训练中...")
                self._update_round_display()

        except Exception as e:
            print(f"开始新一轮训练失败: {e}")
            # 重置状态
            self._reset_pretraining_state()

    def _get_next_round_number(self) -> int:
        """获取下一轮训练的轮次号"""
        try:
            if not hasattr(self, 'current_patient') or not self.current_patient:
                return 1

            patient_id = self.current_patient.get('id', '') or self.current_patient.get('bianhao', '')
            if not patient_id:
                return 1

            from services.multi_round_training_manager import MultiRoundTrainingManager
            manager = MultiRoundTrainingManager(patient_id)
            return manager.get_next_round_number()

        except Exception as e:
            print(f"获取下一轮次号失败: {e}")
            return 1



    def _start_treatment(self):
        """开始治疗"""
        # 完整的前置条件验证
        validation_result = self._validate_treatment_conditions()
        if not validation_result['valid']:
            from ui.components.themed_message_box import show_warning
            show_warning(self, "无法开始治疗", validation_result['message'])
            return

        # 启动治疗工作流程
        self._start_treatment_workflow()

    def _start_treatment_workflow(self):
        """启动治疗工作流程"""
        try:
            print("🚀 开始治疗工作流程...")

            # 更新UI状态
            self.treatment_active = True
            self.treatment_paused = False
            self.treatment_main_btn.setText("暂停治疗")
            self.treatment_main_btn.setObjectName("btn_pause")
            self.treatment_main_btn.setStyle(self.treatment_main_btn.style())  # 刷新样式
            self.treatment_main_btn.setEnabled(True)  # 保持可用状态

            # 禁用训练开始按钮（训练和治疗不能同时进行）
            self.pretraining_btn.setEnabled(False)

            self.stop_treatment_btn.setEnabled(True)  # 启用停止按钮（变为红色）
            self.system_status.setText("治疗进行中")

            # 初始化治疗会话
            self._initialize_treatment_session()

            # 🔧 初始化统一定时器管理器（解决多定时器竞争问题）
            self._initialize_unified_timer_manager()

            # 启动实时分类系统
            self._initialize_realtime_classification()

            # 确保实时集成管理器已初始化并启动
            if not hasattr(self, 'realtime_integration_manager') or not self.realtime_integration_manager:
                print("⚠️ 实时集成管理器未初始化，正在初始化...")
                self._initialize_realtime_integration_manager()

            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                if not self.realtime_integration_manager.is_running:
                    self.realtime_integration_manager.start()
                    # print("✅ 实时集成管理器已启动")

                # 重新设置窗口就绪回调（修复训练后治疗无数据的问题）
                self.realtime_integration_manager.set_window_ready_callback(
                    self._on_realtime_window_ready
                )
                # print("✅ 窗口就绪回调已重新设置")

                # 注意：分类定时器现在由统一定时器管理器自动管理
                # print("✅ 分类定时器由统一定时器管理器管理")

            # 启动UDP通信（发送TREAT指令）
            self._start_udp_communication()

            # 连接运动想象检测信号
            self._connect_motor_imagery_signals()
            # 初始化触发空白期定时
            try:
                import time as _time
                if self.trigger_blank_enabled:
                    self.trigger_blank_until = _time.time() + float(self.trigger_blank_seconds)
                    self._blanking_reset_done = False
                    # 记录原样式并设置空白期样式（橙色）
                    if hasattr(self, 'motor_imagery_progress_bar') and self.motor_imagery_progress_bar:
                        self._prob_progressbar_original_style = self.motor_imagery_progress_bar.styleSheet()
                        self.motor_imagery_progress_bar.setStyleSheet(
                            "QProgressBar{background:transparent;border-radius:4px;}"
                            "QProgressBar::chunk{background-color:#f59e0b;border-radius:4px;}"
                        )
                    print(f"⏳ 触发空白期启动，持续 {self.trigger_blank_seconds}s")
            except Exception as e:
                print(f"⚠️ 初始化触发空白期失败: {e}")


            # 注意：倒计时现在由统一定时器管理器自动管理
            # print("✅ 倒计时定时器由统一定时器管理器管理")

            # 初始化鼓励间隔计时器
            self._initialize_encouragement_timer()

            # 开始治疗循环：播放初始语音提示
            self._start_treatment_cycle()
            self.treatment_started.emit()

        except Exception as e:
            print(f"❌ 启动治疗工作流程失败: {e}")
            self._stop_treatment()

    def _initialize_treatment_session(self):
        """初始化治疗会话"""
        try:
            from datetime import datetime

            # 创建治疗会话数据
            self.treatment_session = {
                'patient_id': self.current_patient.get('bianhao', ''),
                'patient_name': self.current_patient.get('name', ''),
                'start_time': datetime.now(),
                'end_time': None,
                'total_imagination_count': 0,
                'successful_trigger_count': 0,
                'treatment_duration_minutes': 0,
                'success_rate': 0.0,
                'treatment_score': 0,
                'treatment_grade': '待评定'
            }

            # 重置统计计数器（保持兼容性）
            self.success_triggers = 0
            self.total_triggers = 0

            # 重置治疗流程计数器
            self.imagination_request_count = 0  # 要求想象次数
            self.successful_trigger_count = 0   # 实际触发次数

            # 重置概率显示
            self._update_probability_display(0)

            # 重置电刺激状态
            self.stimulation_active = False
            self.voice_playing = False

            # 重置暂停状态
            self.treatment_paused = False
            self.paused_countdown = 0

            # 读取鼓励间隔设置
            self.encouragement_interval = self._get_encouragement_interval_from_settings()

        except Exception as e:
            print(f"❌ 初始化治疗会话失败: {e}")

    def _start_udp_communication(self):
        """启动UDP通信"""
        try:
            # 初始化UDP通信器
            if not hasattr(self, 'udp_communicator'):
                from core.udp_communicator import get_udp_communicator
                self.udp_communicator = get_udp_communicator()

            # 启动UDP监听
            if not self.udp_communicator.is_connected:
                if not self.udp_communicator.start_listening():
                    print("❌ UDP监听启动失败")
                    return

            # 发送TREAT指令到VR系统
            self.udp_communicator.send_treat_command()

        except Exception as e:
            print(f"❌ 启动UDP通信失败: {e}")

    def _connect_motor_imagery_signals(self):
        """连接运动想象检测信号"""
        try:
            # 这里可以连接实时分类的信号
            # 当检测到运动想象时触发电刺激
            # print("🔗 运动想象检测信号已连接")
            pass

        except Exception as e:
            print(f"❌ 连接运动想象信号失败: {e}")

    def _initialize_encouragement_timer(self):
        """初始化鼓励间隔计时器"""
        try:
            from PySide6.QtCore import QTimer

            if not hasattr(self, 'encouragement_timer') or self.encouragement_timer is None:
                self.encouragement_timer = QTimer()
                self.encouragement_timer.setSingleShot(True)
                self.encouragement_timer.timeout.connect(self._on_encouragement_timeout)
                # print("✅ 鼓励间隔计时器初始化完成")

        except Exception as e:
            print(f"❌ 初始化鼓励间隔计时器失败: {e}")

    def _start_treatment_cycle(self):
        """开始治疗循环"""
        try:
            # 播放初始语音提示
            self._play_treatment_voice("treatment_start")

            # 要求想象次数+1
            self.imagination_request_count += 1

            # 更新UI显示
            self._update_treatment_status()

            # 启动鼓励间隔计时器
            self._start_encouragement_timer()

            print(f"🎯 开始治疗循环 - 要求想象次数: {self.imagination_request_count}")

        except Exception as e:
            print(f"❌ 开始治疗循环失败: {e}")

    def _play_treatment_voice(self, prompt_key: str):
        """播放治疗语音提示"""
        try:
            from core.simple_voice import speak_treatment_prompt

            # 设置语音播放状态
            self.voice_playing = True

            # 播放语音（异步）
            speak_treatment_prompt(prompt_key, async_mode=True)

            # 根据语音内容设置合适的播放时长
            voice_durations = {
                'treatment_start': 2500,      # "开始治疗，请想象运动" - 2.5秒
                'encouragement': 2000,        # "请继续努力，保持专注" - 2秒
                'success_feedback': 3000,     # "你做的很棒，请继续进行运动想象" - 3秒
                'treatment_resume': 2500,     # "治疗恢复，请继续想象运动" - 2.5秒
                'treatment_paused': 1500,     # "暂停治疗" - 1.5秒
                'treatment_complete': 1500,   # "治疗完成" - 1.5秒
                'treatment_stopped': 1500     # "治疗停止" - 1.5秒
            }

            duration = voice_durations.get(prompt_key, 2000)  # 默认2秒

            # 设置语音播放完成的定时器
            from PySide6.QtCore import QTimer
            if not hasattr(self, 'voice_timer'):
                self.voice_timer = QTimer()
                self.voice_timer.setSingleShot(True)
                self.voice_timer.timeout.connect(self._on_voice_finished)

            self.voice_timer.start(duration)

        except Exception as e:
            print(f"❌ 播放治疗语音失败: {e}")
            self.voice_playing = False

    def _on_voice_finished(self):
        """语音播放完成回调"""
        self.voice_playing = False

    def _start_encouragement_timer(self):
        """启动鼓励间隔计时器"""
        try:
            if self.encouragement_timer and not self.encouragement_timer.isActive():
                timeout_ms = self.encouragement_interval * 1000
                self.encouragement_timer.start(timeout_ms)

        except Exception as e:
            print(f"❌ 启动鼓励间隔计时器失败: {e}")

    def _stop_encouragement_timer(self):
        """停止鼓励间隔计时器"""
        try:
            if self.encouragement_timer and self.encouragement_timer.isActive():
                self.encouragement_timer.stop()

        except Exception as e:
            print(f"❌ 停止鼓励间隔计时器失败: {e}")

    def _on_encouragement_timeout(self):
        """鼓励间隔超时处理"""
        try:
            if not self.treatment_active:
                return

            # 播放鼓励语音
            self._play_treatment_voice("encouragement")

            # 要求想象次数+1
            self.imagination_request_count += 1

            # 更新UI显示
            self._update_treatment_status()

            # 重新启动鼓励间隔计时器
            self._start_encouragement_timer()

        except Exception as e:
            print(f"❌ 处理鼓励间隔超时失败: {e}")

    def _continue_treatment_cycle(self):
        """继续治疗循环（电刺激结束后）"""
        try:
            if not self.treatment_active:
                return

            # 播放成功反馈语音
            self._play_treatment_voice("success_feedback")

            # 要求想象次数+1
            self.imagination_request_count += 1

            # 更新UI显示
            self._update_treatment_status()

            # 重新启动鼓励间隔计时器
            self._start_encouragement_timer()

        except Exception as e:
            print(f"❌ 继续治疗循环失败: {e}")

    def _handle_treatment_button_click(self):
        """处理治疗按钮点击"""
        try:
            if not self.treatment_active:
                # 开始治疗
                self._start_treatment()
            elif self.treatment_paused:
                # 恢复治疗
                self._resume_treatment()
            else:
                # 暂停治疗
                self._pause_treatment()

        except Exception as e:
            print(f"❌ 处理治疗按钮点击失败: {e}")

    def _start_treatment(self):
        """开始治疗"""
        # 完整的前置条件验证
        validation_result = self._validate_treatment_conditions()
        if not validation_result['valid']:
            from ui.components.themed_message_box import show_warning
            show_warning(self, "无法开始治疗", validation_result['message'])
            return

        # 启动治疗工作流程
        self._start_treatment_workflow()

    def _pause_treatment(self):
        """暂停治疗"""
        try:
            self.treatment_paused = True

            # 停止所有电刺激
            self._stop_all_stimulation()

            # 发送UDP STOP指令通知VR系统
            self._send_udp_stop_command()

            # 停止所有定时器
            self._stop_all_timers()

            # 保存当前倒计时值
            self.paused_countdown = self.treatment_countdown

            # 播放暂停治疗语音提示
            self._play_treatment_voice("treatment_paused")

            # 更新按钮状态
            self.treatment_main_btn.setText("恢复治疗")
            self.treatment_main_btn.setObjectName("btn_pause")
            self.treatment_main_btn.setStyle(self.treatment_main_btn.style())  # 刷新样式

            # 停止按钮保持可用状态
            # self.stop_treatment_btn.setEnabled(True) # 已经是可用状态

            self.system_status.setText("治疗已暂停")

        except Exception as e:
            print(f"❌ 暂停治疗失败: {e}")

    def _resume_treatment(self):
        """恢复治疗"""
        try:
            print("▶️ 恢复治疗...")

            self.treatment_paused = False

            # 恢复倒计时
            self.treatment_countdown = self.paused_countdown

            # 更新按钮状态
            self.treatment_main_btn.setText("暂停治疗")
            self.treatment_main_btn.setObjectName("btn_pause")
            self.treatment_main_btn.setStyle(self.treatment_main_btn.style())  # 刷新样式

            # 重新启动治疗流程（不播放成功反馈语音）
            self._restart_treatment_workflow_for_resume()

            # 播放治疗恢复语音提示并开始新的治疗循环
            self._start_resume_treatment_cycle()

            self.system_status.setText("治疗进行中")

        except Exception as e:
            print(f"❌ 恢复治疗失败: {e}")

    def _stop_all_timers(self):
        """停止所有定时器"""
        try:
            # 🔧 停止统一定时器管理器（替代独立的分类和倒计时定时器）
            if hasattr(self, 'unified_timer_manager') and self.unified_timer_manager:
                self.unified_timer_manager.stop()
                print("✅ 统一定时器管理器已停止")

            # 停止鼓励间隔计时器（保留，因为它不是核心治疗定时器）
            self._stop_encouragement_timer()

            # 停止实时集成管理器
            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                self.realtime_integration_manager.stop()

        except Exception as e:
            print(f"❌ 停止定时器失败: {e}")

    def _restart_treatment_workflow(self):
        """重新启动治疗工作流程"""
        try:
            # 🔧 重新启动统一定时器管理器
            self._initialize_unified_timer_manager()

            # 重新启动实时分类系统
            self._initialize_realtime_classification()

            # 确保实时集成管理器已初始化并启动
            if not hasattr(self, 'realtime_integration_manager') or not self.realtime_integration_manager:
                print("⚠️ 实时集成管理器未初始化，正在初始化...")
                self._initialize_realtime_integration_manager()

            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                if not self.realtime_integration_manager.is_running:
                    self.realtime_integration_manager.start()
                    # print("✅ 实时集成管理器已重新启动")

            # 注意：分类和倒计时定时器现在由统一定时器管理器自动管理
            # print("✅ 分类和倒计时定时器由统一定时器管理器管理")

            # 继续治疗循环（播放成功反馈语音）
            self._continue_treatment_cycle()

            # print("✅ 治疗工作流程已重新启动")

        except Exception as e:
            print(f"❌ 重新启动治疗工作流程失败: {e}")

    def _restart_treatment_workflow_for_resume(self):
        """为恢复治疗重新启动工作流程（不播放成功反馈语音）"""
        try:
            # 🔧 重新启动统一定时器管理器
            self._initialize_unified_timer_manager()

            # 重新启动实时分类系统
            self._initialize_realtime_classification()

            # 确保实时集成管理器已初始化并启动
            if not hasattr(self, 'realtime_integration_manager') or not self.realtime_integration_manager:
                print("⚠️ 实时集成管理器未初始化，正在初始化...")
                self._initialize_realtime_integration_manager()

            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                if not self.realtime_integration_manager.is_running:
                    self.realtime_integration_manager.start()
                    # print("✅ 实时集成管理器已重新启动")

            # 注意：分类和倒计时定时器现在由统一定时器管理器自动管理
            # print("✅ 分类和倒计时定时器由统一定时器管理器管理")

            # print("✅ 恢复治疗工作流程已重新启动")

        except Exception as e:
            print(f"❌ 重新启动恢复治疗工作流程失败: {e}")

    def _start_resume_treatment_cycle(self):
        """开始恢复治疗循环"""
        try:
            if not self.treatment_active:
                return

            # print("🔄 开始恢复治疗循环 - 播放恢复语音")

            # 播放恢复治疗语音
            self._play_treatment_voice("treatment_resume")

            # 要求想象次数+1
            self.imagination_request_count += 1

            # 更新UI显示
            self._update_treatment_status()

            # 启动鼓励间隔计时器
            self._start_encouragement_timer()

            print(f"🎯 恢复治疗 - 要求想象次数: {self.imagination_request_count}")

        except Exception as e:
            print(f"❌ 开始恢复治疗循环失败: {e}")

    def _complete_treatment(self):
        """治疗时长到达，自动完成治疗"""
        try:
            print("🎉 治疗时长到达，自动完成治疗...")

            # 停止所有电刺激
            self._stop_all_stimulation()

            # 先完成治疗会话并保存数据（在重置电流之前）
            self._finalize_treatment_session()

            # 将AB通道电流置零并发送指令
            self._reset_all_channels_to_zero()

            # 发送UDP STOPALL指令
            self._send_udp_stopall_command()

            # 播放治疗完成语音提示
            self._play_treatment_voice("treatment_complete")

            # 更新UI状态
            self.treatment_active = False
            self.treatment_paused = False  # 重置暂停状态
            self.paused_countdown = 0      # 重置暂停倒计时
            self.treatment_main_btn.setText("开始治疗")
            self.treatment_main_btn.setObjectName("btn_start")
            self.treatment_main_btn.setStyle(self.treatment_main_btn.style())  # 刷新样式
            self.treatment_main_btn.setEnabled(True)

            # 恢复训练开始按钮（治疗结束后可以开始训练）
            self.pretraining_btn.setEnabled(True)

            self.stop_treatment_btn.setEnabled(False)  # 禁用停止按钮（恢复灰色）
            self.system_status.setText("治疗完成")

            # 停止实时集成管理器
            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                self.realtime_integration_manager.stop()

            # 🔧 停止统一定时器管理器（替代独立的分类和倒计时定时器）
            if hasattr(self, 'unified_timer_manager') and self.unified_timer_manager:
                self.unified_timer_manager.stop()
                print("✅ 统一定时器管理器已停止")

            # 停止鼓励间隔计时器（保留，因为它不是核心治疗定时器）
            self._stop_encouragement_timer()

            # 重置倒计时
            treatment_duration_minutes = self._get_treatment_duration_from_settings()
            self.treatment_countdown = treatment_duration_minutes * 60
            self._update_countdown_display()

            # 重置概率显示
            if hasattr(self, 'motor_imagery_probability_label'):
                self.motor_imagery_probability_label.setText("0%")
            if hasattr(self, 'motor_imagery_progress_bar'):
                self.motor_imagery_progress_bar.setValue(0)

            # 重置轮次显示
            self.current_training_round = 0
            self._update_round_display()

            print("✅ 治疗自动完成")
            self.treatment_stopped.emit()

        except Exception as e:
            print(f"❌ 自动完成治疗失败: {e}")

    def _stop_treatment(self):
        """停止治疗"""
        try:
            print("🛑 开始停止治疗流程...")

            # 停止所有电刺激
            self._stop_all_stimulation()

            # 先完成治疗会话并保存数据（在重置电流之前）
            self._finalize_treatment_session()

            # 将AB通道电流置零并发送指令
            self._reset_all_channels_to_zero()

            # 发送UDP STOPALL指令
            self._send_udp_stopall_command()

            # 播放治疗停止语音提示
            self._play_treatment_voice("treatment_stopped")
            # 恢复概率进度条样式
            try:
                if hasattr(self, 'motor_imagery_progress_bar') and self.motor_imagery_progress_bar:
                    self.motor_imagery_progress_bar.setStyleSheet(self._prob_progressbar_original_style)
            except Exception:
                pass
            # 重置空白期与标志
            self.trigger_blank_until = 0.0
            self._blanking_reset_done = False


            # 更新UI状态
            self.treatment_active = False
            self.treatment_paused = False  # 重置暂停状态
            self.paused_countdown = 0      # 重置暂停倒计时
            self.treatment_main_btn.setText("开始治疗")
            self.treatment_main_btn.setObjectName("btn_start")
            self.treatment_main_btn.setStyle(self.treatment_main_btn.style())  # 刷新样式
            self.treatment_main_btn.setEnabled(True)

            # 恢复训练开始按钮（治疗结束后可以开始训练）
            self.pretraining_btn.setEnabled(True)

            self.stop_treatment_btn.setEnabled(False)  # 禁用停止按钮（恢复灰色）
            self.system_status.setText("系统待机")

            # 停止实时集成管理器
            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                self.realtime_integration_manager.stop()

            # 🔧 停止统一定时器管理器（替代独立的分类和倒计时定时器）
            if hasattr(self, 'unified_timer_manager') and self.unified_timer_manager:
                self.unified_timer_manager.stop()
                print("✅ 统一定时器管理器已停止")

            # 停止鼓励间隔计时器（保留，因为它不是核心治疗定时器）
            self._stop_encouragement_timer()

            # 重置倒计时
            treatment_duration_minutes = self._get_treatment_duration_from_settings()
            self.treatment_countdown = treatment_duration_minutes * 60
            self._update_countdown_display()

            # 重置概率显示
            if hasattr(self, 'motor_imagery_probability_label'):
                self.motor_imagery_probability_label.setText("0%")
            if hasattr(self, 'motor_imagery_progress_bar'):
                self.motor_imagery_progress_bar.setValue(0)

            # 重置轮次显示
            self.current_training_round = 0
            self._update_round_display()

            print("✅ 治疗停止完成")
            self.treatment_stopped.emit()

        except Exception as e:
            print(f"❌ 停止治疗失败: {e}")

    def _stop_all_stimulation(self):
        """停止所有电刺激"""
        try:
            if self.stim_connected and self.stimulation_device:
                # 停止所有通道
                self.stimulation_device.stop_stimulation(1)  # A通道
                self.stimulation_device.stop_stimulation(2)  # B通道

                # 停止自动停止定时器
                if hasattr(self, 'stimulation_stop_timer'):
                    self.stimulation_stop_timer.stop()

            # 重置电刺激状态
            self.stimulation_active = False

        except Exception as e:
            print(f"❌ 停止电刺激失败: {e}")
            # 发生异常时也要重置状态
            self.stimulation_active = False

    def _send_udp_stopall_command(self):
        """发送UDP STOPALL指令"""
        try:
            if hasattr(self, 'udp_communicator') and self.udp_communicator:
                self.udp_communicator.send_stopall_command()
        except Exception as e:
            print(f"❌ 发送UDP STOPALL指令失败: {e}")

    def _finalize_treatment_session(self):
        """完成治疗会话并保存数据"""
        try:
            if not hasattr(self, 'treatment_session'):
                print("⚠️ 没有治疗会话数据")
                return

            import time
            from datetime import datetime

            start_time = time.time()

            # 更新会话结束时间
            self.treatment_session['end_time'] = datetime.now()

            # 计算治疗时长（分钟）
            duration = self.treatment_session['end_time'] - self.treatment_session['start_time']
            duration_minutes = int(duration.total_seconds() / 60)
            self.treatment_session['treatment_duration_minutes'] = duration_minutes

            # 检查是否达到最小保存时长
            min_duration = self._get_min_save_duration_from_settings()
            if duration_minutes < min_duration:
                print(f"⚠️ 治疗时长({duration_minutes}分钟)未达到最小保存时长({min_duration}分钟)，不保存数据")
                return

            # 计算最终统计
            stats_start = time.time()
            self._calculate_final_statistics()
            stats_time = time.time() - stats_start
            # print(f"⏱️ 统计计算耗时: {stats_time:.3f}秒")

            # 保存到本地数据库
            db_start = time.time()
            self._save_treatment_to_database()
            db_time = time.time() - db_start
            # print(f"⏱️ 数据库保存耗时: {db_time:.3f}秒")

            # 异步上传到网络（避免阻塞UI）
            self._upload_treatment_data_async()

            total_time = time.time() - start_time
            # print(f"✅ 治疗会话完成 - 时长: {duration_minutes}分钟, 成功率: {self.treatment_session['success_rate']:.1f}%")
            # print(f"⏱️ 数据处理总耗时: {total_time:.3f}秒")

        except Exception as e:
            print(f"❌ 完成治疗会话失败: {e}")

    def _get_min_save_duration_from_settings(self) -> int:
        """从设置中获取最小保存时长"""
        try:
            import json
            from utils.path_manager import get_config_file_in_dir

            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                duration = settings.get("basic", {}).get("min_save_duration", 5)
                return duration
        except Exception as e:
            print(f"读取最小保存时长设置失败: {e}")

        return 5  # 默认5分钟

    def _get_encouragement_interval_from_settings(self) -> int:
        """从设置中获取鼓励间隔"""
        try:
            import json
            from utils.path_manager import get_config_file_in_dir

            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                interval = settings.get("basic", {}).get("encouragement_interval", 30)
                return interval
        except Exception as e:
            print(f"读取鼓励间隔设置失败: {e}")

        return 30  # 默认30秒

    def _get_treatment_duration_from_settings(self) -> int:
        """从设置中获取治疗时长（分钟）"""
        try:
            import json
            from utils.path_manager import get_config_file_in_dir

            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                duration = settings.get("basic", {}).get("treatment_duration", 30)
                return duration
        except Exception as e:
            print(f"读取治疗时长设置失败: {e}")

        return 30  # 默认30分钟

    def _calculate_final_statistics(self):
        """计算最终治疗统计"""
        try:
            # 计算成功率
            if self.imagination_request_count > 0:
                success_rate = (self.successful_trigger_count / self.imagination_request_count) * 100
            else:
                success_rate = 0.0

            self.treatment_session['success_rate'] = success_rate
            self.treatment_session['treatment_score'] = int(success_rate)  # 得分等于成功率

            # 计算治疗等级
            if success_rate >= 80:
                grade = "优"
            elif success_rate >= 60:
                grade = "良"
            elif success_rate >= 40:
                grade = "中"
            else:
                grade = "差"

            self.treatment_session['treatment_grade'] = grade

            # print(f"📊 最终统计: 成功率={success_rate:.1f}%, 等级={grade}")

        except Exception as e:
            print(f"❌ 计算最终统计失败: {e}")

    def _save_treatment_to_database(self):
        """保存治疗记录到本地数据库"""
        try:
            from core.database import db_manager
            from services.reference_data_service import ReferenceDataService

            # 获取医院信息
            ref_service = ReferenceDataService()
            hospital_info = ref_service.get_hospital_info()

            # 获取治疗编号
            treatment_number = self._get_next_treatment_number()

            # 保存治疗编号到会话中，供上传时使用
            self.treatment_session['treatment_number'] = treatment_number

            # 获取电刺激参数
            stimulation_params = self._get_stimulation_params_for_save()

            # 构建治疗记录
            treatment_record = {
                'bianh': self.treatment_session['patient_id'],
                'zhiliaobh': treatment_number,
                'rq': self.treatment_session['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                'shijian': self.treatment_session['treatment_duration_minutes'],
                'xiaoguo': self.treatment_session['treatment_grade'],
                'yaoqiucs': self.imagination_request_count,
                'shijics': self.successful_trigger_count,
                'defen': self.treatment_session['treatment_score'],
                'yiyuanid': hospital_info.get('id', 3) if hospital_info else 3,
                'keshiming': hospital_info.get('keshi', '康复科') if hospital_info else '康复科',
                'zhuzhiyis': self.current_patient.get('zhuzhi', ''),
                'czy': self._get_current_operator(),
                'shenfenzh': self.current_patient.get('cardid', ''),
                'shebeih': hospital_info.get('shebeiid', 'NK001') if hospital_info else 'NK001',
                'stimulation_params': stimulation_params,  # 电刺激参数
                'status': '0'  # 未上传
            }

            # 构建SQL插入语句
            fields = list(treatment_record.keys())
            placeholders = ', '.join(['?' for _ in fields])
            sql = f"INSERT INTO zhiliao ({', '.join(fields)}) VALUES ({placeholders})"

            # 执行插入
            record_id = db_manager.execute_insert(sql, tuple(treatment_record.values()))

            if record_id:
                # print(f"✅ 治疗记录保存成功 - ID: {record_id}")
                if stimulation_params:
                    # print(f"📋 电刺激参数已保存: {stimulation_params}")
                    pass
                self.treatment_session['record_id'] = record_id
            else:
                print("❌ 治疗记录保存失败")

        except Exception as e:
            print(f"❌ 保存治疗记录失败: {e}")

    def _get_stimulation_params_for_save(self) -> str:
        """获取治疗时使用的电刺激参数，格式化为JSON字符串"""
        try:
            import json

            stimulation_params = {}
            # print("🔍 开始获取电刺激参数...")

            # 从设置中读取默认频率和脉宽
            frequency, pulse_width = self._get_stimulation_frequency_and_pulse_width()
            # print(f"📋 从设置读取 - 频率: {frequency}Hz, 脉宽: {pulse_width}us")

            # 检查A通道
            has_channel_a_checkbox = hasattr(self, 'channel_a_checkbox')
            channel_a_checked = has_channel_a_checkbox and self.channel_a_checkbox.isChecked()
            channel_a_current = self.stim_params.get("channel_a", 0)
            # print(f"🔍 A通道检查 - 复选框存在: {has_channel_a_checkbox}, 选中: {channel_a_checked}, 电流: {channel_a_current}mA")

            if has_channel_a_checkbox and channel_a_checked:
                if channel_a_current > 0:
                    stimulation_params["channel_a"] = {
                        "channel": "A",
                        "current": channel_a_current,
                        "unit": "mA"
                    }
                    # print(f"✅ A通道参数已添加: {channel_a_current}mA")
                else:
                    print("⚠️ A通道电流为0，未添加参数")

            # 检查B通道
            has_channel_b_checkbox = hasattr(self, 'channel_b_checkbox')
            channel_b_checked = has_channel_b_checkbox and self.channel_b_checkbox.isChecked()
            channel_b_current = self.stim_params.get("channel_b", 0)
            # print(f"🔍 B通道检查 - 复选框存在: {has_channel_b_checkbox}, 选中: {channel_b_checked}, 电流: {channel_b_current}mA")

            if has_channel_b_checkbox and channel_b_checked:
                if channel_b_current > 0:
                    stimulation_params["channel_b"] = {
                        "channel": "B",
                        "current": channel_b_current,
                        "unit": "mA"
                    }
                    print(f"✅ B通道参数已添加: {channel_b_current}mA")
                else:
                    print("⚠️ B通道电流为0，未添加参数")

            # 添加公共参数（如果有任何通道被使用）
            if stimulation_params:
                stimulation_duration = self._get_stimulation_duration_from_settings()
                stimulation_params["duration"] = {
                    "value": stimulation_duration,
                    "unit": "seconds"
                }
                stimulation_params["frequency"] = {
                    "value": frequency,
                    "unit": "Hz"
                }
                stimulation_params["pulse_width"] = {
                    "value": pulse_width,
                    "unit": "us"
                }
                # print(f"✅ 公共参数已添加 - 时长: {stimulation_duration}s, 频率: {frequency}Hz, 脉宽: {pulse_width}us")

            # 转换为JSON字符串
            result = json.dumps(stimulation_params, ensure_ascii=False) if stimulation_params else ""
            # print(f"📋 最终电刺激参数JSON: {result}")
            return result

        except Exception as e:
            print(f"❌ 获取电刺激参数失败: {e}")
            return ""

    def _get_stimulation_frequency_and_pulse_width(self) -> tuple:
        """从设置中获取电刺激的默认频率和脉宽"""
        try:
            import json
            from utils.path_manager import get_config_file_in_dir

            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # 读取电刺激设备配置
                stimulation_config = settings.get("stimulation", {})
                frequency = stimulation_config.get("default_frequency", 25)  # 默认25Hz
                pulse_width = stimulation_config.get("default_pulse_width", 220)  # 默认220us

                return frequency, pulse_width
        except Exception as e:
            print(f"读取电刺激频率和脉宽设置失败: {e}")

        return 25, 220  # 默认值：25Hz, 220us

    def _get_next_treatment_number(self) -> int:
        """获取下一个治疗编号"""
        try:
            from services.treatment_service import treatment_service
            patient_id = self.treatment_session['patient_id']
            current_count = treatment_service.get_treatment_count_by_patient(patient_id)
            return current_count + 1
        except Exception as e:
            print(f"获取治疗编号失败: {e}")
            return 1



    def _get_current_operator(self) -> str:
        """获取当前操作员"""
        try:
            # 从认证服务获取当前登录用户
            from services.auth_service import auth_service
            current_user = auth_service.get_current_user()
            if current_user:
                # 优先使用真实姓名，其次使用用户名
                return current_user.get('name') or current_user.get('username', '操作员')
            else:
                return "操作员"
        except Exception as e:
            print(f"获取当前操作员失败: {e}")
            return "操作员"

    def _upload_treatment_data_async(self):
        """异步上传治疗数据到网络"""
        try:
            from PySide6.QtCore import QThread, Signal

            class UploadThread(QThread):
                upload_finished = Signal(bool, str)  # 上传完成信号 (成功, 消息)

                def __init__(self, parent, treatment_session, current_patient, success_triggers, imagination_request_count):
                    super().__init__(parent)
                    self.treatment_session = treatment_session
                    self.current_patient = current_patient
                    self.success_triggers = success_triggers
                    self.imagination_request_count = imagination_request_count
                    self.parent_page = parent

                def run(self):
                    try:
                        from services.api_client import ApiClient
                        from services.reference_data_service import ReferenceDataService

                        # 获取医院信息
                        ref_service = ReferenceDataService()
                        hospital_info = ref_service.get_hospital_info()

                        # 构建上传数据
                        upload_data = {
                            'actualTimes': self.success_triggers,
                            'commentsOfTreatment': self.treatment_session['treatment_grade'],
                            'timesOfImagination': self.imagination_request_count,  # 要求想象次数
                            'treatScore': self.treatment_session['treatment_score'],
                            'treatTime': self.treatment_session['treatment_duration_minutes'],
                            'usageTime': self.treatment_session['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                            'patientNum': self.treatment_session['patient_id'],
                            'treatNum': str(self.treatment_session.get('treatment_number', 1)),
                            'hospitalID': hospital_info.get('id', 3) if hospital_info else 3,
                            'department': hospital_info.get('keshi', '康复科') if hospital_info else '康复科',
                            'equipmentNum': hospital_info.get('shebeiid', 'NK001') if hospital_info else 'NK001',
                            'attdoctor': self.current_patient.get('zhuzhi', ''),
                            'operator': self.parent_page._get_current_operator(),
                            'idCard': self.current_patient.get('cardid', '')
                        }

                        # 执行上传
                        api_client = ApiClient()
                        success = api_client.upload_treatment(upload_data)

                        if success:
                            self.upload_finished.emit(True, "治疗数据上传成功")
                        else:
                            self.upload_finished.emit(False, "治疗数据上传失败")

                    except Exception as e:
                        self.upload_finished.emit(False, f"上传治疗数据失败: {e}")

            # 创建并启动上传线程
            self.upload_thread = UploadThread(
                self,
                self.treatment_session,
                self.current_patient,
                self.success_triggers,
                self.imagination_request_count
            )
            self.upload_thread.upload_finished.connect(self._on_upload_finished)
            self.upload_thread.start()

            print("📤 治疗数据正在后台上传...")

        except Exception as e:
            print(f"❌ 启动异步上传失败: {e}")

    def _on_upload_finished(self, success: bool, message: str):
        """上传完成回调"""
        try:
            if success:
                print(f"✅ {message}")
                # 更新本地数据库的上传状态
                self._update_upload_status(True)
            else:
                print(f"❌ {message}")

            # 清理线程
            if hasattr(self, 'upload_thread'):
                self.upload_thread.deleteLater()

        except Exception as e:
            print(f"❌ 处理上传结果失败: {e}")

    def _update_upload_status(self, success: bool):
        """更新上传状态"""
        try:
            if hasattr(self, 'treatment_session') and 'record_id' in self.treatment_session:
                from core.database import db_manager

                status = '1' if success else '0'
                sql = "UPDATE zhiliao SET status = ? WHERE rowid = ?"
                db_manager.execute_update(sql, (status, self.treatment_session['record_id']))

        except Exception as e:
            print(f"❌ 更新上传状态失败: {e}")

    def _toggle_bci_params(self):
        """切换BCI参数显示"""
        if self.bci_params_widget.isVisible():
            self.bci_params_widget.hide()
        else:
            self.bci_params_widget.show()

    def _on_bci_param_changed(self, param_name: str, value: float):
        """🔧 BCI参数变化处理（确保UI与系统实时同步）"""
        self.bci_params[param_name] = value
        print(f"BCI参数更新: {param_name} = {value}")

        # 🔧 更新加权投票系统的阈值（立即同步）
        if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:
            if param_name == "trigger_threshold":
                self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=value)

                # 🔧 验证设置是否生效
                actual_threshold = self.weighted_voting_manager.get_current_thresholds()
                print(f"🔧 触发阈值同步验证: UI设置={value}, 系统实际={actual_threshold}")

                if abs(actual_threshold - value) > 0.001:
                    print(f"⚠️ 阈值同步失败，重试设置")
                    self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=value)
                    actual_threshold = self.weighted_voting_manager.get_current_thresholds()
                    print(f"🔧 重试后系统阈值: {actual_threshold}")
                else:
                    print(f"✅ 触发阈值同步成功: {value}")

        self.parameter_changed.emit(param_name, value)

    def _on_difficulty_changed_new(self, param_name: str, value: float):
        """🔧 新的难度等级变化处理（确保UI与系统同步）"""
        difficulty_level = int(value)  # 转换为整数
        self.bci_params["difficulty_level"] = difficulty_level
        print(f"难度等级更新: {difficulty_level}")

        # 🔧 根据难度等级获取对应的触发阈值（使用修正后的映射）
        trigger_threshold = self.difficulty_threshold_mapping.get(difficulty_level, 0.60)
        print(f"难度等级 {difficulty_level} 对应触发阈值: {trigger_threshold}")

        # 🔧 同时更新UI和系统
        self.trigger_threshold_adjuster.set_value(trigger_threshold)
        self.bci_params["trigger_threshold"] = trigger_threshold

        # 🔧 关键：同步更新加权投票系统的阈值
        if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:
            self.weighted_voting_manager.set_difficulty_level(difficulty_level)
            # 验证系统阈值是否与UI一致
            actual_threshold = self.weighted_voting_manager.get_current_thresholds()
            if abs(actual_threshold - trigger_threshold) > 0.001:
                print(f"⚠️ 系统阈值不一致: UI={trigger_threshold}, 系统={actual_threshold}")
                # 强制使用UI的阈值
                self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=trigger_threshold)
                print(f"✅ 已强制同步系统阈值为: {trigger_threshold}")

        print(f"✅ UI触发阈值已更新为: {trigger_threshold}")

        # 如果有加权投票管理器，同时更新管理器状态
        if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:
            print(f"同步更新加权投票系统难度等级: {difficulty_level}")
            self.weighted_voting_manager.set_difficulty_level(difficulty_level)

        self.parameter_changed.emit("difficulty_level", difficulty_level)

    # === 稳定蓝牙连接方法 ===

    def _init_bluetooth_manager(self):
        """初始化标准Bleak蓝牙管理器"""
        try:
            from services.bluetooth.standard_bleak_manager import StandardBleakManager
            self.bluetooth_manager = StandardBleakManager()

            # 连接信号
            self.bluetooth_manager.state_changed.connect(self._on_bluetooth_state_changed)
            self.bluetooth_manager.device_found.connect(self._on_bluetooth_device_found)
            self.bluetooth_manager.connected.connect(self._on_bluetooth_connected)
            self.bluetooth_manager.disconnected.connect(self._on_bluetooth_disconnected)
            self.bluetooth_manager.connection_failed.connect(self._on_bluetooth_connection_failed)
            self.bluetooth_manager.data_received.connect(self._on_bluetooth_data_received)

        except Exception as e:
            print(f"蓝牙管理器初始化失败: {e}")
            self.bluetooth_manager = None

        # 初始化电刺激设备管理器
        try:
            from services.stimulation import StimulationDevice
            self.stimulation_device = StimulationDevice()

            # 连接信号
            self.stimulation_device.device_connected.connect(self._on_stim_device_connected)
            self.stimulation_device.device_disconnected.connect(self._on_stim_device_disconnected)
            self.stimulation_device.connection_failed.connect(self._on_stim_connection_failed)
            self.stimulation_device.connection_status_changed.connect(self._on_stim_status_changed)



            # 预刺激定时器
            self.channel_a_pre_timer = None
            self.channel_b_pre_timer = None

            # 预刺激状态跟踪
            self.channel_a_pre_stimulating = False
            self.channel_b_pre_stimulating = False

            # 刺激状态跟踪（基于设备反馈）
            self.channel_a_stimulating = False
            self.channel_b_stimulating = False

            # 设备状态监控定时器
            self.device_status_timer = QTimer()
            self.device_status_timer.timeout.connect(self._check_device_status)
            self.device_status_timer.start(250)  # 每250ms检查一次设备状态，平衡响应性和性能

        except Exception as e:
            print(f"电刺激设备管理器初始化失败: {e}")
            self.stimulation_device = None

    def _init_eeg_data_processor(self):
        """初始化脑电数据处理器"""
        try:
            self.eeg_data_processor = EEGDataProcessor()

        except Exception as e:
            print(f"脑电数据处理器初始化失败: {e}")
            self.eeg_data_processor = None
        # 无效包提示节流
        self._invalid_packet_alert_cooldown_sec = 60
        self._last_invalid_packet_alert_time = 0
        self._invalid_packet_alert_shown = False  # 防止重复弹窗的标记
        self._invalid_packet_dialog = None  # 弹窗实例跟踪


    def _toggle_eeg_connection(self):
        """切换脑电设备连接"""
        if not self.bluetooth_manager:
            self.eeg_status.setText("连接失败")
            return

        if not self.eeg_connected:
            # 开始连接
            self.eeg_status.setText("连接中...")
            self.eeg_connect_btn.setText("扫描中...")
            self.eeg_connect_btn.setEnabled(False)

            # 启动扫描和连接
            self.bluetooth_manager.start_scan_and_connect()
        else:
            # 断开连接
            self._disconnect_eeg()

    def _disconnect_eeg(self):
        """断开脑电设备"""
        if self.bluetooth_manager:
            self.bluetooth_manager.disconnect_device()
        else:
            # 直接更新UI状态
            self._update_eeg_disconnected_state()

    def _update_eeg_disconnected_state(self):
        """更新脑电设备断开状态"""
        self.eeg_connected = False
        self.eeg_status.setText("未连接")
        self.eeg_connect_btn.setText("连接脑电")
        self.eeg_connect_btn.setObjectName("btn_secondary")
        self.eeg_connect_btn.setEnabled(True)

        # 停止监测数据更新
        if self.monitoring_timer:
            self.monitoring_timer.stop()
            self._update_monitoring_display()

        # 🔧 修复：停止实时集成管理器（防止蓝牙断开后仍产生数据）
        if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
            try:
                self.realtime_integration_manager.stop()
                print("✓ 实时集成管理器已停止（蓝牙断开）")
            except Exception as e:
                print(f"⚠️ 停止实时集成管理器失败: {e}")

        # 🔧 修复：停止分类定时器（防止蓝牙断开后仍进行分类）
        if hasattr(self, 'classification_timer') and self.classification_timer:
            try:
                if self.classification_timer.isActive():
                    self.classification_timer.stop()
                    print("✓ 分类定时器已停止（蓝牙断开）")
            except Exception as e:
                print(f"⚠️ 停止分类定时器失败: {e}")

        # 清空新增的实时显示组件
        if hasattr(self, 'realtime_curves_widget') and self.realtime_curves_widget:
            self.realtime_curves_widget.clear_display()

        # 实时指标组件已移除，无需清空
        pass

        # 地形图组件已移除，无需清空显示
        pass

        # 更新顶部栏信号状态
        self._update_topbar_signal_status()

        self.device_disconnected.emit("eeg")

    # === 蓝牙信号处理方法 ===

    def _on_bluetooth_state_changed(self, state: str):
        """蓝牙状态变化处理"""
        # 将技术状态映射为用户友好的状态
        state_mapping = {
            'scanning': '连接中...',
            'connecting': '连接中...',
            'connected': '已连接',
            'disconnected': '未连接',
            'failed': '连接失败',
            'error': '连接失败'
        }

        # 如果状态包含特定关键词，进行映射
        display_state = state
        for key, value in state_mapping.items():
            if key in state.lower():
                display_state = value
                break

        # 如果没有匹配到映射，检查是否包含连接相关的词汇
        if display_state == state:
            if any(word in state.lower() for word in ['连接', 'connect', '扫描', 'scan']):
                display_state = '连接中...'
            elif any(word in state.lower() for word in ['失败', 'fail', 'error', '错误']):
                display_state = '连接失败'
            elif any(word in state.lower() for word in ['成功', 'success', '已连接']):
                display_state = '已连接'
            elif any(word in state.lower() for word in ['断开', 'disconnect', '未连接']):
                display_state = '未连接'

        self.eeg_status.setText(display_state)
        print(f"蓝牙状态: {state} -> 显示: {display_state}")

    def _on_bluetooth_device_found(self, device_name: str, device_address: str, rssi: int):
        """发现蓝牙设备处理"""
        print(f"发现设备: {device_name} ({device_address}) RSSI: {rssi}dBm")

    def _on_bluetooth_connected(self, device_address: str):
        """蓝牙设备连接成功处理"""
        self.eeg_connected = True
        self.eeg_status.setText("已连接")
        self.eeg_connect_btn.setText("断开脑电")
        self.eeg_connect_btn.setObjectName("btn_primary")
        self.eeg_connect_btn.setEnabled(True)

        # 开始监测数据更新
        if self.monitoring_timer:
            self.monitoring_timer.start(1000)

        # 延迟更新顶部栏信号状态，等待START指令发送完成
        # 如果START指令发送失败，connection_failed信号会重置状态
        QTimer.singleShot(2000, self._delayed_signal_status_update)

        # 重置无效包弹窗标记（新连接开始）
        self._invalid_packet_alert_shown = False
        self._invalid_packet_dialog = None

        self.device_connected.emit("eeg")
        print(f"脑电设备连接成功: {device_address}")

    def _delayed_signal_status_update(self):
        """延迟更新信号状态 - 确保START指令发送完成"""
        # 只有在设备仍然连接时才更新为"信号正常"
        if self.eeg_connected:
            self._update_topbar_signal_status()
            print("延迟更新信号状态: 信号正常")

    def _on_bluetooth_disconnected(self, device_address: str):
        """蓝牙设备断开处理"""
        # 🔧 修复：如果在治疗过程中蓝牙断开，自动停止治疗
        if hasattr(self, 'treatment_active') and self.treatment_active:
            print("⚠️ 治疗过程中脑电设备断开，自动停止治疗")
            try:
                # 停止治疗流程
                self._stop_treatment()
                print("✓ 治疗已自动停止（脑电设备断开）")
            except Exception as e:
                print(f"❌ 自动停止治疗失败: {e}")

        # 重置无效包弹窗标记（连接断开）
        self._invalid_packet_alert_shown = False
        self._invalid_packet_dialog = None

        self._update_eeg_disconnected_state()
        print(f"脑电设备已断开: {device_address}")

    def _on_bluetooth_connection_failed(self, device_address: str, error: str):
        """蓝牙连接失败处理 - 只有在所有重试都失败后才重置UI"""
        print(f"脑电设备连接失败 {device_address}: {error}")

        # 🔧 修复：检查错误信息，只有在"重试次数已耗尽"时才重置UI
        # 这样可以让蓝牙管理器的内置重试机制正常工作
        if "重试次数已耗尽" in error or "所有重试尝试均已耗尽" in error:
            # 所有重试都失败了，重置UI状态
            self.eeg_status.setText("连接失败")
            self.eeg_connect_btn.setText("连接脑电")
            self.eeg_connect_btn.setObjectName("btn_secondary")
            self.eeg_connect_btn.setEnabled(True)

            # 应用样式
            self.eeg_connect_btn.style().unpolish(self.eeg_connect_btn)
            self.eeg_connect_btn.style().polish(self.eeg_connect_btn)

            # 连接失败时更新脑电设备状态和顶部栏信号状态
            self.eeg_connected = False
            self._update_topbar_signal_status()

            # 重置无效包弹窗标记（连接失败）
            self._invalid_packet_alert_shown = False
            self._invalid_packet_dialog = None
        else:
            # 单次连接失败，但重试还在进行中，只更新状态文本
            self.eeg_status.setText("连接中...")
            print(f"蓝牙重试进行中，不重置UI状态")

    def _auto_retry_connection(self):
        """自动重试连接（已弃用，StandardBleakManager内置重试）"""
        # 这个方法现在不再需要，因为StandardBleakManager已经内置了重试机制
        # 保留方法避免潜在的调用错误，但不执行任何操作
        pass

    def _on_bluetooth_data_received(self, data: bytes):
        """蓝牙数据接收处理（支持分片重组）"""
        # 使用脑电数据处理器处理数据流
        if self.eeg_data_processor:
            # 使用新的数据流处理方法
            eeg_data_list = self.eeg_data_processor.process_data_stream(data)

            if eeg_data_list:
                # 处理每个提取到的数据包
                for eeg_data in eeg_data_list:
                    self.eeg_packet_counter += 1

                    # 处理单个数据包
                    self._process_eeg_data(eeg_data, self.eeg_packet_counter)

                    # 检查累计无效包数并节流提示（防止重复弹窗）
                    if (self.eeg_data_processor.get_invalid_packet_count() >= 10 and
                        not self._invalid_packet_alert_shown and
                        self._invalid_packet_dialog is None):
                        now = time.time()
                        if now - self._last_invalid_packet_alert_time > self._invalid_packet_alert_cooldown_sec:
                            try:
                                # 使用非阻塞方式显示弹窗
                                self._show_safe_warning()
                                # 设置已弹窗标记，防止重复弹窗
                                self._invalid_packet_alert_shown = True
                            except Exception as e:
                                print(f"弹窗显示失败: {e}")
                            # 重置计数与节流时间
                            self.eeg_data_processor.reset_invalid_packet_count()
                            self._last_invalid_packet_alert_time = now
        else:
            print("脑电数据处理器未初始化")

    def _show_safe_warning(self):
        """安全的弹窗显示方法（非阻塞）"""
        try:
            # 检查是否在主线程
            from PySide6.QtCore import QThread
            if QThread.currentThread() != self.thread():
                print("⚠️ 非主线程调用弹窗，跳过显示")
                return

            # 检查是否已有弹窗实例
            if self._invalid_packet_dialog is not None:
                print("⚠️ 弹窗已存在，跳过重复显示")
                return

            # 创建非阻塞弹窗
            from ui.components.themed_message_box import ThemedMessageBox
            from PySide6.QtWidgets import QMessageBox

            self._invalid_packet_dialog = ThemedMessageBox(
                self,
                "信号异常",
                "检测到多次数据异常。\n请检查脑电设备连接或环境干扰，必要时重启设备。",
                "warning",
                QMessageBox.StandardButton.Ok
            )

            # 连接关闭信号，清理引用
            self._invalid_packet_dialog.finished.connect(self._on_warning_dialog_closed)

            # 非阻塞显示
            self._invalid_packet_dialog.show()
            print("✓ 信号异常弹窗已显示（非阻塞模式）")

        except Exception as e:
            print(f"安全弹窗显示失败: {e}")
            self._invalid_packet_dialog = None

    def _on_warning_dialog_closed(self):
        """弹窗关闭回调"""
        try:
            self._invalid_packet_dialog = None
            print("✓ 信号异常弹窗已关闭")
        except Exception as e:
            print(f"弹窗关闭处理失败: {e}")

    def _process_eeg_data(self, eeg_data, packet_num=0):
        """处理转换后的脑电数据"""
        # eeg_data: numpy数组 (8, 4) float32格式
        # 通道顺序: PZ, P3, P4, C3, CZ, C4, F3, F4

        try:
            # 如果实时集成管理器已初始化，使用新的处理流程
            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                result = self.realtime_integration_manager.process_packet(eeg_data)

                # 可以在这里处理结果，例如更新UI状态
                if result.get('status') == 'error':
                    print(f"实时处理错误: {result.get('error')}")

            else:
                # 传统处理方式（向后兼容）
                pass

        except Exception as e:
            print(f"脑电数据处理失败: {e}")

    def _on_realtime_window_ready(self, window_data):
        """实时窗口数据就绪回调（仅用于预训练）"""
        try:
            # window_data: [8, 250] - 8通道，250样本（2秒窗口）

            # 存储最新的窗口数据
            self.latest_window_data = window_data.copy()

            # 注意：治疗时不在此处进行分类，避免与定时器双重触发
            # 治疗时的分类由classification_timer严格控制

            # 如果正在预训练，进行特征提取用于训练数据收集
            if self.pre_training_active and self.feature_manager:
                # 检查特征管理器是否已拟合
                if not hasattr(self.feature_manager, '_is_fitted') or not self.feature_manager._is_fitted:
                    # 在预训练阶段，这是正常的，数据会被训练会话管理器收集
                    return

                # 转换数据格式：[8, 250] -> [1, 8, 250] (添加batch维度)
                input_data = window_data[np.newaxis, :, :]

                # 使用特征融合管理器处理窗口数据
                features = self.feature_manager.transform(input_data)

                if features is not None:
                    # 移除batch维度：[1, n_features] -> [n_features]
                    features = features[0]

                print(f"预训练窗口处理完成，特征维度: {features.shape if features is not None else 'None'}")

        except Exception as e:
            print(f"实时窗口处理失败: {e}")

    def _get_current_window_data(self, trial_type: str, duration: float):
        """获取当前窗口数据（供训练会话管理器使用）- 只返回真实数据"""
        try:
            import time
            max_wait_time = 10.0  # 最大等待10秒
            wait_interval = 0.1   # 每100ms检查一次
            start_time = time.time()

            print(f"🔍 开始获取真实窗口数据: {trial_type}, 预期时长: {duration}秒")

            # 首先检查蓝牙连接状态
            if not hasattr(self, 'eeg_connected') or not self.eeg_connected:
                print("❌ 脑电设备未连接，无法获取真实数据")
                raise RuntimeError("脑电设备未连接，训练需要真实数据")

            # 确保实时集成管理器正在运行
            if not hasattr(self, 'realtime_integration_manager') or not self.realtime_integration_manager:
                print("❌ 实时集成管理器未初始化")
                raise RuntimeError("实时集成管理器未初始化，无法获取数据")

            if not self.realtime_integration_manager.is_running:
                print("⚠️ 实时集成管理器未运行，尝试启动...")
                self.realtime_integration_manager.start()
                time.sleep(1.0)  # 给启动过程一些时间

            # 等待获取真实窗口数据
            while (time.time() - start_time) < max_wait_time:
                # 方法1：使用latest_window_data（由回调更新）
                if hasattr(self, 'latest_window_data') and self.latest_window_data is not None:
                    # 验证数据的真实性（基本检查）
                    if self._validate_real_eeg_data(self.latest_window_data):
                        print(f"✅ 获取到有效的实时窗口数据: {trial_type}, 形状: {self.latest_window_data.shape}")
                        return self.latest_window_data.copy()
                    else:
                        print("⚠️ 窗口数据未通过真实性验证，继续等待...")

                # 方法2：直接从实时集成管理器获取当前窗口
                try:
                    current_window = self.realtime_integration_manager.get_current_window()
                    if current_window is not None and self._validate_real_eeg_data(current_window):
                        print(f"✅ 从实时集成管理器获取到有效数据: {trial_type}, 形状: {current_window.shape}")
                        return current_window.copy()
                except AttributeError:
                    # 如果get_current_window方法不存在，继续使用其他方法
                    pass

                # 等待更多数据
                time.sleep(wait_interval)
                print(f"⏳ 等待真实数据... ({time.time() - start_time:.1f}s)")

            # 超时后仍无法获取数据
            print(f"❌ 等待{max_wait_time}秒后仍无法获取真实数据")
            raise TimeoutError(f"无法获取真实脑电数据，训练无法继续")

        except Exception as e:
            print(f"❌ 获取当前窗口数据失败: {e}")
            # 不返回None，而是抛出异常，避免使用模拟数据
            raise RuntimeError(f"无法获取真实脑电数据: {e}")

    def _validate_real_eeg_data(self, data: np.ndarray) -> bool:
        """验证数据基本有效性（简化版）"""
        try:
            if data is None or data.size == 0:
                return False

            # 检查数据形状
            if len(data.shape) != 2 or data.shape[0] != 8:
                print(f"❌ 数据形状不正确: {data.shape}, 期望: (8, N)")
                return False

            # 检查是否全部为零（设备未工作）
            if np.all(data == 0):
                print("❌ 数据全为零，设备可能未工作")
                return False

            # 基本的数据有效性检查（范围很宽松）
            data_range = np.max(data) - np.min(data)
            if data_range < 0.1:  # 数据完全无变化
                print(f"❌ 数据无变化: {data_range}")
                return False

            print(f"✅ 数据基本验证通过 - 形状: {data.shape}, 范围: [{np.min(data):.2f}, {np.max(data):.2f}]")
            return True

        except Exception as e:
            print(f"❌ 数据验证失败: {e}")
            return False

    def _perform_realtime_classification(self, features):
        """执行实时分类 - 使用加权投票系统"""
        try:
            # 检查是否有训练好的加权投票分类器
            if not hasattr(self, 'weighted_voting_manager') or not self.weighted_voting_manager:
                # 如果没有加权投票管理器，尝试加载
                self._load_weighted_voting_manager()

            if not self.weighted_voting_manager or not self.weighted_voting_manager._is_fitted:
                # 如果仍然没有训练好的模型，弹出错误对话框并停止治疗
                from ui.components.themed_message_box import show_critical

                print("❌ 没有可用的分类器模型，无法进行实时分类")

                # 弹出主题化错误对话框
                show_critical(
                    self,
                    "分类器模型缺失",
                    "系统检测到没有可用的分类器模型。\n\n"
                    "请先完成以下步骤：\n"
                    "1. 进入训练页面\n"
                    "2. 完成脑电信号训练\n"
                    "3. 确保模型训练成功\n\n"
                    "训练完成后再开始治疗。"
                )

                # 停止治疗
                self._stop_treatment()
                return

            # 准备输入数据 - features应该是窗口数据 [8, 250]
            if features.ndim == 1:
                # 如果是特征向量，需要重新构造窗口数据
                print("警告: 收到特征向量而非窗口数据，无法进行加权投票分类")
                return

            # 输入数据格式: [8, 250] -> [1, 8, 250]
            input_data = features[np.newaxis, :, :] if features.ndim == 2 else features

            # 监控数据质量
            try:
                self._monitor_data_quality(features)
            except AttributeError:
                # 方法可能因为缓存问题暂时不可用，跳过监控
                pass
            except Exception as e:
                print(f"数据质量监控跳过: {e}")

            # 使用加权投票分类器预测
            detailed_result = self.weighted_voting_manager.predict_with_details(input_data)

            # 获取分类结果
            final_prediction = detailed_result['final_prediction']
            confidence = detailed_result['confidence']
            trigger_decision = detailed_result['trigger_decision']

            # 更新显示
            result = 'motor_imagery' if final_prediction == 1 else 'rest'

            # 更新概率显示（真实分类概率）
            # 直接使用最终概率中的运动想象概率（索引1）
            final_probability = detailed_result['final_probability']
            motor_imagery_prob = final_probability[1] * 100  # 运动想象概率

            self._update_probability_display(motor_imagery_prob)

            # 显示详细信息（已禁用以提升性能）
            # print(detailed_result['classification_details'])

            # 注意：不在此处更新计数，计数由语音提示控制

            # 根据触发决策控制电刺激（加入空白期门控 & 结束时一次性重置平滑器）
            try:
                import time as _time
                now_ts = _time.time()
                in_blank = self.trigger_blank_enabled and (now_ts < self.trigger_blank_until)

                # 空白期内：不触发电刺激
                if in_blank:
                    # 确保进度条样式维持空白期颜色
                    if hasattr(self, 'motor_imagery_progress_bar') and self.motor_imagery_progress_bar:
                        # 已在开始时设置为橙色，无需每次设置
                        pass
                else:
                    # 空白期刚结束：执行一次性平滑器重置，并恢复进度条样式
                    if self.trigger_blank_enabled and not self._blanking_reset_done:
                        if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:
                            try:
                                self.weighted_voting_manager.reset_temporal_smoother()
                                print("🔁 空白期结束：时序平滑器状态已重置")
                            except Exception as _e:
                                print(f"⚠️ 重置时序平滑器失败: {_e}")
                        # 恢复进度条原样式
                        if hasattr(self, 'motor_imagery_progress_bar') and self.motor_imagery_progress_bar:
                            try:
                                self.motor_imagery_progress_bar.setStyleSheet(self._prob_progressbar_original_style)
                            except Exception:
                                # 退化处理：清空样式
                                self.motor_imagery_progress_bar.setStyleSheet("")
                        self._blanking_reset_done = True

                    # 空白期外：按正常逻辑触发
                    if trigger_decision:
                        self._trigger_stimulation()
            except Exception as e:
                print(f"⚠️ 触发门控逻辑异常: {e}")

        except Exception as e:
            print(f"实时分类失败: {e}")

    def _load_patient_model(self) -> bool:
        """加载患者训练好的模型"""
        try:
            # 获取当前患者信息
            if not hasattr(self, 'current_patient') or not self.current_patient:
                print("❌ 患者信息未设置")
                return False

            patient_id = self.current_patient.get('id', '') or self.current_patient.get('bianhao', '')
            if not patient_id:
                print("❌ 患者ID无效")
                return False

            # 使用ClassifierTrainingManager加载模型（使用优化配置）
            from services.classifier_training_manager import ClassifierTrainingManager
            from utils.path_manager import get_config_file_in_dir

            training_manager = ClassifierTrainingManager()

            # 尝试加载最新的Plan A模型
            voting_manager = training_manager.load_plan_a_model(patient_id, version='latest')

            if voting_manager is None:
                print(f"❌ 未找到患者 {patient_id} 的训练模型")
                return False

            # 设置管理器（复用变量名以最小化UI改动）
            self.weighted_voting_manager = voting_manager

            # 调试与平滑（PlanA内部只做EWMA概率平滑）
            self.weighted_voting_manager.set_realtime_debug(enabled=True)
            try:
                if hasattr(self, 'weighted_voting_manager') and self.weighted_voting_manager:
                    self.weighted_voting_manager.set_temporal_smoothing(True, method='ewma', alpha=0.2)
                    print("✅ 已启用EWMA概率平滑: alpha=0.2")
            except Exception as e:
                print(f"⚠️ 启用平滑失败: {e}")


            # 🔧 应用当前的BCI参数设置（保留用户自定义设置）
            difficulty_level = self.bci_params.get("difficulty_level", 3)
            current_trigger_threshold = self.bci_params.get("trigger_threshold", 0.60)

            # 设置难度等级
            self.weighted_voting_manager.set_difficulty_level(difficulty_level)

            # 🔧 关键修复：应用用户的自定义触发阈值，而不是被系统默认值覆盖
            self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=current_trigger_threshold)

            # 验证设置是否生效
            actual_threshold = self.weighted_voting_manager.get_current_thresholds()
            print(f"🔧 阈值设置验证: UI设置={current_trigger_threshold}, 系统实际={actual_threshold}")

            # 确保UI显示与系统实际一致
            if abs(actual_threshold - current_trigger_threshold) > 0.001:
                print(f"⚠️ 阈值不一致，强制同步: {current_trigger_threshold}")
                self.weighted_voting_manager.set_custom_thresholds(trigger_threshold=current_trigger_threshold)
                actual_threshold = self.weighted_voting_manager.get_current_thresholds()

            # 更新UI显示为实际使用的阈值
            self.trigger_threshold_adjuster.set_value(actual_threshold)
            self.bci_params["trigger_threshold"] = actual_threshold

            print(f"✅ 患者 {patient_id} 的Plan A模型加载成功")

            return True

        except Exception as e:
            print(f"❌ 加载患者模型失败: {e}")
            self.weighted_voting_manager = None
            return False

    def _load_weighted_voting_manager(self):
        """加载加权投票管理器（兼容旧接口）"""
        return self._load_patient_model()

    def _initialize_realtime_classification(self):
        """初始化实时分类系统"""
        try:
            # 初始化滑动窗口分类器参数
            self.window_duration = 2.0  # 2秒窗口
            self.slide_interval = 0.5   # 500ms滑动间隔
            self.sampling_rate = 125    # 125Hz采样率

            self.window_samples = int(self.window_duration * self.sampling_rate)  # 250样本

            # 🔧 重要修复：停止旧的独立分类定时器，现在使用统一定时器管理器
            if hasattr(self, 'classification_timer') and self.classification_timer:
                try:
                    self.classification_timer.stop()
                    self.classification_timer.timeout.disconnect()
                    print("✅ 旧的独立分类定时器已停止并断开连接")
                except Exception as e:
                    print(f"⚠️ 停止旧定时器失败: {e}")
                finally:
                    self.classification_timer = None

            # 注意：分类定时器现在由统一定时器管理器管理（每500ms = 5×100ms）
            # print("✅ 分类定时器已迁移到统一定时器管理器")

            # 分类统计
            self.classification_count = 0
            self.last_classification_time = 0

            # 注意：时序平滑器已关闭，无需重置
            # 🔧 时序平滑器已关闭：避免双重平滑导致响应迟钝和概率变化幅度过小

            # print(f"✅ 实时分类系统初始化完成")
            # print(f"   - 窗口大小: {self.window_duration}秒 ({self.window_samples}样本)")
            # print(f"   - 滑动间隔: {self.slide_interval}秒")
            # print(f"   - 分类频率: 每{self.slide_interval}秒一次")
            # print(f"   - 数据源: RealtimeIntegrationManager（与预训练相同）")

        except Exception as e:
            print(f"❌ 初始化实时分类系统失败: {e}")

    def _initialize_unified_timer_manager(self):
        """初始化统一定时器管理器（智能重用）"""
        try:
            from ui.components.unified_timer_manager import UnifiedTimerManager

            # 🔧 智能重用：如果已有可用的统一定时器管理器，直接重启而不重新创建
            if hasattr(self, 'unified_timer_manager') and self.unified_timer_manager:
                if self.unified_timer_manager.is_running:
                    # 如果正在运行，先停止
                    self.unified_timer_manager.stop()
                    print("✅ 统一定时器管理器已停止")

                # 重新启动现有的定时器管理器
                self.unified_timer_manager.start()
                # print("✅ 统一定时器管理器已重启（重用现有实例）")
                return

            # 只有在没有定时器管理器时才创建新的
            self.unified_timer_manager = UnifiedTimerManager(self)

            # 注册回调函数
            self.unified_timer_manager.register_classification_callback(self._perform_sliding_window_classification)
            self.unified_timer_manager.register_countdown_callback(self._update_countdown)

            # 启动统一定时器管理器
            self.unified_timer_manager.start()

            # print("✅ 统一定时器管理器初始化完成（新建实例）")
            # print("   - 基准时钟: 100ms")
            # print("   - 分类频率: 每500ms (5个tick)")
            # print("   - 倒计时频率: 每1000ms (10个tick)")

        except Exception as e:
            print(f"❌ 初始化统一定时器管理器失败: {e}")
            self.unified_timer_manager = None

    def _initialize_realtime_integration_manager(self):
        """初始化实时集成管理器"""
        try:
            from services.eeg_preprocessing.realtime_integration_manager import RealtimeIntegrationManager
            from services.eeg_preprocessing.preprocessing_config import PreprocessingConfig

            # 创建预处理配置
            config = PreprocessingConfig()

            # 创建实时集成管理器
            self.realtime_integration_manager = RealtimeIntegrationManager(config)

        except Exception as e:
            print(f"❌ 初始化实时集成管理器失败: {e}")
            self.realtime_integration_manager = None

    def _perform_sliding_window_classification(self):
        """执行滑动窗口分类（性能优化版）"""
        try:
            # 🔧 修复：检查脑电设备连接状态，防止蓝牙断开后继续分类
            if not hasattr(self, 'eeg_connected') or not self.eeg_connected:
                if self.classification_count % 20 == 0:  # 减少日志频率
                    print("⚠️ 脑电设备未连接，跳过分类")
                return

            # 添加调试信息（已禁用以提升性能）
            # if self.classification_count % 10 == 0:  # 每10次分类输出一次调试信息
            #     print(f"🔍 分类定时器触发 #{self.classification_count}")

            # 直接从RealtimeIntegrationManager获取当前窗口数据（与预训练相同的方式）
            if hasattr(self, 'realtime_integration_manager') and self.realtime_integration_manager:
                if not self.realtime_integration_manager.is_running:
                    print("⚠️ 实时集成管理器未运行")
                    return

                # 1. 现有分类逻辑：使用2秒窗口（保持每500ms执行）
                window_data = self.realtime_integration_manager.get_current_window()

                if window_data is not None:
                    # 执行分类
                    self._perform_realtime_classification(window_data)

                    # 更新统计
                    self.classification_count += 1
                    self.last_classification_time = time.time()

                    # 2. 实时显示更新：直接使用分类器输入的标准化数据
                    self._update_realtime_curves_with_classification_data(window_data)

                else:
                    # 数据不足，跳过本次分类
                    if self.classification_count % 20 == 0:  # 每20次输出一次数据不足警告
                        buffer_size = len(self.realtime_integration_manager.data_buffer) if self.realtime_integration_manager.data_buffer else 0
                        print(f"⚠️ 数据不足，缓冲区大小: {buffer_size}, 需要: {self.realtime_integration_manager.window_samples}")
                    return
            else:
                print("⚠️ 实时集成管理器不可用")
                return

        except Exception as e:
            print(f"❌ 滑动窗口分类失败: {e}")

    def _update_realtime_curves_with_classification_data(self, window_data: np.ndarray):
        """使用分类器输入的标准化数据更新实时曲线（零资源浪费方案）

        Args:
            window_data: 分类器使用的标准化窗口数据 [8, 250]
        """
        try:
            if hasattr(self, 'realtime_curves_widget') and self.realtime_curves_widget:
                # 直接使用分类器的标准化数据，提取最新0.5秒（约63样本）用于显示
                display_samples = min(63, window_data.shape[1])  # 0.5秒 ≈ 63样本
                curve_data = window_data[:, -display_samples:]  # [8, 63] 或更少
                self.realtime_curves_widget.update_data(curve_data)

        except Exception as e:
            print(f"❌ 实时曲线更新失败: {e}")

    # 实时指标更新和频域特征计算方法已完全移除（性能优化）
    # def _update_realtime_metrics(self, data):
    # def _compute_frequency_features(self, data):
    #     pass



    # 地形图特征计算方法已完全移除（性能优化）
    # def _compute_topography_features(self, data):
    # def _compute_balanced_features(self, data):
    # def _compute_band_power(self, data, low_freq, high_freq):
    #     pass

    def _on_eeg_window_ready(self, window_data: np.ndarray):
        """EEG窗口数据就绪回调"""
        try:
            # window_data: [8, 250] - 8通道，250样本（2秒）
            # 存储最新窗口数据（优化：减少不必要的数据复制）
            self.latest_window_data = window_data  # 直接引用，避免复制

            # 治疗时的实时分类由定时器触发，直接从RealtimeIntegrationManager获取数据
            # 这里只需要更新最新窗口数据即可

        except Exception as e:
            print(f"❌ EEG窗口数据处理失败: {e}")



    def _trigger_stimulation(self):
        """触发电刺激"""
        try:
            # 检查电刺激设备连接状态
            if not self.stim_connected or not self.stimulation_device:
                return

            # 如果当前已经在进行电刺激，不重复触发
            if self.stimulation_active:
                return

            # 如果语音正在播放，跳过本次触发（避免语音播放期间触发）
            if self.voice_playing:
                return

            # 获取当前电流设置
            channel_a_current = self.stim_params.get("channel_a", 0)
            channel_b_current = self.stim_params.get("channel_b", 0)

            # 获取选中的通道
            selected_channels = []
            if self.channel_a_checkbox.isChecked() and channel_a_current > 0:
                selected_channels.append(("A", 1, channel_a_current))
            if self.channel_b_checkbox.isChecked() and channel_b_current > 0:
                selected_channels.append(("B", 2, channel_b_current))

            if not selected_channels:
                print("⚠️ 没有可用的电刺激通道")
                return

            # 发送UDP START指令（只在开始新的刺激时发送）
            self._send_udp_start_command()

            # 启动电刺激
            success_channels = []
            for channel_name, channel_num, current_value in selected_channels:
                try:
                    # 设置电流
                    if self.stimulation_device.set_current(channel_num, current_value):
                        # 启动刺激
                        if self.stimulation_device.start_stimulation(channel_num):
                            success_channels.append(f"{channel_name}({current_value}mA)")
                        else:
                            print(f"❌ {channel_name}通道启动失败")
                    else:
                        print(f"❌ {channel_name}通道电流设置失败")
                except Exception as e:
                    print(f"❌ {channel_name}通道刺激异常: {e}")

            if success_channels:
                # 设置电刺激状态为活跃
                self.stimulation_active = True

                # 停止鼓励间隔计时器（成功触发）
                self._stop_encouragement_timer()

                # 更新治疗统计
                self._update_treatment_statistics()

                # 启动自动停止定时器
                self._start_stimulation_auto_stop()
            else:
                print("❌ 所有通道刺激失败")

        except Exception as e:
            print(f"❌ 触发电刺激失败: {e}")

    def _stop_current_stimulation(self):
        """停止当前正在进行的电刺激"""
        try:
            # 停止之前的自动停止定时器
            if hasattr(self, 'stimulation_stop_timer') and self.stimulation_stop_timer.isActive():
                self.stimulation_stop_timer.stop()
                print("🔧 停止了之前的刺激定时器")

            # 停止所有通道的刺激
            if self.stim_connected and self.stimulation_device:
                self.stimulation_device.stop_stimulation(1)  # A通道
                self.stimulation_device.stop_stimulation(2)  # B通道
                print("🔧 停止了之前的电刺激")

            # 重置电刺激状态
            self.stimulation_active = False

        except Exception as e:
            print(f"❌ 停止当前刺激失败: {e}")
            # 发生异常时也要重置状态
            self.stimulation_active = False

    def _send_udp_start_command(self):
        """发送UDP START指令"""
        try:
            if hasattr(self, 'udp_communicator') and self.udp_communicator:
                self.udp_communicator.send_start_command()
        except Exception as e:
            print(f"❌ 发送UDP START指令失败: {e}")

    def _update_treatment_statistics(self):
        """更新治疗统计"""
        try:
            # 实际触发次数+1
            self.successful_trigger_count += 1

            # 保持兼容性
            self.success_triggers = self.successful_trigger_count
            self.total_triggers = self.imagination_request_count

            if hasattr(self, 'treatment_session'):
                self.treatment_session['successful_trigger_count'] = self.successful_trigger_count
                self.treatment_session['total_imagination_count'] = self.imagination_request_count

                # 计算成功率
                if self.imagination_request_count > 0:
                    success_rate = (self.successful_trigger_count / self.imagination_request_count) * 100
                    self.treatment_session['success_rate'] = success_rate
                else:
                    success_rate = 0.0

            # 更新UI显示（无论是否有treatment_session都要更新）
            self._update_treatment_status()

        except Exception as e:
            print(f"❌ 更新治疗统计失败: {e}")

    def _start_stimulation_auto_stop(self):
        """启动电刺激自动停止定时器"""
        try:
            # 从设置中获取刺激时长
            stimulation_duration = self._get_stimulation_duration_from_settings()

            # 创建定时器自动停止电刺激
            if not hasattr(self, 'stimulation_stop_timer'):
                from PySide6.QtCore import QTimer
                self.stimulation_stop_timer = QTimer()
                self.stimulation_stop_timer.setSingleShot(True)
                self.stimulation_stop_timer.timeout.connect(self._auto_stop_stimulation)
                print("🔧 创建了新的刺激停止定时器")
            else:
                # 如果定时器已存在，先停止之前的定时器
                if self.stimulation_stop_timer.isActive():
                    self.stimulation_stop_timer.stop()
                    print("🔧 停止了之前的刺激定时器")

            # 启动定时器
            timeout_ms = stimulation_duration * 1000
            self.stimulation_stop_timer.start(timeout_ms)

        except Exception as e:
            print(f"❌ 启动自动停止定时器失败: {e}")

    def _get_stimulation_duration_from_settings(self) -> int:
        """从设置中获取刺激时长"""
        try:
            import json
            from utils.path_manager import get_config_file_in_dir

            settings_file = get_config_file_in_dir("settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                duration = settings.get("basic", {}).get("stimulation_duration", 11)
                return duration
        except Exception as e:
            print(f"读取刺激时长设置失败: {e}")

        return 11  # 默认11秒

    def _auto_stop_stimulation(self):
        """自动停止电刺激"""
        try:
            if self.stim_connected and self.stimulation_device:
                # 停止所有通道
                self.stimulation_device.stop_stimulation(1)  # A通道
                self.stimulation_device.stop_stimulation(2)  # B通道

                # 重置电刺激状态
                self.stimulation_active = False

                # 发送UDP STOP指令
                self._send_udp_stop_command()

                # 播放成功反馈语音并继续治疗循环
                self._continue_treatment_cycle()
            else:
                # 即使设备未连接，也要重置状态
                self.stimulation_active = False

        except Exception as e:
            # 发生异常时也要重置状态，避免状态卡死
            self.stimulation_active = False

    def _send_udp_stop_command(self):
        """发送UDP STOP指令"""
        try:
            if hasattr(self, 'udp_communicator') and self.udp_communicator:
                self.udp_communicator.send_stop_command()
        except Exception as e:
            print(f"❌ 发送UDP STOP指令失败: {e}")

    def cleanup_duplicate(self):
        """清理资源 - 重复方法，已合并到主cleanup方法"""
        # 这个方法的内容已经合并到主cleanup方法中
        pass

    # 方案A：移除旧配置验证逻辑（占位，避免误调用）
    def _verify_optimized_configuration(self, config):
        return True




    # 方案A：移除Stacking优化验证
    def _verify_stacking_optimization(self):
        return

    def _monitor_data_quality(self, input_data: np.ndarray):
        """
        监控实时数据质量

        Args:
            input_data: 输入数据 [n_channels, n_samples]
        """
        try:
            # 检查数据基本信息
            if input_data is None or input_data.size == 0:
                print("⚠️ 数据质量警告: 接收到空数据")
                return

            # 统计无效值
            nan_count = np.sum(np.isnan(input_data))
            inf_count = np.sum(np.isinf(input_data))
            total_elements = input_data.size
            invalid_ratio = (nan_count + inf_count) / total_elements

            # 数据质量评估
            if invalid_ratio == 0:
                quality_status = "优秀"
                status_icon = "✅"
            elif invalid_ratio < 0.05:
                quality_status = "良好"
                status_icon = "🟢"
            elif invalid_ratio < 0.15:
                quality_status = "一般"
                status_icon = "🟡"
            else:
                quality_status = "差"
                status_icon = "🔴"

            # 通道级别分析
            if input_data.ndim >= 2:
                n_channels = input_data.shape[0]
                problematic_channels = []

                for ch in range(n_channels):
                    ch_data = input_data[ch, :]
                    ch_invalid_ratio = np.sum(np.isnan(ch_data) | np.isinf(ch_data)) / ch_data.size

                    if ch_invalid_ratio > 0.1:  # 通道无效值超过10%
                        problematic_channels.append(f"CH{ch+1}({ch_invalid_ratio:.1%})")

                if problematic_channels:
                    print(f"⚠️ 问题通道: {', '.join(problematic_channels)}")

            # 输出质量报告
            if invalid_ratio > 0.05:  # 只在质量不佳时输出详细信息
                print(f"{status_icon} 数据质量: {quality_status} | "
                      f"无效值: {nan_count+inf_count}/{total_elements} ({invalid_ratio:.1%})")

                if invalid_ratio > 0.15:
                    print("🚨 数据质量严重下降，建议检查设备连接和电极接触")

        except Exception as e:
            print(f"数据质量监控失败: {e}")
