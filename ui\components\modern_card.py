# -*- coding: utf-8 -*-
"""
现代化卡片组件
Modern Card Component

完全按照HTML设计实现的卡片组件
包含标题、内容区域、样式效果
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont


class ModernCard(QFrame):
    """现代化卡片组件 - 完全按照HTML设计实现"""
    
    # 卡片信号
    card_clicked = Signal()
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        
        self.title_text = title
        self.header_widget = None
        self.body_widget = None
        self.title_label = None
        self.action_widget = None
        
        # 设置卡片属性
        self.setObjectName("placeholder_card")
        self.setFrameStyle(QFrame.Shape.NoFrame)
        
        # 初始化UI
        self._init_ui()
        self._setup_layout()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建卡片头部
        if self.title_text:
            self._create_header()
        
        # 创建卡片主体
        self._create_body()
    
    def _create_header(self):
        """创建卡片头部"""
        self.header_widget = QWidget()
        self.header_widget.setObjectName("placeholder_card")
        
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(20, 16, 20, 16)  # 按照HTML中的padding
        header_layout.setSpacing(16)
        
        # 标题标签
        self.title_label = QLabel(self.title_text)
        self.title_label.setObjectName("page_title")
        self.title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()  # 推送到左侧
        
        # 预留操作区域
        self.action_widget = QWidget()
        self.action_widget.setObjectName("placeholder_card")
        header_layout.addWidget(self.action_widget)
        
        self.main_layout.addWidget(self.header_widget)
    
    def _create_body(self):
        """创建卡片主体"""
        self.body_widget = QWidget()
        self.body_widget.setObjectName("placeholder_card")
        
        # 创建主体布局
        self.body_layout = QVBoxLayout(self.body_widget)
        self.body_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距，让内容布局自己控制
        self.body_layout.setSpacing(0)
        self.body_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)
        
        self.main_layout.addWidget(self.body_widget, 1)
    
    def _setup_layout(self):
        """设置布局属性"""
        # 设置大小策略
        self.setSizePolicy(
            QSizePolicy.Policy.Expanding, 
            QSizePolicy.Policy.Preferred
        )
    
    def set_title(self, title: str):
        """设置卡片标题"""
        self.title_text = title
        if self.title_label:
            self.title_label.setText(title)
        elif title:
            # 如果之前没有标题，现在需要创建头部
            self._create_header()
    
    def add_content(self, widget: QWidget):
        """添加内容到卡片主体"""
        self.body_layout.addWidget(widget)
    
    def add_action(self, widget: QWidget):
        """添加操作按钮到卡片头部"""
        if self.action_widget:
            if not self.action_widget.layout():
                action_layout = QHBoxLayout(self.action_widget)
                action_layout.setContentsMargins(0, 0, 0, 0)
                action_layout.setSpacing(8)
            
            self.action_widget.layout().addWidget(widget)
    
    def clear_content(self):
        """清空卡片内容"""
        # 清空主体内容
        while self.body_layout.count():
            child = self.body_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def clear_actions(self):
        """清空卡片操作"""
        if self.action_widget and self.action_widget.layout():
            while self.action_widget.layout().count():
                child = self.action_widget.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
    
    def get_body_layout(self) -> QVBoxLayout:
        """获取主体布局"""
        return self.body_layout
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.card_clicked.emit()
        super().mousePressEvent(event)
