# -*- coding: utf-8 -*-
"""
参数调节组件
Parameter Adjuster Component

完全按照HTML设计实现的参数调节器
包含+/-按钮、数值显示、参数调节逻辑
"""

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton, QSizePolicy
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont


class ParameterAdjuster(QWidget):
    """参数调节器组件 - 完全按照HTML设计实现"""
    
    # 参数变化信号
    value_changed = Signal(float)
    
    def __init__(self, 
                 label: str = "",
                 initial_value: float = 0.0,
                 min_value: float = 0.0,
                 max_value: float = 100.0,
                 step: float = 1.0,
                 precision: int = 2,
                 unit: str = "",
                 parent=None):
        super().__init__(parent)
        
        self.label_text = label
        self.current_value = initial_value
        self.min_value = min_value
        self.max_value = max_value
        self.step = step
        self.precision = precision
        self.unit = unit
        
        # UI组件
        self.label_widget = None
        self.decrease_btn = None
        self.value_label = None
        self.increase_btn = None
        
        # 设置组件属性
        self.setObjectName("parameter_adjuster")
        
        # 初始化UI
        self._init_ui()
        self._update_display()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 参数标签（如果有）
        if self.label_text:
            self.label_widget = QLabel(self.label_text)
            self.label_widget.setObjectName("param_label")
            self.label_widget.setFont(QFont("Microsoft YaHei", 11))
            layout.addWidget(self.label_widget)
            layout.addStretch()  # 推送到右侧
        
        # 减少按钮
        self.decrease_btn = QPushButton("−")
        self.decrease_btn.setFixedSize(28, 28)
        self.decrease_btn.setFont(QFont("Microsoft YaHei", 14))
        self.decrease_btn.clicked.connect(self._decrease_value)
        layout.addWidget(self.decrease_btn)
        
        # 数值显示
        self.value_label = QLabel()
        self.value_label.setObjectName("param_value")
        self.value_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setMinimumWidth(45)
        layout.addWidget(self.value_label)
        
        # 增加按钮
        self.increase_btn = QPushButton("+")
        self.increase_btn.setFixedSize(28, 28)
        self.increase_btn.setFont(QFont("Microsoft YaHei", 14))
        self.increase_btn.clicked.connect(self._increase_value)
        layout.addWidget(self.increase_btn)
    
    def _decrease_value(self):
        """减少数值"""
        new_value = max(self.min_value, self.current_value - self.step)
        self.set_value(new_value)
    
    def _increase_value(self):
        """增加数值"""
        new_value = min(self.max_value, self.current_value + self.step)
        self.set_value(new_value)
    
    def _update_display(self):
        """更新显示"""
        if self.value_label:
            # 格式化数值显示
            if self.precision == 0:
                value_text = f"{int(self.current_value)}"
            else:
                value_text = f"{self.current_value:.{self.precision}f}"
            
            # 添加单位
            if self.unit:
                value_text += self.unit
            
            self.value_label.setText(value_text)
        
        # 更新按钮状态
        if self.decrease_btn:
            self.decrease_btn.setEnabled(self.current_value > self.min_value)
        
        if self.increase_btn:
            self.increase_btn.setEnabled(self.current_value < self.max_value)
    
    def set_value(self, value: float):
        """设置数值"""
        # 限制在范围内
        value = max(self.min_value, min(self.max_value, value))
        # 根据精度进行四舍五入，避免浮点数精度问题
        value = round(value, self.precision)

        if value != self.current_value:
            self.current_value = value
            self._update_display()
            self.value_changed.emit(value)
    
    def get_value(self) -> float:
        """获取当前数值"""
        return self.current_value
    
    def set_range(self, min_value: float, max_value: float):
        """设置数值范围"""
        self.min_value = min_value
        self.max_value = max_value
        
        # 确保当前值在新范围内
        self.set_value(self.current_value)
    
    def set_step(self, step: float):
        """设置步长"""
        self.step = step
    
    def set_precision(self, precision: int):
        """设置精度"""
        self.precision = precision
        self._update_display()
    
    def set_unit(self, unit: str):
        """设置单位"""
        self.unit = unit
        self._update_display()
    
    def set_enabled(self, enabled: bool):
        """设置启用状态"""
        super().setEnabled(enabled)
        if self.decrease_btn:
            self.decrease_btn.setEnabled(enabled and self.current_value > self.min_value)
        if self.increase_btn:
            self.increase_btn.setEnabled(enabled and self.current_value < self.max_value)


class CompactParameterAdjuster(QWidget):
    """紧凑型参数调节器 - 用于EEGNet参数调节"""

    # 参数变化信号
    value_changed = Signal(str, float)  # 参数名, 新值

    def __init__(self, param_name: str, label: str, initial_value: float,
                 min_value: float, max_value: float, step: float,
                 precision: int = 2, layout_direction: str = "horizontal", parent=None):
        super().__init__(parent)

        self.param_name = param_name
        self.label_text = label
        self.current_value = initial_value
        self.min_value = min_value
        self.max_value = max_value
        self.step = step
        self.precision = precision
        self.layout_direction = layout_direction  # "horizontal" 或 "vertical"

        # 设置组件属性
        self.setObjectName("compact_parameter_adjuster")

        # 初始化UI
        self._init_ui()
        self._update_display()
    
    def _init_ui(self):
        """初始化UI组件"""
        if self.layout_direction == "vertical":
            self._init_vertical_ui()
        else:
            self._init_horizontal_ui()

    def _init_horizontal_ui(self):
        """初始化水平布局UI"""
        # 创建主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 参数标签
        label = QLabel(self.label_text)
        label.setObjectName("compact_param_label")
        label.setFont(QFont("Microsoft YaHei", 11))
        layout.addWidget(label)
        layout.addStretch()

        # 控制区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(8)

        # 减少按钮
        self.decrease_btn = QPushButton("−")
        self.decrease_btn.setFixedSize(28, 28)
        self.decrease_btn.setFont(QFont("Microsoft YaHei", 14))
        self.decrease_btn.clicked.connect(self._decrease_value)
        control_layout.addWidget(self.decrease_btn)

        # 数值显示
        self.value_label = QLabel()
        self.value_label.setObjectName("compact_param_value")
        self.value_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setMinimumWidth(45)
        control_layout.addWidget(self.value_label)

        # 增加按钮
        self.increase_btn = QPushButton("+")
        self.increase_btn.setFixedSize(28, 28)
        self.increase_btn.setFont(QFont("Microsoft YaHei", 14))
        self.increase_btn.clicked.connect(self._increase_value)
        control_layout.addWidget(self.increase_btn)

        layout.addLayout(control_layout)

    def _init_vertical_ui(self):
        """初始化垂直布局UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)

        # 参数标签
        label = QLabel(self.label_text)
        label.setObjectName("compact_param_label")
        label.setFont(QFont("Microsoft YaHei", 10))
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)

        # 控制区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(8)

        # 减少按钮
        self.decrease_btn = QPushButton("−")
        self.decrease_btn.setFixedSize(28, 28)
        self.decrease_btn.setFont(QFont("Microsoft YaHei", 14))
        self.decrease_btn.clicked.connect(self._decrease_value)
        control_layout.addWidget(self.decrease_btn)

        # 数值显示
        self.value_label = QLabel()
        self.value_label.setObjectName("compact_param_value")
        self.value_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setMinimumWidth(45)
        control_layout.addWidget(self.value_label)

        # 增加按钮
        self.increase_btn = QPushButton("+")
        self.increase_btn.setFixedSize(28, 28)
        self.increase_btn.setFont(QFont("Microsoft YaHei", 14))
        self.increase_btn.clicked.connect(self._increase_value)
        control_layout.addWidget(self.increase_btn)

        layout.addLayout(control_layout)
    
    def _decrease_value(self):
        """减少数值"""
        new_value = max(self.min_value, self.current_value - self.step)
        self.set_value(new_value)
    
    def _increase_value(self):
        """增加数值"""
        new_value = min(self.max_value, self.current_value + self.step)
        self.set_value(new_value)
    
    def _update_display(self):
        """更新显示"""
        if self.value_label:
            if self.precision == 0:
                value_text = f"{int(self.current_value)}"
            else:
                value_text = f"{self.current_value:.{self.precision}f}"
            
            self.value_label.setText(value_text)
        
        # 更新按钮状态
        if hasattr(self, 'decrease_btn'):
            self.decrease_btn.setEnabled(self.current_value > self.min_value)
        
        if hasattr(self, 'increase_btn'):
            self.increase_btn.setEnabled(self.current_value < self.max_value)
    
    def set_value(self, value: float):
        """设置数值"""
        value = max(self.min_value, min(self.max_value, value))
        # 根据精度进行四舍五入，避免浮点数精度问题
        value = round(value, self.precision)

        if value != self.current_value:
            self.current_value = value
            self._update_display()
            self.value_changed.emit(self.param_name, value)
    
    def get_value(self) -> float:
        """获取当前数值"""
        return self.current_value


class DifficultyLevelAdjuster(CompactParameterAdjuster):
    """难度等级调节器 - 专用于难度等级调节，显示数字和文字描述"""

    # 难度等级映射
    DIFFICULTY_LABELS = {
        1: "简单",
        2: "较简单",
        3: "中等",
        4: "较困难",
        5: "困难"
    }

    def __init__(self, param_name: str = "difficulty_level", label: str = "难度等级",
                 initial_value: int = 3, layout_direction: str = "vertical", parent=None):
        # 固定参数：范围1-5，步长1，精度0
        super().__init__(
            param_name=param_name,
            label=label,
            initial_value=float(initial_value),
            min_value=1.0,
            max_value=5.0,
            step=1.0,
            precision=0,
            layout_direction=layout_direction,
            parent=parent
        )

    def _update_display(self):
        """更新显示 - 只显示数字"""
        if self.value_label:
            level = int(self.current_value)
            value_text = f"{level}"
            self.value_label.setText(value_text)

        # 更新按钮状态
        if hasattr(self, 'decrease_btn'):
            self.decrease_btn.setEnabled(self.current_value > self.min_value)

        if hasattr(self, 'increase_btn'):
            self.increase_btn.setEnabled(self.current_value < self.max_value)

    def get_difficulty_level(self) -> int:
        """获取当前难度等级（整数）"""
        return int(self.current_value)

    def set_difficulty_level(self, level: int):
        """设置难度等级"""
        self.set_value(float(level))
