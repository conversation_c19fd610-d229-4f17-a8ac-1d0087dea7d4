# 患者出院和重新入院逻辑说明文档

## 📋 文档概述

**文档目的**：详细记录患者出院和重新入院的完整业务逻辑、数据库操作和技术实现，供治疗系统开发时参考。

**创建时间**：2025-01-27  
**适用版本**：脑机接口康复训练系统 v1.0  
**维护人员**：开发团队

---

## 🏥 患者出院流程

### 1. 用户操作步骤
```
患者列表页面 → 点击患者卡片"出院"按钮 → 出院确认对话框 → 填写出院信息 → 确认出院
```

### 2. 出院对话框数据收集
- **出院日期**：QDateEdit选择器（默认今天）
- **出院原因**：QLineEdit文本框（可选填写）

### 3. 数据库操作详情

**更新住院记录表 (hospitalization_records)**：
```sql
UPDATE hospitalization_records
SET discharge_date = ?,           -- 设置出院日期
    discharge_reason = ?,         -- 设置出院原因  
    status = 0,                   -- 状态改为出院
    updated_at = CURRENT_TIMESTAMP
WHERE patient_id = ? AND discharge_date IS NULL  -- 只更新当前住院记录
```

**更新的字段**：
- `discharge_date`：NULL → 用户选择的出院日期
- `discharge_reason`：NULL → 用户输入的出院原因
- `status`：1（在院）→ 0（出院）
- `updated_at`：更新时间戳

### 4. 数据处理特点
- ✅ **患者基本信息不删除**：bingren表数据完全保留
- ✅ **住院记录保留**：只标记出院，不删除记录
- ✅ **治疗记录保留**：所有历史治疗数据完整保存
- ✅ **患者列表隐藏**：出院患者从列表中消失（因为只显示在院患者）

### 5. 相关代码实现
**文件位置**：`services/patient_service.py`
**方法名称**：`discharge_patient(patient_id, discharge_date, discharge_reason)`

---

## 🔄 患者重新入院流程

### 1. 触发机制
```
新增患者页面 → 输入患者编号 → 编号输入框失去焦点 → 自动检查患者状态
```

### 2. 状态检查逻辑
```python
def check_patient_existence_and_status(patient_id):
    # 1. 检查患者是否存在（bingren表）
    # 2. 检查当前住院状态（hospitalization_records表，discharge_date IS NULL）
    # 3. 返回状态：not_found/in_hospital/discharged
```

**三种状态处理**：

| 状态 | 检查结果 | 系统响应 | 用户操作 |
|------|---------|---------|---------|
| not_found | 患者不存在 | 正常新增流程 | 继续填写表单 |
| in_hospital | 患者已在院 | 警告提示对话框 | 清空编号重新输入 |
| discharged | 患者已出院 | 再次入院确认对话框 | 选择是否再次入院 |

### 3. 再次入院对话框

**只读信息**（不可修改）：
- 患者编号、姓名、性别、身份证号

**可更新信息**：
- 年龄、联系电话、地址、主治医生、诊断信息

**入院信息**：
- 入院日期（QDateEdit，默认今天）
- 入院原因（QLineEdit，选填）

### 4. 数据库操作详情

**4.1 更新患者信息**（可选，只更新有值的字段）：
```sql
UPDATE bingren SET 
    age = ?,           -- 年龄可能有变化
    phone = ?,         -- 联系电话可能有变化
    address = ?,       -- 地址可能有变化
    zhuzhi = ?,        -- 主治医生可能换人
    zhenduan = ?       -- 诊断信息可能更新
WHERE bianhao = ?
```

**4.2 创建新的住院记录**：
```sql
INSERT INTO hospitalization_records
(patient_id, admission_date, admission_reason, department, doctor, status)
VALUES (?, ?, ?, ?, ?, 1)
```

**保存的数据**：
- `patient_id`：患者编号（关联bingren.bianhao）
- `admission_date`：入院日期
- `admission_reason`：入院原因
- `department`：科室
- `doctor`：主治医师
- `status`：1（在院状态）

### 5. 相关代码实现
**文件位置**：`services/patient_service.py`
**方法名称**：
- `check_patient_existence_and_status(patient_id)`
- `readmit_patient(patient_id, updated_info, admission_info)`
- `_update_patient_fields(patient_id, fields)`

---

## 📊 数据库表结构和关联

### 核心表结构

**1. 患者表 (bingren)**
```sql
-- 患者基本信息，永不删除
bianhao (患者编号) - 主键
name (姓名)
age (年龄)
xingbie (性别)
cardid (身份证)
zhenduan (诊断)
keshi (科室)
zhuzhi (主治医师)
phone (电话) - 可更新
address (地址) - 可更新
```

**2. 住院记录表 (hospitalization_records)**
```sql
id - 主键
patient_id - 患者编号（外键）
admission_date - 入院日期
discharge_date - 出院日期（NULL=在院）
discharge_reason - 出院原因
admission_reason - 入院原因
department - 科室
doctor - 主治医师
status - 状态（1=在院，0=出院）
created_at - 创建时间
updated_at - 更新时间
```

**3. 治疗记录表 (zhiliao)**
```sql
-- 原有字段 + 新增关联字段
hospitalization_id - 住院记录ID（外键）
-- 其他治疗相关字段...
```

### 数据关联关系
```
患者表 (bingren)
    ↓ 1:N
住院记录表 (hospitalization_records)
    ↓ 1:N  
治疗记录表 (zhiliao)
```

---

## 🔍 关键技术实现

### 1. 患者列表显示逻辑
```sql
-- 只显示在院患者
SELECT DISTINCT p.* FROM bingren p
JOIN hospitalization_records h ON p.bianhao = h.patient_id
WHERE h.discharge_date IS NULL
```

### 2. 当前住院状态检查
```sql
SELECT * FROM hospitalization_records
WHERE patient_id = ? AND discharge_date IS NULL
ORDER BY admission_date DESC LIMIT 1
```

### 3. 字段级更新策略
```python
# 只更新有内容的字段，避免空值覆盖
filtered_info = {k: v for k, v in updated_info.items() 
                if (v.strip() if isinstance(v, str) else v)}
```

### 4. 事务处理保证
- 患者信息更新 + 住院记录创建 = 原子操作
- 任一步骤失败则全部回滚

---

## 🎯 治疗系统开发注意事项

### 1. 治疗记录保存时的关联逻辑
```python
# 获取患者当前住院记录ID
def get_current_hospitalization_id(patient_id):
    hospitalization = get_current_hospitalization(patient_id)
    return hospitalization['id'] if hospitalization else None

# 保存治疗记录时必须关联住院记录
def save_treatment_record(treatment_data):
    hospitalization_id = get_current_hospitalization_id(treatment_data['patient_id'])
    if not hospitalization_id:
        raise Exception("患者当前不在院，无法保存治疗记录")
    
    treatment_data['hospitalization_id'] = hospitalization_id
    # 保存治疗记录...
```

### 2. 治疗记录查询逻辑
```sql
-- 查询患者某次住院期间的所有治疗记录
SELECT * FROM zhiliao 
WHERE hospitalization_id = ?
ORDER BY rq DESC

-- 查询患者所有住院期间的治疗记录
SELECT z.*, h.admission_date, h.discharge_date 
FROM zhiliao z
JOIN hospitalization_records h ON z.hospitalization_id = h.id
WHERE h.patient_id = ?
ORDER BY h.admission_date DESC, z.rq DESC
```

### 3. 数据完整性检查
- 保存治疗记录前必须验证患者在院状态
- 治疗记录必须关联到有效的住院记录
- 出院后不能再保存新的治疗记录到该住院期间

---

## 📈 业务价值和规范符合

### 医疗规范符合
- ✅ **患者信息永不删除**：符合医疗数据保存规范
- ✅ **完整住院历史**：支持多次入院出院记录
- ✅ **治疗记录关联**：每次治疗都关联到具体住院期间
- ✅ **数据可追溯**：完整的患者生命周期管理

### 用户体验优化
- ✅ **自动识别老患者**：避免重复录入基本信息
- ✅ **智能状态检查**：防止重复入院等错误操作
- ✅ **选择性更新**：只更新需要变化的信息
- ✅ **操作流畅**：一键完成复杂的再次入院流程

### 数据完整性保证
- ✅ **历史数据保留**：所有住院和治疗历史完整保存
- ✅ **状态管理清晰**：通过discharge_date字段明确区分在院/出院状态
- ✅ **关联关系完整**：患者-住院-治疗三级关联关系清晰

---

## 📝 更新日志

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|---------|--------|
| 2025-01-27 | v1.0 | 初始版本，完整的出院和重新入院逻辑说明 | 开发团队 |

---

**注意**：本文档将随着系统功能的完善而持续更新，请在开发治疗系统时参考最新版本。
