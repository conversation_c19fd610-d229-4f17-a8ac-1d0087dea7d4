# -*- coding: utf-8 -*-
"""
参考数据服务
Reference Data Service

提供医院、医生、操作员等参考数据的获取服务
"""

import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from core.database import db_manager

class ReferenceDataService:
    """参考数据服务类"""

    def __init__(self):
        # 缓存数据，避免重复查询
        self._hospital_info = None
        self._doctors = None
        self._operators = None
        # 配置文件修改时间缓存，用于检测配置变更
        self._settings_file_mtime = None
    
    def get_hospital_info(self) -> Optional[Dict[str, Any]]:
        """
        获取医院信息

        Returns:
            医院信息字典，包含id、hname、keshi、shebeiid等字段
        """
        # 检查配置文件是否有变更，如果有变更则清除缓存
        if self._check_settings_file_changed():
            self._hospital_info = None

        if self._hospital_info is None:
            try:
                # 从settings.json配置文件读取医院信息
                settings_data = self._load_settings_from_file()
                basic_config = settings_data.get('basic', {})

                if basic_config:
                    # 从配置文件构建医院信息
                    self._hospital_info = {
                        'id': int(basic_config.get('hospital_id', 3)),
                        'hname': basic_config.get('hospital_name', '泰安中医医院'),
                        'province': '',  # 配置文件中暂无此字段
                        'city': '',      # 配置文件中暂无此字段
                        'addr': '',      # 配置文件中暂无此字段
                        'mobile': '',    # 配置文件中暂无此字段
                        'keshi': basic_config.get('department_name', '康复科'),
                        'shebeiid': basic_config.get('device_id', '160701'),
                        'createtime': '',  # 配置文件中暂无此字段
                        'updatetime': '',  # 配置文件中暂无此字段
                        'delflag': ''      # 配置文件中暂无此字段
                    }
                else:
                    # 如果配置文件没有数据，返回默认值
                    self._hospital_info = self._get_default_hospital_info()

            except Exception as e:
                print(f"从配置文件获取医院信息失败: {e}")
                # 返回默认值
                self._hospital_info = self._get_default_hospital_info()

        return self._hospital_info
    
    def get_doctors(self) -> List[str]:
        """
        获取医生列表
        
        Returns:
            医生姓名列表
        """
        if self._doctors is None:
            try:
                sql = "SELECT name FROM doctor ORDER BY name"
                rows = db_manager.execute_query(sql)
                self._doctors = [row['name'] for row in rows if row['name']]
                
                # 如果没有数据，返回默认值
                if not self._doctors:
                    self._doctors = ['张三', '李四', '王五']
                    
            except Exception as e:
                print(f"获取医生列表失败: {e}")
                # 返回默认值
                self._doctors = ['张三', '李四', '王五']
        
        return self._doctors
    
    def get_operators(self) -> List[str]:
        """
        获取操作员列表

        Returns:
            操作员姓名列表
        """
        if self._operators is None:
            try:
                # 更新SQL查询以适应新的表结构
                sql = "SELECT name FROM operator WHERE status = 'active' ORDER BY name"
                rows = db_manager.execute_query(sql)
                self._operators = [row['name'] for row in rows if row['name']]

                # 如果没有数据，返回默认值
                if not self._operators:
                    self._operators = ['操作员1', '操作员2']

            except Exception as e:
                print(f"获取操作员列表失败: {e}")
                # 返回默认值
                self._operators = ['操作员1', '操作员2']

        return self._operators
    
    def get_default_operator(self) -> str:
        """
        获取默认操作员（当前登录用户）

        Returns:
            当前登录用户姓名
        """
        try:
            # 从认证服务获取当前登录用户
            from services.auth_service import auth_service
            current_user = auth_service.get_current_user()
            if current_user:
                # 优先使用真实姓名，其次使用用户名
                return current_user.get('name') or current_user.get('username', '操作员')
            else:
                # 如果没有登录用户，使用第一个操作员
                operators = self.get_operators()
                return operators[0] if operators else '操作员'
        except Exception as e:
            print(f"获取当前操作员失败: {e}")
            # 出错时使用第一个操作员
            operators = self.get_operators()
            return operators[0] if operators else '操作员'
    
    def _load_settings_from_file(self) -> Dict[str, Any]:
        """从settings.json文件加载配置"""
        try:
            from utils.path_manager import get_config_file_in_dir
            settings_file = get_config_file_in_dir("settings.json")

            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print("配置文件settings.json不存在，使用默认配置")
                return {}

        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return {}

    def _get_default_hospital_info(self) -> Dict[str, Any]:
        """获取默认医院信息"""
        return {
            'id': 3,
            'hname': '泰安中医医院',
            'province': '',
            'city': '',
            'addr': '',
            'mobile': '',
            'keshi': '康复科',
            'shebeiid': '160701',
            'createtime': '',
            'updatetime': '',
            'delflag': ''
        }

    def _check_settings_file_changed(self) -> bool:
        """检查配置文件是否有变更"""
        try:
            from utils.path_manager import get_config_file_in_dir
            settings_file = get_config_file_in_dir("settings.json")

            if settings_file.exists():
                current_mtime = settings_file.stat().st_mtime
                if self._settings_file_mtime is None or current_mtime != self._settings_file_mtime:
                    self._settings_file_mtime = current_mtime
                    return True
            return False
        except Exception as e:
            print(f"检查配置文件变更失败: {e}")
            return False

    def refresh_cache(self):
        """刷新缓存数据"""
        self._hospital_info = None
        self._doctors = None
        self._operators = None
        self._settings_file_mtime = None
    
    def get_hospital_id(self) -> int:
        """获取医院ID"""
        hospital_info = self.get_hospital_info()
        return hospital_info['id'] if hospital_info else 3

    def get_department(self) -> str:
        """获取科室名称"""
        hospital_info = self.get_hospital_info()
        return hospital_info['keshi'] if hospital_info else '康复科'

    def get_device_id(self) -> str:
        """获取设备ID"""
        hospital_info = self.get_hospital_info()
        return hospital_info['shebeiid'] if hospital_info else '160701'

# 全局参考数据服务实例
reference_data_service = ReferenceDataService()
