# BLE5201 产品规格书

**版本：V1.3**

深圳市思为无线科技有限公司  
NiceRF Wireless Technology Co.,Ltd

---

## 产品特点

- 超低发射和接收电流
- 蓝牙5.2协议
- 支持单从机和一主多从

---

## 目录

1. [概述](#概述)
2. [使用注意事项](#使用注意事项)
3. [性能参数](#性能参数)
4. [硬件参数](#硬件参数)
5. [功能说明](#功能说明)
6. [AT指令](#at指令)
7. [快速使用指南](#快速使用指南)

---

## 一、概述

### 1.1 产品简介

本公司的BLE5201蓝牙数传模块使用蓝牙5.2协议，采用Silicon Labs的EFR32BG22C224 SOC芯片。具有功耗低、体积小、传输距离远、抗干扰能力强等特点。

BLE5201具备超低发射和接收电流和高性能、低功耗M33内核，可将纽扣电池的寿命延长至十年。目标应用包括蓝牙Mesh低功耗节点、智能门锁、个人医疗保健和健身设备。该SOC的蓝牙到达角（AoA）和离开角（AoD）功能以及1米以内定位精度也使得资产跟踪标签、信标和室内导航等应用将从中受益。EFR32BG22C224 SOC提供IQ采样能力，适用于测向应用，并支持125 KB和500 KB Bluetooth Low Energy Coded PHY，可将接收灵敏度提高至-106 dBm。支持需要测向功能或低功耗蓝牙Mesh节点的应用。

BLE5201蓝牙数传模块严格使用无铅工艺生产和测试，符合RoHS、Reach的标准。

### 1.2 芯片特征

EFR32BG22C224芯片拥有丰富的外设功能，比如PWM、串口（UART）、中断、I2C、SPI、定时器等。因此BLE5201也可用作常用MCU进行二次开发。它具有以下特征：

#### 主要特性
- 射频频率范围：2400-2483.5MHz
- 发射功率：最高6dBm
- 1.71-3.8V供电
- 超低耗静态模式 < 5uA
- 高性能32位Cortex®-M33内核，最高工作频率为76.8MHz
- 芯片内部最高512kB可重复擦写Flash和32kB RAM
- 支持2(G)FSK、OQPSK DSSS、(G)MSK调制格式
- 2个模拟数字转换器（ADC）：12位，1Msps；16位，76.9Ksps
- 18个带有输出状态保持和异步中断功能的通用I/O引脚
- 8信道DMA控制器
- 12信道外围设备反射系统（PRS）
- 4个16位定时器/计数器(3个比较/捕获/PWM通道)
- 32位实时计数器
- 24位低能耗定时器，用于波形生成
- 1个看门狗定时器
- 2个通用同步/异步接收器/传输器(UART/SPI/SmartCard(ISO 7816)/IrDA/I2S)
- 1个增强型通用异步接收器/传输器(EUART)
- 2个I2C接口，带SMBus支持
- 数字麦克风接口（PDM）
- 可选OOK模式的RFSENSE
- 单点校准后具有+/-1.5℃精度的芯片温度传感器

### 1.3 产品特点

- 支持蓝牙BLE5.2协议
- 支持单从机和一主多从，可同时连接多个从机，最多8个连接
- 支持配置、透传2种工作模式
- 支持空中升级（OTA DFU）
- 支持多种串口参数配置
- 支持自动广播、扫描、连接
- 支持手动连接与自动连接2种连接方式
- MTU最大为247 bytes
- 支持自定义的16位UUID，包括一个Service uuid、两个Characteristic uuid
- 最大通讯距离150m（6dBm、Coded PHY）
- 支持蓝牙设备绑定
- 支持低功耗睡眠

### 1.4 应用领域

- 无线抄表无线传感
- 智能家居
- 工业遥控、遥测
- 智能楼宇、智能建筑
- 自动化数据采集
- 健康传感器
- 智能穿戴设备
- 智能机器人
- 无线传感
- 电子标签
- 智能控制

---

## 二、使用注意事项

### 2.1 BLE说明

#### 2.1.1 MTU

指BLE空中单包数据有效负荷大小。在BLE4.0/4.1协议的MTU为27字节，从BLE4.2以及更高版本的MTU可扩展至251个字节。在实际使用时，单包数据为MTU-3，即用户使用BLE4.0/4.1协议单包最大可发送24个字节，BLE4.2及更高版本可扩展至247个字节。

需要说明的是，在实际应用中，不同设备的MTU会有所不同。

### 2.2 模块应用注意事项

#### 2.2.1 主从角色的判定条件

当模块主动扫描连接其他蓝牙设备时，模块在此次连接中为主机角色；当模块因广播自身而被其他蓝牙设备连接时，模块在此次连接中为从机角色。

#### 2.2.2 距离对数据传输速率的响应

模块采用PCB天线。BLE无线信号相对于陶瓷天线，外接天线发射和接收能力较强。尽管如此，模块的数据发送速度仍然会因距离的增加和电源输出功率大小而衰减。

#### 2.2.3 模块数据发送速度变化

模块的数据发送到接收的时间会因距离的增加而增加。当模块为主机时可以连接多个从机，发送数据是以轮询链接号的形式发送的，数据的总发送-接收时间会因连接数量而发生变化。

#### 2.2.4 模块使用Coded PHY的注意事项

模块启用Coded PHY进行扫描/广播，以及建立连接后的数据收发时，相较于1M PHY，虽然牺牲了传输速率，增加了功耗，但获得了更远的通讯距离。

---

## 三、性能参数

| 主要参数 | 性能 | 备注 |
|----------|------|------|
| | 最小值 | 典型值 | 最大值 | |
| 工作电压(V) | 1.8 | 3.3 | 4.3 | >= 3.3V可保证输出功率 |
| 通信电平(V) | | 3.3 | | |
| 工作温度(℃) | -40 | | 85 | 工业级设计 |
| 工作频段(MHz) | 2400 | | 2483.5 | |
| 发射功率(dBm) | 0 | | 6 | |
| 接收灵敏度(dBm) | | -94.6 | | |

| 功耗 | | | |
|------|------|------|------|
| 发射电流(mA) | | 7.5 | |
| 接收电流(mA) | | 8.5 | |
| 休眠电流(uA) | | < 5 | 无连接时 |
| 空闲状态电流(mA) | | 6.58 | 空闲状态：指无连接、模块不广播/扫描 |

---

## 四、硬件参数

### 4.1 内部框图

*[此处原文档包含内部框图，但在文本转换中无法显示]*

### 4.2 引脚定义

| 引脚序号 | 引脚定义 | 引脚方向 | 引脚功能 | 说明 |
|----------|----------|----------|----------|------|
| 1 | GND | 输入 | 电源地 | |
| 2 | PA00 | 输入 | 断开连接引脚 | 默认上拉。低电平：断开所有蓝牙连接，并停止广播/扫描；高电平：恢复广播/扫描 |
| 3 | PA01 | | SWCLK | |
| 4 | PA02 | | SWDIO | |
| 5 | PA03 | 输入 | 唤醒引脚CS | 默认上拉：低电平工作，高电平休眠 |
| 6 | PA04 | 输入 | 模块选择SET | 默认上拉。高电平：透传模式；低电平：配置模式 |
| 7 | PA05 | | TXD | 模块串口数据发送脚 |
| 8 | PA06 | | RXD | 模块串口数据接收脚 |
| 9 | GND | | 电源地 | |
| 10 | VCC | | 电源正极 | |
| 11 | PC00 | | GPIO | |
| 12 | PC01 | | GPIO | |
| 13 | PC02 | | GPIO | |
| 14 | PC03 | | GPIO | |
| 15 | PC04 | | GPIO | |
| 16 | PC05 | | GPIO | |
| 17 | PB02 | 输出 | 睡眠状态 | 唤醒模式：高电平；低功耗模式：低电平 |
| 18 | PB01 | 输出 | 模式状态 | 透传模式：低电平；配置模式：高电平 |
| 19 | RESET | 输出 | 复位 | 内置上拉电阻，低电平有效 |
| 20 | PB00 | 输出 | 连接状态 | 有连接：高电平；无连接：低电平 |

### 4.3 外设路由

BLE5201所有GPIO均可路由映射以使用外设资源，可作为MCU使用下列功能。

**注：这些外设不代表本模块出厂程序所拥有的功能，需要客户联系我司定制或自行编写程序实现。**

*[此处原文档包含详细的外设路由表，由于篇幅较长，在此简化显示]*

### 4.4 典型应用电路

```
1.71-3.8v
    |
    VCC
    GND ---- 用户设备
    RXD      TXD
    TXD      RXD  ---- BLE5201
    IO1 ---- SET
    IO2 ---- CS
    IO3 ---- Reset
    IO4 ---- DISC
```

### 4.5 机械尺寸（单位：mm）

- 长度：10.82±0.1mm
- 宽度：14.5±0.1mm
- 厚度：2.4±0.1mm

---

## 五、功能说明

### 5.1 角色说明

模块支持两种角色：主机、从机，这两种类型通过发送AT指令AT+ROLE修改。

模块最多支持8个连接，支持透明传输，支持手动、自动连接。

#### 5.1.1 主机

- 配置模式下发送指令AT+ROLE=1切换为主机角色，可使用扫描功能。
- 配置模式下发送指令AT+SCAN开启扫描，用于手动连接从机；
- 透传模式下模块会自动扫描连接从机。
- 支持一主多从连接。最多8个连接。

##### 5.1.1.1 主机连接策略

模块按服务UUID过滤广播包。

UUID过滤依据AT+UUID所配置内容过滤，该条件不能关闭。

模块在扫描时会分析扫描到的广播包，如果广播包内有包含Service UUID并且该UUID与模块自身的Service UUID相同时，模块会自动发起连接。连接建立后，模块作为主机角色会试图读取对端设备该Service UUID下的Characteristic UUID。如果模块没有读取到Service UUID与其下的Characteristic UUID，或者读取到的UUID值与模块自身不符，模块会自动断开其连接。

该过滤条件适用于手动连接与自动连接。

#### 5.1.2 从机

- 配置模式下发送指令AT+ROLE=0切换为从机角色。
- 透传模式下模块会自动广播自身。
- 广播内容包含设备名称、mac地址，以及透传数据需要的Service UUID。

### 5.2 电源模式

模块支持2种电源模式：低功耗模式、唤醒模式。

#### 5.2.1 低功耗模式

低功耗模式指模块进入到该模式时，BLE功能会继续运行，关闭部分外设，停止广播、扫描，以尽量降低模块运行时的功耗。

低功耗模式下，串口功能会关闭，当有未断开的连接时，如模块接收到对端数据，或连接状态发生改变时，模块会临时唤醒恢复串口功能并处理相关任务，任务完成后，模块暂时没有任务时会立即进入睡眠。

如何进入低功耗模式：拉高CS引脚，当指示脚拉低时表示已进入低功耗模式。

如何退出低功耗模式：拉低CS引脚。

模块有连接时进入低功耗模式，模块可能在短时间内仍然维持连接，这种情况下电流会偏高，但总小于1mA。

#### 5.2.2 唤醒模式

唤醒模式指模块在低功耗模式外正常运行时的状态，所有外设与功能均正常运行。

如何唤醒：拉低CS引脚。

### 5.3 数据透传

数据透传指将串口接收到的数据，不经任何处理，通过BLE发送到对端设备，或将BLE接收到的数据，不经任何处理输出到串口。

当模块作为主机时，模块数据透传会通过轮询的方式发送到每一个已连接的蓝牙设备，因此，在极限条件下，不能保证每一个设备一定能接收到数据。

### 5.4 通信速率

模块默认使用1M PHY的传输速率进行数据传输。即为每秒由蓝牙物理层调试过后传到空中的速率为1兆比特，这并不代表蓝牙接收端接收的数据传输率可以到1M/bps。

通过AT指令设置，模块可以更改广播/扫描以及蓝牙连接时使用的传输速率。目前模块支持的传输速率有1M PHY、2M PHY以及Coded PHY（125K和500K）。

使用Coded PHY，可以以降低传输速率、增加功耗为代价，使得蓝牙设备能更远距离的传输信号，获得更远的通信距离，从而更好的适应互物联网应用。

### 5.5 UUID说明

表格中列出的UUID值均可通过AT指令修改，UUID属性除外。

| 服务UUID（Service UUID） | FFF0 |
|---------------------------|------|
| **特征值（Characteristic UUID）** | **UUID属性** | **说明** |
| FFF1 | Read/Notify | 从机发送，主机接收数据通道 |
| FFF2 | Write/Write no response | 主机发送，从机接收数据通道 |

### 5.6 连接句柄

1. 通常情况下，模块的连接句柄为指定链路的编号（1-8），原则上模块每次连接的句柄不一定相同。

2. 可以通过AT指令对指定链路进行操作。

### 5.7 空中升级（OTA DFU）与串口升级

模块支持空中升级与串口升级二选一，用户亦可自行定制自己所需的固件。

具体升级步骤请查看第七章快速使用。

### 5.8 状态打印

进行部分操作时串口会输出状态。

| 状态 | 打印信息 |
|------|----------|
| 连接成功 | [CONNECTED] |
| 连接断开 | [DISCONNECTED] |
| 进入配置模式 | [ENTER AT MODE] |
| 退出配置模式 | [EXIT AT MODE] |
| 进入低功耗模式 | [ENTER SLEEP] |
| 退出低功耗模式 | [EXIT SLEEP] |
| 绑定成功 | [BOND SUCCESS] |
| 绑定失败 | [BOND FAIL] |

---

## 六、AT指令

模块必须进入配置模式发送AT指令，否则命令将不会响应。

### 6.1 配置模式说明

- 所有AT指令都以ASCII格式进行传输；
- 指令发送格式：所有的指令都以"AT"开头，以回车换行符"\r\n"结束。指令除参数外必须大写；
- 指令返回格式：返回跟发送指令对应的相关字符串，均以回车换行符"\r\n"结束；
- 配置模式中，为防止数据干扰，来自其他设备发送的数据将不会显示。
- 进入配置模式时串口参数会被重新设置（115200 bps、8 databits、1 stopbit、None parity）；

### 6.2 错误代码

当发送的指令不符合某些情况时，模块会返回错误，格式为"+ERROR=<Error num>\r\n"。

| Error num | 说明 | 错误原因 | 解决方法 |
|-----------|------|----------|----------|
| 0 | 数据读取失败 | | |
| -1 | 指令不存在 | AT指令字符有误 | 检查AT指令字符串格式 |
| -2 | 未连接 | 模块未建立任何连接 | |
| -3 | 已达最大连接数 | 模块的蓝牙连接已达最大值 | |
| -4 | 参数错误 | 指令参数格式错误或参数不在取值范围内 | 对照指令确定指令格式与取值范围 |
| -5 | 忙碌中 | 模块正在执行其他指令 | 等待其他指令操作完成 |
| -6 | 链路不存在 | 模块没有该链路号对应的连接句柄 | 操作其他链路号或等待该链路号被使用后再操作 |
| -7 | 无法发起扫描/连接 | 模块为从机角色 | 将模块切换为主机角色后重试 |
| -8 | 临时黑名单已满 | | |

### 6.3 AT指令集

#### 6.3.1 测试指令

| 指令 | 响应 |
|------|------|
| AT | OK |

#### 6.3.2 AT+VERSION 查询固件版本号

| 指令 | 响应 |
|------|------|
| AT+VERSION? | +VERSION=<param> |

**说明：** `<param>`: 模块固件版本号

#### 6.3.3 AT+MAC 查询模块mac地址

| 指令 | 响应 |
|------|------|
| AT+MAC? | +MAC=<param> |

**说明：** `<param>`: mac地址

#### 6.3.4 AT+RESET 重启指令

| 指令 | 响应 |
|------|------|
| AT+RESET | OK |

#### 6.3.5 AT+DEFAULT 恢复默认参数

| 指令 | 响应 |
|------|------|
| AT+DEFAULT | OK |

**说明：** 发送该命令后立即重启并生效。

#### 6.3.6 AT+NAME 修改模块名称

| 指令 | 响应 |
|------|------|
| 查询：AT+NAME? | +NAME=<param> |
| 设置：AT+NAME=<param> | OK |

**说明：** `<param>`: 设备名称字符串，最多不超过8个字符，默认值：BLE5201

#### 6.3.7 AT+UUID 修改服务UUID

| 指令 | 响应 |
|------|------|
| 查询：AT+UUID? | +UUID=<param>,<param1>,<param2> |
| 设置：AT+UUID=<param>,<param1>,<param2> | OK |

**说明：**
- `<param>`: Service UUID
- `<param1>`: Characteristic UUID (read,notify)
- `<param2>`: Characteristic UUID (write,write no response)
- 取值范围：0001-FFFE
- 默认值：FFF0,FFF1,FFF2

#### 6.3.8 AT+ROLE 切换主从角色

| 指令 | 响应 |
|------|------|
| 查询：AT+ROLE? | +ROLE=<param> |
| 设置：AT+ROLE=<param> | OK |

**说明：** `<param>`: 0——从机（默认值）；1——主机  
如果切换时仍有蓝牙连接，模块会断开连接。

#### 6.3.9 AT+UART 修改串口配置参数

| 指令 | 响应 |
|------|------|
| 查询：AT+UART? | +UART=<param>,<param1>,<param2>,<param3> |
| 设置：AT+UART=<param>,<param1>,<param2>,<param3> | OK |

**参数说明：**

| 参数 | 说明 | 取值范围 |
|------|------|----------|
| `<param>` | 串口波特率 | 0：4800bps<br>1：9600bps<br>2：14400bps<br>3：19200bps<br>4：38400bps<br>5：56000bps<br>6：57600bps<br>7：115200bps（默认值） |
| `<param1>` | 数据位 | 0：5bits<br>1：6bits<br>2：7bits<br>3：8bits（默认值） |
| `<param2>` | 校验位 | 0：NONE（默认值）<br>1：EVEN<br>2：ODD |
| `<param3>` | 停止位 | 0：1bit（默认值）<br>1：2bit |

#### 6.3.10 AT+ADVINT 修改广播间隔

| 指令 | 响应 |
|------|------|
| 查询：AT+ADVINT? | +ADVINT=<param> |
| 设置：AT+ADVINT=<param> | OK |

**说明：** `<param>`:
- 0：100ms（默认值）
- 1：200ms
- 2：500ms
- 3：1000ms
- 4：2000ms
- 5：5000ms

#### 6.3.11 AT+INQINT 修改扫描间隔

| 指令 | 响应 |
|------|------|
| 查询：AT+INQINT? | +INQINT=<param> |
| 设置：AT+INQINT=<param> | OK |

**说明：** `<param>`:
- 0：10ms（默认值）
- 1：100ms
- 2：200ms
- 3：500ms
- 4：1000ms
- 5：2000ms

#### 6.3.12 AT+POWER 修改发射功率

| 指令 | 响应 |
|------|------|
| 查询：AT+POWER? | +POWER=<param> |
| 设置：AT+POWER=<param> | OK |

**说明：** `<param>`:
- 0：0dBm
- 1：1dBm
- 2：2dBm
- 3：3dBm
- 4：4dBm
- 5：5dBm
- 6：6dBm（默认值）

**注：** 修改发射功率时，系统堆栈会在极短时间内处于停止状态，短时间内频繁使用可能会对蓝牙连接产生不可预知的影响。

*[继续其他AT指令...]*

---

## 七、快速使用指南

### 7.1 快速使用

#### 7.1.1 环境准备

**硬件：** 2个BLE5201模块  
**软件：** SSCOM串口调试助手

**注：** 由于模块CS脚默认为高电平，而低功耗模式为高电平有效，因此需要提前拉低CS脚以正常使用模块的所有功能。

#### 7.1.2 自动连接

模块上电后拉低SET脚进入配置模式，由于模块默认为从机角色，需要对其中一个模块发送"AT+ROLE=1"将其切换为主机角色。

2个模块都在透传模式时，会自动建立蓝牙连接。连接建立后，即可开始透传数据。

#### 7.1.3 手动连接

模块在配置模式时为空闲状态，不做广播、扫描操作，此时可以使用手动连接。

向模块A发送指令"AT+SCAN"开启扫描。模块A会扫描并打印出模块B的设备信息。

向模块A发送建立连接指令AT+CONA，等待连接建立。成功建立连接后，拉高SET脚退出配置模式，即可开始透传数据。

#### 7.1.4 断开连接

拉低DISC引脚，或者在配置模式中使用AT+DISC指令，即可断开当前连接。

DISC引脚持续拉低时，模块不做广播、扫描操作，无法建立连接，直到DISC引脚恢复高电平为止。

使用后者的方式可以在有多个连接时，断开指定的连接。

### 7.2 固件升级

#### 7.2.1 空中升级

1. 打开串口调试助手，拉低BLE5201的SET引脚进入配置模式，发送AT+UPDATE命令后，BLE5201会响应"OK\r\n"，并重启模块以进入升级模式

2. 手机打开蓝牙功能，打开EFR Connect应用软件，点击Browser，搜索并连接需要升级的设备。

   （EFR Connect应用软件可在谷歌应用商店下载）

3. 升级模式下的BLE5201模块固定广播名称为"OTA-BLE5201"。点击Connect连接。

4. 点击右上角菜单，点击OTA DFU选项。

5. 在弹出的对话框里点击FULL OTA，将Application文件与Applocaer文件依次导入，然后点击OTA选项开始传输文件。

6. 文件传输完毕后，点击END，蓝牙连接将自动断开，模块开始安装升级固件。

7. 升级完毕后，模块自动重启，模块将运行升级后的程序；如果升级失败，模块将运行升级前的程序。

**注：进入程序升级模式后会重置配置模式下保存的参数**

---

## 文档修订记录

| 历史版本号 | 发布时间 | 修改内容 |
|------------|----------|----------|
| V1.0 | 2021-4 | 初次发布 |
| V1.1 | 2021-7 | 修改规格书内容；新增部分AT指令；修改了固件功能使之与其他产品兼容 |
| V1.2 | 2021-9 | 修改了7.2固件升级的全部内容 |
| V1.3 | 2021-12 | 新增了多种传输速率，以满足不同的使用环境；新增部分AT指令 |

---

**联系信息：**
- 地址：深圳市宝安区十三区鸿都商务大厦A栋三楼309-314
- 电话：0755-23080616
- 邮件：<EMAIL>
- 网址：www.nicerf.cn