# C2设备定制客户DLL使用说明书

## 目录

1. [概述](#1-概述)
2. [接口描述](#2-接口描述)
   - 2.1 [打开串口函数定义](#21-打开串口函数定义)
   - 2.2 [数据回调函数定义](#22-数据回调函数定义)
   - 2.3 [判断串口是否打开函数定义](#23-判断串口是否打开函数定义)
   - 2.4 [关闭串口函数定义](#24-关闭串口函数定义)
   - 2.5 [读设备信息命令函数定义](#25-读设备信息命令函数定义)
   - 2.6 [切换设备工作状态命令函数定义](#26-切换设备工作状态命令函数定义)
   - 2.7 [切换通道工作状态命令函数定义](#27-切换通道工作状态命令函数定义)
   - 2.8 [电流调节命令函数定义](#28-电流调节命令函数定义)
   - 2.9 [设置循环刺激参数命令函数定义](#29-设置循环刺激参数命令函数定义)
   - 2.10 [循环刺激参数命令函数定义](#210-循环刺激参数命令函数定义)
   - 2.11 [电流强度偏移量调节命令函数定义](#211-电流强度偏移量调节命令函数定义)
   - 2.12 [电流强度设定命令函数定义](#212-电流强度设定命令函数定义)
   - 2.13 [读取串口函数定义](#213-读取串口函数定义)
3. [使用说明](#3-使用说明)
   - 3.1 [电刺激调用步骤及流程图](#31-电刺激调用步骤及流程图)
     - 3.1.1 [调用步骤](#311-调用步骤)
     - 3.1.2 [流程图](#312-流程图)
4. [参考文献](#4-参考文献)

## 1. 概述

此动态链接库适用上海诺诚电气有限公司所生产的C2设备，动态链接库将需要向硬件发送的命令封装，以接口的形式提供给用户。

本接口说明书详细介绍了动态链接库接口函数的用法。用户在开发软件时必须阅读此说明书，同时可参考demo程序。

## 2. 接口描述

### 2.1 打开串口函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int OpenRecPort(int portNum, int iReadSize, funDataProc fun, HANDLE pHandle);
```

**函数功能：**
该函数在应用程序启动后，可根据实际情况自动调用或在相应的设置之后调用，串口号填写连接设备的串口号，同时在调用该函数时要将回调函数提前声明。

**参数：**

| 参数名称 | 参数类型 | 描述 |
|----------|----------|------|
| portNum | int | 串口号 |
| iReadSize | int | 要去串口读取数据的个数 |
| fun | funDataProc | 回调函数 |
| pHandle | HANDLE | 句柄 |

**返回值：**
- `1`：打开串口成功
- `-1`：串口打开失败
- `-2`：线程创建失败

### 2.2 数据回调函数定义

**函数定义：**
```c
typedef int (__stdcall *funDataProc)(HANDLE pHandle, const LPVOID lpBuffer, const int nSize);
```

**函数功能：**
数据回调函数；在上层应用中，该函数的函数体用户自行编写，回调函数被触发后，打包好的数据就会存储在缓存中，函数体中需将数据解析出来，做相应的显示编码。

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| pHandle | HANDLE | 句柄 | |
| lpBuffer | const LPVOID | 存放打包好数据的缓存 | 存放打包好数据的缓存；<br/>缓存中的数据格式：<br/>自由刺激数据帧：<br/>0x55AA<br/>0x01<br/>A通道状态<br/>A通道该状态下的进度条数据<br/>B通道状态<br/>B通道该状态下的进度条数据 |
| nSize | const int | 缓存中数据的个数 | |

**返回值：**

**备注：**
该回调函数在硬件有数据上来后被触发，上层应用可以在该回调中根据缓存中的数据格式将数据解析出来，进行波形绘制或数据显示，而不需要额外开启线程，或是定时器。

### 2.3 判断串口是否打开函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) BOOL IsRecOpen();
```

**函数功能：**
在上层应用中调用该函数，可以判断串口是否打开。

**返回值：**
- 返回`TRUE`：串口打开着
- 返回`FALSE`：串口关闭着

### 2.4 关闭串口函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int CloseRecPort();
```

**函数功能：**
在退出应用程序时要调用该函数，将串口关闭。

**返回值：**
- `1`：串口关闭成功

### 2.5 读设备信息命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int ReadDeviceInfo(char* DevInfo);
```

**函数功能：**
读取设备信息。

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| DevInfo | char* | 设备信息 | 初始化全为0的数组，数组长度为20。函数调用后，数组中存放的是设备信息。 |

**返回值：**
- `0`：命令执行正确
- `1`：命令错误，包括收到不执行的命令码、通道号等
- `2`：命令参数错误，收到的命令中参数超限
- `3`：校验错误
- `4`：没有读取到硬件的命令应答
- `5`：将命令写入串口时失败

### 2.6 切换设备工作状态命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int SwitchDeviceState(int nStateNum);
```

**函数功能：**
在打开串口成功后，如是自由刺激模块应用，要调用该命令将设备切换循环刺激状态；如肌电评估和反馈刺激模块应用，要将设备切换到肌电反馈状态；在每一个应用模块退出时，都要调用该命令，将设备切换到空闲状态。

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| nStateNum | int | 设备状态编码 | `0`：空闲状态<br/>`1`：循环刺激状态 |

**返回值：**（同2.5）
- `0`：命令执行正确
- `1`：命令错误，包括收到不执行的命令码、通道号等
- `2`：命令参数错误，收到的命令中参数超限
- `3`：校验错误
- `4`：没有读取到硬件的命令应答
- `5`：将命令写入串口时失败

### 2.7 切换通道工作状态命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int SwitchChannelState(int nChanNum, int nStateNum);
```

**函数功能：**
在自由刺激模块应用时，在调节电流时要调用该函数将通道切换到电流调节状态，在开始刺激后要调用该命令将通道状态设切换为正常工作状态，暂停时要将通道切换到暂停状态，恢复时要将通道切换到正常工作状态，停止刺激时要将通道切换到停止状态。

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| nChanNum | int | 通道编号 | `1`：为通道1<br/>`2`：为通道2 |
| nStateNum | int | 通道状态编号 | `0`：停止<br/>`1`：暂停<br/>`2`：电流调节<br/>`3`：正常工作 |

**返回值：**（同上）

### 2.8 电流调节命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int RegulateCurrent(int nChanNum, int nStep, bool bClimb);
```

**函数功能：**
通道状态在电流调节状态时，要加减通道的电流强度时要调用该函数实现。

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| nChanNum | int | 通道编号 | `1`：为通道1<br/>`2`：为通道2 |
| nStep | int | 调节步长 | `0`：0.1mA<br/>`1`：0.2mA<br/>`2`：0.5mA<br/>`3`：1mA<br/>`4`：2mA<br/>`5`：5mA |
| bClimb | bool | 步长调节 | `TRUE`：向下调节一个步长<br/>`FALSE`：向上调节一个步长 |

**返回值：**（同上）

### 2.9 设置循环刺激参数命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int StimParaSet(CYCLEACTPARA& CycleActParaNew);
```

**函数功能：**
在反馈刺激模块应用中，在开始采集之前如需修改相应的参数，侧需在修改后，调用该函数设置参数。

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| CycleActParaNew | CYCLEACTPARA | 刺激参数结构体 | 结构体的具体内容如下：<br/>`typedef struct _CYCLEACTPARA`<br/>`{`<br/>`    int ChanNum;        // 通道编号：1：为通道1，2：为通道2`<br/>`    double ActFreq;     // 频率（2~160HZ）`<br/>`    double PulseWidth;  // 脉宽（10~500μV）`<br/>`    double RelaxTime;   // 修休息时间（0~16S）`<br/>`    double ClimbTime;   // 上升时间(0~5S)`<br/>`    double WorkTime;    // 工作时间(0~10S)`<br/>`    double FallTime;    // 下降时间(0~5S)`<br/>`    int WaveType;       // 波形类型`<br/>`}` |

**返回值：**（同上）

### 2.10 循环刺激参数命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int StimPara(int ChanNum,
    double ActFreq,
    double PulseWidth,
    double RelaxTime,
    double ClimbTime,
    double WorkTime,
    double FallTime,
    int WaveType);
```

**函数功能：**
在反馈刺激模块应用中，在开始采集之前如需修改相应的参数，侧需在修改后，调用该函数设置参数。（备注：在调用StimParaSet函数，使用结构体不方便可调用该函数发参数命令，函数功能等价于StimParaSet函数）

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| ChanNum | int | 通道编号 | `1`：为通道1<br/>`2`：为通道2 |
| ActFreq | double | 频率 | 2~160HZ |
| PulseWidth | double | 脉宽 | 10~500μV |
| RelaxTime | double | 修休息时间 | 0~16S |
| ClimbTime | double | 上升时间 | 0~5S |
| WorkTime | double | 工作时间 | 0~10S |
| FallTime | double | 下降时间 | 0~5S |
| WaveType | int | 波形类型 | `1`：单向波（此版用单向波）<br/>`2`：双向波 |

**返回值：**（同上）

### 2.11 电流强度偏移量调节命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int RegulateCurrentOffet(int nChanNum, bool bCurrentRaise);
```

**函数功能：**
在对硬件设备的电流校正时需要调用电流强度偏移量调节命令函数向上或向下调节电流。详细可参看电流强度设定命令功能。（备注：该命令函数用于电流校正）

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| nChanNum | int | 通道编号 | `1`：为通道1<br/>`2`：为通道2 |
| bCurrentRaise | bool | 调节步长 | `TRUE`：向下调节一个步长<br/>`FALSE`：向上调节一个步长 |

**返回值：**（同上）

### 2.12 电流强度设定命令函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int CurrentSet(int nChanNum, int nValue);
```

**函数功能：**
在对硬件设备电流校正时需要调用电流强度设定命令，设置硬件电流的大小，再通过示波器看硬件的电流输出是否是设置电流的大小，如有偏差，可调用电流强度偏移量调节命令函数进行调节。（备注：该命令函数用于电流校正）

**参数：**

| 参数名称 | 参数类型 | 描述 | 取值 |
|----------|----------|------|------|
| nChanNum | int | 通道编号 | `1`：为通道1<br/>`2`：为通道2 |
| nValue | int | 电流强度设定值 | 1、2、3、4、5、6、7、8、9、10、20、30、40、50、60、70、80、90、100（mA） |

**返回值：**（同上）

### 2.13 读取串口函数定义

**函数定义：**
```c
extern "C" __declspec(dllexport) int ReadData(BYTE *byBuffer, int nReadOnceDataNum);
```

**函数功能：**
调用该函数，可以直接读取硬件串口数据，读取到的数据是未经解析的原始数据，上层应用中无需调用该函数。

**参数：**

| 参数名称 | 参数类型 | 描述 |
|----------|----------|------|
| byBuffer | BYTE * | 存储数据的缓存 |
| nReadOnceDataNum | int | 要去串口读取数据的个数 |

**返回值：**
实际从串口读取数据的个数

## 3. 使用说明

### 3.1 电刺激调用步骤及流程图

#### 3.1.1 调用步骤

1. **调用 `OpenRecPort(int portNum, int iReadSize, funDataProc fun, HANDLE pHandle)` 函数打开串口**

2. **打开串口成功后，调用 `SwitchDeviceState(int nStateNum)` 函数，切换设备的工作状态，将设备切换到循环刺激状态**

3. **同时调用 `StimParaSet` 函数或 `StimPara` 函数将循环刺激参数命令发下去**
   - 备注：在设置参数后，参数发生改动，要重新调用循环刺激参数命令函数发送刺激参数

4. **随后调节电流**
   - 在点击电流上调的按钮时，先调用 `SwitchChannelState(int nChanNum, int nStateNum)` 函数，将通道的状态切换到电流调节状态
   - 再调用电流调节命令函数 `RegulateCurrent(int nChanNum, int nStep, bool bClimb)` 调节电流

5. **电流调节后，点击开始采集**
   - 此时调用 `SwitchChannelState(int nChanNum, int nStateNum)` 函数，切换通道状态到正常工作状态
   - 停止采集时切换通道状态到停止状态

6. **在开始采集后，在回调函数的函数体中将数据根据格式解析出来并做相应的显示，无需另外开启线程**

7. **退出时，将设备状态切换到空闲状态，在调用关闭串口的函数关闭串口**

#### 3.1.2 流程图

```mermaid
flowchart TD
    A[开始] --> B[调用OpenRecPort打开串口]
    B --> C{串口打开成功?}
    C -->|否| D[返回错误]
    C -->|是| E[调用SwitchDeviceState切换到循环刺激状态]
    E --> F[调用StimParaSet或StimPara设置参数]
    F --> G[调用SwitchChannelState切换到电流调节状态]
    G --> H[调用RegulateCurrent调节电流]
    H --> I[调用SwitchChannelState切换到正常工作状态]
    I --> J[开始采集]
    J --> K[在回调函数中解析数据]
    K --> L{继续采集?}
    L -->|是| K
    L -->|否| M[调用SwitchChannelState切换到停止状态]
    M --> N[调用SwitchDeviceState切换到空闲状态]
    N --> O[调用CloseRecPort关闭串口]
    O --> P[结束]
    D --> P
```

## 4. 参考文献

[1]. C2通信协议-定制客户