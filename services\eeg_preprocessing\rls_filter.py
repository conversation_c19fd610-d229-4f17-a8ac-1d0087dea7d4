"""
RLS自适应滤波器模块

用于脑电信号的实时自适应滤波处理，专门针对低SNR环境优化。
"""

import numpy as np
import logging
from typing import Optional, Dict, Any
from .preprocessing_config import PreprocessingConfig


class RLSFilter:
    """
    递归最小二乘(RLS)自适应滤波器
    
    专为低SNR脑电信号设计的实时自适应滤波器，具有快速收敛和优秀的跟踪能力。
    """
    
    def __init__(self, config: PreprocessingConfig):
        """
        初始化RLS滤波器
        
        Args:
            config: 预处理配置对象
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # RLS参数
        self.filter_length = config.rls_filter_length
        self.forgetting_factor = config.rls_forgetting_factor
        self.regularization = config.rls_regularization
        self.initial_covariance = config.rls_initial_covariance
        
        # 通道数
        self.n_channels = config.n_channels
        
        # 为每个通道初始化RLS滤波器状态
        self._initialize_filters()
        
        # 性能统计
        self.performance_stats = {
            'total_updates': 0,
            'convergence_errors': [],
            'condition_numbers': [],
            'processing_times': []
        }
        
        self.logger.info(f"RLS滤波器初始化完成 - 滤波器长度: {self.filter_length}, "
                        f"遗忘因子: {self.forgetting_factor}")
    
    def _initialize_filters(self):
        """初始化所有通道的RLS滤波器状态"""
        # 权重向量 [n_channels, filter_length]
        self.weights = np.zeros((self.n_channels, self.filter_length))
        
        # 协方差矩阵 [n_channels, filter_length, filter_length]
        self.covariance_matrices = np.zeros((self.n_channels, self.filter_length, self.filter_length))
        
        # 初始化协方差矩阵为对角矩阵
        for ch in range(self.n_channels):
            self.covariance_matrices[ch] = np.eye(self.filter_length) * self.initial_covariance
        
        # 输入缓冲区 [n_channels, filter_length]
        self.input_buffers = np.zeros((self.n_channels, self.filter_length))
        
        # 数值稳定性监控
        self.stability_counter = 0
        self.reset_threshold = 1000  # 每1000次更新检查一次稳定性
        
        self.logger.debug(f"RLS滤波器状态初始化完成 - {self.n_channels}通道")
    
    def process_packet(self, packet_data: np.ndarray) -> np.ndarray:
        """
        处理数据包（实时处理）
        
        Args:
            packet_data: 输入数据包 [8, 4] - 8通道，4个样本点
            
        Returns:
            filtered_data: 滤波后的数据包 [8, 4]
        """
        import time
        start_time = time.perf_counter()
        
        try:
            if packet_data.shape != (self.n_channels, 4):
                raise ValueError(f"输入数据形状错误: {packet_data.shape}")
            
            # 逐样本处理
            filtered_packet = np.zeros_like(packet_data)
            
            for sample_idx in range(4):
                sample = packet_data[:, sample_idx]  # [8,]
                filtered_sample = self._update_filters(sample)

                # 防止数值溢出
                filtered_sample = np.clip(filtered_sample, -1e6, 1e6)

                # 检查并处理异常值
                if np.any(np.isnan(filtered_sample)) or np.any(np.isinf(filtered_sample)):
                    logger.warning(f"RLS滤波器输出异常值，使用原始样本")
                    filtered_sample = sample

                filtered_packet[:, sample_idx] = filtered_sample
            
            # 记录性能统计
            processing_time = (time.perf_counter() - start_time) * 1000
            self.performance_stats['processing_times'].append(processing_time)
            self.performance_stats['total_updates'] += 4  # 4个样本
            
            # 定期检查数值稳定性
            self.stability_counter += 4
            if self.stability_counter >= self.reset_threshold:
                self._check_numerical_stability()
                self.stability_counter = 0
            
            return filtered_packet
            
        except Exception as e:
            self.logger.error(f"RLS滤波处理失败: {e}")
            # 返回原始数据作为备选
            return packet_data.copy()
    
    def _update_filters(self, input_sample: np.ndarray) -> np.ndarray:
        """
        更新所有通道的RLS滤波器
        
        Args:
            input_sample: 输入样本 [8,]
            
        Returns:
            filtered_sample: 滤波后的样本 [8,]
        """
        filtered_sample = np.zeros(self.n_channels)
        
        for ch in range(self.n_channels):
            # 更新输入缓冲区
            self.input_buffers[ch, 1:] = self.input_buffers[ch, :-1]
            self.input_buffers[ch, 0] = input_sample[ch]
            
            # RLS更新
            filtered_sample[ch] = self._rls_update_single_channel(ch, input_sample[ch])
        
        return filtered_sample
    
    def _rls_update_single_channel(self, channel: int, desired_signal: float) -> float:
        """
        单通道RLS更新
        
        Args:
            channel: 通道索引
            desired_signal: 期望信号
            
        Returns:
            filtered_output: 滤波输出
        """
        try:
            # 获取当前通道的状态
            x = self.input_buffers[channel]  # [filter_length,]
            w = self.weights[channel]        # [filter_length,]
            P = self.covariance_matrices[channel]  # [filter_length, filter_length]
            
            # 预测输出
            y = np.dot(w, x)
            
            # 预测误差
            error = desired_signal - y
            
            # Kalman增益向量
            Px = np.dot(P, x)
            denominator = self.forgetting_factor + np.dot(x, Px)
            
            # 数值稳定性检查
            if denominator < self.regularization:
                denominator = self.regularization
            
            k = Px / denominator
            
            # 更新权重
            w_new = w + k * error

            # 检查权重是否包含无效值
            if np.any(np.isnan(w_new)) or np.any(np.isinf(w_new)):
                self.logger.warning(f"通道{channel}权重更新产生无效值，重置中...")
                self.weights[channel] = np.zeros(self.filter_length)
                self.covariance_matrices[channel] = np.eye(self.filter_length) * self.initial_covariance
            else:
                self.weights[channel] = w_new
            
            # 更新协方差矩阵（Joseph形式，保证数值稳定性）
            I_kx = np.eye(self.filter_length) - np.outer(k, x)

            # 检查中间结果的数值稳定性
            try:
                P_new = (I_kx @ P @ I_kx.T) / self.forgetting_factor + np.outer(k, k) * self.regularization

                # 检查结果是否包含无效值
                if np.any(np.isnan(P_new)) or np.any(np.isinf(P_new)):
                    self.logger.warning(f"通道{channel}协方差矩阵更新产生无效值，重置中...")
                    self.covariance_matrices[channel] = np.eye(self.filter_length) * self.initial_covariance
                else:
                    self.covariance_matrices[channel] = P_new

            except (np.linalg.LinAlgError, RuntimeWarning):
                self.logger.warning(f"通道{channel}协方差矩阵更新失败，重置中...")
                self.covariance_matrices[channel] = np.eye(self.filter_length) * self.initial_covariance
            
            # 记录收敛误差
            self.performance_stats['convergence_errors'].append(abs(error))

            # 检查输出值的稳定性
            if np.isnan(y) or np.isinf(y):
                self.logger.warning(f"通道{channel}输出值无效，返回0")
                return 0.0

            return y
            
        except Exception as e:
            self.logger.warning(f"通道{channel}的RLS更新失败: {e}")
            # 返回简单的加权平均作为备选
            return np.mean(self.input_buffers[channel])
    
    def _check_numerical_stability(self):
        """检查数值稳定性并在必要时重置"""
        for ch in range(self.n_channels):
            P = self.covariance_matrices[ch]
            
            # 检查条件数
            try:
                cond_num = np.linalg.cond(P)
                self.performance_stats['condition_numbers'].append(cond_num)
                
                # 如果条件数过大，重置协方差矩阵
                if cond_num > 1e12:
                    self.logger.warning(f"通道{ch}协方差矩阵条件数过大({cond_num:.2e})，重置中...")
                    self.covariance_matrices[ch] = np.eye(self.filter_length) * self.initial_covariance
                    
            except np.linalg.LinAlgError:
                self.logger.warning(f"通道{ch}协方差矩阵奇异，重置中...")
                self.covariance_matrices[ch] = np.eye(self.filter_length) * self.initial_covariance
    
    def reset(self):
        """重置滤波器状态"""
        self.logger.info("重置RLS滤波器状态")
        self._initialize_filters()
        
        # 清空性能统计
        self.performance_stats = {
            'total_updates': 0,
            'convergence_errors': [],
            'condition_numbers': [],
            'processing_times': []
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        
        if stats['processing_times']:
            stats['avg_processing_time'] = np.mean(stats['processing_times'])
            stats['max_processing_time'] = np.max(stats['processing_times'])
        
        if stats['convergence_errors']:
            stats['avg_convergence_error'] = np.mean(stats['convergence_errors'])
            stats['convergence_trend'] = np.mean(stats['convergence_errors'][-100:]) if len(stats['convergence_errors']) >= 100 else None
        
        if stats['condition_numbers']:
            stats['avg_condition_number'] = np.mean(stats['condition_numbers'])
            stats['max_condition_number'] = np.max(stats['condition_numbers'])
        
        return stats
    
    def get_filter_status(self) -> Dict[str, Any]:
        """获取滤波器状态信息"""
        return {
            'filter_length': self.filter_length,
            'forgetting_factor': self.forgetting_factor,
            'n_channels': self.n_channels,
            'total_updates': self.performance_stats['total_updates'],
            'is_converged': len(self.performance_stats['convergence_errors']) > 100 and 
                           np.mean(self.performance_stats['convergence_errors'][-50:]) < 0.1
        }
