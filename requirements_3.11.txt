# 脑机接口康复训练系统最终兼容版本
# 解决所有已知依赖冲突，适用于方案2（conda pip-only环境）
# 验证日期: 2025-07-23
# 推荐Python版本: 3.11

# GUI框架
PySide6==6.6.1
shiboken6==6.6.1

# 科学计算核心（解决numpy版本冲突）
numpy>=1.25.0,<2.0.0  # 避开pyriemann 0.7的1.24.0排除，兼容matplotlib
scipy>=1.9.0,<1.12.0
pandas>=1.5.0,<2.1.0
scikit-learn>=1.3.0,<1.4.0

# 信号处理（使用兼容版本）
mne>=1.0.0,<1.6.0
pyriemann>=0.7,<0.8  # 使用0.7版本，兼容numpy 1.25+

# 可视化
matplotlib>=3.5.0,<3.9.0
pyqtgraph>=0.13.0,<0.14.0

# 网络通信
requests>=2.28.0,<2.33.0
urllib3>=2.0.0,<2.6.0
certifi>=2023.0.0
charset-normalizer>=3.0.0,<4.0.0
idna>=3.0.0,<4.0.0

# 硬件通信
bleak>=0.20.0,<1.1.0
pyserial>=3.5,<4.0

# 语音合成
pyttsx3>=2.90,<3.0

# 加密安全
cryptography>=3.4.0,<46.0.0

# 数据格式
Pillow>=8.3.0,<12.0.0
python-dateutil>=2.8.0,<3.0.0
