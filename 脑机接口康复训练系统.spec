# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all
from PyInstaller.utils.hooks import copy_metadata

datas = [('D:\\NK_Python\\脑机接口康复训练\\config', 'config'), ('D:\\NK_Python\\脑机接口康复训练\\icons', 'icons'), ('D:\\NK_Python\\脑机接口康复训练\\libs', 'libs'), ('D:\\NK_Python\\脑机接口康复训练\\ShuJu.db', '.')]
binaries = []
hiddenimports = ['PySide6', 'shiboken6', 'PySide6.QtCore', 'PySide6.QtGui', 'PySide6.QtWidgets', 'PySide6.QtNetwork', 'PySide6.QtSql', 'numpy', 'numpy.core', 'numpy.core.multiarray', 'numpy.lib', 'numpy.linalg', 'numpy.fft', 'numpy.random', 'scipy', 'scipy.sparse', 'scipy.sparse.linalg', 'scipy.spatial', 'scipy.spatial.distance', 'scipy.signal', 'scipy.stats', 'scipy.linalg', 'pandas', 'pandas.core', 'matplotlib', 'matplotlib.pyplot', 'matplotlib.figure', 'matplotlib.backends', 'matplotlib.backends.backend_qt5agg', 'matplotlib.backends.backend_agg', 'pyqtgraph', 'pyqtgraph.graphicsItems', 'pyqtgraph.graphicsItems.PlotItem', 'pyqtgraph.graphicsItems.ViewBox', 'pyqtgraph.graphicsItems.AxisItem', 'pyqtgraph.graphicsItems.PlotCurveItem', 'pyqtgraph.graphicsItems.PlotDataItem', 'pyqtgraph.graphicsItems.GraphicsObject', 'pyqtgraph.graphicsItems.GraphicsWidget', 'pyqtgraph.widgets', 'pyqtgraph.widgets.PlotWidget', 'pyqtgraph.widgets.GraphicsLayoutWidget', 'pyqtgraph.Qt', 'pyqtgraph.Qt.QtCore', 'pyqtgraph.Qt.QtGui', 'pyqtgraph.Qt.QtWidgets', 'pyqtgraph.functions', 'pyqtgraph.Point', 'pyqtgraph.Vector', 'pyqtgraph.Transform3D', 'pyqtgraph.SRTTransform3D', 'pyqtgraph.debug', 'pyqtgraph.reload', 'pyqtgraph.colormap', 'pyqtgraph.parametertree', 'mne', 'mne.io', 'mne.utils', 'mne.utils._testing', 'mne.preprocessing', 'mne.viz', 'mne.channels', 'mne.filter', 'unittest', 'unittest.mock', 'collections', 'collections.abc', 'functools', 'itertools', 'warnings', 'inspect', 'pydoc', 'bleak', 'bleak.backends', 'bleak.backends.winrt', 'PIL', 'PIL.Image', 'cryptography', 'cryptography.fernet', 'cryptography.hazmat', 'cryptography.hazmat.primitives', 'cryptography.hazmat.primitives.hashes', 'cryptography.hazmat.backends', 'cryptography.hazmat.backends.openssl', 'hashlib', 'time', 'sqlite3', 'sklearn', 'sklearn.base', 'sklearn.linear_model', 'sklearn.svm', 'sklearn.ensemble', 'sklearn.model_selection', 'sklearn.metrics', 'sklearn.preprocessing', 'sklearn.pipeline', 'sklearn.decomposition', 'sklearn.discriminant_analysis', 'joblib', 'pickle', 'json', 'pathlib', 'setuptools', 'jaraco', 'jaraco.text', 'jaraco.functools', 'jaraco.collections', 'more_itertools', 'app.application', 'app.background_loader', 'app.config', 'core.database', 'core.network_config', 'core.simple_voice', 'core.udp_communicator', 'services.auth_service', 'services.api_client', 'services.patient_service', 'services.reference_data_service', 'services.treatment_service', 'services.user_service', 'services.training_session_manager', 'services.classifier_training_manager', 'services.plan_a_classifier', 'services.plan_a_manager', 'services.data_protection', 'services.data_protection.hardware_fingerprint_protector', 'services.stimulation', 'services.stimulation.stimulation_device', 'services.bluetooth', 'services.bluetooth.standard_bleak_manager', 'services.eeg_processing', 'services.eeg_processing.eeg_data_processor', 'services.eeg_preprocessing', 'services.eeg_preprocessing.kalman_processor', 'services.eeg_preprocessing.preprocessing_config', 'ui.login_window', 'ui.main_window', 'ui.components.modern_card', 'ui.components.parameter_adjuster', 'ui.components.no_wheel_widgets', 'ui.components.mne_topography_widget', 'ui.components.pyqtgraph_curves_widget', 'ui.themes.theme_manager', 'utils.db_helpers', 'utils.chart_helpers', 'utils.user_helpers']
datas += copy_metadata('PySide6')
datas += copy_metadata('shiboken6')
datas += copy_metadata('mne')
datas += copy_metadata('numpy')
datas += copy_metadata('scipy')
datas += copy_metadata('matplotlib')
datas += copy_metadata('pyqtgraph')
datas += copy_metadata('setuptools')
datas += copy_metadata('jaraco.text')
tmp_ret = collect_all('PySide6')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('shiboken6')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('mne')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['pyqtgraph.examples', 'pyqtgraph.jupyter', 'pyqtgraph.opengl', 'setuptools.extern', 'tkinter', 'doctest', 'pydoc_data', 'test', 'unittest.test', 'pip', 'wheel', 'PySide6.QtWebEngineWidgets', 'PySide6.QtWebEngineCore', 'PySide6.QtWebChannel', 'PySide6.QtQuick', 'PySide6.QtQml', 'PySide6.QtMultimedia', 'PySide6.QtOpenGL', 'PySide6.Qt3DAnimation', 'PySide6.Qt3DCore', 'PySide6.Qt3DExtras', 'PySide6.Qt3DInput', 'PySide6.Qt3DLogic', 'PySide6.Qt3DRender', 'PySide6.scripts', 'matplotlib.tests', 'numpy.tests', 'scipy.tests', 'sklearn.tests', 'pandas.tests', 'mne.datasets', 'mne.gui', 'mne.datasets.sample', 'mne.datasets.testing', 'matplotlib.mpl-data.sample_data'],
    noarchive=False,
    optimize=1,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [('O', None, 'OPTION')],
    exclude_binaries=True,
    name='脑机接口康复训练系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['D:\\NK_Python\\脑机接口康复训练\\icons\\ht.png'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='脑机接口康复训练系统',
)
