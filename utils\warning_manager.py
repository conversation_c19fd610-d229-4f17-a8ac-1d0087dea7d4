#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
警告管理模块
Warning Manager Module

专门处理numpy 2.0+与PyQtGraph兼容性问题的警告管理
解决"overflow encountered in cast"等RuntimeWarning问题

作者: AI Assistant
创建时间: 2025-01-30
"""

import warnings
import numpy as np
import logging
from typing import Optional, List, Dict, Any


class WarningManager:
    """警告管理器 - 统一处理各种警告配置"""
    
    def __init__(self, debug_mode: bool = False):
        """
        初始化警告管理器
        
        Args:
            debug_mode: 是否启用调试模式（显示更多警告信息）
        """
        self.debug_mode = debug_mode
        self.logger = logging.getLogger(__name__)
        self._original_settings = {}
        self._is_configured = False
        
    def configure_numpy_warnings(self) -> None:
        """配置numpy警告处理"""
        try:
            # 保存原始设置
            self._original_settings['numpy'] = np.geterr()
            
            if self.debug_mode:
                # 调试模式：只忽略溢出警告，保留其他警告
                np.seterr(
                    divide='warn',      # 保留除零警告
                    over='ignore',      # 忽略溢出警告（主要问题）
                    under='warn',       # 保留下溢警告
                    invalid='warn'      # 保留无效值警告
                )
                self.logger.info("✅ Numpy警告配置完成（调试模式）")
            else:
                # 生产模式：忽略所有数值计算警告
                np.seterr(
                    divide='ignore',    # 忽略除零警告
                    over='ignore',      # 忽略溢出警告
                    under='ignore',     # 忽略下溢警告
                    invalid='ignore'    # 忽略无效值警告
                )
                self.logger.info("✅ Numpy警告配置完成（生产模式）")
                
        except Exception as e:
            self.logger.error(f"❌ Numpy警告配置失败: {e}")
    
    def configure_python_warnings(self) -> None:
        """配置Python warnings模块"""
        try:
            if self.debug_mode:
                # 调试模式：只过滤特定的已知问题
                warning_filters = [
                    # PyQtGraph + numpy 2.0 兼容性问题
                    ("ignore", RuntimeWarning, ".*overflow encountered in cast.*"),
                    ("ignore", RuntimeWarning, ".*invalid value encountered.*"),
                    # PyQtGraph模块特定警告
                    ("ignore", RuntimeWarning, "pyqtgraph.*"),
                    # Qt相关警告
                    ("ignore", UserWarning, ".*iCCP.*"),
                    ("ignore", UserWarning, ".*libpng warning.*"),
                ]
            else:
                # 生产模式：过滤更多警告
                warning_filters = [
                    # 数值计算警告
                    ("ignore", RuntimeWarning, ".*overflow encountered.*"),
                    ("ignore", RuntimeWarning, ".*invalid value encountered.*"),
                    ("ignore", RuntimeWarning, ".*divide by zero encountered.*"),
                    ("ignore", RuntimeWarning, ".*invalid value encountered in.*"),
                    # PyQtGraph相关警告
                    ("ignore", RuntimeWarning, "pyqtgraph.*"),
                    ("ignore", UserWarning, "pyqtgraph.*"),
                    # Qt和图像处理警告
                    ("ignore", UserWarning, ".*iCCP.*"),
                    ("ignore", UserWarning, ".*libpng warning.*"),
                    ("ignore", DeprecationWarning, ".*"),
                    # matplotlib警告
                    ("ignore", UserWarning, ".*matplotlib.*"),
                ]
            
            # 应用警告过滤器
            for action, category, message in warning_filters:
                warnings.filterwarnings(action, category=category, message=message)
            
            self.logger.info(f"✅ Python警告过滤器配置完成（{len(warning_filters)}个规则）")
            
        except Exception as e:
            self.logger.error(f"❌ Python警告配置失败: {e}")
    
    def configure_pyqtgraph_warnings(self) -> None:
        """配置PyQtGraph特定警告"""
        try:
            # 尝试导入并配置PyQtGraph
            import pyqtgraph as pg
            
            # 在警告抑制环境中配置PyQtGraph
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=RuntimeWarning)
                warnings.filterwarnings("ignore", category=UserWarning)
                
                # PyQtGraph配置（移除有问题的颜色配置）
                config_options = {
                    'antialias': True,
                    'useOpenGL': False,
                    'enableExperimental': False,
                    'crashWarning': False  # 禁用崩溃警告
                    # 注意：不设置foreground和background，让PyQtGraph使用默认值
                }
                
                if not self.debug_mode:
                    # 生产模式下的额外配置（只使用已知有效的选项）
                    config_options.update({
                        'leftButtonPan': True
                    })
                
                pg.setConfigOptions(**config_options)
            
            self.logger.info("✅ PyQtGraph警告配置完成")
            
        except ImportError:
            self.logger.warning("⚠️ PyQtGraph未安装，跳过相关配置")
        except Exception as e:
            self.logger.error(f"❌ PyQtGraph警告配置失败: {e}")
    
    def configure_all(self) -> None:
        """配置所有警告处理"""
        if self._is_configured:
            self.logger.info("⚠️ 警告管理器已配置，跳过重复配置")
            return
        
        self.logger.info("🔧 开始配置警告管理器...")
        
        # 配置各个组件的警告
        self.configure_numpy_warnings()
        self.configure_python_warnings()
        self.configure_pyqtgraph_warnings()
        
        self._is_configured = True
        mode_text = "调试模式" if self.debug_mode else "生产模式"
        self.logger.info(f"✅ 警告管理器配置完成（{mode_text}）")
    
    def restore_original_settings(self) -> None:
        """恢复原始警告设置"""
        try:
            if 'numpy' in self._original_settings:
                np.seterr(**self._original_settings['numpy'])
                self.logger.info("✅ Numpy警告设置已恢复")
            
            # 重置warnings过滤器
            warnings.resetwarnings()
            self.logger.info("✅ Python警告设置已恢复")
            
            self._is_configured = False
            
        except Exception as e:
            self.logger.error(f"❌ 警告设置恢复失败: {e}")
    
    def get_current_settings(self) -> Dict[str, Any]:
        """获取当前警告设置"""
        return {
            'numpy_settings': np.geterr(),
            'debug_mode': self.debug_mode,
            'is_configured': self._is_configured,
            'original_settings': self._original_settings
        }


# 全局警告管理器实例
_global_warning_manager: Optional[WarningManager] = None


def get_warning_manager(debug_mode: bool = False) -> WarningManager:
    """获取全局警告管理器实例"""
    global _global_warning_manager
    
    if _global_warning_manager is None:
        _global_warning_manager = WarningManager(debug_mode=debug_mode)
    
    return _global_warning_manager


def configure_warnings(debug_mode: bool = False) -> None:
    """快速配置警告处理（便捷函数）"""
    manager = get_warning_manager(debug_mode=debug_mode)
    manager.configure_all()


def restore_warnings() -> None:
    """恢复原始警告设置（便捷函数）"""
    global _global_warning_manager
    
    if _global_warning_manager is not None:
        _global_warning_manager.restore_original_settings()


# 自动配置（当模块被导入时）
if __name__ != "__main__":
    # 检查是否在开发环境
    import os
    debug_mode = os.getenv('PYTHON_DEBUG', '').lower() in ('1', 'true', 'yes')
    configure_warnings(debug_mode=debug_mode)


if __name__ == "__main__":
    # 测试代码
    import logging
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 测试警告管理器...")
    
    # 测试调试模式
    manager = WarningManager(debug_mode=True)
    manager.configure_all()
    print("当前设置:", manager.get_current_settings())
    
    # 测试numpy操作
    import numpy as np
    x = np.array([1000000], dtype=np.float32)
    y = x.astype(np.float16)  # 这应该不会产生警告
    print(f"转换结果: {y}")
    
    # 恢复设置
    manager.restore_original_settings()
    print("✅ 测试完成")
