# -*- coding: utf-8 -*-
"""
患者管理服务
Patient Management Service

提供患者数据的完整CRUD操作，包括本地存储和网络同步
"""

from typing import List, Dict, Any, Optional, Tuple
import sqlite3
from datetime import datetime
from core.database import db_manager
from core.network_config import SYNC_STATUS
from services.api_client import api_client
from PySide6.QtCore import QThread, Signal, QObject
from utils.db_helpers import (
    validate_patient_data, 
    format_patient_for_ui, 
    format_patient_for_db,
    generate_patient_id,
    build_where_clause,
    paginate_query
)

class PatientUploadThread(QThread):
    """患者数据异步上传线程"""
    upload_finished = Signal(bool, str, str)  # 成功状态, 消息, 患者ID

    def __init__(self, patient_data, is_update=False):
        super().__init__()
        self.patient_data = patient_data
        self.is_update = is_update
        self._should_stop = False

    def stop(self):
        """停止线程"""
        self._should_stop = True

    def run(self):
        try:
            if self._should_stop:
                return

            # 执行网络上传
            success = api_client.upload_patient(self.patient_data, self.is_update)
            patient_id = self.patient_data.get('bianhao', '')

            if self._should_stop:
                return

            if success:
                message = f"患者数据{'更新' if self.is_update else '上传'}成功"
                self.upload_finished.emit(True, message, patient_id)
            else:
                message = f"患者数据{'更新' if self.is_update else '上传'}失败"
                self.upload_finished.emit(False, message, patient_id)

        except Exception as e:
            if not self._should_stop:
                message = f"患者数据{'更新' if self.is_update else '上传'}异常: {str(e)}"
                patient_id = self.patient_data.get('bianhao', '')
                self.upload_finished.emit(False, message, patient_id)


class PatientService:
    """患者管理服务类"""

    def __init__(self):
        self.table_name = "bingren"
        self.active_upload_threads = []  # 跟踪活跃的上传线程
    
    def get_patients(self, page: int = 1, limit: int = 10, search_term: str = "", show_all: bool = False) -> Dict[str, Any]:
        """
        获取患者列表（分页）- 默认只显示在院患者

        Args:
            page: 页码（从1开始）
            limit: 每页记录数
            search_term: 搜索关键词
            show_all: 是否显示所有患者（包括出院），默认False只显示在院患者

        Returns:
            包含患者列表和分页信息的字典
        """
        try:
            # 构建基础查询 - 只显示在院患者
            if show_all:
                # 显示所有患者
                base_sql = f"SELECT * FROM {self.table_name}"
                count_sql = f"SELECT COUNT(*) as total FROM {self.table_name}"
            else:
                # 只显示在院患者（通过住院记录表关联）
                base_sql = f"""
                    SELECT DISTINCT p.* FROM {self.table_name} p
                    JOIN hospitalization_records h ON p.bianhao = h.patient_id
                    WHERE h.discharge_date IS NULL
                """
                count_sql = f"""
                    SELECT COUNT(DISTINCT p.bianhao) as total FROM {self.table_name} p
                    JOIN hospitalization_records h ON p.bianhao = h.patient_id
                    WHERE h.discharge_date IS NULL
                """

            # 处理搜索条件
            where_clause = ""
            params = ()

            if search_term:
                if show_all:
                    where_clause = "name LIKE ? OR bianhao LIKE ?"
                    search_param = f"%{search_term}%"
                    params = (search_param, search_param)
                    base_sql += f" WHERE {where_clause}"
                    count_sql += f" WHERE {where_clause}"
                else:
                    where_clause = "AND (p.name LIKE ? OR p.bianhao LIKE ?)"
                    search_param = f"%{search_term}%"
                    params = (search_param, search_param)
                    base_sql += f" {where_clause}"
                    count_sql += f" {where_clause}"
            
            # 添加排序
            base_sql += " ORDER BY lrshijian DESC"
            
            # 获取总记录数
            total_result = db_manager.execute_one(count_sql, params)
            total_count = total_result['total'] if total_result else 0
            
            # 分页查询
            paginated_sql, paginated_params = paginate_query(base_sql, page, limit, params)
            rows = db_manager.execute_query(paginated_sql, paginated_params)
            
            # 格式化数据
            patients = [format_patient_for_ui(row) for row in rows]
            
            # 计算分页信息
            total_pages = (total_count + limit - 1) // limit
            
            return {
                'patients': patients,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'total_count': total_count,
                    'page_size': limit,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
            
        except Exception as e:
            print(f"获取患者列表失败: {e}")
            return {
                'patients': [],
                'pagination': {
                    'current_page': 1,
                    'total_pages': 0,
                    'total_count': 0,
                    'page_size': limit,
                    'has_next': False,
                    'has_prev': False
                }
            }
    
    def get_patient_by_id(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取患者信息"""
        try:
            sql = f"SELECT * FROM {self.table_name} WHERE bianhao = ?"
            row = db_manager.execute_one(sql, (patient_id,))
            return format_patient_for_ui(row) if row else None
        except Exception as e:
            print(f"获取患者信息失败: {e}")
            return None
    
    def create_patient(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新患者
        
        Returns:
            包含操作结果的字典
        """
        try:
            # 数据验证
            errors = validate_patient_data(patient_data)
            if errors:
                return {'success': False, 'errors': errors}
            
            # 患者编号必须由用户输入，不自动生成
            if not patient_data.get('bianhao'):
                return {'success': False, 'errors': {'bianhao': '患者编号不能为空'}}
            
            # 检查编号是否已存在
            existing = self.get_patient_by_id(patient_data['bianhao'])
            if existing:
                return {'success': False, 'errors': {'bianhao': '患者编号已存在'}}
            
            # 格式化数据
            db_data = format_patient_for_db(patient_data)
            db_data['status'] = SYNC_STATUS['NOT_UPLOADED']  # 初始状态为未上传


            
            # 构建插入SQL
            fields = list(db_data.keys())
            placeholders = ', '.join(['?' for _ in fields])
            sql = f"INSERT INTO {self.table_name} ({', '.join(fields)}) VALUES ({placeholders})"
            
            # 执行插入
            patient_id = db_manager.execute_insert(sql, tuple(db_data.values()))
            
            if patient_id:
                # 创建初始住院记录
                admission_date = datetime.now().strftime('%Y-%m-%d')
                admission_result = self.admit_patient(
                    db_data['bianhao'],
                    admission_date,
                    "新患者入院",
                    db_data.get('keshi', ''),
                    db_data.get('zhuzhi', '')
                )

                if not admission_result['success']:
                    # 如果住院记录创建失败，删除患者记录
                    db_manager.execute_update(f"DELETE FROM {self.table_name} WHERE bianhao = ?", (db_data['bianhao'],))
                    return {'success': False, 'errors': {'general': '创建住院记录失败'}}

                # 异步上传到服务器（不阻塞界面）
                self._upload_patient_async(db_data, is_update=False)

                return {
                    'success': True,
                    'patient_id': db_data['bianhao'],
                    'uploaded': 'pending',  # 标记为待上传
                    'hospitalization_id': admission_result.get('hospitalization_id')
                }
            else:
                return {'success': False, 'errors': {'general': '保存失败'}}
                
        except Exception as e:
            print(f"创建患者失败: {e}")
            return {'success': False, 'errors': {'general': f'创建失败: {str(e)}'}}
    
    def update_patient(self, patient_id: str, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新患者信息"""
        try:
            # 检查患者是否存在
            existing = self.get_patient_by_id(patient_id)
            if not existing:
                return {'success': False, 'errors': {'general': '患者不存在'}}
            
            # 数据验证
            errors = validate_patient_data(patient_data)
            if errors:
                return {'success': False, 'errors': errors}
            
            # 格式化数据
            db_data = format_patient_for_db(patient_data)
            db_data['status'] = SYNC_STATUS['NOT_UPLOADED']  # 更新后重置为未上传
            
            # 构建更新SQL
            fields = [f"{field} = ?" for field in db_data.keys()]
            sql = f"UPDATE {self.table_name} SET {', '.join(fields)} WHERE bianhao = ?"
            params = tuple(db_data.values()) + (patient_id,)
            
            # 执行更新
            affected_rows = db_manager.execute_update(sql, params)
            
            if affected_rows > 0:
                # 异步上传到服务器（不阻塞界面）
                db_data['bianhao'] = patient_id  # 确保包含患者ID
                self._upload_patient_async(db_data, is_update=True)

                return {
                    'success': True,
                    'uploaded': 'pending'  # 标记为待上传
                }
            else:
                return {'success': False, 'errors': {'general': '更新失败'}}
                
        except Exception as e:
            print(f"更新患者失败: {e}")
            return {'success': False, 'errors': {'general': f'更新失败: {str(e)}'}}
    
    def discharge_patient(self, patient_id: str, discharge_date: str, discharge_reason: str = "") -> Dict[str, Any]:
        """患者出院（替代删除功能）"""
        try:
            # 检查患者是否存在
            existing = self.get_patient_by_id(patient_id)
            if not existing:
                return {'success': False, 'errors': {'general': '患者不存在'}}

            # 检查患者是否在院
            current_hospitalization = self.get_current_hospitalization(patient_id)
            if not current_hospitalization:
                return {'success': False, 'errors': {'general': '患者当前不在院'}}

            # 更新住院记录，设置出院信息
            updated_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # 使用本机时间
            sql = """
                UPDATE hospitalization_records
                SET discharge_date = ?, discharge_reason = ?, status = 0, updated_at = ?
                WHERE patient_id = ? AND discharge_date IS NULL
            """
            affected_rows = db_manager.execute_update(sql, (discharge_date, discharge_reason, updated_at, patient_id))

            if affected_rows > 0:
                return {'success': True}
            else:
                return {'success': False, 'errors': {'general': '出院操作失败'}}

        except Exception as e:
            print(f"患者出院失败: {e}")
            return {'success': False, 'errors': {'general': f'出院失败: {str(e)}'}}

    def admit_patient(self, patient_id: str, admission_date: str, admission_reason: str = "",
                     department: str = "", doctor: str = "") -> Dict[str, Any]:
        """患者入院（支持多次入院）"""
        try:
            # 检查患者是否存在
            existing = self.get_patient_by_id(patient_id)
            if not existing:
                return {'success': False, 'errors': {'general': '患者不存在'}}

            # 检查患者是否已在院
            current_hospitalization = self.get_current_hospitalization(patient_id)
            if current_hospitalization:
                return {'success': False, 'errors': {'general': '患者已在院，无需重复入院'}}

            # 创建新的住院记录
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # 使用本机时间
            sql = """
                INSERT INTO hospitalization_records
                (patient_id, admission_date, admission_reason, department, doctor, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, 1, ?, ?)
            """
            hospitalization_id = db_manager.execute_insert(sql, (patient_id, admission_date, admission_reason, department, doctor, current_time, current_time))

            if hospitalization_id:
                return {'success': True, 'hospitalization_id': hospitalization_id}
            else:
                return {'success': False, 'errors': {'general': '入院操作失败'}}

        except Exception as e:
            print(f"患者入院失败: {e}")
            return {'success': False, 'errors': {'general': f'入院失败: {str(e)}'}}

    def get_current_hospitalization(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """获取患者当前住院记录"""
        try:
            sql = """
                SELECT * FROM hospitalization_records
                WHERE patient_id = ? AND discharge_date IS NULL
                ORDER BY admission_date DESC
                LIMIT 1
            """
            row = db_manager.execute_one(sql, (patient_id,))
            return dict(row) if row else None
        except Exception as e:
            print(f"获取当前住院记录失败: {e}")
            return None

    def get_hospitalization_history(self, patient_id: str) -> List[Dict[str, Any]]:
        """获取患者住院历史"""
        try:
            sql = """
                SELECT * FROM hospitalization_records
                WHERE patient_id = ?
                ORDER BY admission_date DESC
            """
            rows = db_manager.execute_query(sql, (patient_id,))
            return [dict(row) for row in rows]
        except Exception as e:
            print(f"获取住院历史失败: {e}")
            return []

    def get_hospitalization_count_by_patient(self, patient_id: str) -> int:
        """获取患者总入院次数"""
        try:
            sql = """
                SELECT COUNT(*) as count FROM hospitalization_records
                WHERE patient_id = ?
            """
            result = db_manager.execute_one(sql, (patient_id,))
            return result['count'] if result else 0
        except Exception as e:
            print(f"获取患者入院次数失败: {e}")
            return 0

    def get_current_hospitalization_number(self, patient_id: str) -> int:
        """获取患者当前入院是第几次入院"""
        try:
            # 获取当前住院记录
            current_hospitalization = self.get_current_hospitalization(patient_id)
            if not current_hospitalization:
                return 0

            current_admission_date = current_hospitalization['admission_date']

            # 计算在当前入院日期之前（包括当前）的入院次数
            # 使用字符串比较，因为日期格式统一为YYYY-MM-DD
            sql = """
                SELECT COUNT(*) as count FROM hospitalization_records
                WHERE patient_id = ? AND admission_date <= ?
                ORDER BY admission_date ASC
            """
            result = db_manager.execute_one(sql, (patient_id, current_admission_date))
            return result['count'] if result else 0
        except Exception as e:
            print(f"获取当前入院序号失败: {e}")
            return 0

    def get_current_patients_count(self) -> int:
        """获取当前在院患者总数"""
        try:
            # 使用与患者列表相同的查询逻辑，统计在院患者数量
            sql = """
                SELECT COUNT(DISTINCT p.bianhao) as total FROM bingren p
                JOIN hospitalization_records h ON p.bianhao = h.patient_id
                WHERE h.discharge_date IS NULL
            """
            result = db_manager.execute_one(sql)
            return result['total'] if result else 0
        except Exception as e:
            print(f"获取在院患者总数失败: {e}")
            return 0

    def check_patient_existence_and_status(self, patient_id: str) -> Dict[str, Any]:
        """检查患者是否存在及其住院状态"""
        try:
            # 检查患者是否存在
            patient = self.get_patient_by_id(patient_id)
            if not patient:
                return {
                    'exists': False,
                    'status': 'not_found',
                    'patient_data': None,
                    'current_hospitalization': None
                }

            # 检查当前住院状态
            current_hospitalization = self.get_current_hospitalization(patient_id)

            if current_hospitalization:
                # 患者当前在院
                return {
                    'exists': True,
                    'status': 'in_hospital',
                    'patient_data': patient,
                    'current_hospitalization': current_hospitalization
                }
            else:
                # 患者已出院
                return {
                    'exists': True,
                    'status': 'discharged',
                    'patient_data': patient,
                    'current_hospitalization': None
                }

        except Exception as e:
            print(f"检查患者状态失败: {e}")
            return {
                'exists': False,
                'status': 'error',
                'patient_data': None,
                'current_hospitalization': None,
                'error': str(e)
            }

    def readmit_patient(self, patient_id: str, updated_info: Dict[str, Any], admission_info: Dict[str, Any]) -> Dict[str, Any]:
        """患者再次入院"""
        try:
            # 检查患者状态
            status_check = self.check_patient_existence_and_status(patient_id)

            if not status_check['exists']:
                return {'success': False, 'errors': {'general': '患者不存在'}}

            if status_check['status'] == 'in_hospital':
                return {'success': False, 'errors': {'general': '患者已在院，无法重复入院'}}

            # 更新患者信息（只更新有值的字段）
            if updated_info:
                # 过滤掉空值，只更新有内容的字段
                filtered_info = {k: v for k, v in updated_info.items() if (v.strip() if isinstance(v, str) else v)}
                if filtered_info:
                    update_result = self._update_patient_fields(patient_id, filtered_info)
                    if not update_result['success']:
                        return update_result

            # 创建新的住院记录
            admission_result = self.admit_patient(
                patient_id,
                admission_info.get('admission_date'),
                admission_info.get('admission_reason', ''),
                admission_info.get('department', ''),
                admission_info.get('doctor', '')
            )

            if admission_result['success']:
                return {
                    'success': True,
                    'patient_id': patient_id,
                    'hospitalization_id': admission_result.get('hospitalization_id')
                }
            else:
                return admission_result

        except Exception as e:
            return {'success': False, 'errors': {'general': f'再次入院失败: {str(e)}'}}

    def _update_patient_fields(self, patient_id: str, fields: Dict[str, Any]) -> Dict[str, Any]:
        """更新患者的指定字段"""
        try:
            # 构建更新SQL
            set_clauses = []
            params = []

            for field, value in fields.items():
                set_clauses.append(f"{field} = ?")
                params.append(value)

            if not set_clauses:
                return {'success': True}  # 没有字段需要更新

            params.append(patient_id)  # 添加WHERE条件的参数

            sql = f"UPDATE {self.table_name} SET {', '.join(set_clauses)} WHERE bianhao = ?"
            affected_rows = db_manager.execute_update(sql, tuple(params))

            if affected_rows > 0:
                return {'success': True}
            else:
                return {'success': False, 'errors': {'general': '更新失败，未找到患者'}}

        except Exception as e:
            return {'success': False, 'errors': {'general': f'更新失败: {str(e)}'}}
    
    def _update_sync_status(self, patient_id: str, status: int):
        """更新同步状态"""
        try:
            sql = f"UPDATE {self.table_name} SET status = ? WHERE bianhao = ?"
            db_manager.execute_update(sql, (status, patient_id))
        except Exception as e:
            print(f"更新同步状态失败: {e}")
    
    def get_unsynced_patients(self) -> List[Dict[str, Any]]:
        """获取未同步的患者列表"""
        try:
            sql = f"SELECT * FROM {self.table_name} WHERE status = ?"
            rows = db_manager.execute_query(sql, (SYNC_STATUS['NOT_UPLOADED'],))
            return [format_patient_for_ui(row) for row in rows]
        except Exception as e:
            print(f"获取未同步患者失败: {e}")
            return []

    def _upload_patient_async(self, patient_data: Dict, is_update: bool = False):
        """异步上传患者数据"""
        try:
            # 创建上传线程
            upload_thread = PatientUploadThread(patient_data, is_update)
            upload_thread.upload_finished.connect(self._on_upload_finished)
            upload_thread.finished.connect(lambda: self._cleanup_thread(upload_thread))

            # 添加到活跃线程列表
            self.active_upload_threads.append(upload_thread)

            upload_thread.start()

            print(f"📤 患者数据正在后台{'更新' if is_update else '上传'}...")

        except Exception as e:
            print(f"启动异步上传失败: {e}")

    def _on_upload_finished(self, success: bool, message: str, patient_id: str):
        """上传完成处理"""
        try:
            if success:
                # 更新同步状态为已上传
                self._update_sync_status(patient_id, SYNC_STATUS['UPLOADED'])
                print(f"✅ {message}")
            else:
                # 保持未上传状态，等待下次同步
                print(f"❌ {message}")

        except Exception as e:
            print(f"处理上传结果失败: {e}")

    def _cleanup_thread(self, thread):
        """清理完成的线程"""
        try:
            if thread in self.active_upload_threads:
                self.active_upload_threads.remove(thread)
            thread.deleteLater()
        except Exception as e:
            print(f"清理线程失败: {e}")

    def cleanup(self):
        """清理所有资源"""
        try:
            # 停止所有活跃的上传线程
            for thread in self.active_upload_threads[:]:  # 使用副本避免修改列表时的问题
                if thread.isRunning():
                    thread.stop()
                    thread.wait(1000)  # 等待最多1秒
                    if thread.isRunning():
                        thread.terminate()  # 强制终止
                self._cleanup_thread(thread)

            self.active_upload_threads.clear()
            print("患者服务清理完成")

        except Exception as e:
            print(f"患者服务清理失败: {e}")

# 全局患者服务实例
patient_service = PatientService()
