# -*- coding: utf-8 -*-
"""
顶部栏组件
TopBar Component

完全按照HTML设计实现的顶部栏
包含菜单切换、页面标题、系统状态、主题切换、快捷操作等
"""

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
    QPushButton, QFrame, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush


class MenuToggleButton(QPushButton):
    """菜单切换按钮 - 按照HTML设计"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("menu_toggle")
        self.setFixedSize(43, 43)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 清除文字，使用自定义绘制
        self.setText("")
        # print("⚠️ 使用QPainter绘制三个点图标")
    
    def paintEvent(self, event):
        """自定义绘制事件 - 绘制三个点"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 获取按钮状态和颜色
        if self.isDown():
            # 按下状态 - 白色点
            color = QColor(255, 255, 255)
        elif self.underMouse():
            # 悬停状态 - 主题色
            color = self._get_primary_color()
        else:
            # 正常状态 - 根据主题选择合适的颜色
            color = self._get_normal_dot_color()
        
        painter.setBrush(QBrush(color))
        painter.setPen(Qt.PenStyle.NoPen)
        
        # 绘制三个圆点（竖直排列）
        dot_radius = 2
        center_x = self.width() // 2
        positions = [12, 21, 30]  # 三个点的y坐标
        
        for y in positions:
            painter.drawEllipse(center_x - dot_radius, y - dot_radius, 
                              dot_radius * 2, dot_radius * 2)
    
    def _get_text_color(self) -> QColor:
        """获取文字颜色"""
        try:
            # 尝试从主题获取颜色
            from ui.themes.theme_manager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = getattr(self.parent(), 'current_theme', 'tech')
            theme_config = theme_manager.themes.get(current_theme, {})
            color_str = theme_config.get('text_primary', '#333333')
            return QColor(color_str)
        except:
            # 默认颜色
            return QColor(51, 51, 51)  # #333333
    
    def _get_primary_color(self) -> QColor:
        """获取主题色"""
        try:
            # 尝试从主题获取颜色
            from ui.themes.theme_manager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = getattr(self.parent(), 'current_theme', 'tech')
            theme_config = theme_manager.themes.get(current_theme, {})
            color_str = theme_config.get('primary_color', '#007bff')
            return QColor(color_str)
        except:
            # 默认颜色
            return QColor(0, 123, 255)  # #007bff
    
    def _get_normal_dot_color(self) -> QColor:
        """获取正常状态下的点颜色 - 根据主题智能选择"""
        try:
            # 获取当前主题
            current_theme = self._get_current_theme()
            
            if current_theme == 'tech':
                # 科技主题：深色背景，使用白色点
                return QColor(255, 255, 255)
            elif current_theme == 'medical':
                # 医疗主题：浅色背景，使用深色点
                return QColor(51, 51, 51)  # 深灰色
            else:
                # 默认使用白色
                return QColor(255, 255, 255)
        except:
            # 出错时默认使用白色
            return QColor(255, 255, 255)
    
    def _get_current_theme(self) -> str:
        """获取当前主题名称"""
        try:
            # 尝试从父组件获取主题
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_theme'):
                    return parent.current_theme
                parent = parent.parent()
            
            # 如果找不到，尝试从主题管理器获取
            from ui.themes.theme_manager import ThemeManager
            theme_manager = ThemeManager()
            return getattr(theme_manager, 'current_theme', 'tech')
        except:
            return 'tech'  # 默认科技主题


class StatusItem(QFrame):
    """状态项组件 - 按照HTML设计"""
    
    def __init__(self, text: str, status_type: str = "online"):
        super().__init__()
        self.setObjectName("status_item")
        self.status_type = status_type
        
        self._init_ui(text)
        self._setup_style()
    
    def _init_ui(self, text: str):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 8, 16, 8)
        layout.setSpacing(8)
        
        # 状态点
        self.status_dot = QLabel()
        self.status_dot.setObjectName("status_dot")
        self.status_dot.setFixedSize(8, 8)
        layout.addWidget(self.status_dot)
        
        # 状态文字
        self.status_text = QLabel(text)
        # 调整状态文字字体大小以匹配HTML视觉效果
        status_font = QFont("Microsoft YaHei", 11, QFont.Weight.DemiBold)
        status_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.status_text.setFont(status_font)
        layout.addWidget(self.status_text)
        
        # 设置状态样式
        self.setProperty("status_type", self.status_type)
    
    def _setup_style(self):
        """设置样式"""
        # 添加脉冲动画效果
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self._pulse_animation)
        self.pulse_timer.start(2000)  # 2秒脉冲一次
    
    def _pulse_animation(self):
        """脉冲动画"""
        # 这里可以添加状态点的脉冲效果
        pass


class SlidingSwitch(QFrame):
    """滑动开关组件"""
    
    # 状态切换信号
    toggled = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("sliding_switch")
        self.setFixedSize(60, 30)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 滑块
        self.slider = QLabel(self)
        self.slider.setObjectName("switch_slider")
        self.slider.setFixedSize(28, 28)
        self.slider.move(30, 2)  # 初始位置（科技主题）
        
        # 滑动动画
        self.slide_animation = QPropertyAnimation(self.slider, b"pos")
        self.slide_animation.setDuration(300)  # 300ms动画
        self.slide_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 当前状态
        self.is_tech = True  # True=科技主题, False=医疗主题
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.toggle()
        super().mousePressEvent(event)
    
    def toggle(self):
        """切换开关状态"""
        self.is_tech = not self.is_tech
        self._animate_to_position()
        self.toggled.emit()  # 发送切换信号
    
    def set_state(self, is_tech: bool):
        """设置开关状态（不发送信号）"""
        if self.is_tech != is_tech:
            self.is_tech = is_tech
            self._animate_to_position()
    
    def _animate_to_position(self):
        """动画到目标位置"""
        if self.is_tech:
            # 科技主题：滑块移动到右侧
            target_pos = QRect(30, 2, 26, 26).topLeft()
        else:
            # 医疗主题：滑块移动到左侧
            target_pos = QRect(2, 2, 26, 26).topLeft()
        
        self.slide_animation.setStartValue(self.slider.pos())
        self.slide_animation.setEndValue(target_pos)
        self.slide_animation.start()


class ThemeSwitch(QFrame):
    """主题切换器 - 带滑动效果的设计"""
    
    theme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.setObjectName("theme_switch")
        self.current_theme = "tech"  # 默认科技主题
        
        self._init_ui()
        self._setup_style()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 8, 16, 8)
        layout.setSpacing(12)
        
        # 医疗主题标签
        self.medical_label = QLabel("浅色")
        self.medical_label.setObjectName("theme_label")
        self.medical_label.setCursor(Qt.CursorShape.PointingHandCursor)
        # 调整主题标签字体大小以匹配HTML视觉效果
        theme_font = QFont("Microsoft YaHei", 11, QFont.Weight.DemiBold)
        theme_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.medical_label.setFont(theme_font)
        self.medical_label.mousePressEvent = lambda event: self._set_theme("medical")
        layout.addWidget(self.medical_label)

        # 滑动开关
        self.sliding_switch = SlidingSwitch()
        self.sliding_switch.toggled.connect(self._toggle_theme)
        layout.addWidget(self.sliding_switch)

        # 科技主题标签
        self.tech_label = QLabel("深色")
        self.tech_label.setObjectName("theme_label")
        self.tech_label.setCursor(Qt.CursorShape.PointingHandCursor)
        self.tech_label.setFont(theme_font)
        self.tech_label.mousePressEvent = lambda event: self._set_theme("tech")
        layout.addWidget(self.tech_label)
        
        # 设置初始状态
        self._update_switch_state()
    
    def _setup_style(self):
        """设置样式"""
        # 样式会在主题系统中定义
        pass
    
    def _toggle_theme(self):
        """切换主题"""
        self.current_theme = "medical" if self.current_theme == "tech" else "tech"
        self._update_switch_state()
        self.theme_changed.emit(self.current_theme)
    
    def _set_theme(self, theme: str):
        """设置指定主题"""
        if theme != self.current_theme:
            self.current_theme = theme
            self._update_switch_state()
            self.theme_changed.emit(self.current_theme)
    
    def _update_switch_state(self):
        """更新开关状态"""
        is_tech = self.current_theme == "tech"
        
        # 更新滑动开关状态
        self.sliding_switch.set_state(is_tech)
        
        # 更新标签样式
        self.medical_label.setProperty("active", not is_tech)
        self.tech_label.setProperty("active", is_tech)
        
        # 刷新样式
        self.medical_label.style().unpolish(self.medical_label)
        self.medical_label.style().polish(self.medical_label)
        self.tech_label.style().unpolish(self.tech_label)
        self.tech_label.style().polish(self.tech_label)
    
    def set_theme(self, theme: str):
        """设置主题"""
        if theme != self.current_theme:
            self.current_theme = theme
            self._update_switch_state()


class TopBar(QFrame):
    """顶部栏组件 - 完全按照HTML设计实现"""
    
    # 信号
    menu_toggle_clicked = Signal()
    theme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        
        # 页面信息
        self.page_titles = {
            "patients": ("患者管理", "Patient Management"),
            "treatment": ("治疗系统", "Treatment System"),
            "reports": ("报告分析", "Report Analysis"),
            "users": ("用户管理", "User Management"),
            "settings": ("系统设置", "System Settings")
        }
        
        self._init_ui()
        self._setup_style()
    
    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("top_bar")
        self.setFixedHeight(60)  # 按照HTML中的高度
        
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(32, 0, 32, 0)  # 按照HTML中的padding
        layout.setSpacing(6)
        
        # 左侧区域
        self.left_area = self._create_left_area()
        layout.addWidget(self.left_area)
        
        # 中央区域
        self.center_area = self._create_center_area()
        layout.addWidget(self.center_area, 1)
        
        # 右侧区域
        self.right_area = self._create_right_area()
        layout.addWidget(self.right_area)
    
    def _create_left_area(self) -> QWidget:
        """创建左侧区域"""
        left_area = QWidget()
        layout = QHBoxLayout(left_area)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(24)
        
        # 菜单切换按钮
        self.menu_toggle = MenuToggleButton()
        self.menu_toggle.clicked.connect(self.menu_toggle_clicked.emit)
        layout.addWidget(self.menu_toggle)
        
        # 页面标题区域
        title_area = QWidget()
        title_layout = QVBoxLayout(title_area)
        # 增加上边距，使标题远离顶部，消除压迫感
        title_layout.setContentsMargins(0, 0, 0, 8)
        title_layout.setSpacing(4)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)  # 垂直居中对齐
        
        self.page_title = QLabel("患者管理")
        self.page_title.setObjectName("page_title")
        # 调整页面标题字体大小以匹配HTML视觉效果
        title_font = QFont("Microsoft YaHei", 17, QFont.Weight.Bold)
        title_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.page_title.setFont(title_font)
        title_layout.addWidget(self.page_title)

        self.page_subtitle = QLabel("Patient Management")
        self.page_subtitle.setObjectName("page_subtitle")
        # 调整页面副标题字体大小以匹配HTML视觉效果
        subtitle_font = QFont("Microsoft YaHei", 10)
        subtitle_font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.page_subtitle.setFont(subtitle_font)
        title_layout.addWidget(self.page_subtitle)
        
        layout.addWidget(title_area)
        
        return left_area
    
    def _create_center_area(self) -> QWidget:
        """创建中央区域 - 系统状态"""
        center_area = QWidget()
        layout = QHBoxLayout(center_area)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(24)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 系统状态项
        self.device_status = StatusItem("设备断开", "offline")
        layout.addWidget(self.device_status)
        
        self.signal_status = StatusItem("信号断开", "offline")
        layout.addWidget(self.signal_status)
        
        return center_area
    
    def _create_right_area(self) -> QWidget:
        """创建右侧区域"""
        right_area = QWidget()
        layout = QHBoxLayout(right_area)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)
        
        # 主题切换器
        self.theme_switch = ThemeSwitch()
        self.theme_switch.theme_changed.connect(self.theme_changed.emit)
        layout.addWidget(self.theme_switch)
        
        return right_area
    
    def _setup_style(self):
        """设置样式"""
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(2)
        shadow.setColor(QColor(0, 0, 0, 20))
        self.setGraphicsEffect(shadow)
    
    def update_page_info(self, page_id: str):
        """更新页面信息"""
        if page_id in self.page_titles:
            title, subtitle = self.page_titles[page_id]
            self.page_title.setText(title)
            self.page_subtitle.setText(subtitle)
    
    def set_theme(self, theme: str):
        """设置主题"""
        self.theme_switch.set_theme(theme)
