# -*- coding: utf-8 -*-
"""
用户认证服务
User Authentication Service

提供用户登录验证和会话管理功能
"""

import hashlib
import json
import secrets
from pathlib import Path
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta
from core.database import db_manager


class AuthService:
    """用户认证服务"""
    
    def __init__(self):
        self.current_user = None
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            if ":" in password_hash:
                # 新的加盐哈希格式
                hash_part, salt = password_hash.split(":")
                return hashlib.sha256((password + salt).encode()).hexdigest() == hash_part
            else:
                # 兼容旧的简单哈希格式
                return hashlib.sha256(password.encode()).hexdigest() == password_hash
        except Exception as e:
            print(f"密码验证失败: {e}")
            return False
    
    def _get_user_from_db(self, username: str) -> Optional[Dict]:
        """从数据库获取用户信息"""
        try:
            sql = """
            SELECT id, username, password_hash, email, name, role, status,
                   created_at, last_login_at, login_count, department, phone
            FROM operator
            WHERE username = ? AND status = 'active'
            """
            result = db_manager.execute_one(sql, (username,))

            if result:
                return dict(result)
            return None

        except Exception as e:
            print(f"从数据库获取用户失败: {e}")
            return None

    def _update_user_login(self, username: str):
        """更新用户登录信息"""
        try:
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            sql = """
            UPDATE operator
            SET last_login_at = ?,
                login_count = login_count + 1,
                updated_at = ?
            WHERE username = ?
            """
            db_manager.execute_update(sql, (now, now, username))

        except Exception as e:
            print(f"更新用户登录信息失败: {e}")
    
    def authenticate(self, username: str, password: str) -> Tuple[bool, str]:
        """
        用户认证

        Args:
            username: 用户名
            password: 密码

        Returns:
            (是否成功, 错误信息或成功信息)
        """
        try:
            # 从数据库获取用户信息
            user = self._get_user_from_db(username)

            if not user:
                return False, "用户不存在或已被禁用"

            # 验证密码
            if not self.verify_password(password, user["password_hash"]):
                return False, "密码错误"

            # 更新登录信息
            self._update_user_login(username)

            # 设置当前用户
            self.current_user = {
                "id": user["id"],
                "username": username,
                "role": user["role"],
                "name": user["name"],
                "email": user["email"],
                "department": user.get("department"),
                "phone": user.get("phone")
            }

            return True, f"欢迎，{user['name'] or username}"

        except Exception as e:
            print(f"认证过程出错: {e}")
            return False, f"认证过程出错: {str(e)}"
    
    def logout(self):
        """用户登出"""
        self.current_user = None
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.current_user is not None
    
    def get_current_user(self) -> Optional[Dict]:
        """获取当前用户信息"""
        return self.current_user
    

    
    def change_password(self, username: str, old_password: str, new_password: str) -> Tuple[bool, str]:
        """
        修改密码

        Args:
            username: 用户名
            old_password: 旧密码
            new_password: 新密码

        Returns:
            (是否成功, 消息)
        """
        try:
            # 获取用户信息
            user = self._get_user_from_db(username)
            if not user:
                return False, "用户不存在"

            # 验证旧密码
            if not self.verify_password(old_password, user["password_hash"]):
                return False, "原密码错误"

            # 生成新密码哈希
            salt = secrets.token_hex(16)
            new_password_hash = hashlib.sha256((new_password + salt).encode()).hexdigest() + ":" + salt

            # 更新数据库
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            sql = "UPDATE operator SET password_hash = ?, updated_at = ? WHERE username = ?"
            affected_rows = db_manager.execute_update(sql, (new_password_hash, now, username))

            if affected_rows > 0:
                return True, "密码修改成功"
            else:
                return False, "密码修改失败"

        except Exception as e:
            print(f"修改密码失败: {e}")
            return False, f"修改密码失败: {str(e)}"
    
    def get_user_info(self, username: str) -> Optional[Dict]:
        """获取用户信息"""
        try:
            user = self._get_user_from_db(username)
            if user:
                # 移除敏感信息
                user_info = user.copy()
                user_info.pop("password_hash", None)
                return user_info
        except Exception as e:
            print(f"获取用户信息失败: {e}")
        return None


# 全局认证服务实例
auth_service = AuthService()
