# -*- coding: utf-8 -*-
"""
主题管理器
Theme Manager

管理医疗主题和科技主题的切换
完全按照HTML中的CSS变量系统实现
"""

from typing import Dict, Any
from pathlib import Path


class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.themes_dir = Path(__file__).parent
        self._themes = {
            "medical": self._get_medical_theme(),
            "tech": self._get_tech_theme()
        }
    
    def _get_medical_theme(self) -> Dict[str, Any]:
        """获取医疗主题配置"""
        return {
            # 主色调
            "primary_color": "#2563eb",
            "primary_light": "#3b82f6", 
            "primary_dark": "#1d4ed8",
            "secondary_color": "#06b6d4",
            "accent_color": "#0ea5e9",
            
            # 状态颜色
            "success_color": "#10b981",
            "warning_color": "#f59e0b",
            "danger_color": "#ef4444",
            
            # 中性色
            "neutral_50": "#f8fafc",
            "neutral_100": "#f1f5f9",
            "neutral_200": "#e2e8f0",
            "neutral_300": "#cbd5e1",
            "neutral_400": "#94a3b8",
            "neutral_500": "#64748b",
            "neutral_600": "#475569",
            "neutral_700": "#334155",
            "neutral_800": "#1e293b",
            "neutral_900": "#0f172a",
            
            # 背景色
            "bg_primary": "#f8fafc",
            "bg_secondary": "#ffffff",
            "bg_tertiary": "#f1f5f9",
            "bg_glass": "rgba(255, 255, 255, 0.95)",
            
            # 文字颜色
            "text_primary": "#1e293b",
            "text_secondary": "#475569",
            "text_tertiary": "#64748b",
            
            # 边框颜色
            "border_color": "#e2e8f0",
            
            # 阴影
            "shadow_sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
            "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            "shadow_lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
            "shadow_xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            
            # 渐变
            "gradient_primary": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2563eb, stop:1 #06b6d4)",
            "gradient_secondary": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0ea5e9, stop:1 #3b82f6)"
        }
    
    def _get_tech_theme(self) -> Dict[str, Any]:
        """获取科技主题配置"""
        return {
            # 主色调
            "primary_color": "#06b6d4",
            "primary_light": "#0891b2",
            "primary_dark": "#0e7490", 
            "secondary_color": "#0891b2",
            "accent_color": "#00d4ff",
            
            # 状态颜色
            "success_color": "#00ff88",
            "warning_color": "#ffaa00",
            "danger_color": "#ff3366",
            
            # 中性色（深色主题反转）
            "neutral_50": "#0f172a",
            "neutral_100": "#1e293b",
            "neutral_200": "#334155",
            "neutral_300": "#475569",
            "neutral_400": "#64748b",
            "neutral_500": "#94a3b8",
            "neutral_600": "#cbd5e1",
            "neutral_700": "#e2e8f0",
            "neutral_800": "#f1f5f9",
            "neutral_900": "#f8fafc",
            
            # 背景色
            "bg_primary": "#0f172a",
            "bg_secondary": "#1e293b",
            "bg_tertiary": "#334155",
            "bg_glass": "rgba(30, 41, 59, 0.95)",
            
            # 文字颜色
            "text_primary": "#f1f5f9",
            "text_secondary": "#cbd5e1",
            "text_tertiary": "#94a3b8",
            
            # 边框颜色
            "border_color": "#475569",
            
            # 阴影（科技主题带蓝色光晕）
            "shadow_sm": "0 1px 2px 0 rgba(6, 182, 212, 0.1)",
            "shadow": "0 4px 6px -1px rgba(6, 182, 212, 0.1), 0 2px 4px -1px rgba(6, 182, 212, 0.06)",
            "shadow_lg": "0 10px 15px -3px rgba(6, 182, 212, 0.2), 0 4px 6px -2px rgba(6, 182, 212, 0.1)",
            "shadow_xl": "0 20px 25px -5px rgba(6, 182, 212, 0.3), 0 10px 10px -5px rgba(6, 182, 212, 0.2)",
            
            # 渐变
            "gradient_primary": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #06b6d4, stop:1 #00d4ff)",
            "gradient_secondary": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0891b2, stop:1 #06b6d4)"  # 修改为浅蓝色渐变
        }
    
    def get_theme_config(self, theme_name: str) -> Dict[str, Any]:
        """获取主题配置"""
        return self._themes.get(theme_name, self._themes["tech"])
    
    def get_stylesheet(self, theme_name: str) -> str:
        """获取主题样式表"""
        theme_config = self.get_theme_config(theme_name)
        
        # 生成QSS样式表
        stylesheet = self._generate_stylesheet(theme_config, theme_name)
        
        return stylesheet
    
    def _generate_stylesheet(self, theme_config: Dict[str, Any], theme_name: str) -> str:
        """生成QSS样式表 - 完全按照HTML的CSS转换"""

        # 基础样式
        base_style = f"""
        /* 全局样式 */
        QMainWindow {{
            background-color: {theme_config['bg_primary']};
            color: {theme_config['text_primary']};
            font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, sans-serif;
        }}

        QWidget {{
            background-color: transparent;
            color: {theme_config['text_primary']};
            font-family: "Microsoft YaHei", sans-serif;
        }}

        /* 主内容区 */
        QWidget#main_content {{
            background-color: {theme_config['bg_primary']};
        }}

        QWidget#content_area {{
            background-color: {theme_config['bg_primary']};
        }}

        QStackedWidget#page_stack {{
            background-color: {theme_config['bg_primary']};
        }}
        """

        # 侧边栏样式
        sidebar_style = self._get_sidebar_style(theme_config, theme_name)

        # 顶部栏样式
        topbar_style = self._get_topbar_style(theme_config, theme_name)

        # 报告页面样式
        reports_style = self._get_reports_style(theme_config, theme_name)

        # 图表和指标组件样式
        charts_style = self._get_charts_style(theme_config, theme_name)

        # PyQtGraph组件样式
        pyqtgraph_style = self._get_pyqtgraph_style(theme_config, theme_name)

        # 组合所有样式
        return base_style + sidebar_style + topbar_style + reports_style + charts_style + pyqtgraph_style

    def _get_sidebar_style(self, theme_config: Dict[str, Any], theme_name: str) -> str:
        """获取侧边栏样式 - 按照HTML的CSS转换"""
        return f"""
        /* 侧边栏样式 */
        QFrame#sidebar {{
            background-color: {theme_config['bg_glass']};
            border-right: 1px solid {theme_config['border_color']};
            min-width: 220px;
            max-width: 220px;
        }}

        /* 侧边栏头部 */
        QWidget#sidebar_header {{
            background-color: transparent;
            border-bottom: 1px solid {theme_config['border_color']};
        }}

        /* Logo */
        QLabel#logo {{
            background: {theme_config['gradient_primary']};
            border-radius: 16px;
            color: white;
        }}

        QLabel#logo_text {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        QLabel#logo_subtitle {{
            color: {theme_config['text_tertiary']};
        }}

        /* 导航项 */
        QPushButton#nav_item {{
            background-color: transparent;
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            margin: 0px 16px 8px 16px;
            text-align: left;
            color: {theme_config['text_secondary']};
        }}

        QPushButton#nav_item:hover {{
            background-color: rgba(6, 182, 212, 0.08);
            color: {theme_config['text_primary']};
        }}

        QPushButton#nav_item:pressed {{
            background: {theme_config['gradient_primary']};
            color: white;
        }}

        QPushButton#nav_item[active="true"] {{
            background: {theme_config['gradient_primary']};
            color: white;
        }}

        /* 收起状态的导航项样式 */
        QPushButton#nav_item[collapsed="true"] {{
            border-radius: 8px;
            margin: 0px 6px 8px 6px;
        }}

        QPushButton#nav_item[collapsed="true"]:hover {{
            background-color: rgba(6, 182, 212, 0.08);
        }}

        QPushButton#nav_item[collapsed="true"][active="true"] {{
            background: {theme_config['gradient_primary']};
            color: white;
            border-radius: 4px;
        }}

        /* 区段标题 */
        QLabel#nav_title {{
            color: {theme_config['text_tertiary']};
            font-weight: bold;
        }}

        /* 导航徽章 */
        QLabel#nav_badge {{
            background-color: {theme_config['danger_color']};
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            min-width: 16px;
        }}

        /* 用户信息卡片 */
        QFrame#user_profile {{
            background-color: {theme_config['bg_secondary']};
            border-radius: 12px;
            border: 1px solid {theme_config['border_color']};
        }}

        QLabel#user_avatar {{
            background: {theme_config['gradient_secondary']};
            border-radius: 12px;
            color: white;
            font-weight: bold;
        }}

        QLabel#user_name {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        QLabel#user_role {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#user_status {{
            background-color: {theme_config['success_color']};
            border-radius: 4px;
        }}
        """

    def _get_topbar_style(self, theme_config: Dict[str, Any], theme_name: str) -> str:
        """获取顶部栏样式 - 按照HTML的CSS转换"""
        return f"""
        /* 顶部栏样式 */
        QFrame#top_bar {{
            background-color: {theme_config['bg_glass']};
            border-bottom: 1px solid {theme_config['border_color']};
        }}

        /* 菜单切换按钮 */
        QPushButton#menu_toggle {{
            background-color: {theme_config['bg_tertiary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            color: {theme_config['text_primary']};
        }}

        QPushButton#menu_toggle:hover {{
            background-color: {theme_config['bg_secondary']};
            border-color: {theme_config['primary_color']};
            color: {theme_config['text_primary']};
        }}

        QPushButton#menu_toggle:pressed {{
            background: {theme_config['gradient_primary']};
            border-color: {theme_config['primary_color']};
            color: white;
        }}



        /* 页面标题 */
        QLabel#page_title {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        QLabel#page_subtitle {{
            color: {theme_config['text_tertiary']};
        }}

        /* 状态项 */
        QFrame#status_item {{
            background-color: {theme_config['bg_secondary']};
            border-radius: 20px;
            padding: 8px 16px;
        }}

        QFrame#status_item[status_type="online"] {{
            background-color: rgba(16, 185, 129, 0.1);
            color: {theme_config['success_color']};
        }}

        QFrame#status_item[status_type="warning"] {{
            background-color: rgba(245, 158, 11, 0.1);
            color: {theme_config['warning_color']};
        }}

        QFrame#status_item[status_type="offline"] {{
            background-color: rgba(239, 68, 68, 0.1);
            color: {theme_config['danger_color']};
        }}

        /* 状态文字颜色 */
        QFrame#status_item[status_type="online"] QLabel {{
            color: #10b981 !important;
        }}

        QFrame#status_item[status_type="warning"] QLabel {{
            color: {theme_config['warning_color']};
        }}

        QFrame#status_item[status_type="offline"] QLabel {{
            color: {theme_config['danger_color']};
        }}



        QLabel#status_dot {{
            border-radius: 4px;
        }}

        /* 主题切换器 */
        QFrame#theme_switch {{
            background-color: {theme_config['bg_secondary']};
            border-radius: 12px;
            padding: 8px 16px;
        }}

        /* 滑动开关样式 */
        QFrame#sliding_switch {{
            background-color: {theme_config['bg_tertiary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 14px;
        }}

        QLabel#switch_slider {{
            background: {theme_config['gradient_primary']};
            border-radius: 12px;
            border: 1px solid {theme_config['primary_color']};
        }}

        /* 主题标签样式 */
        QLabel#theme_label {{
            color: {theme_config['text_secondary']};
        }}

        QLabel#theme_label[active="true"] {{
            color: {theme_config['primary_color']};
            font-weight: bold;
        }}

        QLabel#theme_label:hover {{
            color: {theme_config['text_primary']};
        }}



        /* 页面样式 */
        QFrame#placeholder_card {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            padding: 10px;
        }}

        QLabel#count_badge {{
            background-color: {theme_config['accent_color']};
            color: white;
            border-radius: 10px;
            padding: 4px 8px;
        }}

        /* 输入框样式 */
        QLineEdit {{
            padding: 16px 20px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QLineEdit:focus {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QLineEdit[readOnly="true"] {{
            background-color: {theme_config['bg_secondary']};
            color: {theme_config['text_tertiary']};
            border-color: {theme_config['border_color']};
        }}

        /* 文本编辑框样式 */
        QTextEdit {{
            padding: 16px 20px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QTextEdit:focus {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        /* 日期选择器样式 */
        QDateEdit {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 8px 12px;
            font-family: "Microsoft YaHei";
            font-size: 12px;
        }}

        QDateEdit:hover {{
            border-color: {theme_config['accent_color']};
        }}

        QDateEdit:focus {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QDateEdit::drop-down {{
            border: none;
            background-color: {theme_config['bg_tertiary']};
            border-radius: 4px;
            width: 20px;
            margin: 2px;
        }}

        QDateEdit::drop-down:hover {{
            background-color: {theme_config['accent_color']};
        }}

        QDateEdit::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid {theme_config['text_primary']};
            width: 0px;
            height: 0px;
            margin: 2px;
        }}

        QDateEdit::down-arrow:hover {{
            border-top-color: white;
        }}

        /* 日历弹窗样式 */
        QCalendarWidget {{
            background-color: {theme_config['bg_primary']};
            color: {theme_config['text_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
        }}

        QCalendarWidget QToolButton {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            border: none;
            border-radius: 4px;
            padding: 4px;
        }}

        QCalendarWidget QToolButton:hover {{
            background-color: {theme_config['accent_color']};
        }}

        QCalendarWidget QMenu {{
            background-color: {theme_config['bg_primary']};
            color: {theme_config['text_primary']};
            border: 1px solid {theme_config['border_color']};
        }}

        QCalendarWidget QSpinBox {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 4px;
        }}

        QCalendarWidget QAbstractItemView {{
            background-color: {theme_config['bg_primary']};
            color: {theme_config['text_primary']};
            selection-background-color: {theme_config['accent_color']};
            selection-color: white;
        }}

        /* 输入框错误状态 */
        QLineEdit[error="true"], QTextEdit[error="true"], QComboBox[error="true"] {{
            border-color: {theme_config['danger_color']};
            background-color: rgba(239, 68, 68, 0.1);
        }}

        /* 错误提示标签 */
        QLabel#error_label {{
            color: {theme_config['danger_color']};
            font-size: 12px;
            margin-top: 4px;
        }}

        /* 对话框样式 */
        QDialog {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 16px;
        }}



        /* 对话框容器样式 */
        QFrame#discharge_dialog_container {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}

        /* 对话框标题样式 */
        QLabel#dialog_title {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        /* 对话框信息样式 */
        QLabel#dialog_info {{
            color: {theme_config['text_secondary']};
        }}

        /* 对话框图标样式 */
        QLabel#dialog_icon_information {{
            color: {theme_config['primary_color']};
            background-color: rgba(6, 182, 212, 0.15);
            border-radius: 18px;
            font-weight: bold;
        }}

        QLabel#dialog_icon_warning {{
            color: {theme_config['warning_color']};
            background-color: rgba(245, 158, 11, 0.15);
            border-radius: 18px;
            font-weight: bold;
        }}

        QLabel#dialog_icon_critical {{
            color: {theme_config['danger_color']};
            background-color: rgba(239, 68, 68, 0.15);
            border-radius: 18px;
            font-weight: bold;
        }}

        QLabel#dialog_icon_question {{
            color: {theme_config['primary_color']};
            background-color: rgba(6, 182, 212, 0.15);
            border-radius: 18px;
            font-weight: bold;
        }}

        /* 对话框标签样式 */
        QLabel#dialog_label {{
            color: {theme_config['text_primary']};
        }}

        /* 按钮样式 */
        QPushButton {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            padding: 12px 24px;
            color: {theme_config['text_primary']};
            font-weight: 500;
            font-family: "Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", "PingFang SC", sans-serif;
        }}

        QPushButton:hover {{
            background-color: {theme_config['bg_tertiary']};
            border-color: {theme_config['accent_color']};
        }}

        QPushButton:pressed {{
            background-color: {theme_config['accent_color']};
            color: white;
        }}

        /* 主要按钮样式 */
        QPushButton#btn_primary {{
            background: {theme_config['gradient_primary']};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            font-weight: bold;
            font-size: 13px;
            min-height: 28px;
            min-width: 120px;
            font-family: "Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", "PingFang SC", sans-serif;
        }}

        QPushButton#btn_primary:hover {{
            background: {theme_config['gradient_secondary']};
        }}

        QPushButton#btn_primary:pressed {{
            background: {theme_config['gradient_secondary']};
        }}

        /* 开始按钮专用样式 */
        QPushButton#btn_start:enabled {{
            background: {theme_config['gradient_primary']};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            font-weight: bold;
            font-size: 13px;
            min-height: 28px;
            min-width: 120px;
            font-family: "Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", "PingFang SC", sans-serif;
        }}

        QPushButton#btn_start:enabled:hover {{
            background: {theme_config['gradient_secondary']};
        }}

        QPushButton#btn_start:enabled:pressed {{
            background: {theme_config['gradient_secondary']};
        }}

        QPushButton#btn_start:disabled {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 10px 16px;
            font-weight: bold;
            font-size: 13px;
            min-height: 28px;
            min-width: 120px;
            opacity: 0.5;
            font-family: "Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", "PingFang SC", sans-serif;
        }}

        /* 暂停状态按钮样式 */
        QPushButton#btn_pause {{
            background-color: #f59e0b;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            font-weight: bold;
            font-size: 13px;
            min-height: 28px;
            min-width: 120px;
            font-family: "Source Han Sans SC", "Noto Sans CJK SC", "Microsoft YaHei", "PingFang SC", sans-serif;
        }}

        QPushButton#btn_pause:hover {{
            background-color: #d97706;
        }}

        QPushButton#btn_pause:pressed {{
            background-color: #b45309;
        }}

        /* 次要按钮样式 */
        QPushButton#btn_secondary {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 8px 12px;
            font-weight: normal;
            font-size: 12px;
            min-height: 28px;
            min-width: 60px;
        }}

        QPushButton#btn_secondary:hover {{
            background-color: {theme_config['bg_secondary']};
            color: {theme_config['text_primary']};
            border-color: {theme_config['primary_color']};
        }}

        QPushButton#btn_secondary:pressed {{
            background: {theme_config['gradient_primary']};
            color: white;
            border-color: {theme_config['primary_color']};
        }}

        /* 停止按钮专用样式 */
        QPushButton#btn_stop:disabled {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 8px 12px;
            font-weight: normal;
            font-size: 12px;
            min-height: 28px;
            min-width: 60px;
            opacity: 0.5;
        }}

        QPushButton#btn_stop:enabled {{
            background-color: #dc2626;
            color: white;
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 8px 12px;
            font-weight: normal;
            font-size: 12px;
            min-height: 28px;
            min-width: 60px;
        }}

        QPushButton#btn_stop:enabled:hover {{
            background-color: #b91c1c;
            border-color: #b91c1c;
        }}

        QPushButton#btn_stop:enabled:pressed {{
            background-color: #991b1b;
            border-color: #991b1b;
        }}



        /* 对话框关闭按钮样式 */
        QPushButton#dialog_close_btn {{
            background-color: {theme_config['bg_tertiary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 18px;
            color: {theme_config['text_secondary']};
            font-size: 18px;
            font-weight: bold;
            padding: 0px;
        }}

        QPushButton#dialog_close_btn:hover {{
            background-color: {theme_config['danger_color']};
            color: white;
            border-color: {theme_config['danger_color']};
        }}

        QPushButton#dialog_close_btn:pressed {{
            background-color: #dc2626;
            color: white;
            border-color: #dc2626;
        }}

        /* 患者详情基本信息标签样式 */
        QLabel#info_label {{
            color: {theme_config['text_tertiary']};
            font-weight: normal;
        }}

        QLabel#info_value {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        /* 长文本字段样式（QTextEdit，模拟QLabel外观） */
        QTextEdit#info_value_text {{
            color: {theme_config['text_primary']};
            font-weight: bold;
            background-color: transparent;
            border: none;
            padding: 0px;
            margin: 0px;
        }}

        /* 长文本字段的滚动条样式 */
        QTextEdit#info_value_text QScrollBar:vertical {{
            background-color: transparent;
            width: 6px;
            border-radius: 3px;
        }}

        QTextEdit#info_value_text QScrollBar::handle:vertical {{
            background-color: {theme_config['border_color']};
            border-radius: 3px;
            min-height: 20px;
        }}

        QTextEdit#info_value_text QScrollBar::handle:vertical:hover {{
            background-color: {theme_config['text_tertiary']};
        }}

        /* 患者卡片样式 - 完全按照HTML设计 */
        QFrame#patient_card {{
            background-color: {theme_config['bg_tertiary']};
            border: 2px solid transparent;
            border-radius: 12px;
            margin: 0px 0px 16px 0px;
        }}

        QFrame#patient_card:hover {{
            /* 悬停效果将通过代码实现 */
        }}

        QFrame#patient_card[selected="true"] {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QLabel#patient_avatar {{
            background: {theme_config['gradient_primary']};
            border-radius: 14px;
            color: white;
            font-weight: 700;
        }}

        QLabel#patient_avatar_placeholder {{
            background: {theme_config['bg_tertiary']};
            border: 2px dashed {theme_config['border_color']};
            border-radius: 14px;
            color: {theme_config['text_secondary']};
            font-weight: 400;
        }}

        QLabel#patient_name {{
            color: {theme_config['text_primary']};
            font-weight: 600;
        }}

        QLabel#patient_name_placeholder {{
            color: {theme_config['text_secondary']};
            font-weight: 400;
            font-style: italic;
        }}

        QLabel#patient_info {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#patient_status_badge {{
            background-color: rgba(16, 185, 129, 0.1);
            color: {theme_config['success_color']};
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 600;
        }}

        QLabel#patient_treatments_badge {{
            background-color: rgba(6, 182, 212, 0.1);
            color: {theme_config['primary_color']};
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 600;
        }}

        QLabel#patient_time_label {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#patient_time_value {{
            color: {theme_config['text_tertiary']};
        }}

        /* 患者详情面板样式 */
        QFrame#patient_detail_panel {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}

        QFrame#patient_header {{
            background-color: {theme_config['bg_tertiary']};
            border-radius: 12px 12px 0px 0px;
            border-bottom: 1px solid {theme_config['border_color']};
        }}

        QLabel#detail_avatar {{
            background: {theme_config['gradient_secondary']};
            border-radius: 40px;
            color: white;
        }}

        /* 患者详情标签页按钮样式 - 按照HTML设计 */
        QPushButton#patient_tab_btn {{
            background-color: transparent;
            border: none;
            border-bottom: 2px solid transparent;
            color: {theme_config['text_secondary']};
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            min-height: 20px;
        }}

        QPushButton#patient_tab_btn:hover {{
            color: {theme_config['text_primary']};
        }}

        QPushButton#patient_tab_btn:pressed {{
            background: {theme_config['gradient_primary']};
            color: white;
        }}

        QPushButton#patient_tab_btn[active="true"] {{
            color: {theme_config['primary_color']};
            border-bottom: 2px solid {theme_config['primary_color']};
            font-weight: 600;
        }}

        QPushButton#patient_tab_btn[active="false"] {{
            color: {theme_config['text_secondary']};
            border-bottom: 2px solid transparent;
            font-weight: 500;
        }}

        /* 标签页边框线 */
        QFrame#tab_border_line {{
            background-color: {theme_config['border_color']};
            border: none;
        }}

        /* 标签页内容堆叠区域 */
        QStackedWidget#tab_content_stack {{
            background-color: transparent;
            border: none;
        }}

        /* 治疗记录样式 */
        QFrame#treatment_record {{
            background-color: {theme_config['bg_tertiary']};
            border-radius: 12px;
            border: 1px solid {theme_config['border_color']};
        }}

        QLabel#record_status_dot {{
            background-color: {theme_config['success_color']};
            border-radius: 6px;
        }}

        QLabel#record_time {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#record_status {{
            background-color: rgba(16, 185, 129, 0.1);
            color: {theme_config['success_color']};
            padding: 4px 8px;
            border-radius: 6px;
        }}

        QLabel#stat_value {{
            color: {theme_config['primary_color']};
        }}

        QLabel#stat_desc {{
            color: {theme_config['text_tertiary']};
        }}

        /* 历史数据样式 */
        QFrame#history_stats_card {{
            background-color: {theme_config['bg_tertiary']};
            border-radius: 12px;
            border: 1px solid {theme_config['border_color']};
        }}

        QLabel#history_label {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#history_value {{
            color: {theme_config['text_primary']};
        }}

        /* 搜索框样式 */
        QLineEdit#patient_search {{
            padding: 16px 48px 16px 20px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QLineEdit#patient_search:focus {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        /* 分页按钮样式 */
        QPushButton#pagination_btn {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            font-size: 13px;
            min-height: 20px;
            font-family: "Microsoft YaHei";
        }}

        QPushButton#pagination_btn:hover {{
            background-color: {theme_config['bg_secondary']};
            border-color: {theme_config['primary_color']};
        }}

        QPushButton#pagination_btn:pressed {{
            background: {theme_config['gradient_primary']};
            color: white;
        }}

        QLabel#page_info {{
            color: {theme_config['text_secondary']};
            font-weight: 500;
            padding: 8px 16px;
            min-height: 28px;
        }}

        QPushButton#pagination_btn:disabled {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_tertiary']};
            border-color: {theme_config['border_color']};
        }}

        /* 用户卡片样式 - 按照HTML设计 */
        QFrame#user_card {{
            background-color: {theme_config['bg_tertiary']};
            border: 2px solid transparent;
            border-radius: 12px;
            margin: 0px 0px 12px 0px;
        }}

        QFrame#user_card:hover {{
            background-color: {theme_config['bg_secondary']};
            border: 2px solid transparent;
        }}

        QFrame#user_card[selected="true"] {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QLabel#user_avatar {{
            background: {theme_config['gradient_primary']};
            border-radius: 14px;  /* 与患者头像保持一致的方形圆角 */
            color: white;
            font-weight: 700;
        }}

        QLabel#user_name {{
            color: {theme_config['text_primary']};
            font-weight: 600;
        }}

        QLabel#user_email {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#user_login {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#user_role_badge {{
            background-color: rgba(6, 182, 212, 0.1);
            color: {theme_config['primary_color']};
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 11px;
        }}

        QLabel#user_status_badge {{
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 11px;
        }}

        QLabel#user_status_badge[status="active"] {{
            background-color: rgba(16, 185, 129, 0.1);
            color: {theme_config['success_color']};
        }}

        QLabel#user_status_badge[status="inactive"] {{
            background-color: rgba(239, 68, 68, 0.1);
            color: {theme_config['danger_color']};
        }}

        QLabel#user_login_title {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#user_login_time {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#user_info {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#user_status_dot {{
            border-radius: 4px;
        }}

        QLabel#user_status_dot[status="active"] {{
            background-color: {theme_config['success_color']};
        }}

        QLabel#user_status_dot[status="inactive"] {{
            background-color: {theme_config['danger_color']};
        }}

        /* 用户详情面板样式 - 已合并到患者详情面板样式 */

        QFrame#user_detail_header {{
            background-color: {theme_config['bg_tertiary']};
            border-radius: 20px 20px 0px 0px;
            border-bottom: 1px solid {theme_config['border_color']};
        }}

        QLabel#detail_avatar {{
            background: {theme_config['gradient_secondary']};
            border-radius: 12px;  /* 调整为方形圆角，与患者详情保持一致 */
            color: white;
            font-weight: 700;
        }}

        /* 用户标签页按钮样式 */
        QPushButton#user_tab_btn {{
            background-color: transparent;
            border: none;
            border-bottom: 2px solid transparent;
            color: {theme_config['text_secondary']};
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            min-height: 20px;
        }}

        QPushButton#user_tab_btn:hover {{
            color: {theme_config['text_primary']};
        }}

        QPushButton#user_tab_btn:pressed {{
            background: {theme_config['gradient_primary']};
            color: white;
        }}

        QPushButton#user_tab_btn[active="true"] {{
            color: {theme_config['primary_color']};
            border-bottom: 2px solid {theme_config['primary_color']};
            font-weight: 600;
        }}

        QPushButton#user_tab_btn[active="false"] {{
            color: {theme_config['text_secondary']};
            border-bottom: 2px solid transparent;
            font-weight: 500;
        }}

        /* 权限标签样式 */
        QLabel#permission_tag {{
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 11px;
        }}

        QLabel#permission_tag[permission_type="system"] {{
            background-color: rgba(16, 185, 129, 0.1);
            color: {theme_config['success_color']};
        }}

        QLabel#permission_tag[permission_type="data"] {{
            background-color: rgba(6, 182, 212, 0.1);
            color: {theme_config['primary_color']};
        }}

        /* 警告按钮样式 */
        QPushButton#btn_warning {{
            background-color: {theme_config['warning_color']};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 12px;
            min-height: 28px;
            font-family: "Microsoft YaHei";
        }}

        QPushButton#btn_warning:hover {{
            background-color: #d97706;
        }}

        QPushButton#btn_warning:pressed {{
            background-color: #b45309;
        }}

        /* 小型按钮样式 - 适用于所有28x28像素的小按钮，特别是加减号按钮 */
        QPushButton[text="+"], QPushButton[text="−"], QPushButton[text="-"] {{
            background-color: {theme_config['bg_tertiary']};
            border: 2px solid {theme_config['border_color']};
            border-radius: 6px;
            color: {theme_config['text_primary']};
            font-weight: bold;
            font-size: 16px;
            font-family: "Microsoft YaHei";
            padding: 0px;
            min-width: 28px;
            min-height: 28px;
            max-width: 28px;
            max-height: 28px;
        }}

        QPushButton[text="+"]:hover, QPushButton[text="−"]:hover, QPushButton[text="-"]:hover {{
            background-color: {theme_config['bg_secondary']};
            border-color: {theme_config['accent_color']};
            color: {theme_config['accent_color']};
        }}

        QPushButton[text="+"]:pressed, QPushButton[text="−"]:pressed, QPushButton[text="-"]:pressed {{
            background-color: {theme_config['accent_color']};
            border-color: {theme_config['accent_color']};
            color: white;
        }}

        QPushButton[text="+"]:disabled, QPushButton[text="−"]:disabled, QPushButton[text="-"]:disabled {{
            background-color: {theme_config['bg_secondary']};
            border-color: {theme_config['border_color']};
            color: {theme_config['text_secondary']};
        }}

        /* 单选按钮样式 - 现代化设计，确保选中状态清晰可见，适用于所有QRadioButton */
        QRadioButton {{
            color: {theme_config['text_primary']};
            font-weight: 500;
            font-family: "Microsoft YaHei";
            spacing: 10px;
            padding: 6px 0px;
        }}

        QRadioButton::indicator {{
            width: 20px;
            height: 20px;
            border-radius: 10px;
            border: 3px solid {theme_config['border_color']};
            background-color: {theme_config['bg_tertiary']};
        }}

        QRadioButton::indicator:hover {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QRadioButton::indicator:checked {{
            border-color: {theme_config['primary_color']};
            background-color: {theme_config['primary_color']};
            background-image: radial-gradient(circle, white 25%, {theme_config['primary_color']} 25%);
        }}

        QRadioButton::indicator:checked:hover {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['accent_color']};
            background-image: radial-gradient(circle, white 25%, {theme_config['accent_color']} 25%);
        }}

        QRadioButton::indicator:disabled {{
            border-color: {theme_config['border_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QRadioButton::indicator:checked:disabled {{
            border-color: {theme_config['text_secondary']};
            background-color: {theme_config['text_secondary']};
            background-image: radial-gradient(circle, white 25%, {theme_config['text_secondary']} 25%);
        }}

        QRadioButton:hover {{
            color: {theme_config['accent_color']};
        }}

        QRadioButton:checked {{
            color: {theme_config['primary_color']};
            font-weight: 700;
        }}

        QRadioButton:disabled {{
            color: {theme_config['text_secondary']};
        }}

        /* 复选框样式 - 现代化设计，确保选中状态清晰可见，适用于所有QCheckBox */
        QCheckBox {{
            color: {theme_config['text_primary']};
            font-weight: 500;
            font-family: "Microsoft YaHei";
            spacing: 10px;
            padding: 6px 0px;
        }}

        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 3px solid {theme_config['border_color']};
            background-color: {theme_config['bg_tertiary']};
        }}

        QCheckBox::indicator:hover {{
            border-color: {theme_config['primary_color']};
            background-color: {theme_config['primary_color']};
        }}

        QCheckBox::indicator:checked {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['accent_color']};
            image: none;
        }}

        QCheckBox::indicator:checked:hover {{
            border-color: {theme_config['primary_color']};
            background-color: {theme_config['primary_color']};
        }}

        QCheckBox::indicator:disabled {{
            border-color: {theme_config['border_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QCheckBox::indicator:checked:disabled {{
            border-color: {theme_config['text_secondary']};
            background-color: {theme_config['text_secondary']};
        }}

        QCheckBox:hover {{
            color: {theme_config['primary_color']};
        }}

        QCheckBox:checked {{
            color: {theme_config['accent_color']};
            font-weight: 700;
        }}

        QCheckBox:disabled {{
            color: {theme_config['text_secondary']};
        }}

        /* 用户搜索框样式 */
        QLineEdit#user_search {{
            padding: 16px 48px 16px 20px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QLineEdit#user_search:focus {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        /* 下拉框样式 */
        QComboBox {{
            padding: 16px 20px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QComboBox:hover {{
            border-color: {theme_config['primary_color']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border: none;
            width: 0px;
            height: 0px;
        }}

        QComboBox QAbstractItemView {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            color: {theme_config['text_primary']};
            selection-background-color: {theme_config['primary_color']};
            selection-color: white;
        }}

        /* 用户列表和详情卡片样式 - 已合并到患者管理页面样式 */

        QFrame#users_main_container {{
            background-color: transparent;
        }}

        /* 滚动区域样式 - 统一用户和患者管理页面 */
        QScrollArea#users_scroll, QScrollArea#patients_scroll {{
            border: none;
            background-color: transparent;
        }}

        QScrollArea#users_scroll QScrollBar:vertical,
        QScrollArea#patients_scroll QScrollBar:vertical {{
            background-color: {theme_config['bg_tertiary']};
            width: 8px;
            border-radius: 4px;
            margin: 0px;
        }}

        QScrollArea#users_scroll QScrollBar::handle:vertical,
        QScrollArea#patients_scroll QScrollBar::handle:vertical {{
            background-color: {theme_config['border_color']};
            border-radius: 4px;
            min-height: 20px;
        }}

        QScrollArea#users_scroll QScrollBar::handle:vertical:hover,
        QScrollArea#patients_scroll QScrollBar::handle:vertical:hover {{
            background-color: {theme_config['text_tertiary']};
        }}

        QScrollArea#users_scroll QScrollBar::add-line:vertical,
        QScrollArea#users_scroll QScrollBar::sub-line:vertical,
        QScrollArea#patients_scroll QScrollBar::add-line:vertical,
        QScrollArea#patients_scroll QScrollBar::sub-line:vertical {{
            height: 0px;
        }}

        /* 标签页堆叠区域 */
        QStackedWidget#user_tab_stack {{
            background-color: transparent;
            border: none;
        }}

        /* 用户详情状态标签 */
        QLabel#detail_status[status="active"] {{
            background-color: rgba(16, 185, 129, 0.1);
            color: {theme_config['success_color']};
            padding: 6px 12px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 12px;
        }}

        QLabel#detail_status[status="inactive"] {{
            background-color: rgba(239, 68, 68, 0.1);
            color: {theme_config['danger_color']};
            padding: 6px 12px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 12px;
        }}

        QLabel#detail_role {{
            background-color: rgba(6, 182, 212, 0.1);
            color: {theme_config['primary_color']};
            padding: 6px 12px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 12px;
        }}

        /* 用户管理页面特定样式 - 已合并到通用样式 */

        QFrame#search_filter_bar {{
            background-color: transparent;
            padding: 0px 26px 16px 26px;
        }}

        QFrame#pagination_frame {{
            background-color: transparent;
            border-top: 1px solid {theme_config['border_color']};
            padding: 20px 32px;
        }}

        QFrame#tab_nav_frame {{
            background-color: transparent;
            border-bottom: 1px solid {theme_config['border_color']};
            padding: 0px 32px;
        }}

        QFrame#action_buttons_frame {{
            background-color: transparent;
            border-top: 1px solid {theme_config['border_color']};
            padding: 24px 32px;
        }}

        QFrame#info_card, QFrame#permission_card {{
            background-color: {theme_config['bg_tertiary']};
            border-radius: 12px;
            padding: 16px;
        }}

        QFrame#stats_card {{
            background-color: {theme_config['bg_tertiary']};
            border-radius: 12px;
            padding: 4px;  /* 减小统计卡片的padding */
        }}

        QLabel#permission_title, QLabel#detail_username {{
            color: {theme_config['text_primary']};
        }}

        QLabel#detail_email {{
            color: {theme_config['text_secondary']};
        }}

        QComboBox#role_filter {{
            min-width: 120px;
        }}

        /* 患者管理页面卡片样式 */
        QFrame#patients_list_card {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}

        QFrame#patient_detail_card {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}

        /* 系统设置页面样式 */
        QFrame#settings_main_container {{
            background-color: transparent;
        }}

        QFrame#settings_nav_container {{
            background-color: {theme_config['bg_secondary']};
            border-radius: 12px 12px 0px 0px;
            border-bottom: 1px solid {theme_config['border_color']};
        }}

        QPushButton#settings_tab_btn {{
            background-color: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            color: {theme_config['text_secondary']};
            padding: 20px 16px;
            font-weight: 500;
            font-size: 15px;
            text-align: center;
            margin: 0px 4px;
        }}

        QPushButton#settings_tab_btn:hover {{
            color: {theme_config['text_primary']};
        }}

        QPushButton#settings_tab_btn:pressed {{
            background: {theme_config['gradient_primary']};
            color: white;
        }}

        QPushButton#settings_tab_btn[active="true"] {{
            color: {theme_config['primary_color']};
            border-bottom: 3px solid {theme_config['primary_color']};
            font-weight: 600;
        }}

        QFrame#settings_content_container {{
            background-color: transparent;
        }}

        QScrollArea#settings_scroll_area {{
            border: none;
            background-color: transparent;
        }}

        QWidget#settings_card_content {{
            background-color: transparent;
            border: none;
        }}

        QStackedWidget#settings_tab_stack {{
            background-color: transparent;
            border: none;
        }}

        QFrame#settings_card {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}

        QFrame#settings_card_header {{
            background-color: {theme_config['bg_tertiary']};
            border-radius: 20px 20px 0px 0px;
            border-bottom: 1px solid {theme_config['border_color']};
        }}

        QLabel#settings_card_title {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        QLabel#settings_field_label {{
            color: {theme_config['text_secondary']};
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
        }}

        QLineEdit#settings_input {{
            padding: 12px 16px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QLineEdit#settings_input:focus {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        QComboBox#settings_combo {{
            padding: 12px 16px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QComboBox#settings_combo:hover {{
            border-color: {theme_config['primary_color']};
        }}

        QComboBox#settings_combo::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox#settings_combo::down-arrow {{
            image: none;
            border: none;
            width: 0px;
            height: 0px;
        }}

        QComboBox#settings_combo QAbstractItemView {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            color: {theme_config['text_primary']};
            selection-background-color: {theme_config['primary_color']};
            selection-color: white;
        }}

        QSpinBox#settings_spinbox, QDoubleSpinBox#settings_spinbox {{
            padding: 12px 16px;
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            font-size: 14px;
        }}

        QSpinBox#settings_spinbox:focus, QDoubleSpinBox#settings_spinbox:focus {{
            border-color: {theme_config['accent_color']};
            background-color: {theme_config['bg_secondary']};
        }}

        /* SpinBox上下调节按钮样式 - 简化版本，只调整大小 */
        QSpinBox#settings_spinbox::up-button, QDoubleSpinBox#settings_spinbox::up-button {{
            width: 22px;
            height: 22px;
        }}

        QSpinBox#settings_spinbox::down-button, QDoubleSpinBox#settings_spinbox::down-button {{
            width: 22px;
            height: 22px;
        }}

        /* 全局禁用数字选择框和下拉框的滚轮响应 - 通过代码实现 */
        QSpinBox, QDoubleSpinBox, QComboBox {{
            /* 滚轮响应已在代码中禁用 */
        }}

        /* 全局SpinBox上下调节按钮样式 - 简化版本，只调整大小 */
        QSpinBox::up-button, QDoubleSpinBox::up-button {{
            width: 18px;
            height: 14px;
        }}

        QSpinBox::down-button, QDoubleSpinBox::down-button {{
            width: 18px;
            height: 14px;
        }}

        QFrame#settings_bottom_container {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}
        """

    def _get_reports_style(self, theme_config: Dict[str, Any], theme_name: str) -> str:
        """获取报告页面样式"""
        return f"""
        /* 报告页面样式 */

        /* 筛选面板 */
        QFrame#filter_panel {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}

        /* 右侧内容区域 */
        QWidget#reports_content_area {{
            background-color: {theme_config['bg_secondary']};
            border: none;
            border-radius: 12px;
        }}

        QLabel#filter_panel_title {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        QFrame#filter_group {{
            background-color: {theme_config['bg_tertiary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
        }}

        QLabel#filter_group_title {{
            color: {theme_config['text_primary']};
            font-weight: bold;
        }}

        QComboBox#filter_combo {{
            background-color: {theme_config['bg_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 6px;
            padding: 6px 12px;
            color: {theme_config['text_primary']};
            font-size: 11px;
        }}

        QComboBox#filter_combo:hover {{
            border-color: {theme_config['primary_color']};
        }}

        QComboBox#filter_combo::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox#filter_combo::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid {theme_config['text_secondary']};
            margin-right: 8px;
        }}

        QLineEdit#filter_input {{
            background-color: {theme_config['bg_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 6px;
            padding: 6px 12px;
            color: {theme_config['text_primary']};
            font-size: 11px;
        }}

        QLineEdit#filter_input:focus {{
            border-color: {theme_config['primary_color']};
        }}

        QDateEdit#filter_date {{
            background-color: {theme_config['bg_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 6px;
            padding: 6px 12px;
            color: {theme_config['text_primary']};
            font-size: 11px;
        }}

        QDateEdit#filter_date:hover {{
            border-color: {theme_config['primary_color']};
        }}

        QCheckBox#filter_checkbox {{
            color: {theme_config['text_primary']};
            spacing: 8px;
        }}

        QCheckBox#filter_checkbox::indicator {{
            width: 16px;
            height: 16px;
            border: 2px solid {theme_config['border_color']};
            border-radius: 3px;
            background-color: {theme_config['bg_primary']};
        }}

        QCheckBox#filter_checkbox::indicator:checked {{
            background-color: {theme_config['primary_color']};
            border-color: {theme_config['primary_color']};
        }}

        QPushButton#filter_apply_btn {{
            background: {theme_config['gradient_primary']};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: bold;
        }}

        QPushButton#filter_apply_btn:hover {{
            background: {theme_config['gradient_secondary']};
        }}

        QPushButton#filter_apply_btn:pressed {{
            background-color: {theme_config['primary_dark']};
        }}

        QPushButton#filter_reset_btn {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: bold;
        }}

        QPushButton#filter_reset_btn:hover {{
            background-color: {theme_config['bg_primary']};
            border-color: {theme_config['primary_color']};
        }}

        /* 统计卡片 */
        QFrame#stats_card {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
            padding: 4px;  /* 设置更小的padding */
        }}

        QFrame#stats_card:hover {{
            border-color: {theme_config['primary_color']};
            background-color: {theme_config['bg_tertiary']};
        }}

        QLabel#stats_card_icon {{
            color: {theme_config['primary_color']};
        }}

        QLabel#stats_card_title {{
            color: {theme_config['text_secondary']};
        }}

        QLabel#stats_card_value {{
            color: {theme_config['text_primary']};
        }}

        QLabel#stats_card_subtitle {{
            color: {theme_config['text_tertiary']};
        }}

        QLabel#stats_card_trend_up {{
            color: {theme_config['success_color']};
        }}

        QLabel#stats_card_trend_down {{
            color: {theme_config['danger_color']};
        }}

        QLabel#stats_card_trend_neutral {{
            color: {theme_config['text_secondary']};
        }}

        /* 图表组件 */
        QFrame#chart_widget {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 12px;
        }}

        QLabel#chart_title {{
            color: {theme_config['text_primary']};
        }}

        QFrame#chart_container {{
            background-color: {theme_config['bg_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
        }}

        QLabel#chart_display {{
            color: {theme_config['text_secondary']};
            background-color: transparent;
        }}

        QComboBox#chart_type_combo {{
            background-color: {theme_config['bg_tertiary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 6px;
            padding: 4px 8px;
            color: {theme_config['text_primary']};
            font-size: 10px;
            min-width: 80px;
        }}

        QPushButton#chart_refresh_btn {{
            background-color: {theme_config['bg_tertiary']};
            color: {theme_config['text_primary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 6px;
            padding: 4px 12px;
        }}

        QPushButton#chart_refresh_btn:hover {{
            background-color: {theme_config['primary_color']};
            color: white;
            border-color: {theme_config['primary_color']};
        }}

        /* 滚动区域 */
        QScrollArea#reports_scroll_area {{
            border: none;
            background-color: transparent;
        }}

        QScrollArea#filter_scroll_area {{
            border: none;
            background-color: transparent;
        }}

        QScrollArea#filter_scroll_area QScrollBar:vertical {{
            background-color: {theme_config['bg_tertiary']};
            width: 8px;
            border-radius: 4px;
        }}

        QScrollArea#filter_scroll_area QScrollBar::handle:vertical {{
            background-color: {theme_config['border_color']};
            border-radius: 4px;
            min-height: 20px;
        }}

        QScrollArea#filter_scroll_area QScrollBar::handle:vertical:hover {{
            background-color: {theme_config['primary_color']};
        }}
        """

    def _get_charts_style(self, theme_config: Dict[str, Any], theme_name: str) -> str:
        """获取图表和指标组件的全局样式"""
        return f"""
        /* 实时指标面板样式 */
        RealtimeMetricsPanel {{
            background-color: transparent;
            color: {theme_config['text_primary']};
        }}

        /* 指标面板框架样式 */
        RealtimeMetricsPanel QFrame {{
            border: 1px solid {theme_config['border_color']};
            border-radius: 4px;
            background-color: {theme_config['bg_secondary']};
            padding: 3px;
        }}

        /* 指标面板进度条样式 */
        RealtimeMetricsPanel QProgressBar {{
            border: 1px solid {theme_config['border_color']};
            border-radius: 2px;
            background-color: {theme_config['bg_tertiary']};
            text-align: center;
            font-size: 7px;
            color: {theme_config['text_secondary']};
        }}

        /* 进度条填充部分保持原色 */
        RealtimeMetricsPanel QProgressBar::chunk {{
            border-radius: 1px;
        }}

        /* 实时曲线组件样式 */
        RealtimeCurvesWidget {{
            background-color: transparent;
        }}

        /* 实时曲线显示区域样式 */
        QLabel#realtime_curves_display {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 5px;
        }}

        /* 标题标签样式 */
        RealtimeMetricsPanel QLabel {{
            color: {theme_config['text_primary']};
        }}
        """

    def _get_pyqtgraph_style(self, theme_config: Dict[str, Any], theme_name: str) -> str:
        """获取PyQtGraph组件的全局样式配置"""

        # 根据主题设置PyQtGraph的颜色配置
        if theme_name == "medical":
            # 医疗主题：浅色背景
            pg_bg_color = "#ffffff"  # 白色背景
            pg_grid_color = "#e2e8f0"  # 浅灰色网格
            pg_text_color = "#334155"  # 深色文字
            pg_axis_color = "#64748b"  # 轴线颜色
        else:  # tech主题
            # 科技主题：深色背景
            pg_bg_color = "#1e293b"  # 深色背景
            pg_grid_color = "#475569"  # 深灰色网格
            pg_text_color = "#e2e8f0"  # 浅色文字
            pg_axis_color = "#94a3b8"  # 轴线颜色

        return f"""
        /* PyQtGraph实时曲线组件样式 */
        PyQtGraphCurvesWidget {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 5px;
        }}

        /* PyQtGraph绘图区域样式 - 通过属性传递给组件 */
        PyQtGraphCurvesWidget {{
            qproperty-bgColor: "{pg_bg_color}";
            qproperty-gridColor: "{pg_grid_color}";
            qproperty-textColor: "{pg_text_color}";
            qproperty-axisColor: "{pg_axis_color}";
        }}

        /* 地形图组件样式 */
        MNETopographyWidget {{
            background-color: {theme_config['bg_secondary']};
            border: 1px solid {theme_config['border_color']};
            border-radius: 8px;
            padding: 5px;
        }}

        /* 地形图显示标签样式 */
        MNETopographyWidget QLabel#topography_label {{
            background-color: {theme_config['bg_secondary']};
            border: none;
            border-radius: 4px;
        }}
        """

    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        return list(self._themes.keys())
