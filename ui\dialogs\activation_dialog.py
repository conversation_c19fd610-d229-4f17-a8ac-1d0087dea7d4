# -*- coding: utf-8 -*-
"""
激活对话框
Activation Dialog

用于软件激活的对话框，包含硬件ID显示和注册码输入
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QTextEdit, QGroupBox, QGridLayout,
    QApplication, QProgressBar, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, QTimer, QPoint
from PySide6.QtGui import QFont, QIcon, QMouseEvent, QPainterPath, QBrush, QPainter, QColor
from services.license_manager import license_system
from app.config import AppConfig
from ui.themes.theme_manager import ThemeManager


class ActivationDialog(QDialog):
    """激活对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 加载配置和主题
        self.config = AppConfig()
        self.theme_manager = ThemeManager()
        self.current_theme = self.config.get('theme', 'tech')
        
        self.setWindowTitle("软件激活")
        self.setFixedSize(580, 600)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        
        # 设置窗口图标
        self.setWindowIcon(QIcon("icons/ht.png"))
        
        # 获取机器信息
        self.machine_info = license_system.get_machine_info()
        
        # 居中显示
        self._center_window()
        
        # 初始化UI
        self._init_ui()
        self._setup_style()
        
        # 设置焦点到注册码输入框
        self.license_input.setFocus()
        
        # 拖拽相关变量
        self.drag_position = QPoint()
    
    def _center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
    
    def _init_ui(self):
        """初始化UI"""
        # 主容器
        self.main_container = QFrame()
        self.main_container.setObjectName("activation_dialog_container")
        
        layout = QVBoxLayout(self.main_container)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(18)
        
        # 标题区域
        self._create_title_area(layout)
        
        # 机器信息区域
        self._create_machine_info_area(layout)
        
        # 注册码输入区域
        self._create_license_input_area(layout)
        
        # 按钮区域
        self._create_button_area(layout)
        
        # 设置主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.main_container)
    
    def _create_title_area(self, layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(8)
        
        # 主标题
        title_label = QLabel("软件激活")
        title_label.setObjectName("activation_title")
        title_label.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("请输入注册码以激活—脑机接口康复训练系统")
        subtitle_label.setObjectName("activation_subtitle")
        subtitle_label.setFont(QFont("Microsoft YaHei", 11))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setWordWrap(True)
        title_layout.addWidget(subtitle_label)
        
        layout.addWidget(title_frame)
    
    def _create_machine_info_area(self, layout):
        """创建机器信息区域"""
        info_group = QGroupBox("详细信息")
        info_group.setObjectName("activation_group")
        info_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        
        info_layout = QGridLayout(info_group)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(8)
        
        # 硬件ID
        hw_id_label = QLabel("软件ID:")
        hw_id_label.setFont(QFont("Microsoft YaHei", 10))
        hw_id_label.setObjectName("activation_label")
        hw_id_value = QLabel(self.machine_info['formatted_id'])
        hw_id_value.setFont(QFont("Consolas", 11, QFont.Weight.Bold))
        hw_id_value.setObjectName("activation_value")
        hw_id_value.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        
        info_layout.addWidget(hw_id_label, 0, 0)
        info_layout.addWidget(hw_id_value, 0, 1)
        
        # 操作系统
        os_label = QLabel("操作系统:")
        os_label.setFont(QFont("Microsoft YaHei", 10))
        os_label.setObjectName("activation_label")
        os_value = QLabel(self.machine_info['os_info'])
        os_value.setFont(QFont("Microsoft YaHei", 10))
        os_value.setObjectName("activation_value")
        
        info_layout.addWidget(os_label, 1, 0)
        info_layout.addWidget(os_value, 1, 1)
        
        # 处理器架构
        arch_label = QLabel("架构:")
        arch_label.setFont(QFont("Microsoft YaHei", 10))
        arch_label.setObjectName("activation_label")
        arch_value = QLabel(self.machine_info['machine'])
        arch_value.setFont(QFont("Microsoft YaHei", 10))
        arch_value.setObjectName("activation_value")
        
        info_layout.addWidget(arch_label, 2, 0)
        info_layout.addWidget(arch_value, 2, 1)
        
        # 设置列宽
        info_layout.setColumnStretch(0, 0)
        info_layout.setColumnStretch(1, 1)
        
        layout.addWidget(info_group)
    
    def _create_license_input_area(self, layout):
        """创建注册码输入区域"""
        license_group = QGroupBox("注册码")
        license_group.setObjectName("activation_group")
        license_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        
        license_layout = QVBoxLayout(license_group)
        license_layout.setContentsMargins(15, 15, 15, 15)
        license_layout.setSpacing(8)
        
        # 说明文字
        instruction_label = QLabel("请联系软件供应商获取注册码并输入（格式：XXXXX-XXXXX-XXXXX-XXXXX-X）")
        instruction_label.setFont(QFont("Microsoft YaHei", 9))
        instruction_label.setObjectName("activation_instruction")
        license_layout.addWidget(instruction_label)
        
        # 注册码输入框
        self.license_input = QLineEdit()
        self.license_input.setPlaceholderText("输入注册码...")
        self.license_input.setFont(QFont("Consolas", 12))
        self.license_input.setFixedHeight(45)
        self.license_input.setObjectName("activation_input")
        self.license_input.setMaxLength(25)  # 21位+4个分隔符
        self.license_input.textChanged.connect(self._on_license_text_changed)
        self.license_input.returnPressed.connect(self._on_activate_clicked)
        license_layout.addWidget(self.license_input)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("Microsoft YaHei", 9))
        self.status_label.setObjectName("activation_status")
        self.status_label.setWordWrap(True)
        license_layout.addWidget(self.status_label)
        
        layout.addWidget(license_group)
    
    def _create_button_area(self, layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)
        
        # 弹性空间
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("activation_cancel_btn")
        cancel_btn.setFont(QFont("Microsoft YaHei", 11))
        cancel_btn.setFixedSize(80, 40)
        cancel_btn.clicked.connect(self._on_cancel_clicked)
        button_layout.addWidget(cancel_btn)
        
        # 激活按钮
        self.activate_btn = QPushButton("激活")
        self.activate_btn.setObjectName("activation_activate_btn")
        self.activate_btn.setFont(QFont("Microsoft YaHei", 11))
        self.activate_btn.setFixedSize(80, 40)
        self.activate_btn.setEnabled(False)
        self.activate_btn.clicked.connect(self._on_activate_clicked)
        button_layout.addWidget(self.activate_btn)
        
        layout.addWidget(button_frame)
    
    def _setup_style(self):
        """设置样式 - 根据当前主题动态生成"""
        theme_config = self.theme_manager.get_theme_config(self.current_theme)
        
        # 根据主题生成样式
        stylesheet = self._generate_activation_stylesheet(theme_config)
        self.setStyleSheet(stylesheet)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(10)
        shadow.setColor(QColor(0, 0, 0, 30))
        self.main_container.setGraphicsEffect(shadow)
    
    def _generate_activation_stylesheet(self, theme_config):
        """根据主题配置生成激活对话框样式表"""
        return f"""
            QDialog {{
                background: transparent;
            }}
            
            /* 主容器 */
            QFrame#activation_dialog_container {{
                background-color: {theme_config['bg_glass']};
                border: 2px solid {theme_config['neutral_200']};
                border-radius: 16px;
            }}
            
            /* 标题 */
            QLabel#activation_title {{
                color: {theme_config['neutral_800']};
                margin: 10px 0;
            }}
            
            QLabel#activation_subtitle {{
                color: {theme_config['neutral_500']};
                margin-bottom: 10px;
            }}
            
            /* 分组框 */
            QGroupBox#activation_group {{
                font-weight: bold;
                color: {theme_config['neutral_700']};
                border: 2px solid {theme_config['neutral_200']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: {theme_config['bg_secondary']};
            }}
            
            QGroupBox#activation_group::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                background: transparent;
                border-radius: 4px;
            }}
            
            /* 标签 */
            QLabel#activation_label {{
                color: {theme_config['text_secondary']};
                font-weight: 500;
            }}
            
            /* 值标签 */
            QLabel#activation_value {{
                color: {theme_config['success_color']};
                font-weight: 600;
                padding: 5px;
                background: {theme_config['bg_secondary']};
                border: 1px solid {theme_config['border_color']};
                border-radius: 4px;
            }}
            
            QLabel#activation_instruction {{
                color: {theme_config['neutral_600']};
                font-style: italic;
            }}
            
            /* 输入框 */
            QLineEdit#activation_input {{
                padding: 12px 15px;
                border: 2px solid {theme_config['neutral_200']};
                border-radius: 8px;
                background: {theme_config['bg_primary']};
                color: {theme_config['neutral_800']};
                font-size: 12px;
                font-family: 'Consolas', monospace;
            }}
            
            QLineEdit#activation_input:focus {{
                border-color: {theme_config['primary_color']};
            }}
            
            /* 状态标签 */
            QLabel#activation_status {{
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: 500;
                margin: 5px 0;
            }}
            
            QLabel#activation_status[status="success"] {{
                background: {theme_config['bg_secondary']};
                color: {theme_config['success_color']};
                border: 1px solid {theme_config['border_color']};
            }}
            
            QLabel#activation_status[status="error"] {{
                background: {theme_config['bg_secondary']};
                color: {theme_config['danger_color']};
                border: 1px solid {theme_config['border_color']};
            }}
            
            QLabel#activation_status[status="info"] {{
                background: {theme_config['bg_secondary']};
                color: {theme_config['primary_color']};
                border: 1px solid {theme_config['border_color']};
            }}
            
            /* 按钮 */
            QPushButton#activation_cancel_btn {{
                background: {theme_config['neutral_100']};
                color: {theme_config['neutral_600']};
                border: 2px solid {theme_config['neutral_200']};
                border-radius: 8px;
                font-weight: 600;
            }}
            
            QPushButton#activation_cancel_btn:hover {{
                background: {theme_config['neutral_200']};
                color: {theme_config['neutral_700']};
            }}
            
            QPushButton#activation_activate_btn {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {theme_config['primary_color']}, stop:1 {theme_config['primary_dark']});
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: 600;
            }}
            
            QPushButton#activation_activate_btn:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {theme_config['primary_light']}, stop:1 {theme_config['primary_color']});
            }}
            
            QPushButton#activation_activate_btn:disabled {{
                background: {theme_config['neutral_300']};
                color: {theme_config['neutral_500']};
            }}
        """
    
    def _on_license_text_changed(self, text):
        """注册码文本变化处理"""
        # 只保留字母和数字字符，过滤掉汉字和特殊符号
        clean_text = ''.join(c for c in text.replace('-', '').replace(' ', '') if c.isascii() and c.isalnum()).upper()
        
        # 限制最多21个字符
        if len(clean_text) > 21:
            clean_text = clean_text[:21]
        
        # 添加分隔符格式化
        formatted = ''
        for i, char in enumerate(clean_text):
            if i > 0 and i % 5 == 0:
                formatted += '-'
            formatted += char
        
        # 避免无限循环，只有在格式化后的文本与输入不同时才更新
        if formatted != text:
            cursor_pos = self.license_input.cursorPosition()
            self.license_input.blockSignals(True)  # 阻止信号，避免递归
            self.license_input.setText(formatted)
            self.license_input.blockSignals(False)  # 恢复信号
            
            # 调整光标位置 - 考虑添加的分隔符
            new_cursor_pos = min(cursor_pos, len(formatted))
            self.license_input.setCursorPosition(new_cursor_pos)
        
        # 检查输入完整性
        clean_input = self.license_input.text().replace('-', '').replace(' ', '')
        if len(clean_input) >= 21:
            self.activate_btn.setEnabled(True)
            self.status_label.setText("✓ 注册码格式正确，可以激活")
            self.status_label.setProperty("status", "success")
        else:
            self.activate_btn.setEnabled(False)
            self.status_label.setText("⚠ 请输入完整的21位注册码")
            self.status_label.setProperty("status", "info")
        
        # 刷新样式
        self.status_label.style().unpolish(self.status_label)
        self.status_label.style().polish(self.status_label)
    

    
    def _on_activate_clicked(self):
        """激活按钮点击处理"""
        license_key = self.license_input.text().strip()
        
        if not license_key:
            self.status_label.setText("❌ 请输入注册码")
            self.status_label.setProperty("status", "error")
            self.status_label.style().unpolish(self.status_label)
            self.status_label.style().polish(self.status_label)
            return
        
        # 显示激活中状态
        self.activate_btn.setText("激活中...")
        self.activate_btn.setEnabled(False)
        self.status_label.setText("🔄 正在验证注册码...")
        self.status_label.setProperty("status", "info")
        self.status_label.style().unpolish(self.status_label)
        self.status_label.style().polish(self.status_label)
        
        # 使用定时器延迟执行激活（避免UI卡顿）
        QTimer.singleShot(100, lambda: self._perform_activation(license_key))
    
    def _perform_activation(self, license_key):
        """执行激活操作"""
        try:
            success, message = license_system.activate_with_license(license_key)
            
            if success:
                self.status_label.setText(f"✅ {message}")
                self.status_label.setProperty("status", "success")
                
                # 立即关闭对话框，避免延迟关闭影响父窗口渲染
                self.accept()
            else:
                self.status_label.setText(f"❌ {message}")
                self.status_label.setProperty("status", "error")
                
                # 恢复按钮状态
                self.activate_btn.setText("激活")
                self.activate_btn.setEnabled(True)
            
            # 刷新样式
            self.status_label.style().unpolish(self.status_label)
            self.status_label.style().polish(self.status_label)
            
        except Exception as e:
            self.status_label.setText(f"❌ 激活过程出错: {str(e)}")
            self.status_label.setProperty("status", "error")
            self.status_label.style().unpolish(self.status_label)
            self.status_label.style().polish(self.status_label)
            
            # 恢复按钮状态
            self.activate_btn.setText("激活")
            self.activate_btn.setEnabled(True)
    
    def _on_cancel_clicked(self):
        """取消按钮点击处理 - 关闭激活窗口"""
        print("用户取消激活")
        self.reject()
    
    def get_machine_info_text(self) -> str:
        """获取机器信息文本（用于复制）"""
        return f"""硬件ID: {self.machine_info['formatted_id']}
操作系统: {self.machine_info['os_info']}
架构: {self.machine_info['machine']}
处理器: {self.machine_info['processor']}"""
    
    def paintEvent(self, event):
        """自定义绘制事件 - 备用方案，用于在透明背景有问题时使用"""
        # 如果透明背景工作正常，这个方法不会被调用
        # 只有在移除透明背景属性时才会启用
        super().paintEvent(event)
        
        # 可选：绘制圆角背景
        # painter = QPainter(self)
        # painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        # 
        # # 创建圆角路径
        # path = QPainterPath()
        # path.addRoundedRect(self.rect(), 16, 16)
        # 
        # # 绘制背景
        # painter.fillPath(path, QBrush(QColor(255, 255, 255, 250)))
        # 
        # painter.end()
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 开始拖拽"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 执行拖拽"""
        if event.buttons() == Qt.MouseButton.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件 - 结束拖拽"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = QPoint()
            # 拖拽结束后，确保窗口状态正常
            self._ensure_window_state()
            event.accept()
    
    def _ensure_window_state(self):
        """确保窗口状态正常 - 拖拽后调用"""
        try:
            # 强制更新布局
            self.updateGeometry()
            self.update()
            
            # 确保所有子控件都能正常接收事件
            for child in self.findChildren(QPushButton):
                child.setEnabled(child.isEnabled())  # 刷新按钮状态
                
        except Exception as e:
            print(f"确保窗口状态失败: {e}") 