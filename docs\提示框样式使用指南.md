# 提示框样式使用指南

## 📋 文档概述

**文档目的**：详细记录系统中所有提示框的样式设置、使用方法和技术实现，为后续页面开发提供标准参考。

**创建时间**：2025-01-27  
**适用版本**：脑机接口康复训练系统 v1.0  
**维护人员**：开发团队

---

## 🎨 提示框类型和样式

### 1. 信息提示框 (Information)
**用途**：显示成功操作、一般信息提示
**样式特点**：蓝色主题，圆角背景，"确定"按钮

### 2. 警告提示框 (Warning)  
**用途**：显示警告信息、需要用户注意的内容
**样式特点**：橙色主题，圆角背景，"确定"按钮

### 3. 错误提示框 (Critical)
**用途**：显示错误信息、操作失败提示
**样式特点**：红色主题，圆角背景，"确定"按钮

### 4. 确认提示框 (Question)
**用途**：需要用户确认的操作
**样式特点**：圆角背景，"是"/"否"按钮或自定义按钮组合

---

## 🔧 技术实现

### 核心文件位置
- **主要实现文件**：`ui/components/themed_message_box.py`
- **样式定义文件**：`ui/themes/theme_manager.py`
- **使用示例**：`ui/pages/patients_page.py`

### 自定义消息框类
```python
class ThemedMessageBox(QDialog):
    """自定义圆角消息框"""
    
    def __init__(self, parent=None, title="", text="", icon_type="information", buttons=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 200)
        
        # 设置无边框窗口
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        
        # 应用全局样式
        if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
            self.setStyleSheet(parent.styleSheet())
```

### UI结构设计
```python
def _init_ui(self, title, text, icon_type, buttons):
    # 主容器 - 提供圆角背景
    main_container = QFrame()
    main_container.setObjectName("discharge_dialog_container")  # 统一样式
    
    layout = QVBoxLayout(main_container)
    layout.setContentsMargins(24, 24, 24, 24)
    layout.setSpacing(16)
    
    # 标题
    title_label = QLabel(title)
    title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
    title_label.setObjectName("dialog_title")
    title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    
    # 消息内容
    message_label = QLabel(text)
    message_label.setFont(QFont("Microsoft YaHei", 12))
    message_label.setObjectName("dialog_info")
    message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    message_label.setWordWrap(True)
    
    # 按钮区域
    button_layout = QHBoxLayout()
    button_layout.addStretch()
    
    # 根据按钮类型创建按钮...
```

---

## 📖 使用方法

### 导入方式
```python
from ui.components.themed_message_box import (
    show_information,
    show_warning, 
    show_critical,
    show_question
)
```

### 1. 信息提示框
```python
# 基本用法
show_information(self, "成功", "患者信息保存成功！")

# 使用场景
- 操作成功提示
- 数据保存成功
- 功能完成通知
```

### 2. 警告提示框
```python
# 基本用法
show_warning(self, "警告", "请先选择要编辑的患者")

# 使用场景
- 操作前提醒
- 数据验证失败
- 权限不足提示
```

### 3. 错误提示框
```python
# 基本用法
show_critical(self, "错误", "网络连接失败，请检查网络设置")

# 使用场景
- 系统错误
- 网络错误
- 数据库错误
- 文件操作失败
```

### 4. 确认提示框
```python
# 基本用法 - 是/否
result = show_question(self, "确认删除", "确定要删除这条记录吗？")
if result == QMessageBox.StandardButton.Yes:
    # 用户点击"是"
    pass

# 自定义按钮组合
result = show_question(
    self, 
    "保存确认", 
    "是否保存当前修改？",
    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel
)

# 使用场景
- 删除确认
- 保存确认
- 退出确认
- 覆盖确认
```

---

## 🎯 样式配置

### 全局样式表设置
**文件位置**：`ui/themes/theme_manager.py`

```css
/* 对话框容器样式 */
QFrame#discharge_dialog_container {
    background-color: {theme_config['bg_secondary']};
    border: 1px solid {theme_config['border_color']};
    border-radius: 12px;
}

/* 对话框标题样式 */
QLabel#dialog_title {
    color: {theme_config['text_primary']};
    font-weight: bold;
}

/* 对话框信息样式 */
QLabel#dialog_info {
    color: {theme_config['text_secondary']};
}

/* 按钮样式 */
QPushButton#btn_primary {
    background: {theme_config['gradient_primary']};
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
}

QPushButton#btn_secondary {
    background-color: {theme_config['bg_tertiary']};
    color: {theme_config['text_primary']};
    border: 1px solid {theme_config['border_color']};
    border-radius: 8px;
    padding: 12px 24px;
}
```

### 主题适配
- **科技主题**：深色背景 + 蓝色边框
- **医疗主题**：浅色背景 + 灰色边框
- **自动切换**：跟随系统主题设置

---

## 🔍 技术要点

### 1. 窗口属性设置
```python
# 必须的窗口属性组合
self.setWindowFlags(
    Qt.WindowType.FramelessWindowHint |      # 无边框
    Qt.WindowType.Dialog |                   # 对话框类型
    Qt.WindowType.WindowStaysOnTopHint       # 保持在顶层
)
self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)  # 透明背景
self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)     # 无系统背景
```

### 2. 容器结构
```python
# 关键：使用主容器提供圆角背景
main_container = QFrame()
main_container.setObjectName("discharge_dialog_container")  # 统一样式名

# 对话框布局
dialog_layout = QVBoxLayout(self)
dialog_layout.setContentsMargins(0, 0, 0, 0)
dialog_layout.addWidget(main_container)
```

### 3. 按钮处理
```python
def _set_result_and_close(self, result):
    """设置结果并关闭对话框"""
    self.result_value = result
    if result in [QMessageBox.StandardButton.Ok, QMessageBox.StandardButton.Yes]:
        self.accept()
    else:
        self.reject()
```

---

## 📋 开发规范

### 1. 命名规范
- 函数名：`show_[type]` (如：show_information)
- 样式名：`dialog_[element]` (如：dialog_title)
- 容器名：`discharge_dialog_container` (统一使用)

### 2. 字体规范
- 标题：16px 加粗 Microsoft YaHei
- 内容：12px 常规 Microsoft YaHei  
- 按钮：12px 常规 Microsoft YaHei

### 3. 尺寸规范
- 默认大小：400x200px
- 内边距：24px
- 按钮间距：16px
- 圆角半径：12px

### 4. 按钮规范
- 主要按钮：`btn_primary` (蓝色渐变)
- 次要按钮：`btn_secondary` (灰色边框)
- 中文文字：确定、取消、是、否

---

## 🚀 使用示例

### 完整使用示例
```python
# 在页面类中使用
class SomePage(BasePage):
    def save_data(self):
        try:
            # 保存数据逻辑
            result = self.service.save(data)
            
            if result['success']:
                # 成功提示
                show_information(self, "成功", "数据保存成功！")
            else:
                # 错误提示
                error_msg = result.get('error', '保存失败')
                show_critical(self, "保存失败", error_msg)
                
        except Exception as e:
            # 异常提示
            show_critical(self, "错误", f"保存时发生错误：{str(e)}")
    
    def delete_item(self):
        # 删除确认
        result = show_question(self, "确认删除", "确定要删除这条记录吗？\n删除后无法恢复。")
        
        if result == QMessageBox.StandardButton.Yes:
            try:
                self.service.delete(item_id)
                show_information(self, "成功", "记录删除成功！")
                self.refresh_list()
            except Exception as e:
                show_critical(self, "删除失败", f"删除时发生错误：{str(e)}")
```

---

## 📝 更新日志

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|---------|--------|
| 2025-01-27 | v1.0 | 初始版本，完整的提示框样式使用指南 | 开发团队 |

---

---

## 🚀 快速参考

### 导入语句
```python
from ui.components.themed_message_box import show_information, show_warning, show_critical, show_question
from PySide6.QtWidgets import QMessageBox  # 用于按钮常量
```

### 常用代码片段
```python
# 成功提示
show_information(self, "成功", "操作完成！")

# 警告提示
show_warning(self, "警告", "请检查输入内容")

# 错误提示
show_critical(self, "错误", "操作失败，请重试")

# 确认对话框
if show_question(self, "确认", "确定要执行此操作吗？") == QMessageBox.StandardButton.Yes:
    # 用户确认
    pass
```

### 样式对象名
- 容器：`discharge_dialog_container`
- 标题：`dialog_title`
- 内容：`dialog_info`
- 主按钮：`btn_primary`
- 次按钮：`btn_secondary`

---

**注意**：
1. 所有新页面都应使用这套统一的提示框样式
2. 不要直接使用QMessageBox，使用封装好的便捷函数
3. 确保在使用前正确导入相关模块
4. 遵循命名和样式规范，保持系统一致性
