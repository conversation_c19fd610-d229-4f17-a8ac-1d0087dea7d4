"""
脑电信号预处理模块

实现三级串联预处理流程：传统滤波 + RLS自适应滤波 + <PERSON><PERSON>滤波
专门针对低信噪比脑电信号的实时处理，满足32ms处理要求

架构特点：
- 传统滤波：实时处理，基础去噪和标准化
- RLS自适应滤波：快速收敛，实时自适应去噪
- Ka<PERSON>滤波：状态估计，精细去噪

性能指标：
- 总延迟：~25ms
- 输出频率：31.25次/秒
- 实时性：优秀
- 内存效率：优秀
"""

from .preprocessing_config import PreprocessingConfig, QuickConfigs
from .traditional_filter import TraditionalFilterProcessor
from .preprocessing_pipeline import EEGPreprocessingPipeline
from .realtime_integration_manager import RealtimeIntegrationManager

__all__ = [
    'PreprocessingConfig',
    'QuickConfigs',
    'TraditionalFilterProcessor',
    'EEGPreprocessingPipeline',
    'RealtimeIntegrationManager'
]
