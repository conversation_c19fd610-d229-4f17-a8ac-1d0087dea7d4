# -*- coding: utf-8 -*-
"""
统一路径管理器
Unified Path Manager

解决开发环境和PyInstaller打包环境的路径差异问题
提供统一的资源文件路径获取接口
"""

import sys
import os
from pathlib import Path
from typing import Optional, Union


class PathManager:
    """统一路径管理器"""
    
    _instance = None
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self._base_dir = self._get_base_directory()
            self.initialized = True
    
    def _get_base_directory(self) -> Path:
        """获取程序基础目录"""
        try:
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller打包后的环境
                # sys._MEIPASS是PyInstaller解压临时文件的目录，这里包含了所有资源文件
                meipass_dir = Path(sys._MEIPASS)
                exe_dir = Path(sys.executable).parent
                print(f"📁 检测到PyInstaller环境，可执行文件目录：{exe_dir}")
                print(f"📁 临时解压目录：{meipass_dir}")
                # 在打包环境中，资源文件都在_MEIPASS目录中
                return meipass_dir
            else:
                # 开发环境：使用main.py所在的目录作为基础目录
                if hasattr(sys.modules['__main__'], '__file__'):
                    main_file = Path(sys.modules['__main__'].__file__)
                    base_dir = main_file.parent
                else:
                    # 备用方案：使用当前工作目录
                    base_dir = Path.cwd()
                return base_dir
        except Exception as e:
            # 异常情况下的备用方案
            print(f"⚠️ 获取基础目录失败: {e}")
            fallback_dir = Path.cwd()
            print(f"📁 使用备用目录: {fallback_dir}")
            return fallback_dir
    
    def get_database_path(self) -> Path:
        """获取数据库文件路径"""
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：根据PyInstaller的--add-data配置，数据库应该在_internal目录下
            # 但实际测试发现可能在不同位置，我们需要检查多个可能的位置
            possible_paths = [
                self._base_dir / "ShuJu.db",  # 根目录
                self._base_dir / "_internal" / "ShuJu.db",  # _internal目录
                Path(sys._MEIPASS) / "ShuJu.db",  # 临时解压目录
            ]
            for db_path in possible_paths:
                if db_path.exists():
                    return db_path
            # 如果都不存在，返回默认路径
            db_path = self._base_dir / "_internal" / "ShuJu.db"
        else:
            # 开发环境：数据库在根目录
            db_path = self._base_dir / "ShuJu.db"
        return db_path
    
    def get_config_file_path(self) -> Path:
        """获取主配置文件路径"""
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：配置文件在根目录
            config_path = self._base_dir / "config.json"
        else:
            # 开发环境：配置文件在根目录
            config_path = self._base_dir / "config.json"
        return config_path
    
    def get_config_dir_path(self) -> Path:
        """获取配置目录路径"""
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：检查多个可能的位置
            possible_paths = [
                self._base_dir / "config",  # 根目录
                self._base_dir / "_internal" / "config",  # _internal目录
                Path(sys._MEIPASS) / "config",  # 临时解压目录
            ]
            for config_dir in possible_paths:
                if config_dir.exists():
                    return config_dir
            # 如果都不存在，返回默认路径
            config_dir = self._base_dir / "config"
        else:
            # 开发环境：配置目录在根目录
            config_dir = self._base_dir / "config"
        return config_dir
    
    def get_data_dir_path(self) -> Path:
        """获取数据目录路径"""
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：检查多个可能的位置
            possible_paths = [
                self._base_dir / "data",  # 根目录
                Path(sys._MEIPASS) / "data",  # 临时解压目录
            ]
            for data_dir in possible_paths:
                if data_dir.exists():
                    # print(f"📂 数据目录路径（找到）：{data_dir}")
                    return data_dir
            # 如果都不存在，返回临时解压目录中的data路径（PyInstaller环境）
            data_dir = Path(sys._MEIPASS) / "data"
            # print(f"📂 数据目录路径（默认）：{data_dir}")
        else:
            # 开发环境：数据目录在根目录
            data_dir = self._base_dir / "data"
            # print(f"📂 数据目录路径：{data_dir}")
        return data_dir

    def get_icons_dir_path(self) -> Path:
        """获取图标目录路径"""
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：检查多个可能的位置
            possible_paths = [
                self._base_dir / "icons",  # 根目录
                self._base_dir / "_internal" / "icons",  # _internal目录
                Path(sys._MEIPASS) / "icons",  # 临时解压目录
            ]
            for icons_dir in possible_paths:
                if icons_dir.exists():
                    return icons_dir
            # 如果都不存在，返回默认路径
            icons_dir = self._base_dir / "_internal" / "icons"
        else:
            # 开发环境：图标目录在根目录
            icons_dir = self._base_dir / "icons"
        return icons_dir

    def get_libs_dir_path(self) -> Path:
        """获取库文件目录路径"""
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：检查多个可能的位置
            possible_paths = [
                self._base_dir / "libs",  # 根目录
                self._base_dir / "_internal" / "libs",  # _internal目录
                Path(sys._MEIPASS) / "libs",  # 临时解压目录
            ]
            for libs_dir in possible_paths:
                if libs_dir.exists():
                    return libs_dir
            # 如果都不存在，返回默认路径
            libs_dir = self._base_dir / "_internal" / "libs"
        else:
            # 开发环境：库文件目录在根目录
            libs_dir = self._base_dir / "libs"
        return libs_dir
    
    def get_assets_dir_path(self) -> Path:
        """获取资源目录路径"""
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：资源目录在_internal目录下
            assets_dir = self._base_dir / "_internal" / "assets"
        else:
            # 开发环境：资源目录在根目录
            assets_dir = self._base_dir / "assets"
        return assets_dir
    
    def get_icon_path(self, icon_name: str) -> Path:
        """获取特定图标文件路径"""
        icon_path = self.get_icons_dir_path() / icon_name
        return icon_path
    
    def get_config_file_in_dir(self, filename: str) -> Path:
        """获取config目录下的特定配置文件路径"""
        config_file = self.get_config_dir_path() / filename
        return config_file
    
    def get_data_file(self, relative_path: str) -> Path:
        """获取data目录下的文件路径"""
        data_file = self.get_data_dir_path() / relative_path
        return data_file
    
    def get_lib_file(self, filename: str) -> Path:
        """获取libs目录下的库文件路径"""
        lib_file = self.get_libs_dir_path() / filename
        return lib_file
    
    def ensure_directory_exists(self, dir_path: Union[str, Path]) -> Path:
        """确保目录存在，如果不存在则创建"""
        path = Path(dir_path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def validate_file_exists(self, file_path: Union[str, Path]) -> bool:
        """验证文件是否存在"""
        return Path(file_path).exists()
    
    def validate_directory_exists(self, dir_path: Union[str, Path]) -> bool:
        """验证目录是否存在"""
        return Path(dir_path).is_dir()
    
    def get_relative_path(self, target_path: Union[str, Path]) -> Path:
        """获取相对于基础目录的相对路径"""
        target = Path(target_path)
        try:
            return target.relative_to(self._base_dir)
        except ValueError:
            # 如果无法计算相对路径，返回绝对路径
            return target
    
    def resolve_path(self, path: Union[str, Path]) -> Path:
        """解析路径，支持相对路径和绝对路径"""
        path = Path(path)
        if path.is_absolute():
            return path
        else:
            return self._base_dir / path
    
    def get_base_directory(self) -> Path:
        """获取基础目录（只读）"""
        return self._base_dir
    
    def debug_info(self) -> dict:
        """获取调试信息"""
        info = {
            "base_directory": str(self._base_dir),
            "is_packaged": hasattr(sys, '_MEIPASS'),
            "executable_path": sys.executable,
            "working_directory": str(Path.cwd()),
            "main_module": getattr(sys.modules.get('__main__'), '__file__', 'Unknown')
        }
        
        # 检查关键路径是否存在
        key_paths = {
            "database": self.get_database_path(),
            "config_file": self.get_config_file_path(),
            "config_dir": self.get_config_dir_path(),
            "data_dir": self.get_data_dir_path(),
            "icons_dir": self.get_icons_dir_path(),
            "libs_dir": self.get_libs_dir_path()
        }
        
        path_status = {}
        for name, path in key_paths.items():
            path_status[name] = {
                "path": str(path),
                "exists": path.exists(),
                "is_file": path.is_file() if path.exists() else False,
                "is_dir": path.is_dir() if path.exists() else False
            }
        
        info["path_status"] = path_status
        return info


# 全局路径管理器实例
path_manager = PathManager()


# 便捷函数
def get_database_path() -> Path:
    """获取数据库文件路径"""
    return path_manager.get_database_path()


def get_config_file_path() -> Path:
    """获取主配置文件路径"""
    return path_manager.get_config_file_path()


def get_config_dir_path() -> Path:
    """获取配置目录路径"""
    return path_manager.get_config_dir_path()


def get_data_dir_path() -> Path:
    """获取数据目录路径"""
    return path_manager.get_data_dir_path()


def get_icon_path(icon_name: str) -> Path:
    """获取图标文件路径"""
    return path_manager.get_icon_path(icon_name)


def get_config_file_in_dir(filename: str) -> Path:
    """获取config目录下的配置文件路径"""
    return path_manager.get_config_file_in_dir(filename)


def get_data_file(relative_path: str) -> Path:
    """获取data目录下的文件路径"""
    return path_manager.get_data_file(relative_path)


def get_lib_file(filename: str) -> Path:
    """获取libs目录下的库文件路径"""
    return path_manager.get_lib_file(filename)
