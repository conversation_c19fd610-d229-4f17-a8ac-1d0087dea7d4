"""
脑电数据处理器

负责将蓝牙接收的原始数据包转换为标准化的numpy数组格式
支持数据流缓冲和分片重组，解决蓝牙传输中的粘包分包问题
"""

import numpy as np
import logging
from typing import Optional, Tuple, List
import time


class EEGDataProcessor:
    """
    脑电数据处理器

    功能：
    1. 验证数据包格式（包头包尾）
    2. 解析24位有符号整数
    3. 转换为标准化numpy数组格式
    """

    # 数据包格式常量
    PACKET_SIZE = 100
    PACKET_HEADER = bytes([0x5A, 0xA5])  # 包头
    PACKET_FOOTER = bytes([0x0D, 0x0A])  # 包尾
    DATA_START_INDEX = 2  # 数据开始位置
    DATA_END_INDEX = 98   # 数据结束位置

    # 数据格式常量
    CHANNELS = 8          # 通道数
    GROUPS_PER_PACKET = 4 # 每包数据组数
    BYTES_PER_CHANNEL = 3 # 每通道字节数
    BYTES_PER_GROUP = CHANNELS * BYTES_PER_CHANNEL  # 每组字节数(24)

    def __init__(self):
        """初始化脑电数据处理器"""
        self.logger = logging.getLogger(__name__)

        # 初始化数据缓冲器
        self.data_buffer = EEGDataBuffer()

        self.logger.info("脑电数据处理器初始化完成")

    def process_data_stream(self, raw_data: bytes) -> List[np.ndarray]:
        """
        处理数据流（支持分片和粘包）

        Args:
            raw_data: 原始数据流（任意长度）

        Returns:
            List[np.ndarray]: 解析出的EEG数据数组列表
        """
        try:
            # 使用缓冲器提取完整数据包
            complete_packets = self.data_buffer.add_data(raw_data)

            # 处理每个完整的数据包
            eeg_data_list = []
            for packet in complete_packets:
                eeg_data = self._process_single_packet(packet)
                if eeg_data is not None:
                    eeg_data_list.append(eeg_data)

            return eeg_data_list

        except Exception as e:
            self.logger.error(f"数据流处理失败: {e}")
            return []

    def process_data_packet(self, raw_data: bytes) -> Optional[np.ndarray]:
        """
        处理单个数据包（兼容性方法）

        Args:
            raw_data: 100字节的原始数据包

        Returns:
            numpy数组 (8, 4) 或 None（如果数据无效）
        """
        return self._process_single_packet(raw_data)

    def _process_single_packet(self, raw_data: bytes) -> Optional[np.ndarray]:
        """
        处理单个完整数据包

        Args:
            raw_data: 100字节的原始数据包

        Returns:
            numpy数组 (8, 4) 或 None（如果数据无效）
            - 8通道
            - 4个样本点
            - float32格式
        """
        try:
            # 验证数据包格式
            if not self._validate_packet_format(raw_data):
                return None

            # 提取有效数据部分（96字节）
            data_section = raw_data[self.DATA_START_INDEX:self.DATA_END_INDEX]

            # 解析为8x4的数组
            eeg_data = self._parse_eeg_data(data_section)

            return eeg_data

        except Exception as e:
            self.logger.error(f"数据包处理失败: {e}")
            return None

    def _validate_packet_format(self, raw_data: bytes) -> bool:
        """
        验证数据包格式

        Args:
            raw_data: 原始数据包

        Returns:
            bool: 格式是否正确
        """
        # 检查数据包长度
        if len(raw_data) != self.PACKET_SIZE:
            self.logger.warning(f"数据包长度错误: {len(raw_data)}, 期望: {self.PACKET_SIZE}")
            return False

        # 检查包头
        if raw_data[:2] != self.PACKET_HEADER:
            self.logger.warning(f"包头错误: {raw_data[:2].hex()}, 期望: {self.PACKET_HEADER.hex()}")
            return False

        # 检查包尾
        if raw_data[-2:] != self.PACKET_FOOTER:
            self.logger.warning(f"包尾错误: {raw_data[-2:].hex()}, 期望: {self.PACKET_FOOTER.hex()}")
            return False

        return True

    def _parse_eeg_data(self, data_section: bytes) -> np.ndarray:
        """
        解析脑电数据

        Args:
            data_section: 96字节的数据部分

        Returns:
            numpy数组 (8, 4)
        """
        # 预分配结果数组
        eeg_data = np.zeros((self.CHANNELS, self.GROUPS_PER_PACKET), dtype=np.float32)

        # 逐组解析数据
        for group_idx in range(self.GROUPS_PER_PACKET):
            group_start = group_idx * self.BYTES_PER_GROUP
            group_data = data_section[group_start:group_start + self.BYTES_PER_GROUP]

            # 解析该组的8个通道数据
            for channel_idx in range(self.CHANNELS):
                channel_start = channel_idx * self.BYTES_PER_CHANNEL
                channel_bytes = group_data[channel_start:channel_start + self.BYTES_PER_CHANNEL]

                # 转换24位有符号整数
                channel_value = self._convert_24bit_to_int(channel_bytes)
                eeg_data[channel_idx, group_idx] = float(channel_value)

        return eeg_data

    def _convert_24bit_to_int(self, three_bytes: bytes) -> int:
        """
        将3字节转换为24位有符号整数

        Args:
            three_bytes: 3字节数据

        Returns:
            int: 有符号整数值
        """
        # 组合为24位无符号整数（大端序）
        unsigned_value = (three_bytes[0] << 16) | (three_bytes[1] << 8) | three_bytes[2]

        # 处理符号扩展（24位有符号数的范围：-8388608 到 8388607）
        if unsigned_value & 0x800000:  # 检查符号位
            # 负数：进行符号扩展
            signed_value = unsigned_value - 0x1000000  # 2^24
        else:
            # 正数：直接使用
            signed_value = unsigned_value

        return signed_value

    def get_data_info(self) -> dict:
        """
        获取数据格式信息

        Returns:
            dict: 数据格式信息
        """
        return {
            "channels": self.CHANNELS,
            "samples_per_packet": self.GROUPS_PER_PACKET,
            "output_shape": (self.CHANNELS, self.GROUPS_PER_PACKET),
            "output_dtype": "float32",
            "packet_size": self.PACKET_SIZE,
            "data_range": "24位有符号整数范围：-8388608 到 8388607"
        }

    def get_buffer_statistics(self) -> dict:
        """
        获取数据缓冲器统计信息

        Returns:
            dict: 缓冲器统计信息
        """
        return self.data_buffer.get_statistics()

    def reset_buffer(self):
        """重置数据缓冲器"""
    def get_invalid_packet_count(self) -> int:
        """获取累计无效包计数（包头/包尾错误等）"""
        return self.data_buffer.invalid_packets_total

    def reset_invalid_packet_count(self):
        """重置累计无效包计数"""
        self.data_buffer.invalid_packets_total = 0

        self.data_buffer.reset()


class EEGDataBuffer:
    """
    脑电数据流缓冲器

    解决蓝牙传输中的数据包分片和粘包问题
    维护数据流缓冲区，自动提取完整的100字节数据包
    """

    def __init__(self, max_buffer_size: int = 2048):
        """
        初始化数据缓冲器

        Args:
            max_buffer_size: 最大缓冲区大小（字节），防止内存泄漏
        """
        self.logger = logging.getLogger(__name__)

        # 缓冲区管理
        self.buffer = bytearray()
        self.max_buffer_size = max_buffer_size

        # 数据包格式常量
        self.packet_size = 100
        self.packet_header = bytes([0x5A, 0xA5])
        self.packet_footer = bytes([0x0D, 0x0A])

        # 性能与质量统计
        self.packets_extracted = 0
        self.bytes_processed = 0
        self.last_extract_time = 0
        self.invalid_packets_total = 0  # 累计的数据包格式错误（包头/包尾错误等）

        self.logger.info("脑电数据缓冲器初始化完成")

    def add_data(self, new_data: bytes) -> List[bytes]:
        """
        添加新数据并提取完整数据包

        Args:
            new_data: 新接收的数据

        Returns:
            List[bytes]: 提取到的完整数据包列表
        """
        start_time = time.perf_counter()

        # 添加新数据到缓冲区
        self.buffer.extend(new_data)
        self.bytes_processed += len(new_data)

        # 检查缓冲区大小，防止内存泄漏
        if len(self.buffer) > self.max_buffer_size:
            self._cleanup_buffer()

        # 提取完整数据包
        packets = self._extract_complete_packets()

        # 记录性能
        process_time = (time.perf_counter() - start_time) * 1000
        if packets:
            self.last_extract_time = process_time
            self.logger.debug(f"提取到{len(packets)}个数据包，处理时间: {process_time:.2f}ms")

        return packets

    def _extract_complete_packets(self) -> List[bytes]:
        """
        从缓冲区提取完整的数据包

        Returns:
            List[bytes]: 完整数据包列表
        """
        packets = []

        while len(self.buffer) >= self.packet_size:
            # 寻找包头位置
            header_pos = self.buffer.find(self.packet_header)

            if header_pos == -1:
                # 没有找到包头，清空缓冲区避免积累垃圾数据
                self.logger.warning("缓冲区中未找到包头，清空缓冲区")
                self.buffer.clear()
                break

            # 如果包头不在开始位置，丢弃包头前的数据
            if header_pos > 0:
                discarded = self.buffer[:header_pos]
                self.buffer = self.buffer[header_pos:]
                self.logger.debug(f"丢弃{len(discarded)}字节无效数据")

            # 检查是否有完整的数据包
            if len(self.buffer) >= self.packet_size:
                # 提取可能的数据包
                potential_packet = bytes(self.buffer[:self.packet_size])

                # 验证包尾
                if potential_packet[-2:] == self.packet_footer:
                    # 找到完整有效的数据包
                    packets.append(potential_packet)
                    self.buffer = self.buffer[self.packet_size:]
                    self.packets_extracted += 1
                    self.logger.debug(f"成功提取数据包#{self.packets_extracted}")
                else:
                    # 包尾不匹配，可能是错误的包头，跳过这个字节继续搜索
                    self.buffer = self.buffer[1:]
                    self.invalid_packets_total += 1
                    self.logger.debug("包尾不匹配，继续搜索")
            else:
                # 数据不够一个完整包，等待更多数据
                break

        return packets

    def _cleanup_buffer(self):
        """清理缓冲区，防止内存泄漏"""
        # 保留最后500字节，丢弃其余数据
        keep_size = 500
        if len(self.buffer) > keep_size:
            discarded_size = len(self.buffer) - keep_size
            self.buffer = self.buffer[-keep_size:]
            self.logger.warning(f"缓冲区过大，丢弃{discarded_size}字节数据")

    def get_statistics(self) -> dict:
        """
        获取缓冲器统计信息

        Returns:
            dict: 统计信息
        """
        return {
            "packets_extracted": self.packets_extracted,
            "bytes_processed": self.bytes_processed,
            "buffer_size": len(self.buffer),
            "last_extract_time_ms": self.last_extract_time,
            "extraction_rate": self.packets_extracted / max(1, self.bytes_processed / 100)
        }

    def reset(self):
        """重置缓冲器状态"""
        self.buffer.clear()
        self.packets_extracted = 0
        self.bytes_processed = 0
        self.last_extract_time = 0
        self.logger.info("数据缓冲器已重置")
