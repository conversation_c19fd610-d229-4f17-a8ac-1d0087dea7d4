# 脑机接口康复训练系统用户使用说明书

## 系统概述

脑机接口康复训练系统是一套专业的医疗康复设备，通过采集患者脑电信号，实时识别运动意念，结合电刺激治疗和VR视觉反馈，为脑卒中等神经系统疾病患者提供个性化的康复训练方案。

### 系统信息
- **系统名称**：脑机接口康复训练系统
- **软件版本**：基于PySide6开发
- **适用科室**：康复科、神经内科
- **设备类型**：医疗器械级康复训练设备

### 核心功能
- **脑电信号采集**：通过蓝牙脑电设备实时采集8通道脑电信号
- **运动意念识别**：使用机器学习算法识别患者运动想象意念
- **电刺激治疗**：根据识别结果触发功能性电刺激
- **VR视觉反馈**：提供实时视觉反馈增强训练效果
- **数据管理**：完整的患者信息管理和治疗数据记录

## 系统要求

### 硬件要求
- **操作系统**：Windows 10/11 (64位)
- **内存**：最低4GB，推荐8GB以上
- **存储空间**：至少2GB可用磁盘空间
- **显示器**：分辨率1200×800以上，推荐1400×900
- **蓝牙**：支持BLE 4.0以上的蓝牙适配器
- **USB接口**：用于连接电刺激设备

### 外部设备
- **脑电采集设备**：BLE5201或兼容的8通道脑电设备
- **电刺激设备**：C2电刺激器或兼容设备
- **VR反馈系统**：配套VR软件（可选）

### 软件依赖
系统已集成所有必要的运行库，无需额外安装。

## 系统启动

### 启动方式
1. **桌面快捷方式**：双击桌面上的"脑机接口康复训练系统"图标
2. **开始菜单**：开始菜单 → 所有程序 → 脑机接口康复训练系统
3. **直接运行**：进入安装目录，双击`main.exe`文件

### 启动过程
1. 系统自动检查运行环境和依赖项
2. 加载系统配置和用户数据
3. 初始化设备连接模块
4. 显示用户登录界面

*此处需要截图：[系统启动画面]*

### 常见启动问题
- **启动缓慢**：首次启动需要初始化数据库，请耐心等待
- **启动失败**：检查是否有杀毒软件阻止，将程序添加到白名单
- **缺少组件**：确保安装了Visual C++ Redistributable

## 用户登录

### 登录界面
系统启动后显示登录窗口，包含：
- **系统标题**：脑机接口康复训练系统
- **用户选择**：下拉菜单选择用户账户
- **密码输入**：输入对应用户密码
- **登录按钮**：确认登录
- **退出按钮**：退出系统

*此处需要截图：[登录界面]*

### 默认用户账户

| 用户名 | 默认密码 | 角色 | 主要权限 |
|--------|----------|------|----------|
| admin | admin123 | 管理员 | 所有功能权限，用户管理，系统设置 |
| doctor | doctor123 | 医生 | 患者管理，治疗操作，报告查看 |
| operator | operator123 | 操作员 | 治疗操作，基础查看功能 |

### 登录步骤
1. 从下拉菜单中选择用户名
2. 在密码框中输入对应密码
3. 点击"登录"按钮或按回车键
4. 系统验证成功后自动进入主界面

### 安全提示
- 首次登录后请及时修改默认密码
- 密码区分大小写，请注意输入
- 系统会记录登录时间和操作日志
- 长时间无操作会自动锁定，需重新登录

## 主界面介绍

### 界面布局
主界面采用现代化医疗软件设计，分为三个主要区域：

#### 1. 左侧导航栏
- **系统Logo**：显示"BCI"标识
- **功能菜单**：包含所有主要功能模块
- **用户信息**：显示当前登录用户
- **折叠控制**：可收起/展开导航栏

#### 2. 顶部状态栏
- **页面标题**：显示当前页面名称（中英文）
- **设备状态**：实时显示设备连接状态
- **主题切换**：医疗主题/科技主题切换
- **系统时间**：显示当前系统时间

#### 3. 主内容区域
- **页面内容**：显示当前选中功能的详细界面
- **操作面板**：各种功能操作按钮和控件
- **数据展示**：图表、列表、表单等数据展示

*此处需要截图：[主界面整体布局]*

### 导航菜单功能

| 图标 | 功能名称 | 英文名称 | 功能说明 |
|------|----------|----------|----------|
| 👥 | 患者管理 | Patient Management | 患者信息录入、查看、编辑 |
| 🔬 | 治疗系统 | Treatment System | 脑机接口治疗操作 |
| 📊 | 报告分析 | Report Analysis | 治疗数据分析和报告 |
| 👤 | 用户管理 | User Management | 系统用户账户管理 |
| ⚙️ | 系统设置 | System Settings | 系统参数配置 |
| 🚪 | 退出系统 | Exit System | 安全退出系统 |

### 主题切换
系统提供两种界面主题：
- **医疗主题**：柔和的蓝绿色调，适合医疗环境
- **科技主题**：现代化深色主题，科技感强

切换方法：点击顶部状态栏的主题切换按钮

## 患者管理

### 功能概述
患者管理模块是系统的核心模块之一，用于管理所有患者的基本信息、治疗记录和康复进度跟踪。

### 患者列表界面

#### 界面元素
- **搜索框**：支持按患者姓名、编号搜索
- **筛选器**：按在院状态筛选患者
- **新增按钮**：添加新患者
- **患者卡片**：以卡片形式展示患者信息
- **分页控制**：支持大量数据分页浏览

*此处需要截图：[患者列表界面]*

#### 患者卡片信息
每个患者卡片显示：
- **头像**：患者姓名首字母圆形头像
- **基本信息**：姓名、编号、性别、年龄
- **状态标识**：在院/出院状态
- **操作按钮**：查看详情、开始治疗

### 新增患者

#### 操作步骤
1. 点击患者列表页面的"新增患者"按钮
2. 在弹出的对话框中填写患者信息
3. 点击"保存"按钮完成添加

#### 必填信息
- **患者姓名**：患者真实姓名
- **患者编号**：唯一标识，不可重复
- **性别**：男/女
- **年龄**：患者年龄

#### 可选信息
- **联系电话**：患者或家属联系方式
- **诊断信息**：疾病诊断结果
- **科室**：所属科室
- **主治医生**：负责医生姓名
- **备注**：其他相关信息

*此处需要截图：[新增患者对话框]*

### 患者详情查看

#### 查看方式
- 点击患者卡片进入详情页面
- 详情页面包含多个标签页

#### 基本信息标签
显示患者的完整基本信息，包括：
- 个人信息（姓名、性别、年龄等）
- 医疗信息（诊断、科室、医生等）
- 联系信息（电话、地址等）
- 入院信息（入院时间、状态等）

#### 治疗记录标签
显示患者的历史治疗记录：
- 治疗日期和时间
- 治疗时长
- 治疗参数设置
- 治疗效果数据
- 操作医生信息

#### 康复进度标签
显示患者的康复进度分析：
- 治疗次数统计
- 准确率变化趋势图
- 康复效果评估
- 进度对比分析

*此处需要截图：[患者详情界面]*

### 编辑患者信息

#### 编辑权限
- 管理员：可编辑所有信息
- 医生：可编辑医疗相关信息
- 操作员：只能查看，不能编辑

#### 编辑步骤
1. 在患者详情页面点击"编辑"按钮
2. 修改需要更新的信息
3. 点击"保存"确认修改
4. 系统自动记录修改历史

### 开始治疗
1. 在患者列表或详情页面点击"开始治疗"按钮
2. 系统自动跳转到治疗系统页面
3. 患者信息自动带入治疗界面
4. 可以立即开始治疗流程

## 治疗系统

### 功能概述
治疗系统是软件的核心功能模块，集成了脑电信号采集、实时信号处理、运动意念识别、电刺激治疗和VR反馈等完整的脑机接口康复治疗流程。

### 治疗界面布局

治疗系统界面分为多个功能区域：

#### 1. 患者信息区（顶部）
- **当前患者**：显示正在治疗的患者基本信息
- **治疗时间**：显示治疗开始时间和持续时间
- **治疗状态**：显示当前治疗阶段状态

#### 2. 设备状态区
显示三个主要设备的连接状态：
- **脑电设备**：蓝牙连接状态（绿色=已连接，红色=未连接）
- **电刺激设备**：串口连接状态
- **VR系统**：UDP通信状态

#### 3. 治疗参数区
包含两组参数调节器：

**脑机接口参数**
- **难度等级**：1-5级，影响触发敏感度
- **触发阈值**：0.55-0.95，控制识别准确度要求
- **分类器权重**：性能优先/平均/自定义

**电刺激参数**
- **电流强度**：A通道和B通道独立设置（1-100mA）
- **刺激频率**：2-160Hz
- **脉冲宽度**：10-500μs
- **工作时间**：0-30秒
- **休息时间**：0-16秒

#### 4. 实时监测区
- **分类准确率**：实时显示当前识别准确率
- **触发统计**：成功触发次数/总触发次数
- **治疗倒计时**：剩余治疗时间
- **脑电波形**：实时脑电信号波形显示

#### 5. 治疗控制区
- **开始治疗**：启动完整治疗流程
- **暂停治疗**：暂停当前治疗
- **停止治疗**：结束治疗并保存数据
- **紧急停止**：立即停止所有设备

*此处需要截图：[治疗系统主界面]*

### 设备连接

#### 脑电设备连接
1. **设备准备**
   - 确保BLE5201脑电设备已开机
   - 检查设备电量充足
   - 确认设备处于可发现状态

2. **连接步骤**
   - 点击"连接脑电设备"按钮
   - 系统自动搜索附近的BLE5201设备
   - 连接成功后状态指示器变为绿色
   - 开始接收实时脑电数据

3. **连接验证**
   - 检查波形显示是否正常
   - 确认信号质量良好
   - 测试数据接收稳定性

*此处需要截图：[脑电设备连接界面]*

#### 电刺激设备连接
1. **硬件连接**
   - 使用USB线连接C2电刺激器到电脑
   - 确认设备驱动已正确安装
   - 检查设备管理器中的COM端口号

2. **软件配置**
   - 进入系统设置→电刺激设备设置
   - 选择正确的COM端口（通常为COM7）
   - 设置通信参数

3. **连接测试**
   - 点击"连接电刺激设备"按钮
   - 系统建立串口连接
   - 连接成功后进行设备自检

*此处需要截图：[电刺激设备连接界面]*

#### VR系统连接
1. **系统要求**
   - 确保VR反馈软件正在运行
   - 检查网络连接正常
   - 确认端口配置正确

2. **连接配置**
   - VR系统地址：127.0.0.1（本机）
   - VR系统端口：3004
   - 本地监听端口：3005

3. **连接验证**
   - 系统自动检测VR软件状态
   - 发送测试指令验证通信
   - 确认反馈指令正常响应

### 治疗流程

#### 准备阶段
1. **患者准备**
   - 选择或输入患者信息
   - 确认患者身体状况适合治疗
   - 向患者说明治疗流程

2. **设备连接**
   - 按顺序连接脑电设备、电刺激设备、VR系统
   - 确认所有设备状态正常
   - 检查设备连接稳定性

3. **电极放置**
   - 按照标准位置放置脑电电极
   - 确保电极与皮肤良好接触
   - 放置电刺激电极到目标肌肉

4. **参数设置**
   - 根据患者情况调整治疗参数
   - 设置合适的难度等级和电流强度
   - 确认所有参数在安全范围内

#### 治疗阶段
1. **开始治疗**
   - 点击"开始治疗"按钮
   - 系统开始采集脑电信号
   - 启动实时信号处理

2. **运动想象训练**
   - 系统提示患者进行运动想象
   - 实时分析脑电信号特征
   - 计算运动意念分类结果

3. **反馈控制**
   - 根据识别结果触发VR反馈
   - 同时启动相应的电刺激
   - 记录治疗效果数据

4. **循环训练**
   - 按设定间隔重复训练循环
   - 动态调整难度等级
   - 监控患者疲劳状态

#### 结束阶段
1. **治疗完成**
   - 治疗时间到达后自动停止
   - 或手动点击"停止治疗"按钮
   - 系统停止所有设备

2. **数据保存**
   - 自动保存完整治疗数据
   - 生成治疗记录
   - 更新患者治疗历史

3. **设备清理**
   - 安全断开所有设备连接
   - 清洁电极和传感器
   - 设备状态复位

*此处需要截图：[治疗进行中界面]*

### 安全机制

#### 电刺激安全
- **电流限制**：最大电流严格限制在100mA以内
- **时间控制**：单次刺激时间受限，防止过度刺激
- **紧急停止**：任何时候都可以立即停止刺激
- **参数验证**：所有参数必须在安全范围内

#### 信号监测
- **信号质量**：实时监测脑电信号质量
- **异常检测**：自动识别异常信号模式
- **连接监控**：持续监控设备连接状态
- **数据完整性**：确保数据传输完整可靠

#### 治疗监控
- **时间控制**：严格控制治疗总时长
- **疲劳检测**：监测患者疲劳状态
- **效果评估**：实时评估治疗效果
- **异常处理**：出现异常立即停止治疗

### 常见问题处理

#### 设备连接问题
- **脑电设备连接失败**：检查蓝牙状态，重启设备
- **电刺激设备无响应**：检查USB连接和COM端口
- **VR系统通信异常**：确认VR软件运行状态

#### 信号质量问题
- **信号噪声大**：检查电极接触，减少干扰源
- **识别准确率低**：调整电极位置，重新校准
- **数据丢失**：检查设备连接稳定性

#### 治疗效果问题
- **电刺激无感觉**：检查电极连接，调整电流强度
- **患者不适**：立即停止治疗，检查参数设置
- **效果不明显**：调整治疗参数，延长治疗周期

## 报告分析

### 功能概述
报告分析模块提供治疗数据的统计分析和可视化展示，帮助医生评估治疗效果，制定个性化治疗方案。

### 报告界面布局

#### 左侧筛选面板
- **时间范围**：选择分析的时间段
- **患者选择**：单选或多选患者
- **治疗类型**：按治疗类型筛选
- **数据来源**：选择数据来源

#### 右侧内容区域
- **概览统计**：关键指标汇总
- **趋势分析**：治疗效果趋势图表
- **分布分析**：数据分布统计
- **对比分析**：不同条件下的对比

*此处需要截图：[报告分析界面]*

### 报告类型

#### 1. 患者个人报告
**基本信息**
- 患者基本资料
- 治疗总体概况
- 最新治疗状态

**治疗统计**
- 治疗次数和总时长
- 平均治疗效果
- 参数使用统计

**效果趋势**
- 准确率变化趋势
- 触发成功率趋势
- 康复进度评估

**参数分析**
- 历次治疗参数记录
- 参数与效果关联分析
- 最优参数推荐

#### 2. 综合统计报告
**时间统计**
- 日统计：每日治疗情况
- 周统计：周治疗汇总
- 月统计：月度治疗分析
- 年统计：年度治疗总结

**患者统计**
- 患者数量统计
- 治疗分布统计
- 效果对比分析

**设备统计**
- 设备使用时长
- 设备故障记录
- 设备效率分析

### 数据可视化

#### 图表类型
- **折线图**：显示治疗效果随时间的变化趋势
- **柱状图**：对比不同时期或患者的数据
- **饼图**：显示治疗类型或结果的分布
- **散点图**：分析参数与效果的相关性
- **热力图**：显示数据密度分布

#### 交互功能
- **缩放**：支持图表缩放查看细节
- **筛选**：动态筛选显示数据
- **导出**：支持图表导出为图片
- **打印**：支持报告打印功能

### 报告生成步骤
1. 进入"报告分析"页面
2. 在左侧筛选面板设置分析条件
3. 选择时间范围和患者
4. 系统自动生成分析结果
5. 查看各类图表和统计数据
6. 可选择导出或打印报告

*此处需要截图：[报告生成过程]*

## 用户管理

### 功能概述
用户管理模块用于管理系统用户账户，包括用户创建、权限分配、密码管理等功能。只有管理员角色可以访问此功能。

### 用户角色权限

#### 管理员 (Admin)
**权限范围**：所有功能权限
- 用户管理：创建、编辑、删除用户
- 系统设置：修改所有系统配置
- 患者管理：完整的患者信息管理
- 治疗操作：所有治疗功能
- 报告分析：查看所有报告数据

#### 医生 (Doctor)
**权限范围**：医疗相关功能
- 患者管理：患者信息查看、编辑
- 治疗操作：治疗系统操作
- 报告分析：查看治疗报告
- 系统设置：查看基本设置（不可修改）

#### 操作员 (Operator)
**权限范围**：基础操作功能
- 治疗操作：治疗系统基本操作
- 患者查看：查看患者基本信息
- 报告查看：查看基础报告数据

### 用户管理界面

#### 用户列表
显示所有系统用户的信息：
- **用户头像**：用户名首字母圆形头像
- **基本信息**：用户名、真实姓名、角色
- **状态信息**：账户状态、最后登录时间
- **操作按钮**：编辑、删除、重置密码

*此处需要截图：[用户管理界面]*

### 用户操作

#### 新增用户
1. 点击"新增用户"按钮
2. 填写用户信息：
   - **用户名**：登录用的唯一标识
   - **真实姓名**：用户的真实姓名
   - **密码**：登录密码（建议8位以上）
   - **角色**：选择用户权限级别
   - **邮箱**：联系邮箱（可选）
3. 点击"保存"创建用户

#### 编辑用户
1. 在用户列表中选择要编辑的用户
2. 点击"编辑"按钮
3. 修改用户信息（用户名不可修改）
4. 点击"保存"确认修改

#### 重置密码
1. 选择需要重置密码的用户
2. 点击"重置密码"按钮
3. 输入新密码并确认
4. 通知用户新密码

#### 删除用户
1. 选择要删除的用户
2. 点击"删除"按钮
3. 确认删除操作
4. 用户将被标记为非活跃状态

### 密码安全
- **密码要求**：建议使用8位以上包含字母数字的密码
- **定期更换**：建议定期更换密码
- **首次登录**：首次登录后应立即修改默认密码
- **安全存储**：系统使用加密方式存储密码

## 系统设置

### 功能概述
系统设置模块用于配置系统运行参数，包括基本设置、设备配置和治疗参数。不同角色用户的设置权限不同。

### 设置界面布局
设置页面分为三个标签页：
- **基本设置**：系统基本信息和治疗配置
- **脑电设备**：脑电设备连接和参数配置
- **电刺激设备**：电刺激设备连接和参数配置

*此处需要截图：[系统设置界面]*

### 基本设置

#### 系统信息
- **系统名称**：脑机接口康复训练系统
- **医院编号**：医疗机构标识码（默认：286）
- **医院名称**：医疗机构名称（默认：山东中医药大学第二附属医院）
- **科室名称**：使用科室（默认：康复科）
- **设备编号**：设备唯一标识（默认：SN230245）

#### 治疗配置
- **刺激时长**：单次电刺激持续时间（默认：10秒，范围：1-60秒）
- **治疗时长**：完整治疗会话时间（默认：11分钟，范围：5-60分钟）
- **鼓励间隔**：语音鼓励间隔时间（默认：30秒，范围：10-120秒）
- **最小保存时长**：数据保存最小时长（默认：1分钟，范围：1-15分钟）

### 脑电设备设置

#### 连接配置
- **目标设备名称**：蓝牙设备名称（默认：BLE5201）
- **自动连接**：启用/禁用自动连接功能
- **连接超时**：连接超时时间（默认：16秒，范围：5-30秒）
- **扫描超时**：设备扫描超时（默认：15秒，范围：5-30秒）

#### 重连配置
- **最大重连次数**：连接失败重试次数（默认：3次，范围：1-10次）
- **重连间隔**：重试间隔时间（默认：3秒，范围：1-10秒）
- **信号强度阈值**：最低信号强度要求（默认：-80dBm，范围：-90到-30dBm）

#### 高级配置
- **连接优先级**：优先已知设备/总是扫描新设备
- **GATT服务模式**：仅连接(推荐)/完整服务发现
- **采样率**：信号采样频率（默认：125Hz，范围：1-1000Hz）
- **通道数**：采集通道数量（默认：8通道，范围：1-64通道）

#### 设备管理
- **扫描设备**：手动搜索可用蓝牙设备
- **清除配对历史**：清除已配对设备记录

### 电刺激设备设置

#### 端口配置
- **端口号**：串口号选择（默认：COM7，范围：COM1-COM19）
- **默认波形类型**：双相波/单相波（默认：单相波）

#### 电流参数
- **最大电流**：安全电流上限（默认：100mA，范围：1-100mA）
- **最小电流**：电流下限（默认：1mA，范围：1-100mA）
- **电流步长**：调节步长（默认：1.0mA，范围：0.1-5.0mA）

#### 刺激参数
- **默认频率**：刺激频率（默认：50Hz，范围：2-160Hz）
- **默认脉宽**：脉冲宽度（默认：300μs，范围：10-500μs）
- **默认休息时间**：刺激间隔（默认：1.0秒，范围：0-16秒）
- **默认上升时间**：电流上升时间（默认：0.0秒，范围：0-5秒）
- **默认工作时间**：刺激持续时间（默认：10秒，范围：0-30秒）
- **默认下降时间**：电流下降时间（默认：0.0秒，范围：0-5秒）

### 设置操作

#### 修改设置
1. 进入"系统设置"页面
2. 选择相应的设置标签页
3. 修改需要调整的参数
4. 点击"保存设置"按钮
5. 系统提示保存成功

#### 重置设置
1. 点击"重置设置"按钮
2. 确认重置操作
3. 所有设置恢复为默认值
4. 需要重新配置个性化参数

#### 设置生效规则
- **基本设置**：立即生效
- **设备连接设置**：需要重新连接设备后生效
- **治疗参数**：在下次开始治疗时生效

## 故障排除

### 常见问题及解决方案

#### 1. 系统启动问题

**问题：系统无法启动**
- **可能原因**：缺少运行库、数据库文件损坏、权限不足
- **解决方案**：
  - 以管理员身份运行程序
  - 安装Visual C++ Redistributable
  - 检查数据库文件完整性
  - 确保有足够的磁盘空间

**问题：启动速度慢**
- **解决方案**：
  - 关闭不必要的后台程序
  - 检查磁盘空间是否充足
  - 优化系统启动项
  - 定期清理系统垃圾文件

#### 2. 登录问题

**问题：无法登录系统**
- **可能原因**：用户名或密码错误、账户被禁用、数据库连接问题
- **解决方案**：
  - 确认用户名和密码正确（注意大小写）
  - 联系管理员检查账户状态
  - 检查数据库文件权限
  - 尝试使用默认管理员账户登录

**问题：忘记密码**
- **解决方案**：
  - 联系系统管理员重置密码
  - 使用管理员账户重置其他用户密码

#### 3. 设备连接问题

**问题：脑电设备连接失败**
- **解决方案**：
  - 检查蓝牙适配器是否正常工作
  - 确认脑电设备已开机且电量充足
  - 重启蓝牙服务
  - 清除设备配对历史后重新连接
  - 检查设备是否在可发现状态
  - 减少周围蓝牙设备干扰

**问题：电刺激设备无法连接**
- **解决方案**：
  - 检查USB连接线是否正常
  - 确认COM端口号设置正确
  - 重新安装设备驱动程序
  - 检查设备电源状态
  - 在设备管理器中查看端口状态

**问题：VR系统通信异常**
- **解决方案**：
  - 确认VR软件正在运行
  - 检查网络连接状态
  - 验证端口配置正确（3004/3005）
  - 重启VR软件
  - 检查防火墙设置

#### 4. 治疗过程问题

**问题：脑电信号质量差**
- **解决方案**：
  - 检查电极与皮肤接触是否良好
  - 清洁电极和皮肤接触面
  - 调整电极位置
  - 减少环境电磁干扰
  - 确保患者放松，减少肌电干扰

**问题：运动意念识别准确率低**
- **解决方案**：
  - 重新训练分类器
  - 调整信号处理参数
  - 检查患者配合度和理解程度
  - 优化电极放置位置
  - 增加训练时间

**问题：电刺激无效果或不适**
- **解决方案**：
  - 检查电刺激电极连接
  - 确认刺激参数设置合理
  - 询问患者感受，调整电流强度
  - 检查电极放置位置是否正确
  - 如有不适立即停止治疗

#### 5. 数据问题

**问题：数据保存失败**
- **解决方案**：
  - 检查磁盘空间是否充足
  - 确认数据库文件权限
  - 验证文件路径正确
  - 重启程序重新尝试
  - 检查数据库文件是否损坏

**问题：报告生成失败**
- **解决方案**：
  - 检查治疗数据完整性
  - 确认报告模板文件存在
  - 验证导出路径权限
  - 重新选择数据范围生成报告

### 系统维护

#### 定期维护建议
- **每日**：检查设备连接状态，清理临时文件
- **每周**：备份重要数据，检查系统运行状态
- **每月**：清理日志文件，优化数据库
- **每季度**：系统全面检查，更新软件版本

#### 数据备份
- **自动备份**：系统自动备份治疗数据到data目录
- **手动备份**：定期手动备份整个数据库文件
- **异地备份**：重要数据建议异地存储

#### 系统更新
- **软件更新**：定期检查软件更新版本
- **驱动更新**：保持设备驱动程序最新
- **安全更新**：及时安装系统安全补丁

## 安全注意事项

### 电气安全
- **设备接地**：确保所有设备良好接地
- **电流限制**：严格控制电刺激电流在安全范围（≤100mA）
- **紧急停止**：熟悉紧急停止操作程序
- **定期检查**：定期检查设备电气安全性能

### 患者安全
- **过敏检查**：治疗前检查患者是否对电极材料过敏
- **皮肤检查**：确认电极放置部位皮肤完整无破损
- **舒适度确认**：治疗过程中持续关注患者舒适度
- **异常处理**：出现任何异常立即停止治疗

### 数据安全
- **隐私保护**：严格保护患者隐私信息
- **访问控制**：合理设置用户权限，防止未授权访问
- **数据备份**：定期备份重要数据，防止数据丢失
- **安全传输**：确保数据传输和存储安全

### 操作安全
- **操作培训**：操作人员必须经过专业培训
- **标准流程**：严格按照标准操作流程执行
- **记录完整**：完整记录治疗过程和参数设置
- **异常报告**：及时报告设备异常和安全事件

## 技术支持

### 联系方式
- **技术热线**：[请联系设备供应商获取]
- **邮箱支持**：[请联系设备供应商获取]
- **在线支持**：[请联系设备供应商获取]

### 服务内容
- **安装调试**：系统安装和初始化配置
- **操作培训**：用户操作培训和技能指导
- **维护保养**：定期维护保养服务
- **故障维修**：设备故障诊断和维修

### 服务时间
- **工作时间**：周一至周五 8:00-18:00
- **紧急支持**：提供紧急技术支持服务
- **响应时间**：工作时间内2小时响应，24小时内解决

## 附录

### 系统文件结构
```
脑机接口康复训练系统/
├── main.exe                    # 主程序文件
├── config/                     # 配置文件目录
│   ├── settings.json          # 系统设置
│   └── users.json             # 用户配置
├── data/                       # 数据目录
│   ├── eeg/                   # 脑电数据
│   ├── treatments/            # 治疗记录
│   └── reports/               # 报告文件
├── logs/                       # 日志目录
└── ShuJu.db                   # 主数据库文件
```

### 默认配置参数
- **治疗时长**：11分钟
- **刺激时长**：10秒
- **鼓励间隔**：30秒
- **脑电采样率**：125Hz
- **脑电通道数**：8通道
- **电刺激最大电流**：100mA
- **电刺激频率**：50Hz
- **脉冲宽度**：300μs

### 版本信息
- **文档版本**：V1.0
- **编制日期**：2025年1月
- **适用软件版本**：基于PySide6的脑机接口康复训练系统
- **编制依据**：基于实际代码功能分析

---

**重要提示**：本使用说明书基于系统实际代码功能编写，如遇到与实际操作不符的情况，请以实际系统界面为准，并及时联系技术支持。

**免责声明**：本设备为医疗器械，使用前请确保操作人员已接受专业培训，严格按照医疗器械使用规范操作。
