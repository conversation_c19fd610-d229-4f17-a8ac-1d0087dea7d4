# -*- coding: utf-8 -*-
"""
报告分析页面
Reports Analysis Page

完全按照HTML设计实现的报告分析页面
包含数据筛选、统计分析、图表可视化等功能
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QScrollArea, QGridLayout, QSplitter
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

from ui.pages.base_page import BasePage
from ui.components.filter_panel import FilterPanel
from ui.components.stats_card import StatsCard
from ui.components.chart_widgets import (
    DistributionChartWidget, InteractiveTrendChartWidget
)
from ui.components.modern_card import ModernCard
from services.report_service import report_service
from typing import Dict, Any


class ReportsPage(BasePage):
    """报告分析页面"""

    def __init__(self):
        # 初始化属性（在调用父类__init__之前）
        self.current_filters = {}
        self.overview_data = {}
        self.trend_data = {}
        self.distribution_data = {}
        self.comparison_data = {}

        # 延迟加载定时器
        self.load_timer = QTimer()
        self.load_timer.setSingleShot(True)
        self.load_timer.timeout.connect(self._load_data)

        # 调用父类初始化
        super().__init__("reports", "报告分析")

    def _init_content(self):
        """初始化页面内容"""
        # 清空默认布局内容
        while self.main_layout.count():
            child = self.main_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 创建主布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)  # 外层已有边距
        main_layout.setSpacing(20)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setChildrenCollapsible(False)

        # 创建左侧筛选面板
        self.filter_panel = FilterPanel()
        self.filter_panel.filters_changed.connect(self._on_filters_changed)
        splitter.addWidget(self.filter_panel)

        # 同步筛选面板的默认筛选条件到当前筛选条件
        self.current_filters = self.filter_panel.get_current_filters()

        # 创建右侧内容区域
        content_area = self._create_content_area()
        splitter.addWidget(content_area)

        # 设置分割器比例
        splitter.setSizes([280, 1000])  # 左侧固定280px，右侧自适应

        main_layout.addWidget(splitter)

        # 将主布局添加到基类的布局中
        self.main_layout.addLayout(main_layout)

        # 立即加载初始数据（避免延迟，提高响应性）
        # 注意：这里只加载一次，不会重复查询
        self._load_data()

        # 在页面创建完成后，立即更新图表主题
        self._update_charts_theme()

    def _create_content_area(self) -> QWidget:
        """创建右侧内容区域"""
        content_widget = QWidget()
        content_widget.setObjectName("reports_content_area")  # 设置对象名以应用圆角边框样式
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(10)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setObjectName("reports_scroll_area")

        # 创建滚动内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(0)  # 减少间距，让界面更紧凑

        # 添加各个内容区域
        self._create_overview_section(scroll_layout)
        self._create_charts_section(scroll_layout)

        # 添加弹性空间
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_content)
        content_layout.addWidget(scroll_area)

        return content_widget

    def _create_overview_section(self, layout: QVBoxLayout):
        """创建概览统计区域"""
        # 概览标题 - 去掉标题，节省空间
        # overview_title = QLabel("数据概览")
        # overview_title.setObjectName("section_title")
        # overview_title.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        # layout.addWidget(overview_title)

        # 统计卡片布局 - 使用水平布局，6个卡片一行显示
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(6)  # 进一步减小间距

        # 创建统计卡片
        self.total_patients_card = StatsCard(
            title="总患者数",
            value="0",
            icon="👥",
            card_id="total_patients"
        )

        self.total_treatments_card = StatsCard(
            title="总治疗次数",
            value="0",
            icon="🏥",
            card_id="total_treatments"
        )

        self.avg_success_rate_card = StatsCard(
            title="平均成功率",
            value="0%",
            icon="📊",
            card_id="avg_success_rate"
        )

        self.device_usage_card = StatsCard(
            title="设备使用率",
            value="0%",
            icon="🔧",
            card_id="device_usage"
        )

        self.total_duration_card = StatsCard(
            title="总治疗时长",
            value="0小时",
            icon="⏱️",
            card_id="total_duration"
        )

        # 添加到水平布局 - 5个卡片一行显示
        stats_layout.addWidget(self.total_patients_card)
        stats_layout.addWidget(self.total_treatments_card)
        stats_layout.addWidget(self.total_duration_card)
        stats_layout.addWidget(self.avg_success_rate_card)
        stats_layout.addWidget(self.device_usage_card)

        # 创建统计卡片容器
        stats_container = QWidget()
        stats_container.setLayout(stats_layout)
        # 设置容器最小宽度，允许压缩
        stats_container.setMinimumWidth(350)  # 5个卡片 * 70px 最小宽度
        layout.addWidget(stats_container)

    def _create_charts_section(self, layout: QVBoxLayout):
        """创建图表分析区域"""
        # 图表标题 - 去掉标题，节省空间
        # charts_title = QLabel("数据分析")
        # charts_title.setObjectName("section_title")
        # charts_title.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        # layout.addWidget(charts_title)

        # 图表垂直布局
        charts_layout = QVBoxLayout()
        charts_layout.setSpacing(10)

        # 创建图表组件（先使用默认主题，稍后会通过update_theme更新）
        self.trend_chart = InteractiveTrendChartWidget("治疗趋势分析", "tech")
        self.distribution_chart = DistributionChartWidget("治疗效果分布", "tech")

        # 设置图表尺寸
        for chart in [self.trend_chart, self.distribution_chart]:
            chart.setMinimumHeight(400)
            chart.setMinimumWidth(300)  # 设置最小宽度，允许在更窄的窗口中显示

        # 添加到垂直布局 - 2个图表竖向排列
        charts_layout.addWidget(self.trend_chart)
        charts_layout.addWidget(self.distribution_chart)

        # 创建图表容器
        charts_container = QWidget()
        charts_container.setLayout(charts_layout)
        layout.addWidget(charts_container)

    def _get_current_theme(self) -> str:
        """获取当前主题"""
        # 优先从主窗口获取当前主题
        try:
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'current_theme'):
                return main_window.current_theme
        except:
            pass

        # 如果主窗口主题不可用，从配置文件直接读取
        try:
            from app.config import AppConfig
            config = AppConfig()
            theme = config.get('theme', 'tech')
            return theme
        except:
            pass

        return "tech"  # 默认主题

    def _on_filters_changed(self, filters: Dict[str, Any]):
        """筛选条件改变处理"""
        self.current_filters = filters

        # 延迟加载数据以避免频繁请求
        self.load_timer.start(300)

    def _load_data(self):
        """加载数据"""
        try:
            # 加载概览数据
            self._load_overview_data()

            # 加载图表数据
            self._load_chart_data()

        except Exception as e:
            print(f"加载报告数据失败: {e}")

    def _load_overview_data(self):
        """加载概览统计数据"""
        try:
            # 获取概览统计
            self.overview_data = report_service.get_overview_statistics(self.current_filters)

            # 更新统计卡片
            self.total_patients_card.update_data(value=str(self.overview_data.get('total_patients', 0)))
            self.total_treatments_card.update_data(value=str(self.overview_data.get('total_treatments', 0)))

            # 平均成功率卡片
            avg_rate = self.overview_data.get('avg_success_rate', 0)
            self.avg_success_rate_card.update_data(value=f"{avg_rate}%")

            # 设备使用率卡片
            usage_rate = self.overview_data.get('device_usage_rate', 0)
            self.device_usage_card.update_data(value=f"{usage_rate}%")

            duration_hours = self.overview_data.get('total_duration_hours', 0)
            self.total_duration_card.update_data(value=f"{duration_hours}小时")

        except Exception as e:
            print(f"加载概览数据失败: {e}")

    def _load_chart_data(self):
        """加载图表数据"""
        try:
            # 加载趋势数据
            self.trend_data = report_service.get_trend_analysis_data(self.current_filters, 'daily')
            trend_data_list = self.trend_data.get('trends', [])
            self.trend_chart.set_data(trend_data_list)

            # 加载分布数据
            self.distribution_data = report_service.get_distribution_analysis_data(self.current_filters)
            effect_distribution = self.distribution_data.get('effect_distribution', [])
            self.distribution_chart.set_data(effect_distribution)

            # 加载对比数据（设备使用统计）
            self.comparison_data = report_service.get_comparison_analysis_data(self.current_filters)
            device_performance = self.comparison_data.get('device_performance', [])



        except Exception as e:
            print(f"加载图表数据失败: {e}")

    def _update_charts_theme(self):
        """更新图表主题（内部方法）"""
        current_theme = self._get_current_theme()

        if hasattr(self, 'trend_chart'):
            self.trend_chart.update_theme(current_theme)
        if hasattr(self, 'distribution_chart'):
            self.distribution_chart.update_theme(current_theme)

    def update_theme(self):
        """更新主题"""
        super().update_theme()

        # 更新图表主题
        self._update_charts_theme()

    def refresh_data(self):
        """刷新数据"""
        self._load_data()

    def export_report(self, format_type: str = "pdf"):
        """导出报告"""
        # TODO: 实现报告导出功能
        print(f"导出报告 - 格式: {format_type}")

    def _get_main_window(self):
        """获取主窗口引用"""
        widget = self
        while widget.parent():
            widget = widget.parent()
            if hasattr(widget, 'switch_to_treatment_with_patient'):
                return widget
        return None
