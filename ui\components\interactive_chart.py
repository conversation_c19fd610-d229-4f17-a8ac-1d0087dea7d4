# -*- coding: utf-8 -*-
"""
交互式图表组件
Interactive Chart Components

使用matplotlib的交互式后端，支持主题跟随和鼠标交互
"""

import matplotlib
matplotlib.use('Qt5Agg')  # 使用Qt交互式后端

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg
from matplotlib.figure import Figure
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from PySide6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy
from PySide6.QtCore import Signal, QTimer
from PySide6.QtGui import QFont


class InteractiveChartCanvas(FigureCanvasQTAgg):
    """交互式图表画布"""
    
    # 数据点悬停信号
    point_hovered = Signal(dict)  # 传递悬停的数据点信息
    
    def __init__(self, theme: str = 'tech', parent=None):
        # 创建图形
        self.fig = Figure(figsize=(10, 6), dpi=100)
        super().__init__(self.fig)
        
        self.setParent(parent)
        self.theme = theme
        self.chart_data = []
        self.chart_type = "line"
        
        # 设置画布属性
        FigureCanvasQTAgg.setSizePolicy(self, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        FigureCanvasQTAgg.updateGeometry(self)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 初始化主题
        self.setup_theme_colors()
        
        # 设置交互功能
        self.setup_interactions()
        
        # 创建坐标轴
        self.ax = self.fig.add_subplot(111)
        self.setup_axis_style()
        
        # 悬停相关
        self.hover_annotation = None
        self.last_hover_point = None
    
    def setup_theme_colors(self):
        """设置主题颜色"""
        if self.theme == 'medical':
            self.colors = {
                'primary': '#3b82f6',
                'secondary': '#10b981', 
                'accent': '#f59e0b',
                'danger': '#ef4444',
                'background': '#ffffff',
                'text': '#1e293b',
                'grid': '#e2e8f0',
                'chart_colors': ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']
            }
        else:  # tech theme
            self.colors = {
                'primary': '#06b6d4',
                'secondary': '#00ff88',
                'accent': '#ffaa00', 
                'danger': '#ff3366',
                'background': '#0f172a',
                'text': '#f1f5f9',
                'grid': '#475569',
                'chart_colors': ['#06b6d4', '#00ff88', '#ffaa00', '#ff3366', '#8b5cf6', '#f97316']
            }
        
        # 应用主题到matplotlib
        self.fig.patch.set_facecolor(self.colors['background'])
    
    def setup_interactions(self):
        """设置交互功能"""
        # 连接鼠标移动事件
        self.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.mpl_connect('axes_leave_event', self.on_mouse_leave)
    
    def setup_axis_style(self):
        """设置坐标轴样式"""
        self.ax.set_facecolor(self.colors['background'])

        # 设置网格
        self.ax.grid(True, color=self.colors['grid'], alpha=0.3, linestyle='-', linewidth=0.5)

        # 设置坐标轴颜色
        self.ax.tick_params(colors=self.colors['text'], labelsize=9)
        for spine in self.ax.spines.values():
            spine.set_color(self.colors['grid'])

        # 设置默认轴标签
        self.ax.set_xlabel('日期', color=self.colors['text'], fontsize=10)

        # 根据图表类型设置Y轴标签
        if self.chart_type == "bar":
            self.ax.set_ylabel('治疗次数', color=self.colors['text'], fontsize=10)
        else:  # line chart
            self.ax.set_ylabel('平均分数', color=self.colors['text'], fontsize=10)

        # 设置标签颜色
        self.ax.xaxis.label.set_color(self.colors['text'])
        self.ax.yaxis.label.set_color(self.colors['text'])
    
    def set_data(self, data: List[Dict[str, Any]], chart_type: str = "line"):
        """设置图表数据"""
        self.chart_data = data
        self.chart_type = chart_type
        self.update_chart()
    
    def update_chart(self):
        """更新图表显示"""
        if not self.chart_data:
            self.ax.clear()
            self.ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', 
                        color=self.colors['text'], fontsize=14,
                        transform=self.ax.transAxes)
            self.setup_axis_style()
            self.draw()
            return
        
        # 清除之前的图表
        self.ax.clear()
        self.setup_axis_style()
        
        try:
            if self.chart_type == "line":
                self._create_line_chart()
            elif self.chart_type == "bar":
                self._create_bar_chart()
            else:
                self._create_line_chart()  # 默认折线图
            
            # 调整布局
            self.fig.tight_layout(pad=2.0)
            self.draw()
            
        except Exception as e:
            print(f"更新图表失败: {e}")
            self.ax.clear()
            self.ax.text(0.5, 0.5, '图表生成失败', ha='center', va='center', 
                        color=self.colors['danger'], fontsize=14,
                        transform=self.ax.transAxes)
            self.setup_axis_style()
            self.draw()
    
    def _create_line_chart(self):
        """创建折线图"""
        # 提取数据
        x_data = [item['date'] for item in self.chart_data]
        y_data = [item['avg_score'] for item in self.chart_data]
        
        # 绘制折线图
        line, = self.ax.plot(x_data, y_data, color=self.colors['primary'], 
                            linewidth=2.5, marker='o', markersize=5, 
                            markerfacecolor=self.colors['primary'],
                            markeredgecolor='white', markeredgewidth=1.5, 
                            alpha=0.9, label='平均分数')
        
        # 轴标签已在setup_axis_style中设置
        
        # 优化X轴标签显示
        if len(x_data) > 15:
            step = max(1, len(x_data) // 10)
            ticks = range(0, len(x_data), step)
            self.ax.set_xticks(ticks)
            self.ax.set_xticklabels([x_data[i] for i in ticks])
        
        # 旋转X轴标签
        plt.setp(self.ax.get_xticklabels(), rotation=45, ha='right', fontsize=8)
        
        # 存储线条对象用于交互
        self.line_object = line
    
    def _create_bar_chart(self):
        """创建柱状图"""
        # 提取数据
        x_data = [item['date'] for item in self.chart_data]
        y_data = [item.get('treatment_count', 0) for item in self.chart_data]
        
        # 创建颜色列表
        colors = [self.colors['chart_colors'][i % len(self.colors['chart_colors'])] 
                 for i in range(len(x_data))]
        
        # 绘制柱状图
        bars = self.ax.bar(x_data, y_data, color=colors, alpha=0.8,
                          edgecolor=self.colors['text'], linewidth=0.5)

        # 在柱子上方添加数值标签
        for i, (bar, value) in enumerate(zip(bars, y_data)):
            height = bar.get_height()
            self.ax.text(bar.get_x() + bar.get_width()/2., height + max(y_data) * 0.01,
                        f'{int(value)}',
                        ha='center', va='bottom',
                        color=self.colors['text'],
                        fontsize=10,
                        fontweight='bold')

        # 轴标签已在setup_axis_style中设置

        # 调整Y轴上限，为数值标签留出空间
        if y_data:
            max_value = max(y_data)
            self.ax.set_ylim(0, max_value * 1.15)  # 增加15%的空间

        # 优化X轴标签显示
        if len(x_data) > 10:
            step = max(1, len(x_data) // 8)
            ticks = range(0, len(x_data), step)
            self.ax.set_xticks(ticks)
            self.ax.set_xticklabels([x_data[i] for i in ticks])
        
        plt.setp(self.ax.get_xticklabels(), rotation=45, ha='right', fontsize=8)
        
        # 存储柱状图对象用于交互
        self.bar_objects = bars
    
    def on_mouse_move(self, event):
        """鼠标移动事件处理"""
        if event.inaxes != self.ax or not self.chart_data:
            return
        
        try:
            # 找到最近的数据点
            closest_point = self._find_closest_point(event.xdata, event.ydata)
            
            if closest_point and closest_point != self.last_hover_point:
                self.last_hover_point = closest_point
                self._show_hover_annotation(event, closest_point)
                self.point_hovered.emit(closest_point)
                
        except Exception as e:
            print(f"鼠标悬停处理失败: {e}")
    
    def on_mouse_leave(self, event):
        """鼠标离开坐标轴事件"""
        self._hide_hover_annotation()
        self.last_hover_point = None
    
    def _find_closest_point(self, x_pos: float, y_pos: float) -> Optional[Dict[str, Any]]:
        """找到最接近鼠标位置的数据点"""
        if not self.chart_data:
            return None
        
        # 对于折线图，主要根据X轴位置查找
        if self.chart_type == "line":
            # 计算最近的X轴索引
            x_index = round(x_pos) if x_pos is not None else 0
            x_index = max(0, min(len(self.chart_data) - 1, x_index))
            return self.chart_data[x_index]
        
        return None
    
    def _show_hover_annotation(self, event, data_point: Dict[str, Any]):
        """显示悬停标注"""
        # 移除之前的标注
        self._hide_hover_annotation()
        
        # 创建新的标注
        text = f"日期: {data_point['date']}\n数值: {data_point['avg_score']:.1f}"
        
        self.hover_annotation = self.ax.annotate(
            text,
            xy=(event.xdata, event.ydata),
            xytext=(20, 20),
            textcoords='offset points',
            bbox=dict(boxstyle='round,pad=0.5', 
                     facecolor=self.colors['background'], 
                     edgecolor=self.colors['primary'],
                     alpha=0.9),
            arrowprops=dict(arrowstyle='->', 
                           connectionstyle='arc3,rad=0',
                           color=self.colors['primary']),
            fontsize=9,
            color=self.colors['text']
        )
        
        self.draw_idle()
    
    def _hide_hover_annotation(self):
        """隐藏悬停标注"""
        if self.hover_annotation:
            self.hover_annotation.remove()
            self.hover_annotation = None
            self.draw_idle()
    
    def update_theme(self, theme: str):
        """更新主题"""
        self.theme = theme
        self.setup_theme_colors()
        self.update_chart()
   

    def wheelEvent(self, event):
        """重写滚轮事件，将滚轮事件传递给父组件"""
        # 不处理滚轮事件，让父组件（滚动区域）处理
        event.ignore()
        # 或者显式传递给父组件
        if self.parent():
            self.parent().wheelEvent(event)

    def focusInEvent(self, event):
        """重写焦点进入事件"""
        # 不接受焦点，避免拦截滚轮事件
        event.ignore()

    def focusOutEvent(self, event):
        """重写焦点离开事件"""
        event.ignore()
