"""
训练会话管理器

管理预训练会话，包括数据收集、特征提取、模型训练等。
"""

import os
import numpy as np
import time
import logging
import threading
from typing import Optional, List, Dict, Any, Callable, Tuple
from dataclasses import dataclass
from enum import Enum

from PySide6.QtCore import QObject, Signal, QTimer
from .multi_round_training_manager import MultiRoundTrainingManager
from .classifier_training_manager import ClassifierTrainingManager
from .data_protection import RehabilitationDataCollector

logger = logging.getLogger(__name__)

class TrainingState(Enum):
    """训练状态枚举"""
    IDLE = "idle"
    PREPARING = "preparing"
    TRIAL_START = "trial_start"
    MOTOR_IMAGERY = "motor_imagery"
    REST = "rest"
    TRIAL_END = "trial_end"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class TrainingConfig:
    """训练配置"""
    trials_per_class: int = 10  # 每类试次数
    trial_duration: float = 2.0  # 试次时长（秒）- 修改为2秒
    rest_duration: float = 2.0   # 休息时长（秒）
    preparation_time: float = 0.0  # 准备时间（秒）
    inter_trial_interval: float = 2.5  # 试次间隔（秒）- 调整为2.5秒
    voice_delay: float = 1.0  # 语音提示后延迟时间（秒）- 新增配置

    # 数据质量要求
    min_signal_quality: float = 0.6
    max_artifact_ratio: float = 0.3

@dataclass
class PersonalizedTrainingConfig:
    """个性化训练配置"""
    # 基础配置
    base_config: TrainingConfig = None

    # 个性化参数范围
    voice_delay_range: Tuple[float, float] = (1.0, 2.5)  # 语音延迟范围
    trial_duration_range: Tuple[float, float] = (1.5, 3.0)  # 试次时长范围

    # 患者特征
    reaction_time_level: str = "normal"  # "fast", "normal", "slow"
    attention_span_level: str = "normal"  # "short", "normal", "long"
    motor_imagery_ability: str = "normal"  # "impaired", "normal", "good"

    def __post_init__(self):
        if self.base_config is None:
            self.base_config = TrainingConfig()

    def get_optimized_config(self) -> TrainingConfig:
        """根据患者特征获取优化的训练配置"""
        config = TrainingConfig(
            trials_per_class=self.base_config.trials_per_class,
            rest_duration=self.base_config.rest_duration,
            preparation_time=self.base_config.preparation_time,
            inter_trial_interval=self.base_config.inter_trial_interval,
            min_signal_quality=self.base_config.min_signal_quality,
            max_artifact_ratio=self.base_config.max_artifact_ratio
        )

        # 根据反应时间调整语音延迟
        if self.reaction_time_level == "fast":
            config.voice_delay = 1.0
        elif self.reaction_time_level == "slow":
            config.voice_delay = 2.0
        else:
            config.voice_delay = 1.5

        # 根据运动想象能力调整试次时长
        if self.motor_imagery_ability == "impaired":
            config.trial_duration = 3.0
        elif self.motor_imagery_ability == "good":
            config.trial_duration = 1.5
        else:
            config.trial_duration = 2.0

        return config

class TrainingSessionManager(QObject):
    """
    训练会话管理器
    
    负责管理整个预训练流程，包括：
    - 数据收集和质量监控
    - 语音提示和用户交互
    - 特征提取和模型训练
    - 进度跟踪和状态管理
    """
    
    # 信号定义
    state_changed = Signal(str)  # 状态变化
    progress_updated = Signal(int, int)  # 当前进度, 总进度
    trial_started = Signal(int, str)  # 试次编号, 类型
    trial_completed = Signal(int, str, dict)  # 试次编号, 类型, 质量信息
    session_completed = Signal(dict)  # 会话完成, 统计信息
    error_occurred = Signal(str)  # 错误信息
    voice_prompt_requested = Signal(str)  # 语音提示请求
    
    def __init__(self, config: Optional[TrainingConfig] = None):
        """
        初始化训练会话管理器
        
        Args:
            config: 训练配置
        """
        super().__init__()
        
        self.config = config or TrainingConfig()
        
        # 状态管理
        self.current_state = TrainingState.IDLE
        self.is_running = False
        
        # 进度跟踪
        self.current_trial = 0
        self.total_trials = self.config.trials_per_class * 2  # 两类
        self.completed_trials = []

        # 语音提示控制
        self.last_trial_type = None  # 记录上一个试次类型，用于判断是否需要语音提示
        self.voice_prompts_enabled = True  # 语音提示启用标志
        self.active_timers = []  # 跟踪活动的定时器
        
        # 数据收集
        self.collected_data = []
        self.collected_labels = []
        self.data_quality_scores = []
        
        # 特征提取管理器
        self.feature_manager = None

        # 分类器训练管理器 - 方案A仅需分类器配置
        from utils.path_manager import get_config_file_in_dir
        classifier_config_path = str(get_config_file_in_dir('classifiers_optimized.json'))

        self.classifier_manager = ClassifierTrainingManager(
            config_path=None,
            classifier_config_path=classifier_config_path
        )

        # 患者信息
        self.patient_id = None

        # 多轮训练管理器
        self.multi_round_manager: Optional[MultiRoundTrainingManager] = None
        self.is_multi_round_training = False
        self.current_round_number = 0

        # 定时器
        self.trial_timer = QTimer()
        self.trial_timer.setSingleShot(True)
        self.trial_timer.timeout.connect(self._on_trial_timer)

        # 数据收集回调
        self.data_callback: Optional[Callable] = None

        # 康复数据收集器
        self.rehabilitation_collector: Optional[RehabilitationDataCollector] = None
        self.rehabilitation_data_collection_enabled = False  # 康复数据收集开关

        # 康复数据收集缓存
        self.motor_imagery_trials = []
        self.rest_trials = []
        self.motor_timestamps = []
        self.rest_timestamps = []
        self.motor_trial_numbers = []
        self.rest_trial_numbers = []

        logger.info("训练会话管理器初始化完成")

    def _schedule_timer(self, delay_ms: int, callback):
        """
        统一定时器管理方法

        Args:
            delay_ms: 延迟时间（毫秒）
            callback: 回调函数
        """
        # 检查会话是否仍在运行，如果已停止则不创建新定时器
        if not self.is_running:
            logger.debug("会话已停止，跳过定时器创建")
            return None

        timer = QTimer()
        timer.setSingleShot(True)

        # 包装回调函数，添加运行状态检查
        def safe_callback():
            try:
                if self.is_running:  # 再次检查运行状态
                    callback()
                else:
                    logger.debug("会话已停止，跳过定时器回调")
            except Exception as e:
                logger.error(f"定时器回调执行失败: {e}")
            finally:
                # 确保定时器被清理
                self._remove_timer(timer)

        timer.timeout.connect(safe_callback)
        self.active_timers.append(timer)
        timer.start(delay_ms)
        return timer

    def _remove_timer(self, timer):
        """移除定时器引用"""
        if timer in self.active_timers:
            self.active_timers.remove(timer)

    def _cancel_all_timers(self):
        """取消所有活动定时器（保留原方法，向后兼容）"""
        self._safe_cancel_all_timers()
    
    def set_feature_manager(self, feature_manager):
        """设置特征提取管理器"""
        self.feature_manager = feature_manager

    def set_patient_id(self, patient_id: str):
        """设置患者ID"""
        self.patient_id = patient_id

        # 如果患者ID改变，重新初始化多轮训练管理器
        if patient_id:
            self.multi_round_manager = MultiRoundTrainingManager(patient_id)
            logger.info(f"多轮训练管理器已初始化，患者ID: {patient_id}")

    def enable_multi_round_training(self, enable: bool = True):
        """启用/禁用多轮训练模式"""
        self.is_multi_round_training = enable
        if enable and not self.multi_round_manager and self.patient_id:
            self.multi_round_manager = MultiRoundTrainingManager(self.patient_id)
        logger.info(f"多轮训练模式: {'启用' if enable else '禁用'}")

    def start_new_training_round(self) -> int:
        """开始新一轮训练"""
        if not self.multi_round_manager:
            raise ValueError("多轮训练管理器未初始化")

        self.current_round_number = self.multi_round_manager.start_new_round({
            'trials_per_class': self.config.trials_per_class,
            'trial_duration': self.config.trial_duration,
            'rest_duration': self.config.rest_duration
        })

        logger.info(f"开始第{self.current_round_number}轮训练")
        return self.current_round_number
        logger.info("特征提取管理器已设置")
    
    def set_data_callback(self, callback: Callable):
        """设置数据收集回调函数"""
        self.data_callback = callback
        logger.info("数据收集回调已设置")

    def set_patient_id(self, patient_id: str):
        """设置患者ID"""
        self.patient_id = patient_id
        logger.info(f"患者ID已设置: {patient_id}")

    def enable_rehabilitation_data_collection(self, hospital_id: Optional[str] = None):
        """
        启用康复数据收集功能

        Args:
            hospital_id: 医院标识
        """
        try:
            self.rehabilitation_collector = RehabilitationDataCollector(hospital_id)
            self.rehabilitation_data_collection_enabled = True
            # logger.info(f"✅ 康复数据收集已启用，医院ID: {hospital_id}")
        except Exception as e:
            logger.error(f"❌ 启用康复数据收集失败: {e}")
            self.rehabilitation_data_collection_enabled = False

    def disable_rehabilitation_data_collection(self):
        """禁用康复数据收集功能"""
        self.rehabilitation_data_collection_enabled = False
        self.rehabilitation_collector = None
        logger.info("康复数据收集已禁用")
    
    def start_session(self):
        """开始训练会话"""
        try:
            if self.is_running:
                logger.warning("训练会话已在运行中")
                return False
            
            # 重置状态
            self._reset_session()

            # 重新启用语音提示
            self.voice_prompts_enabled = True

            # 开始会话
            self.is_running = True
            self._change_state(TrainingState.PREPARING)
            
            # 发送开始提示
            self.voice_prompt_requested.emit('training_start')
            
            # 延迟开始第一个试次
            self._schedule_timer(3000, self._start_next_trial)
            
            logger.info("训练会话已开始")
            return True
            
        except Exception as e:
            logger.error(f"启动训练会话失败: {e}")
            self.error_occurred.emit(f"启动训练会话失败: {e}")
            return False
    
    def stop_session(self):
        """停止训练会话（改进版本，避免卡死）"""
        try:
            if not self.is_running:
                return

            logger.info("开始停止训练会话...")

            # 第一步：立即设置停止标志，防止新的操作
            self.is_running = False
            self.voice_prompts_enabled = False

            # 第二步：停止主要定时器
            try:
                if self.trial_timer.isActive():
                    self.trial_timer.stop()
                logger.debug("主定时器已停止")
            except Exception as e:
                logger.warning(f"停止主定时器失败: {e}")

            # 第三步：安全取消所有活动定时器
            self._safe_cancel_all_timers()

            # 第四步：更新状态
            self._change_state(TrainingState.IDLE)

            # 第五步：停止语音播放（简洁方式）
            try:
                from core.simple_voice import stop_voice
                stop_voice()
                logger.debug("语音播放已停止")
            except Exception as e:
                logger.warning(f"停止语音播放失败: {e}")

            logger.info("训练会话已安全停止")

        except Exception as e:
            logger.error(f"停止训练会话失败: {e}")
            # 确保状态被重置
            self.is_running = False
            self.voice_prompts_enabled = False

    def _safe_cancel_all_timers(self):
        """安全取消所有活动定时器，避免竞争条件"""
        try:
            timer_count = len(self.active_timers)
            if timer_count == 0:
                return

            logger.debug(f"开始取消 {timer_count} 个活动定时器")

            # 创建定时器列表的副本，避免在迭代时修改
            timers_to_cancel = list(self.active_timers)

            for timer in timers_to_cancel:
                try:
                    if timer and timer.isActive():
                        timer.stop()
                        logger.debug("定时器已停止")
                except Exception as e:
                    logger.warning(f"停止单个定时器失败: {e}")

            # 清空定时器列表
            self.active_timers.clear()
            logger.debug(f"已安全取消 {timer_count} 个定时器")

        except Exception as e:
            logger.error(f"安全取消定时器失败: {e}")
            # 强制清空列表
            self.active_timers.clear()
    
    def _reset_session(self):
        """重置会话状态"""
        self.current_trial = 0
        self.completed_trials = []
        self.collected_data = []
        self.collected_labels = []
        self.data_quality_scores = []
        
        self.progress_updated.emit(0, self.total_trials)
    
    def _change_state(self, new_state: TrainingState):
        """改变状态"""
        if self.current_state != new_state:
            self.current_state = new_state
            self.state_changed.emit(new_state.value)
            logger.debug(f"状态变更: {new_state.value}")
    
    def _start_next_trial(self):
        """开始下一个试次"""
        try:
            if not self.is_running:
                return
            
            if self.current_trial >= self.total_trials:
                self._complete_session()
                return
            
            # 确定试次类型（交替进行）
            trial_type = "motor_imagery" if self.current_trial % 2 == 0 else "rest"
            
            self.current_trial += 1
            
            # 发送试次开始信号
            self.trial_started.emit(self.current_trial, trial_type)
            
            # 开始试次
            self._execute_trial(trial_type)
            
        except Exception as e:
            logger.error(f"开始试次失败: {e}")
            self.error_occurred.emit(f"开始试次失败: {e}")
    
    def _execute_trial(self, trial_type: str):
        """执行试次"""
        try:
            # 更新状态（不发送语音提示）
            self._change_state(TrainingState.TRIAL_START)

            # 等待准备时间（只有在会话仍在运行时）
            if self.is_running:
                self._schedule_timer(int(self.config.preparation_time * 1000),
                                lambda: self._start_trial_recording(trial_type))
            else:
                logger.debug("会话已停止，跳过试次执行")

        except Exception as e:
            logger.error(f"执行试次失败: {e}")
            self.error_occurred.emit(f"执行试次失败: {e}")
    
    def _start_trial_recording(self, trial_type: str):
        """开始试次记录"""
        try:
            # 检查会话是否仍在运行
            if not self.is_running:
                logger.debug("会话已停止，跳过试次记录开始")
                return

            # 只在任务类型切换时发送语音提示
            if self.last_trial_type != trial_type:
                if trial_type == "motor_imagery":
                    self._change_state(TrainingState.MOTOR_IMAGERY)
                    if self.voice_prompts_enabled:
                        self.voice_prompt_requested.emit('motor_imagery')
                else:
                    self._change_state(TrainingState.REST)
                    if self.voice_prompts_enabled:
                        self.voice_prompt_requested.emit('rest')

                # 更新上一个试次类型
                self.last_trial_type = trial_type
            else:
                # 不发送语音提示，只更新状态
                if trial_type == "motor_imagery":
                    self._change_state(TrainingState.MOTOR_IMAGERY)
                else:
                    self._change_state(TrainingState.REST)

            # 延迟开始数据收集（语音提示后延迟）（只有在会话仍在运行时）
            if self.is_running:
                delay_ms = int(self.config.voice_delay * 1000)
                self._schedule_timer(delay_ms, lambda: self._start_data_collection(trial_type))

                # 设置试次结束定时器（总时长 = 语音延迟 + 数据收集时长）
                total_duration_ms = int((self.config.voice_delay + self.config.trial_duration) * 1000)
                self.trial_timer.start(total_duration_ms)
            else:
                logger.debug("会话已停止，跳过数据收集定时器")

        except Exception as e:
            logger.error(f"开始试次记录失败: {e}")
            self.error_occurred.emit(f"开始试次记录失败: {e}")
    
    def _start_data_collection(self, trial_type: str):
        """开始数据收集 - 只使用真实数据"""
        try:
            logger.info(f"🔍 开始收集真实数据: {trial_type}")
            
            # 检查数据收集回调是否已设置
            if not self.data_callback:
                logger.error("❌ 数据收集回调未设置，无法获取真实数据")
                self.error_occurred.emit("数据收集回调未设置，训练无法继续")
                # 立即停止会话，避免继续执行后续试次
                logger.info("🛑 数据收集回调未设置，立即停止训练会话")
                self.stop_session()
                return

            # 调用回调函数收集真实数据
            try:
                eeg_data = self.data_callback(trial_type, self.config.trial_duration)
                
                # 验证获取到的数据
                if eeg_data is None:
                    logger.error("❌ 数据收集回调返回None，可能设备未连接或数据源异常")
                    self.error_occurred.emit("无法获取真实脑电数据，请检查设备连接")
                    # 立即停止会话，避免继续执行后续试次
                    logger.info("🛑 无法获取脑电数据，立即停止训练会话")
                    self.stop_session()
                    return
                
                # 验证数据格式
                if not isinstance(eeg_data, np.ndarray):
                    logger.error(f"❌ 收集到的数据类型错误: {type(eeg_data)}, 期望: numpy.ndarray")
                    self.error_occurred.emit("收集到的数据格式错误")
                    # 立即停止会话，避免继续执行后续试次
                    logger.info("🛑 数据格式错误，立即停止训练会话")
                    self.stop_session()
                    return
                
                # 验证数据形状
                expected_shape = (8, 250)  # 8通道，250样本（2秒窗口）
                if eeg_data.shape != expected_shape:
                    logger.error(f"❌ 数据形状错误: {eeg_data.shape}, 期望: {expected_shape}")
                    self.error_occurred.emit(f"数据形状错误: {eeg_data.shape}, 期望: {expected_shape}")
                    # 立即停止会话，避免继续执行后续试次
                    logger.info("🛑 数据形状错误，立即停止训练会话")
                    self.stop_session()
                    return
                
                # 验证数据质量（确保不是模拟数据）
                if not self._validate_real_eeg_data(eeg_data):
                    logger.error("❌ 数据未通过真实性验证，疑似模拟数据或无效数据")
                    self.error_occurred.emit("收集到的数据未通过真实性验证")
                    # 立即停止会话，避免继续执行后续试次
                    logger.info("🛑 数据验证失败，立即停止训练会话")
                    self.stop_session()
                    return
                
                # 存储真实数据
                self.collected_data.append(eeg_data)
                self.collected_labels.append(1 if trial_type == "motor_imagery" else 0)

                # 真实的数据质量评估
                quality_score = self._evaluate_data_quality(eeg_data)
                self.data_quality_scores.append(quality_score)

                # 康复数据收集：保存原始EEG数据用于离线分析
                if self.rehabilitation_data_collection_enabled and self.rehabilitation_collector:
                    self._collect_rehabilitation_data(eeg_data, trial_type)

                logger.info(f"✅ 真实数据收集完成: {trial_type}, 形状: {eeg_data.shape}, 质量: {quality_score:.3f}")
                logger.debug(f"   数据范围: [{np.min(eeg_data):.2f}, {np.max(eeg_data):.2f}]")
                logger.debug(f"   数据标准差: {np.std(eeg_data):.2f}")
                
            except Exception as e:
                logger.error(f"❌ 真实数据收集失败: {e}")
                # 不再回退到模拟数据，直接报错并停止训练
                self.error_occurred.emit(f"真实数据收集失败: {e}")
                # 立即停止会话，避免继续执行后续试次
                logger.info("🛑 数据收集失败，立即停止训练会话")
                self.stop_session()
                return

        except Exception as e:
            logger.error(f"❌ 数据收集过程失败: {e}")
            self.error_occurred.emit(f"数据收集失败: {e}")
            # 立即停止会话，避免继续执行后续试次
            logger.info("🛑 数据收集过程失败，立即停止训练会话")
            self.stop_session()

    def _validate_real_eeg_data(self, data: np.ndarray) -> bool:
        """验证数据基本有效性（简化版，与UI保持一致）"""
        try:
            if data is None or data.size == 0:
                return False
            
            # 检查数据形状
            if len(data.shape) != 2 or data.shape[0] != 8:
                logger.error(f"数据形状不正确: {data.shape}, 期望: (8, N)")
                return False
            
            # 检查是否全部为零（设备未工作）
            if np.all(data == 0):
                logger.error("数据全为零，设备可能未工作")
                return False
            
            # 基本的数据有效性检查（范围很宽松）
            data_range = np.max(data) - np.min(data)
            if data_range < 0.1:  # 数据完全无变化
                logger.error(f"数据无变化: {data_range}")
                return False
            
            logger.debug(f"数据基本验证通过 - 形状: {data.shape}, 范围: [{np.min(data):.2f}, {np.max(data):.2f}]")
            return True
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False

    def _evaluate_data_quality(self, data: np.ndarray) -> float:
        """评估脑电数据质量（宽松版本，适合低信噪比设备）"""
        try:
            # 简化的质量评估，适用于信噪比较低的脑电帽
            quality_scores = []
            
            # 1. 基本信号活跃度检查
            amplitude = np.max(np.abs(data))
            variance = np.var(data)
            
            # 非常宽松的范围，只要有信号就认为是可用的
            if amplitude > 1 and variance > 0.1:
                activity_score = 0.8  # 有信号活动就给较高分
            elif amplitude > 0.1 and variance > 0.01:
                activity_score = 0.6  # 微弱信号也可接受
            else:
                activity_score = 0.3  # 信号过弱
            quality_scores.append(activity_score)
            
            # 2. 数据连续性检查（防止数据包丢失导致的跳跃）
            data_diff = np.diff(data, axis=1)
            avg_change = np.mean(np.abs(data_diff))
            max_change = np.max(np.abs(data_diff))
            
            # 只检查是否有极端的数据跳跃（可能是数据传输错误）
            if avg_change > 0 and max_change / avg_change < 100:  # 很宽松的连续性要求
                continuity_score = 0.8
            else:
                continuity_score = 0.5
            quality_scores.append(continuity_score)
            
            # 简单平均，不使用复杂的权重
            final_score = np.mean(quality_scores)
            
            # 确保分数在合理范围内，底线较低以适应低SNR设备
            final_score = max(0.3, min(1.0, final_score))
            
            logger.debug(f"数据质量评分: 活跃度={activity_score:.2f}, 连续性={continuity_score:.2f}, "
                        f"综合={final_score:.2f} (幅值={amplitude:.2f}, 方差={variance:.2f})")
            
            return final_score

        except Exception as e:
            logger.warning(f"数据质量评估失败: {e}")
            return 0.7  # 默认给较高分数，避免阻碍系统使用
    
    def _on_trial_timer(self):
        """试次定时器超时"""
        try:
            self._end_current_trial()
        except Exception as e:
            logger.error(f"试次结束处理失败: {e}")
    
    def _end_current_trial(self):
        """结束当前试次"""
        try:
            self._change_state(TrainingState.TRIAL_END)
            
            # 计算试次质量
            quality_info = self._evaluate_trial_quality()
            
            # 记录完成的试次
            trial_info = {
                'trial_number': self.current_trial,
                'trial_type': "motor_imagery" if (self.current_trial - 1) % 2 == 0 else "rest",
                'quality': quality_info
            }
            self.completed_trials.append(trial_info)
            
            # 发送试次完成信号
            self.trial_completed.emit(
                self.current_trial, 
                trial_info['trial_type'], 
                quality_info
            )
            
            # 更新进度
            self.progress_updated.emit(self.current_trial, self.total_trials)
            
            # 试次结束提示
            if self.voice_prompts_enabled:
                self.voice_prompt_requested.emit('trial_end')
            
            # 延迟开始下一个试次（只有在会话仍在运行时）
            if self.is_running:
                interval_ms = int(self.config.inter_trial_interval * 1000)
                self._schedule_timer(interval_ms, self._start_next_trial)
            else:
                logger.debug("会话已停止，不安排下一个试次")
            
        except Exception as e:
            logger.error(f"结束试次失败: {e}")
            self.error_occurred.emit(f"结束试次失败: {e}")
    
    def _evaluate_trial_quality(self) -> Dict[str, Any]:
        """评估试次质量"""
        try:
            if not self.data_quality_scores:
                return {'quality_score': 0.0, 'is_usable': False}
            
            latest_score = self.data_quality_scores[-1]
            is_usable = latest_score >= self.config.min_signal_quality
            
            return {
                'quality_score': latest_score,
                'is_usable': is_usable,
                'signal_strength': latest_score * 100,
                'artifact_level': (1 - latest_score) * 100
            }
            
        except Exception as e:
            logger.error(f"评估试次质量失败: {e}")
            return {'quality_score': 0.0, 'is_usable': False}
    
    def _complete_session(self):
        """完成训练会话"""
        try:
            self._change_state(TrainingState.PROCESSING)
            
            # 保存康复评估数据
            self._save_rehabilitation_data()

            # 处理收集的数据
            self._process_collected_data()

            # 计算会话统计
            session_stats = self._calculate_session_stats()
            
            # 发送完成信号
            self.session_completed.emit(session_stats)
            
            # 完成提示
            if self.voice_prompts_enabled:
                self.voice_prompt_requested.emit('training_complete')
            
            self._change_state(TrainingState.COMPLETED)
            self.is_running = False
            
            logger.info("训练会话完成")
            
        except Exception as e:
            logger.error(f"完成训练会话失败: {e}")
            self.error_occurred.emit(f"完成训练会话失败: {e}")
    
    def _process_collected_data(self):
        """处理收集的数据"""
        try:
            if not self.collected_data:
                logger.warning("没有收集到训练数据")
                return

            if not self.patient_id:
                logger.warning("患者ID未设置，使用默认ID")
                self.patient_id = f"patient_{int(time.time())}"

            # 转换数据格式
            X = np.array(self.collected_data)  # [n_trials, n_channels, n_samples]
            y = np.array(self.collected_labels)  # [n_trials]

            logger.info(f"开始处理训练数据，患者ID: {self.patient_id}, 形状: {X.shape}")

            if self.is_multi_round_training and self.multi_round_manager:
                # 多轮训练模式
                self._process_multi_round_data(X, y)
            else:
                # 单轮训练模式
                self._process_single_round_data(X, y)

        except Exception as e:
            logger.error(f"处理收集数据失败: {e}")
            raise

    def _process_single_round_data(self, X: np.ndarray, y: np.ndarray):
        """处理单轮训练数据"""
        try:
            # 方案A：直接训练PlanA分类器
            from services.classifier_training_manager import ClassifierTrainingManager
            ctm = ClassifierTrainingManager()
            version_suffix = "round_1"
            plan_a_files = ctm.train_plan_a(X, y, self.patient_id, version_suffix)
            if not plan_a_files or not plan_a_files.get('plan_a_model'):
                logger.error("Plan A 模型训练失败")
                self.error_occurred.emit("训练失败：Plan A 模型训练失败，请检查数据质量")
                return
            logger.info(f"单轮训练完成，PlanA文件: {plan_a_files}")

        except Exception as e:
            logger.error(f"处理单轮训练数据失败: {e}")
            self.error_occurred.emit(f"训练数据处理失败: {e}")
            raise

    def _process_multi_round_data(self, X: np.ndarray, y: np.ndarray):
        """处理多轮训练数据"""
        try:
            # 添加当前轮次数据到多轮训练管理器
            self.multi_round_manager.add_training_data(X, y)

            # 获取累积的所有数据
            X_all, y_all = self.multi_round_manager.get_accumulated_data()

            if X_all.size == 0:
                logger.warning("没有累积数据")
                return

            # 使用累积数据训练PlanA（使用版本后缀）
            version_suffix = f"round_{self.current_round_number}"
            from services.classifier_training_manager import ClassifierTrainingManager
            ctm = ClassifierTrainingManager()
            plan_a_files = ctm.train_plan_a(X_all, y_all, self.patient_id, version_suffix)
            classifier_files = plan_a_files
            if not plan_a_files or not plan_a_files.get('plan_a_model'):
                logger.error(f"第{self.current_round_number}轮Plan A 训练失败")
                self.error_occurred.emit(f"第{self.current_round_number}轮训练失败：Plan A 模型训练失败，请检查数据质量")
                return

            # 计算性能指标（这里可以添加交叉验证等）
            performance_metrics = self._calculate_performance_metrics(X_all, y_all)

            # 完成当前轮次
            round_info = self.multi_round_manager.complete_round(
                feature_files={},
                classifier_files=classifier_files,  # 添加分类器文件
                performance_metrics=performance_metrics,
                training_config={
                    'trials_per_class': self.config.trials_per_class,
                    'trial_duration': self.config.trial_duration,
                    'total_samples': X_all.shape[0]
                },
                notes=f"第{self.current_round_number}轮训练完成"
            )

            logger.info(f"多轮训练第{self.current_round_number}轮完成，累积样本: {X_all.shape[0]}")

        except Exception as e:
            logger.error(f"处理多轮训练数据失败: {e}")
            raise

    def _calculate_performance_metrics(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """计算性能指标"""
        try:
            # 这里可以实现交叉验证等性能评估
            # 暂时返回基本统计信息
            return {
                'total_samples': float(X.shape[0]),
                # 采集阶段定义: motor_imagery -> 1, rest -> 0
                'motor_imagery_samples': float(np.sum(y == 1)),
                'rest_samples': float(np.sum(y == 0)),
                'data_quality_avg': float(np.mean(self.data_quality_scores)) if self.data_quality_scores else 0.0
            }

        except Exception as e:
            logger.error(f"计算性能指标失败: {e}")
            return {}

    def _load_latest_feature_extractors(self) -> Dict[str, Any]:
        """加载最新的特征提取器"""
        try:
            # 兼容旧接口，已不再使用BaseFeatureExtractor

            extractors = {}
            # 动态获取启用的特征提取器类型
            feature_types = self.feature_manager.get_enabled_extractors()

            for feature_type in feature_types:
                try:
                    # 使用路径管理器获取特征文件路径
                    from utils.path_manager import get_data_file
                    latest_file = str(get_data_file(f"features/{self.patient_id}_latest_{feature_type}.pkl"))
                    if os.path.exists(latest_file):
                        extractor = None  # 特征提取器已移除，兼容占位
                        extractors[feature_type] = extractor
                        logger.debug(f"加载{feature_type}特征提取器成功")
                    else:
                        logger.warning(f"未找到{feature_type}特征提取器: {latest_file}")

                except Exception as e:
                    logger.error(f"加载{feature_type}特征提取器失败: {e}")
                    continue

            return extractors

        except Exception as e:
            logger.error(f"加载特征提取器失败: {e}")
            return {}

    def has_training_history(self) -> bool:
        """检查是否有训练历史"""
        if not self.multi_round_manager:
            return False
        return self.multi_round_manager.has_training_history()

    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要"""
        if not self.multi_round_manager:
            return {}
        return self.multi_round_manager.get_training_summary()

    def get_latest_round_info(self):
        """获取最新轮次信息"""
        if not self.multi_round_manager:
            return None
        return self.multi_round_manager.get_latest_round_info()
    
    def _calculate_session_stats(self) -> Dict[str, Any]:
        """计算会话统计"""
        try:
            total_trials = len(self.completed_trials)
            usable_trials = sum(1 for trial in self.completed_trials 
                              if trial['quality']['is_usable'])
            
            avg_quality = np.mean(self.data_quality_scores) if self.data_quality_scores else 0.0
            
            motor_imagery_trials = sum(1 for trial in self.completed_trials 
                                     if trial['trial_type'] == 'motor_imagery')
            rest_trials = sum(1 for trial in self.completed_trials 
                            if trial['trial_type'] == 'rest')
            
            return {
                'total_trials': total_trials,
                'usable_trials': usable_trials,
                'usability_rate': usable_trials / total_trials if total_trials > 0 else 0.0,
                'average_quality': avg_quality,
                'motor_imagery_trials': motor_imagery_trials,
                'rest_trials': rest_trials,
                'session_duration': time.time(),  # 实际应该记录开始时间
                'data_shape': (len(self.collected_data), 8, 250) if self.collected_data else (0, 0, 0)  # 与2秒窗口(250样本)保持一致
            }
            
        except Exception as e:
            logger.error(f"计算会话统计失败: {e}")
            return {}

    def _collect_rehabilitation_data(self, eeg_data: np.ndarray, trial_type: str):
        """
        收集康复评估数据

        Args:
            eeg_data: EEG数据 [8, 250]
            trial_type: 试次类型 ("motor_imagery" 或 "rest")
        """
        try:
            current_time = time.time()
            current_trial = self.current_trial

            if trial_type == "motor_imagery":
                self.motor_imagery_trials.append(eeg_data.copy())
                self.motor_timestamps.append(current_time)
                self.motor_trial_numbers.append(current_trial)
                logger.debug(f"🧠 收集运动想象数据: 试次 {current_trial}")

            elif trial_type == "rest":
                self.rest_trials.append(eeg_data.copy())
                self.rest_timestamps.append(current_time)
                self.rest_trial_numbers.append(current_trial)
                logger.debug(f"😌 收集平静状态数据: 试次 {current_trial}")

        except Exception as e:
            logger.error(f"❌ 康复数据收集失败: {e}")

    def _save_rehabilitation_data(self):
        """保存康复评估数据到加密文件"""
        try:
            if not self.rehabilitation_data_collection_enabled or not self.rehabilitation_collector:
                return

            if not self.patient_id:
                logger.warning("⚠️ 患者ID未设置，跳过康复数据保存")
                return

            # 检查是否有数据需要保存
            if not self.motor_imagery_trials and not self.rest_trials:
                logger.info("📝 没有康复数据需要保存")
                return

            # 转换为numpy数组
            motor_data = np.array(self.motor_imagery_trials) if self.motor_imagery_trials else np.empty((0, 8, 250))
            rest_data = np.array(self.rest_trials) if self.rest_trials else np.empty((0, 8, 250))
            motor_timestamps = np.array(self.motor_timestamps) if self.motor_timestamps else np.empty(0)
            rest_timestamps = np.array(self.rest_timestamps) if self.rest_timestamps else np.empty(0)
            motor_trial_numbers = np.array(self.motor_trial_numbers) if self.motor_trial_numbers else np.empty(0)
            rest_trial_numbers = np.array(self.rest_trial_numbers) if self.rest_trial_numbers else np.empty(0)

            # 生成会话信息
            from datetime import datetime
            now = datetime.now()
            session_date = now.strftime("%Y%m%d")
            session_time = now.strftime("%H%M%S")

            # 保存康复数据
            session_dir = self.rehabilitation_collector.save_training_data(
                patient_id=self.patient_id,
                session_date=session_date,
                session_time=session_time,
                motor_imagery_data=motor_data,
                rest_data=rest_data,
                motor_timestamps=motor_timestamps,
                rest_timestamps=rest_timestamps,
                motor_trial_numbers=motor_trial_numbers,
                rest_trial_numbers=rest_trial_numbers
            )

            logger.info(f"✅ 康复评估数据已保存: {session_dir}")

            # 清空缓存
            self._clear_rehabilitation_data_cache()

        except Exception as e:
            logger.error(f"❌ 保存康复评估数据失败: {e}")

    def _clear_rehabilitation_data_cache(self):
        """清空康复数据缓存"""
        self.motor_imagery_trials.clear()
        self.rest_trials.clear()
        self.motor_timestamps.clear()
        self.rest_timestamps.clear()
        self.motor_trial_numbers.clear()
        self.rest_trial_numbers.clear()
        logger.debug("🧹 康复数据缓存已清空")



