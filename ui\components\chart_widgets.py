# -*- coding: utf-8 -*-
"""
图表组件
Chart Widgets

用于显示各种数据可视化图表的组件
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QScrollArea, QComboBox, QPushButton
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QPixmap
from typing import List, Dict, Any
from utils.chart_helpers import ChartGenerator
from .interactive_chart import InteractiveChartCanvas





class ChartWidget(QFrame):
    """基础图表组件"""
    
    # 图表点击信号
    chart_clicked = Signal(str, dict)  # 传递图表类型和数据
    
    def __init__(self, title: str = "", chart_type: str = "line", 
                 theme: str = "tech", parent=None):
        super().__init__(parent)
        
        self.title_text = title
        self.chart_type = chart_type
        self.theme = theme
        self.chart_data = []
        
        # 图表生成器
        self.chart_generator = ChartGenerator(theme)
        
        # 设置组件属性
        self.setObjectName("chart_widget")
        self.setFrameStyle(QFrame.Shape.NoFrame)
        
        # 初始化UI
        self._init_ui()
        
        # 延迟加载定时器
        self.load_timer = QTimer()
        self.load_timer.setSingleShot(True)
        self.load_timer.timeout.connect(self._delayed_load)
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建头部
        self._create_header(main_layout)
        
        # 创建图表显示区域
        self._create_chart_area(main_layout)
    
    def _create_header(self, layout: QVBoxLayout):
        """创建头部区域"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(12)
        
        # 标题
        self.title_label = QLabel(self.title_text)
        self.title_label.setObjectName("chart_title")
        self.title_label.setFont(QFont("Microsoft YaHei", 13, QFont.Weight.Bold))
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # 图表类型选择（可选）
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.setObjectName("chart_type_combo")
        self.chart_type_combo.addItems(["折线图", "柱状图", "饼图"])
        self.chart_type_combo.setVisible(False)  # 默认隐藏
        self.chart_type_combo.currentTextChanged.connect(self._on_chart_type_changed)
        header_layout.addWidget(self.chart_type_combo)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setObjectName("chart_refresh_btn")
        self.refresh_btn.setFont(QFont("Microsoft YaHei", 10))
        self.refresh_btn.clicked.connect(self.refresh_chart)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(header_widget)
    
    def _create_chart_area(self, layout: QVBoxLayout):
        """创建图表显示区域"""
        # 图表容器
        self.chart_container = QFrame()
        self.chart_container.setObjectName("chart_container")
        self.chart_container.setMinimumHeight(260)
        
        chart_layout = QVBoxLayout(self.chart_container)
        chart_layout.setContentsMargins(6, 6, 6, 6)
        chart_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 图表显示标签
        self.chart_label = QLabel("暂无数据")
        self.chart_label.setObjectName("chart_display")
        self.chart_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.chart_label.setFont(QFont("Microsoft YaHei", 9))
        self.chart_label.setMinimumHeight(180)
        self.chart_label.setScaledContents(True)  # 默认启用缩放
        chart_layout.addWidget(self.chart_label)
        
        layout.addWidget(self.chart_container)
    
    def set_data(self, data: List[Dict[str, Any]], delayed: bool = True):
        """
        设置图表数据
        
        Args:
            data: 图表数据
            delayed: 是否延迟加载
        """
        self.chart_data = data
        
        if delayed:
            # 延迟加载以避免UI阻塞
            self.load_timer.start(100)
        else:
            self._update_chart()
    
    def _delayed_load(self):
        """延迟加载图表"""
        self._update_chart()
    
    def _update_chart(self):
        """更新图表显示"""
        if not self.chart_data:
            self.chart_label.setText("暂无数据")
            self.chart_label.setPixmap(QPixmap())
            return
        
        try:
            # 根据图表类型设置缩放策略
            if self.chart_type == "pie":
                self.chart_label.setScaledContents(False)  # 饼图禁用缩放，保持圆形
            else:
                self.chart_label.setScaledContents(True)   # 其他图表启用缩放

            # 根据图表类型生成图表
            if self.chart_type == "line":
                pixmap = self._create_line_chart()
            elif self.chart_type == "bar":
                pixmap = self._create_bar_chart()
            elif self.chart_type == "pie":
                pixmap = self._create_pie_chart()
            else:
                pixmap = self._create_line_chart()  # 默认折线图

            if not pixmap.isNull():
                self.chart_label.setPixmap(pixmap)
                self.chart_label.setText("")
            else:
                self.chart_label.setText("图表生成失败")
                self.chart_label.setPixmap(QPixmap())
                
        except Exception as e:
            print(f"更新图表失败: {e}")
            self.chart_label.setText("图表生成失败")
            self.chart_label.setPixmap(QPixmap())
    
    def _create_line_chart(self) -> QPixmap:
        """创建折线图"""
        # 子类需要重写此方法
        return QPixmap()
    
    def _create_bar_chart(self) -> QPixmap:
        """创建柱状图"""
        # 子类需要重写此方法
        return QPixmap()
    
    def _create_pie_chart(self) -> QPixmap:
        """创建饼图"""
        # 子类需要重写此方法
        return QPixmap()
    
    def _on_chart_type_changed(self, chart_type_text: str):
        """图表类型改变"""
        type_mapping = {
            "折线图": "line",
            "柱状图": "bar", 
            "饼图": "pie"
        }
        
        new_type = type_mapping.get(chart_type_text, "line")
        if new_type != self.chart_type:
            self.chart_type = new_type
            self._update_chart()
    
    def refresh_chart(self):
        """刷新图表"""
        self._update_chart()
    
    def enable_chart_type_selection(self, enabled: bool = True):
        """启用图表类型选择"""
        self.chart_type_combo.setVisible(enabled)
    
    def set_title(self, title: str):
        """设置图表标题"""
        self.title_text = title
        self.title_label.setText(title)
    
    def update_theme(self, theme: str):
        """更新主题"""
        self.theme = theme
        self.chart_generator = ChartGenerator(theme)
        self._update_chart()


class InteractiveTrendChartWidget(QFrame):
    """交互式趋势图表组件"""

    # 图表点击信号
    chart_clicked = Signal(str, dict)

    def __init__(self, title: str = "趋势分析", theme: str = "tech", parent=None):
        super().__init__(parent)

        self.title_text = title
        self.theme = theme
        self.chart_data = []
        self.chart_type = "line"

        # 设置组件属性
        self.setObjectName("chart_widget")
        self.setFrameStyle(QFrame.Shape.NoFrame)

        # 初始化UI
        self._init_ui()

        # 延迟加载定时器
        self.load_timer = QTimer()
        self.load_timer.setSingleShot(True)
        self.load_timer.timeout.connect(self._delayed_load)

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建头部
        self._create_header(main_layout)

        # 创建交互式图表区域
        self._create_interactive_chart_area(main_layout)

    def _create_header(self, layout: QVBoxLayout):
        """创建头部区域"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(12)

        # 标题
        self.title_label = QLabel(self.title_text)
        self.title_label.setObjectName("chart_title")
        self.title_label.setFont(QFont("Microsoft YaHei", 13, QFont.Weight.Bold))
        header_layout.addWidget(self.title_label)

        header_layout.addStretch()

        # 图表类型选择
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.setObjectName("chart_type_combo")
        self.chart_type_combo.addItems(["折线图", "柱状图"])
        self.chart_type_combo.currentTextChanged.connect(self._on_chart_type_changed)
        header_layout.addWidget(self.chart_type_combo)

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setObjectName("chart_refresh_btn")
        self.refresh_btn.setFont(QFont("Microsoft YaHei", 10))
        self.refresh_btn.clicked.connect(self.refresh_chart)
        header_layout.addWidget(self.refresh_btn)

        layout.addWidget(header_widget)

    def _create_interactive_chart_area(self, layout: QVBoxLayout):
        """创建交互式图表显示区域"""
        # 图表容器
        self.chart_container = QFrame()
        self.chart_container.setObjectName("chart_container")
        self.chart_container.setMinimumHeight(260)

        chart_layout = QVBoxLayout(self.chart_container)
        chart_layout.setContentsMargins(6, 6,6, 6)

        # 创建交互式图表画布
        self.chart_canvas = InteractiveChartCanvas(theme=self.theme)
        self.chart_canvas.point_hovered.connect(self._on_point_hovered)

        # 添加画布
        chart_layout.addWidget(self.chart_canvas)

        layout.addWidget(self.chart_container)

    def set_data(self, data: List[Dict[str, Any]], delayed: bool = True):
        """设置图表数据"""
        self.chart_data = data

        if delayed:
            # 延迟加载以避免UI阻塞
            self.load_timer.start(100)
        else:
            self._update_chart()

    def _delayed_load(self):
        """延迟加载图表"""
        self._update_chart()

    def _update_chart(self):
        """更新图表显示"""
        if self.chart_canvas:
            self.chart_canvas.set_data(self.chart_data, self.chart_type)

    def _on_chart_type_changed(self, chart_type_text: str):
        """图表类型改变"""
        type_mapping = {
            "折线图": "line",
            "柱状图": "bar"
        }

        new_type = type_mapping.get(chart_type_text, "line")
        if new_type != self.chart_type:
            self.chart_type = new_type
            self._update_chart()

    def _on_point_hovered(self, data_point: Dict[str, Any]):
        """数据点悬停事件"""
        # 可以在这里添加额外的悬停处理逻辑
        pass

    def refresh_chart(self):
        """刷新图表"""
        self._update_chart()

    def set_title(self, title: str):
        """设置图表标题"""
        self.title_text = title
        self.title_label.setText(title)

    def update_theme(self, theme: str):
        """更新主题"""
        self.theme = theme
        if self.chart_canvas:
            self.chart_canvas.update_theme(theme)





class DistributionChartWidget(ChartWidget):
    """分布图表组件"""

    def __init__(self, title: str = "分布分析", theme: str = "tech", parent=None):
        super().__init__(title, "pie", theme, parent)

        # 启用图表类型选择，但只显示饼图和柱状图
        self.enable_chart_type_selection(True)
        # 移除折线图选项
        if hasattr(self, 'chart_type_combo'):
            self.chart_type_combo.clear()
            self.chart_type_combo.addItems(["饼图", "柱状图"])
    
    def _create_pie_chart(self) -> QPixmap:
        """创建分布饼图"""
        if not self.chart_data:
            return QPixmap()

        return self.chart_generator.create_pie_chart(
            data=self.chart_data,
            label_field='effect',
            value_field='count',
            title=self.title_text
            # 使用默认的优化尺寸 (8, 8)
        )
    
    def _create_bar_chart(self) -> QPixmap:
        """创建分布柱状图"""
        if not self.chart_data:
            return QPixmap()

        return self.chart_generator.create_bar_chart(
            data=self.chart_data,
            x_field='effect',
            y_field='count',
            title=self.title_text,
            xlabel='治疗效果',
            ylabel='次数'  # 修改Y轴标签为"次数"
            # 使用默认的优化尺寸 (12, 8)
        )

    def _on_chart_type_changed(self, chart_type_text: str):
        """图表类型改变 - 只支持饼图和柱状图"""
        type_mapping = {
            "柱状图": "bar",
            "饼图": "pie"
        }

        new_type = type_mapping.get(chart_type_text, "pie")
        if new_type != self.chart_type:
            self.chart_type = new_type
            self._update_chart()


class ComparisonChartWidget(ChartWidget):
    """对比图表组件"""
    
    def __init__(self, title: str = "对比分析", theme: str = "tech", parent=None):
        super().__init__(title, "bar", theme, parent)
    
    def _create_bar_chart(self) -> QPixmap:
        """创建对比柱状图"""
        if not self.chart_data:
            return QPixmap()
        
        return self.chart_generator.create_bar_chart(
            data=self.chart_data,
            x_field='doctor_name',
            y_field='avg_score',
            title=self.title_text,
            xlabel='医生',
            ylabel='平均分数',
            figsize=(10, 6)
        )



